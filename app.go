package main

import (
	"context"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/scheduler"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
)

// App struct
type App struct {
	ctx                  context.Context
	Account              *service.AccountService
	Advertiser           *service.AdvertiserService
	Project              *service.ProjectService
	Promotion            *service.PromotionService
	Proxy                *service.ProxyService
	TextInfo             *service.TextInfoService
	Ffmpeg               *service.FfmpegService
	System               *service.SystemService
	Window               *service.WindowService
	Strategy             *service.StrategyService
	StrategyBinding      *service.StrategyBindingService
	StrategyLog          *service.StrategyLogService
	Log                  *service.LogService
	ClipTask             *service.ClipTaskService
	Config               *service.ConfigService
	SensitiveWord        *service.SensitiveWordService
	SensitiveWordMonitor *service.SensitiveWordMonitorService
	RetargetingTag       *service.RetargetingTagService
	DataSyncService      *service.DataSyncService
}

// NewApp creates a new App application struct
func NewApp() *App {
	svcCtx := svc.NewServiceContext(config.Config{})

	// 初始化调度器管理器
	svcCtx.SchedulerManager = scheduler.NewSchedulerManager(svcCtx)

	return &App{
		Account:              service.NewAccountService(svcCtx),
		Advertiser:           service.NewAdvertiserService(svcCtx),
		Project:              service.NewProjectService(svcCtx),
		Promotion:            service.NewPromotionService(svcCtx),
		Proxy:                service.NewProxyService(svcCtx),
		TextInfo:             service.NewTextInfoService(svcCtx),
		Ffmpeg:               service.NewFfmpegService(svcCtx),
		System:               service.NewSystemService(svcCtx),
		Window:               service.NewWindowService(svcCtx),
		Strategy:             service.NewStrategyService(svcCtx),
		StrategyBinding:      service.NewStrategyBindingService(svcCtx),
		StrategyLog:          service.NewStrategyLogService(svcCtx),
		Log:                  service.NewLogService(svcCtx),
		ClipTask:             service.NewClipTaskService(svcCtx),
		Config:               service.NewConfigService(svcCtx),
		SensitiveWord:        service.NewSensitiveWordService(svcCtx),
		SensitiveWordMonitor: service.NewSensitiveWordMonitorService(svcCtx),
		RetargetingTag:       service.NewRetargetingTagService(svcCtx),
		DataSyncService:      service.NewDataSyncService(svcCtx),
	}
}

// SetApp sets the application context for all services
func (a *App) SetApp(app *application.App) {
	a.Account.SetApp(app)
	a.Advertiser.SetApp(app)
	a.Project.SetApp(app)
	a.Promotion.SetApp(app)
	a.Proxy.SetApp(app)
	a.TextInfo.SetApp(app)
	a.Ffmpeg.SetApp(app)
	a.System.SetApp(app)
	a.Window.SetApp(app)
	a.Strategy.SetApp(app)
	a.StrategyBinding.SetApp(app)
	a.StrategyLog.SetApp(app)
	a.Log.SetApp(app)
	a.ClipTask.SetApp(app)
	a.Config.SetApp(app)
	a.SensitiveWord.SetApp(app)
	a.SensitiveWordMonitor.SetApp(app)
	a.RetargetingTag.SetApp(app)
	a.DataSyncService.SetApp(app)
}
