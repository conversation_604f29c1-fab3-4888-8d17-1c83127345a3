// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/GolandProjects/oceanEngineManager/frontend/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/GolandProjects/oceanEngineManager/frontend/node_modules/@vitejs/plugin-react/dist/index.mjs";
import tailwindcss from "file:///C:/Users/<USER>/GolandProjects/oceanEngineManager/frontend/node_modules/@tailwindcss/vite/dist/index.mjs";
import path from "path";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\GolandProjects\\oceanEngineManager\\frontend";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src"),
      "@bindings": path.resolve(__vite_injected_original_dirname, "./bindings"),
      "@services": path.resolve(__vite_injected_original_dirname, "./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service"),
      "@models": path.resolve(__vite_injected_original_dirname, "./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"),
      "@types": path.resolve(__vite_injected_original_dirname, "./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"),
      "@reqTypes": path.resolve(__vite_injected_original_dirname, "./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"),
      "@result": path.resolve(__vite_injected_original_dirname, "./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result")
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
