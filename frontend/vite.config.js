import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@bindings': path.resolve(__dirname, './bindings'),
      '@services': path.resolve(__dirname, './bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service'),
      '@models': path.resolve(__dirname, './bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model'),
      '@types': path.resolve(__dirname, './bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types'),
      '@reqTypes': path.resolve(__dirname, './bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType'),
      '@result': path.resolve(__dirname, './bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result'),
    }
  }
})


