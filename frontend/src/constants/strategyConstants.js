// 策略类型常量
export const STRATEGY_TYPES = {
  ADVERTISER: "advertiser",
  PROJECT: "project",
  PROMOTION: "promotion",
  GLOBAL: "global",
};

// 策略类型显示名称
export const STRATEGY_TYPE_LABELS = {
  [STRATEGY_TYPES.ADVERTISER]: "广告主策略",
  [STRATEGY_TYPES.PROJECT]: "项目策略",
  [STRATEGY_TYPES.PROMOTION]: "广告策略",
  [STRATEGY_TYPES.GLOBAL]: "全局策略",
};

// 策略类型描述
export const STRATEGY_TYPE_DESCRIPTIONS = {
  [STRATEGY_TYPES.ADVERTISER]: "只针对广告主执行操作，不涉及具体广告",
  [STRATEGY_TYPES.PROJECT]: "只针对项目执行操作，不涉及具体广告",
  [STRATEGY_TYPES.PROMOTION]: "只针对单个广告执行操作，会评估广告条件",
  [STRATEGY_TYPES.GLOBAL]: "根据绑定对象应用到其下属的所有广告",
};

// 策略绑定类型
export const BINDING_TYPES = {
  ADVERTISER: "advertiser",
  PROJECT: "project",
  PROMOTION: "promotion",
};

// 绑定类型显示名称
export const BINDING_TYPE_LABELS = {
  [BINDING_TYPES.ADVERTISER]: "广告主",
  [BINDING_TYPES.PROJECT]: "项目",
  [BINDING_TYPES.PROMOTION]: "广告",
};

// 策略类型和绑定类型的关系
export const STRATEGY_BINDING_MAPPING = {
  [STRATEGY_TYPES.ADVERTISER]: [BINDING_TYPES.ADVERTISER],
  [STRATEGY_TYPES.PROJECT]: [BINDING_TYPES.PROJECT],
  [STRATEGY_TYPES.PROMOTION]: [BINDING_TYPES.PROMOTION],
  [STRATEGY_TYPES.GLOBAL]: [BINDING_TYPES.ADVERTISER, BINDING_TYPES.PROJECT],
};

// 获取策略类型选项
export const getStrategyTypeOptions = () => {
  return Object.entries(STRATEGY_TYPE_LABELS).map(([value, label]) => ({
    value,
    label,
    description: STRATEGY_TYPE_DESCRIPTIONS[value],
  }));
};

// 获取绑定类型选项
export const getBindingTypeOptions = (strategyType) => {
  const allowedBindingTypes = STRATEGY_BINDING_MAPPING[strategyType] || [];
  return allowedBindingTypes.map((bindingType) => ({
    value: bindingType,
    label: BINDING_TYPE_LABELS[bindingType],
  }));
};

// 验证策略类型和绑定类型是否匹配
export const isValidStrategyBinding = (strategyType, bindingType) => {
  const allowedBindingTypes = STRATEGY_BINDING_MAPPING[strategyType];
  return allowedBindingTypes && allowedBindingTypes.includes(bindingType);
};
