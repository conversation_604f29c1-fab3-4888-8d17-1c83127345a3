import { useState, useCallback } from 'react';

/**
 * 右键菜单管理Hook
 * 提供右键菜单的显示、隐藏和位置管理功能
 */
export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    items: [],
    data: null // 右键点击的数据对象
  });

  // 显示右键菜单
  const showContextMenu = useCallback((event, items, data = null) => {
    event.preventDefault();
    event.stopPropagation();

    const { clientX, clientY } = event;
    
    setContextMenu({
      visible: true,
      x: clientX,
      y: clientY,
      items: items || [],
      data
    });
  }, []);

  // 隐藏右键菜单
  const hideContextMenu = useCallback(() => {
    setContextMenu(prev => ({
      ...prev,
      visible: false
    }));
  }, []);

  // 处理菜单项点击（带数据传递）
  const handleMenuItemClick = useCallback((item, data) => {
    if (item.disabled) return;
    
    if (item.onClick) {
      item.onClick(data);
    }
    hideContextMenu();
  }, [hideContextMenu]);

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu,
    handleMenuItemClick
  };
}; 