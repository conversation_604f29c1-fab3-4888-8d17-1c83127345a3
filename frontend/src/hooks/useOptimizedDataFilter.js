import { useState, useMemo, useCallback, useRef, useEffect } from 'react';

/**
 * 优化的数据过滤和搜索Hook
 * 使用防抖、memorization等技术提升大数据量场景下的性能
 */
export const useOptimizedDataFilter = ({
  data = [],
  searchFields = [], // 搜索字段数组，如 ['name', 'id']
  filterFields = {}, // 额外过滤字段，如 { status: 1, type: 'active' }
  debounceDelay = 300,
  pageSize = 0 // 0表示不分页，返回所有数据
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState(filterFields);
  
  // 防抖搜索关键词
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const searchTimeoutRef = useRef();

  // 防抖处理搜索词
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // 搜索时重置到第一页
    }, debounceDelay);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm, debounceDelay]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // 获取嵌套对象值的辅助函数
  const getNestedValue = useCallback((obj, path) => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }, []);

  // 核心过滤逻辑 - 使用useMemo优化性能
  const filteredData = useMemo(() => {
    let result = [...data];

    // 搜索过滤
    if (debouncedSearchTerm && searchFields.length > 0) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      result = result.filter(item => 
        searchFields.some(field => {
          const value = getNestedValue(item, field);
          return value && value.toString().toLowerCase().includes(searchLower);
        })
      );
    }

    // 额外字段过滤
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        result = result.filter(item => {
          const itemValue = getNestedValue(item, key);
          return itemValue === value;
        });
      }
    });

    return result;
  }, [data, debouncedSearchTerm, searchFields, filters, getNestedValue]);

  // 分页数据 - 使用useMemo优化性能
  const paginatedData = useMemo(() => {
    if (pageSize <= 0) {
      return filteredData; // 不分页，返回所有数据
    }

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, pageSize]);

  // 分页信息
  const pagination = useMemo(() => {
    const total = filteredData.length;
    const totalPages = pageSize > 0 ? Math.ceil(total / pageSize) : 1;
    
    return {
      current: currentPage,
      pageSize,
      total,
      totalPages,
      hasNext: currentPage < totalPages,
      hasPrev: currentPage > 1
    };
  }, [filteredData.length, currentPage, pageSize]);

  // 搜索处理函数
  const handleSearch = useCallback((term) => {
    setSearchTerm(term);
  }, []);

  // 清空搜索
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
  }, []);

  // 更新过滤器
  const updateFilter = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1); // 过滤时重置到第一页
  }, []);

  // 批量更新过滤器
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
    setCurrentPage(1);
  }, []);

  // 清空所有过滤器
  const clearFilters = useCallback(() => {
    setFilters({});
    setCurrentPage(1);
  }, []);

  // 重置所有状态
  const reset = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setFilters(filterFields);
    setCurrentPage(1);
  }, [filterFields]);

  // 分页控制
  const goToPage = useCallback((page) => {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  }, [filteredData.length, pageSize]);

  const nextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  const prevPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  return {
    // 数据
    filteredData,
    paginatedData,
    pagination,
    
    // 搜索状态
    searchTerm,
    debouncedSearchTerm,
    
    // 过滤状态
    filters,
    
    // 搜索控制
    handleSearch,
    clearSearch,
    
    // 过滤控制
    updateFilter,
    updateFilters,
    clearFilters,
    
    // 分页控制
    goToPage,
    nextPage,
    prevPage,
    
    // 重置
    reset,
    
    // 统计信息
    stats: {
      total: data.length,
      filtered: filteredData.length,
      displayed: paginatedData.length
    }
  };
}; 