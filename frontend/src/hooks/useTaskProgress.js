import { useState, useEffect, useRef } from 'react';
import { Events } from '@wailsio/runtime';

/**
 * 任务进度Hook - 管理任务进度状态和事件监听
 * 
 * @returns {Object} 返回任务进度状态和操作方法
 */
const useTaskProgress = () => {
  const [currentTask, setCurrentTask] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const taskTimeoutRef = useRef(null);

  // 监听任务进度事件
  useEffect(() => {
    const handleTaskProgress = (taskData) => {
      console.log('接收到任务进度事件:', taskData);
      
      if (!taskData) return;

      // 从事件数据中提取真正的任务数据
      let actualTaskData = taskData;
      if (taskData.data && Array.isArray(taskData.data) && taskData.data.length > 0) {
        // 如果是Wails事件格式，取data数组的第一个元素
        actualTaskData = taskData.data[0];
      } else if (taskData.data && typeof taskData.data === 'object') {
        // 如果data是对象，直接使用
        actualTaskData = taskData.data;
      }

      console.log('解析后的任务数据:', actualTaskData);

      if (!actualTaskData || !actualTaskData.task_id) {
        console.warn('无效的任务数据:', actualTaskData);
        return;
      }

      // 更新当前任务
      setCurrentTask(actualTaskData);
      
      // 显示进度条
      if (!isVisible) {
        setIsVisible(true);
      }

      // 清除之前的自动隐藏定时器
      if (taskTimeoutRef.current) {
        clearTimeout(taskTimeoutRef.current);
        taskTimeoutRef.current = null;
      }

      // 根据任务状态处理
      switch (actualTaskData.status) {
        case 'start':
          // 任务开始，显示进度条
          console.log('任务开始:', actualTaskData.task_name);
          break;
          
        case 'progress':
          // 任务进行中，更新进度
          console.log('任务进度更新:', actualTaskData.step_description, actualTaskData.step_progress + '%');
          break;
          
        case 'end':
          // 任务完成，添加到历史记录并在3秒后隐藏
          console.log('任务完成:', actualTaskData.task_name);
          setTaskHistory(prev => [{
            ...actualTaskData,
            completed_at: new Date().toISOString()
          }, ...prev.slice(0, 9)]); // 保留最近10条历史记录
          
          // 3秒后自动隐藏
          taskTimeoutRef.current = setTimeout(() => {
            setIsVisible(false);
            setCurrentTask(null);
          }, 3000);
          break;
          
        case 'error':
          // 任务出错，显示错误信息并在5秒后隐藏
          console.error('任务错误:', actualTaskData.error_message);
          setTaskHistory(prev => [{
            ...actualTaskData,
            completed_at: new Date().toISOString()
          }, ...prev.slice(0, 9)]); // 保留最近10条历史记录
          
          // 5秒后自动隐藏
          taskTimeoutRef.current = setTimeout(() => {
            setIsVisible(false);
            setCurrentTask(null);
          }, 5000);
          break;
          
        default:
          console.warn('未知的任务状态:', actualTaskData.status);
      }
    };

    // 监听主进程事件
    const unsubscribe = Events.On('main_process', handleTaskProgress);
    
    // 同时监听自定义事件（用于测试）
    const handleCustomEvent = (event) => {
      if (event.detail) {
        handleTaskProgress(event.detail);
      }
    };
    
    window.addEventListener('main_process', handleCustomEvent);

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      window.removeEventListener('main_process', handleCustomEvent);
      if (taskTimeoutRef.current) {
        clearTimeout(taskTimeoutRef.current);
      }
    };
  }, [isVisible]);

  // 手动隐藏进度条
  const hideProgress = () => {
    setIsVisible(false);
    setCurrentTask(null);
    if (taskTimeoutRef.current) {
      clearTimeout(taskTimeoutRef.current);
      taskTimeoutRef.current = null;
    }
  };

  // 手动显示进度条（用于调试）
  const showProgress = (taskData) => {
    setCurrentTask(taskData);
    setIsVisible(true);
  };

  // 清空任务历史
  const clearHistory = () => {
    setTaskHistory([]);
  };

  // 获取最近的任务
  const getRecentTask = () => {
    return taskHistory[0] || null;
  };

  return {
    // 状态
    currentTask,
    taskHistory,
    isVisible,
    
    // 方法
    hideProgress,
    showProgress,
    clearHistory,
    getRecentTask,
  };
};

export default useTaskProgress; 