import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  Search,
  Filter,
  CheckSquare,
  Square,
  Trash2,
  Eye,
  EyeOff,
} from "lucide-react";

/**
 * 通用管理页面Hook
 * 处理搜索、筛选、滚动位置管理、快捷键等共同逻辑
 */
export const useManagerPage = (options = {}) => {
  const {
    data = [],
    onSearch,
    onDeleteConfirm,
    onBatchDeleteConfirm,
    onClearSelection,
    onToggleSelectAll,
    searchFields = [], // 要搜索的字段名数组
    statusField = "status_name", // 状态字段名
    filterOptions = [], // 筛选选项
    itemLabel = "项", // 数据项标签
    pageKey = "default", // 用于区分不同页面的滚动位置
  } = options;

  // 对话框状态
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [showFilterDialog, setShowFilterDialog] = useState(false);

  // 筛选状态
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showDeleted, setShowDeleted] = useState(false);

  // 滚动位置管理
  const scrollContainerRef = useRef(null);
  const hasRestoredScroll = useRef(false);
  const scrollPositionKey = `scroll_${pageKey}`;

  // 保存滚动位置
  const saveScrollPosition = useCallback(
    (scrollTop) => {
      if (typeof scrollTop === "number") {
        sessionStorage.setItem(scrollPositionKey, scrollTop.toString());
      }
    },
    [scrollPositionKey]
  );

  // 恢复滚动位置
  const restoreScrollPosition = useCallback(() => {
    const saved = sessionStorage.getItem(scrollPositionKey);
    return saved ? parseInt(saved, 10) : 0;
  }, [scrollPositionKey]);

  // 数据筛选（同时支持搜索过滤和高亮显示）
  const filteredData = useMemo(() => {
    // 确保 data 不为 undefined
    let result = data || [];

    // 已删除数据过滤
    if (!showDeleted) {
      result = result.filter((item) => !item.delete_time);
    }

    // 搜索筛选 - 如果没有任何字段匹配，则不显示该数据
    if (searchTerm && searchTerm.trim() && searchFields.length > 0) {
      const searchLower = searchTerm.toLowerCase().trim();
      result = result.filter((item) => {
        return searchFields.some((field) => {
          const value = item[field];
          if (!value) return false;
          return value.toString().toLowerCase().includes(searchLower);
        });
      });
    }

    // 状态筛选
    if (statusFilter !== "all" && statusField) {
      result = result.filter((item) => {
        const statusValue = item[statusField];
        return statusValue && statusValue.includes(statusFilter);
      });
    }

    return result;
  }, [data, searchTerm, statusFilter, showDeleted, searchFields, statusField]);

  // Ctrl+F 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === "f") {
        e.preventDefault();
        setShowSearchDialog(true);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // 恢复滚动位置
  useEffect(() => {
    if (
      !hasRestoredScroll.current &&
      filteredData.length > 0 &&
      scrollContainerRef.current
    ) {
      const scrollTop = restoreScrollPosition();
      if (scrollTop > 0) {
        setTimeout(() => {
          if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = scrollTop;
          }
        }, 100);
      }
      hasRestoredScroll.current = true;
    }
  }, [filteredData, restoreScrollPosition]);

  // 组件卸载时保存滚动位置
  useEffect(() => {
    return () => {
      if (scrollContainerRef.current) {
        saveScrollPosition(scrollContainerRef.current.scrollTop);
      }
    };
  }, [saveScrollPosition]);

  // 处理滚动事件
  const handleScroll = useCallback(
    (e) => {
      const scrollTop = e.target.scrollTop;
      saveScrollPosition(scrollTop);
    },
    [saveScrollPosition]
  );

  // 搜索处理 - 纯前端筛选
  const handleSearch = useCallback(
    (value) => {
      setSearchTerm(value);
      // 注意：不再调用onSearch，因为现在是纯前端筛选
    },
    []
  );

  // 对话框控制
  const handleShowSearch = useCallback(() => {
    setShowSearchDialog(true);
  }, []);

  const handleCloseSearch = useCallback(() => {
    setShowSearchDialog(false);
  }, []);

  const handleShowFilter = useCallback(() => {
    setShowFilterDialog(true);
  }, []);

  const handleCloseFilter = useCallback(() => {
    setShowFilterDialog(false);
  }, []);

  // 筛选处理
  const handleStatusFilterChange = useCallback((value) => {
    setStatusFilter(value);
  }, []);

  // 清空所有筛选
  const handleClearAllFilters = useCallback(() => {
    setSearchTerm("");
    setStatusFilter("all");
    // 注意：不再调用onSearch，因为现在是纯前端筛选
  }, []);

  // 删除已选中项
  const handleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
  }, []);

  // 生成状态栏筛选配置
  const statusBarFilters = useMemo(() => {
    const filters = [];

    if (searchTerm) {
      filters.push({
        icon: Search,
        iconClass: "text-blue-600",
        label: "搜索",
        value: `"${searchTerm}"`,
        textColor: "#1e40af",
        clearColor: "#60a5fa",
        clearTitle: "清除搜索",
        onClear: () => handleSearch(""),
      });
    }

    if (statusFilter !== "all") {
      const filterOption = filterOptions.find(
        (opt) => opt.value === statusFilter
      );
      if (filterOption) {
        filters.push({
          icon: Filter,
          iconClass: "text-purple-600",
          label: "筛选",
          value: filterOption.label,
          textColor: "#7c3aed",
          clearColor: "#a78bfa",
          clearTitle: "清除筛选",
          onClear: () => setStatusFilter("all"),
        });
      }
    }

    return filters;
  }, [searchTerm, statusFilter, filterOptions, handleSearch]);

  // 生成通用右键菜单项
  const generateContextMenuItems = useCallback(
    (item, selectedItems = [], loading = false, customItems = []) => {
      const commonItems = [
        // 搜索和筛选
        {
          key: "search",
          label: "搜索 (Ctrl+F)",
          icon: Search,
          color: "#3b82f6",
          disabled: loading,
          onClick: handleShowSearch,
        },
      ];

      // 如果有筛选选项，添加筛选菜单
      if (filterOptions.length > 0) {
        commonItems.push({
          key: "filter",
          label: "状态筛选",
          icon: Filter,
          color: "#8b5cf6",
          disabled: loading,
          onClick: handleShowFilter,
        });
      }

      commonItems.push(
        { type: "divider" },
        // 选择操作
        {
          key: "toggleSelectAll",
          label:
            selectedItems.length === filteredData.length ? "取消全选" : "全选",
          icon:
            selectedItems.length === filteredData.length ? Square : CheckSquare,
          color: "#8b5cf6",
          disabled: loading || filteredData.length === 0,
          onClick: onToggleSelectAll,
        },
        {
          key: "clearSelection",
          label: `清空选择 (${selectedItems.length})`,
          icon: Trash2,
          color: "#ef4444",
          disabled: selectedItems.length === 0,
          onClick: onClearSelection,
        }
      );

      // 如果有批量删除处理函数，添加批量删除
      if (onBatchDeleteConfirm) {
        commonItems.push({
          key: "batchDelete",
          label: `批量删除 (${selectedItems.length})`,
          icon: Trash2,
          color: "#ef4444",
          disabled: selectedItems.length === 0,
          onClick: onBatchDeleteConfirm,
        });
      }

      commonItems.push(
        { type: "divider" },
        // 显示已删除
        {
          key: "toggleDeleted",
          label: showDeleted ? "隐藏已删除" : "显示已删除",
          icon: showDeleted ? EyeOff : Eye,
          color: "#8b5cf6",
          onClick: handleShowDeleted,
        }
      );

      // 合并自定义菜单项
      return [...customItems, ...commonItems];
    },
    [
      filteredData,
      filterOptions,
      showDeleted,
      handleShowSearch,
      handleShowFilter,
      onToggleSelectAll,
      onClearSelection,
      onBatchDeleteConfirm,
      handleShowDeleted,
    ]
  );

  return {
    // 状态
    searchTerm,
    statusFilter,
    showDeleted,
    filteredData,
    showSearchDialog,
    showFilterDialog,
    scrollContainerRef,
    statusBarFilters,

    // 方法
    handleSearch,
    handleStatusFilterChange,
    handleClearAllFilters,
    handleShowDeleted,
    handleShowSearch,
    handleCloseSearch,
    handleShowFilter,
    handleCloseFilter,
    handleScroll,
    saveScrollPosition,
    restoreScrollPosition,
    generateContextMenuItems,
  };
};
