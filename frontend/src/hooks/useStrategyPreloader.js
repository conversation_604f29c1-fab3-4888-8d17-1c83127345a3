import { useEffect, useCallback, useRef } from "react";
import { useStrategyMap } from "../contexts/StrategyMapContext";
import strategyService from "../services/strategyService";

/**
 * 策略预加载Hook
 * 用于在应用启动时预加载策略列表，建立策略ID到名称的映射
 */
export const useStrategyPreloader = () => {
  const { updateStrategyMap, strategyMap } = useStrategyMap();
  const isPreloadedRef = useRef(false);
  const retryCountRef = useRef(0);
  const maxRetries = 3;
  const hasAttemptedLoadRef = useRef(false); // 新增：标记是否已经尝试过加载
  const loadingRef = useRef(false); // 新增：防止并发加载
  const debounceTimerRef = useRef(null); // 新增：防抖定时器

  const preloadStrategies = useCallback(
    async (force = false) => {
      // 如果正在加载，则跳过
      if (loadingRef.current) {
        console.log("策略预加载正在进行中，跳过本次调用");
        return;
      }

      // 如果已经预加载过且不是强制刷新，则跳过
      if (isPreloadedRef.current && !force) {
        return;
      }

      // 如果已经尝试过加载且没有策略数据，则不再重试
      if (hasAttemptedLoadRef.current && !force) {
        return;
      }

      // 清除之前的防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // 设置防抖延迟
      debounceTimerRef.current = setTimeout(async () => {
        loadingRef.current = true;

        try {
          console.log("开始预加载策略映射...");
          hasAttemptedLoadRef.current = true;

          // 获取所有策略（不分页，或者使用一个较大的页面大小）
          const response = await strategyService.getStrategyList({
            page: 1,
            pageSize: 1000, // 获取更多策略
            enabled: undefined, // 不筛选启用状态，获取所有策略
          });

          if (response.code === 0 && response.data && response.data.list) {
            // 更新策略映射
            updateStrategyMap(response.data.list);
            isPreloadedRef.current = true;
            retryCountRef.current = 0; // 重置重试计数
            console.log(`预加载了 ${response.data.list.length} 个策略的映射`);
          } else {
            // 如果返回成功但没有数据，说明确实没有策略，标记为已预加载
            if (response.code === 0) {
              isPreloadedRef.current = true;
              retryCountRef.current = 0;
              console.log("策略列表为空，无需预加载");
            } else {
              throw new Error(response.msg || "获取策略列表失败");
            }
          }
        } catch (error) {
          console.warn("预加载策略映射失败:", error);

          // 重试机制
          if (retryCountRef.current < maxRetries) {
            retryCountRef.current++;
            console.log(
              `策略预加载失败，${1000 * retryCountRef.current}ms后重试 (${
                retryCountRef.current
              }/${maxRetries})`
            );
            setTimeout(() => {
              preloadStrategies(force);
            }, 1000 * retryCountRef.current);
          } else {
            console.error("策略预加载最终失败，已达到最大重试次数");
            // 即使失败也标记为已尝试，避免无限重试
            isPreloadedRef.current = true;
          }
        } finally {
          loadingRef.current = false;
        }
      }, 300); // 300ms防抖延迟
    },
    [updateStrategyMap]
  );

  // 在组件挂载时预加载策略
  useEffect(() => {
    preloadStrategies();
  }, [preloadStrategies]);

  // 监听策略映射变化，如果为空则重新加载（但只在未尝试过的情况下）
  useEffect(() => {
    if (
      Object.keys(strategyMap).length === 0 &&
      !isPreloadedRef.current &&
      !hasAttemptedLoadRef.current
    ) {
      console.log("策略映射为空，重新预加载...");
      preloadStrategies(true);
    }
  }, [strategyMap, preloadStrategies]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    preloadStrategies,
    isPreloaded: isPreloadedRef.current,
  };
};
