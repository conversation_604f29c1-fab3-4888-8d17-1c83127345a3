import { useState, useEffect, useCallback, useRef } from 'react';
import { useAccountContext } from '../contexts/AccountContext';
import * as ProjectService from '@services/projectservice.js';
import { toast } from '../components/ui/Toast';

const INITIAL_PAGINATION = {
  page: 1,
  pageSize: 10,
  total: 0
};

export const useProjectList = (preSelectedSubAccount, onSubAccountSelect) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);
  const [selectedItems, setSelectedItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  // 添加编辑状态管理
  const [editingProjectId, setEditingProjectId] = useState(null);
  const [editingProjectName, setEditingProjectName] = useState('');
  
  // 使用全局账户状态
  const { sharedAccount, sharedSubAccount, setAccount, setSubAccount } = useAccountContext();
  
  // 当前使用的账户和子账户：优先使用共享状态，其次使用传入的预选参数
  const selectedAccount = sharedAccount;
  const selectedSubAccount = sharedSubAccount || preSelectedSubAccount;
  
  // 使用ref来追踪查询参数的变化
  const prevParamsRef = useRef(null);

  // 当传入新的预选子账户时，更新到共享状态
  useEffect(() => {
    if (preSelectedSubAccount && (!sharedSubAccount || sharedSubAccount.advertiser_id !== preSelectedSubAccount.advertiser_id)) {
      setSubAccount(preSelectedSubAccount);
      
      // 如果有预选子账户但没有主账户，从子账户信息中构建主账户
      if (!sharedAccount && preSelectedSubAccount.account_id) {
        const mainAccount = {
          account_id: preSelectedSubAccount.account_id,
          account_name: preSelectedSubAccount.main_account_name || `主账户(${preSelectedSubAccount.account_id})`,
          id: `main_${preSelectedSubAccount.account_id}`
        };
        setAccount(mainAccount);
      }
    }
  }, [preSelectedSubAccount, sharedSubAccount, sharedAccount, setAccount, setSubAccount]);

  // 构建查询参数
  const buildQueryParams = useCallback(() => {
    // 根据backend AdvertiserReq结构构建请求参数
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      project_id: '',
      promotion_id: ''
    };

    // 如果有选中的子账户，使用子账户的广告主ID进行筛选
    if (selectedSubAccount?.advertiser_id && selectedAccount?.account_id) {
      params.account_id = selectedAccount.account_id;
      params.keyword = String(selectedSubAccount.advertiser_id);
      if (searchTerm) {
        params.project_name = searchTerm;
      }
    } else if (selectedAccount?.account_id) {
      // 如果只有主账户，使用主账户ID
      params.account_id = selectedAccount.account_id;
      params.keyword = '';
      if (searchTerm) {
        params.project_name = searchTerm;
      }
    }

    return params;
  }, [pagination.page, pagination.pageSize, selectedAccount, selectedSubAccount, searchTerm]);

  // 加载数据
  const loadData = useCallback(async (force = false) => {
    if (!selectedAccount) {
      setData([]);
      setPagination(prev => ({ ...prev, total: 0 }));
      return;
    }

    const currentParams = buildQueryParams();
    const paramsChanged = JSON.stringify(currentParams) !== JSON.stringify(prevParamsRef.current);
    
    // 如果参数没有变化且不是强制刷新，则不重新加载
    if (!force && !paramsChanged && prevParamsRef.current) {
      return;
    }

    prevParamsRef.current = currentParams;
    setLoading(true);

    try {
      const result = await ProjectService.GetProjectListAsync(currentParams);
      
      if (result && result.code === 0 && result.data && result.data.data) {
        // 解析嵌套的数据结构
        const dataList = result.data.data.data_list || [];
        const paginationInfo = result.data.data.pagination || {};
        
        setData(dataList);
        setPagination(prev => ({
          ...prev,
          total: paginationInfo.total || 0
        }));
      } else {
        console.error('加载项目数据失败:', result?.msg);
        toast.error('加载项目数据失败: ' + (result?.msg || '未知错误'));
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      toast.error('加载项目数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [buildQueryParams, selectedAccount]);

  // 删除项目
  const deleteProject = async (projectData) => {
    try {
      // 构建DeleteProjectAsyncReq需要的请求结构
      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        advertiser_id: String(projectData.advertiser_id || ''), // string
        project_ids: [String(projectData.project_id || '')] // []string，即使是单个删除也是数组
      };
      
      console.log('调用DeleteProjectAsync，参数:', req);
      
      // 添加加载提示
      toast.info('正在删除项目，请稍候...');
      
      const result = await ProjectService.DeleteProjectAsync(req);
      
      if (result?.code === 0) {
        // 明显的成功提示
        toast.success(`🎉 删除成功！项目"${projectData.project_name}"已被删除`, 4000);
        
        // 延迟1.5秒后刷新列表
        setTimeout(async () => {
          await loadData(true);
          console.log('项目删除成功，列表已刷新');
        }, 1500);
        
      } else {
        toast.error(`❌ 删除项目失败: ${result?.msg || '未知错误'}`, 5000);
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      toast.error(`❌ 删除项目失败: ${error.message}`, 5000);
    }
  };

  // 批量删除
  const batchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.warning('请选择要删除的项目');
      return;
    }

    try {
      // 构建BatchDeleteProjectAsyncReq需要的请求结构
      const accountDetailList = selectedItems.map(projectId => {
        const projectData = data.find(item => item.project_id === projectId);
        return {
          advertiser_id: String(projectData?.advertiser_id || ''), // string
          id: String(projectData?.project_id || ''), // string
          name: String(projectData?.project_name || '') // string
        };
      });

      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        account_detail_list: accountDetailList
      };
      
      console.log('调用BatchDeleteProjectAsync，参数:', req);
      
      // 添加加载提示
      toast.info(`正在批量删除 ${selectedItems.length} 个项目，请稍候...`);
      
      const result = await ProjectService.BatchDeleteProjectAsync(req);
      
      if (result?.code === 0) {
        // 明显的成功提示
        toast.success(`🎉 批量删除成功！已删除 ${selectedItems.length} 个项目`, 4000);
        
        // 立即清空选中项
        setSelectedItems([]);
        
        // 延迟1.5秒后刷新列表
        setTimeout(async () => {
          await loadData(true);
          console.log(`批量删除 ${selectedItems.length} 个项目成功，列表已刷新`);
        }, 1500);
        
      } else {
        toast.error(`❌ 批量删除项目失败: ${result?.msg || '未知错误'}`, 5000);
      }
    } catch (error) {
      console.error('批量删除项目失败:', error);
      toast.error(`❌ 批量删除项目失败: ${error.message}`, 5000);
    }
  };

  // 更新项目状态
  const updateStatus = async (project, status) => {
    try {
      // 构建状态映射：projectId -> status
      const statusMap = {};
      statusMap[String(project.project_id)] = status;

      const req = {
        account_id: sharedAccount?.account_id || 0,
        advertiser_id: parseInt(project.advertiser_id) || 0,
        status_map: statusMap
      };

      console.log('调用UpdateProjectStatus，参数:', req);

      // 添加加载提示
      toast.info('正在更新项目状态，请稍候...');
      
      const result = await ProjectService.UpdateProjectStatus(req);
      
      if (result?.code === 0) {
        // 明显的成功提示
        toast.success(`🎉 状态更新成功！项目"${project.project_name}"状态已更新`, 4000);

        // 延迟1.5秒后刷新列表
        setTimeout(() => {
          loadData(true);
          console.log('项目状态更新成功，列表已刷新');
        }, 1500);

      } else {
        toast.error(`❌ 状态更新失败: ${result?.msg || '未知错误'}`, 5000);
      }
    } catch (error) {
      console.error('状态更新失败:', error);
      toast.error(`❌ 状态更新失败: ${error.message}`, 5000);
    }
  };

  // 开始编辑项目名称
  const handleEditProjectName = (project) => {
    setEditingProjectId(project.project_id);
    setEditingProjectName(project.project_name || '');
  };

  // 取消编辑项目名称
  const handleCancelEditProjectName = () => {
    setEditingProjectId(null);
    setEditingProjectName('');
  };

  // 保存项目名称
  const handleSaveProjectName = async (project) => {
    if (!editingProjectName.trim()) {
      toast.warning('项目名称不能为空');
      return;
    }

    if (editingProjectName === project.project_name) {
      // 名称没有变化，直接取消编辑
      handleCancelEditProjectName();
      return;
    }

    try {
      const req = {
        account_id: selectedAccount?.account_id || 0,
        advertiser_id: parseInt(project.advertiser_id) || 0,
        project_id: String(project.project_id || ''),
        name: editingProjectName.trim()
      };

      console.log('调用UpdateProjectName，参数:', req);

      // 添加加载提示
      toast.info('正在修改项目名称，请稍候...');

      const result = await ProjectService.UpdateProjectName(req);

      if (result && result.code === 0) {
        // 明显的成功提示
        toast.success(`🎉 修改成功！项目名称已更新为"${editingProjectName}"`, 4000);

        // 取消编辑状态
        handleCancelEditProjectName();

        // 延迟1.5秒后刷新列表
        setTimeout(() => {
          loadData(true);
          console.log('项目名称修改成功，列表已刷新');
        }, 1500);

      } else {
        toast.error(`❌ 修改项目名称失败: ${result?.msg || '未知错误'}`, 5000);
      }
    } catch (error) {
      console.error('修改项目名称失败:', error);
      toast.error(`❌ 修改项目名称失败: ${error.message}`, 5000);
    }
  };

  // 处理编辑输入框的键盘事件
  const handleEditInputKeyDown = (e, project) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveProjectName(project);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEditProjectName();
    }
  };

  // 选择处理
  const toggleSelectItem = (projectId) => {
    setSelectedItems(prev => 
      prev.includes(projectId) ? prev.filter(item => item !== projectId) : [...prev, projectId]
    );
  };

  const toggleSelectAll = () => {
    setSelectedItems(prev => 
      prev.length === data.length && data.length > 0 ? [] : data.map(item => item.project_id)
    );
  };

  // 账号切换处理
  const handleAccountChange = useCallback((account) => {
    // 清空子账户选择
    if (onSubAccountSelect) {
      onSubAccountSelect(null);
    }
    
    if (selectedAccount?.account_id !== account?.account_id) {
      // 清空搜索条件和选中项
      setSearchTerm('');
      setSelectedItems([]);
      setPagination(prev => ({ ...prev, page: 1 }));
      
      // 更新主账户，清空子账户
      setAccount(account);
      setSubAccount(null);
    }
  }, [selectedAccount, setAccount, setSubAccount, onSubAccountSelect]);

  // 搜索处理
  const handleSearch = (value) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // 分页处理
  const handlePageChange = (page, pageSize) => {
    setPagination(prev => ({ ...prev, page, pageSize }));
  };

  // 监听查询条件变化
  useEffect(() => {
    const timer = setTimeout(() => {
      loadData();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [loadData]);

  return {
    loading,
    data,
    pagination,
    selectedItems,
    selectedAccount,
    selectedSubAccount,
    searchTerm,
    editingProjectId,
    editingProjectName,
    setEditingProjectName,
    loadData,
    deleteProject,
    batchDelete,
    updateStatus,
    toggleSelectItem,
    toggleSelectAll,
    handleAccountChange,
    handleSearch,
    handlePageChange,
    handleEditProjectName,
    handleCancelEditProjectName,
    handleSaveProjectName,
    handleEditInputKeyDown
  };
}; 