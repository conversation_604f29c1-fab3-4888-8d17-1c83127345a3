@import 'tailwindcss';

/* 设计系统变量定义 */
:root {
  /* 主品牌色 */
  --primary-color: #07C160;
  --primary-hover: #06AD55;
  --primary-active: #059A4A;
  
  /* 背景颜色 */
  --sidebar-bg: #F5F5F5;
  --panel-bg: #FFFFFF;
  --toolbar-bg: #2E2E2E;
  
  /* 边框和分隔线 */
  --border-color: #E5E5E5;
  --hover-bg: #EBEBEB;
  --pressed-bg: #DADADA;
  
  /* 文字颜色 */
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.45);
  --text-disabled: rgba(0, 0, 0, 0.26);
  --text-white: rgba(255, 255, 255, 0.8);
  --text-white-hover: rgba(255, 255, 255, 1);
  
  /* 状态颜色 */
  --success-color: #52C41A;
  --warning-color: #FAAD14;
  --error-color: #FF4D4F;
  --info-color: #1890FF;
  
  /* 阴影 */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-card-hover: 0 4px 12px rgba(0, 0, 0, 0.12);
  --shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.15);
  
  /* 滚动条 */
  --scrollbar-thumb: #C1C1C1;
  --scrollbar-thumb-hover: #A8A8A8;
}

/* 基础字体配置 */
body {
  font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
  font-size: 14px;
  color: var(--text-primary);
  background-color: var(--panel-bg);
}

/* 标题字体 */
h1, h2, h3, h4, h5, h6 {
  font-size: 16px;
  font-weight: 500;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

/* 通用工具类 */
.transition-wechat {
  transition: all 150ms ease-out;
}

.text-timestamp {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 对话框动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 任务进度条相关样式 */
.task-progress-bar {
  /* 确保进度条始终在最前面 */
  z-index: 1000;
  
  /* 进度条动画 */
  .progress-bar-animation {
    transition: width 0.3s ease;
  }
  
  /* 防止进度条内容被选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 当显示进度条时，调整主内容区域的底部边距 */
.app-content-with-progress {
  padding-bottom: 40px; /* 为进度条留出空间 */
}

/* 进度条进入/退出动画 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

.progress-bar-enter {
  animation: slideUp 0.3s ease-out;
}

.progress-bar-exit {
  animation: slideDown 0.3s ease-in;
}

/* 进度条内的文字截断 */
.progress-text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 进度条背景色变量补充 */
:root {
  --bg-tertiary: #F0F0F0;
}
