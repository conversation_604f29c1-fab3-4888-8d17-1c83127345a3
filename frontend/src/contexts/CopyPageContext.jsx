import React, { createContext, useContext, useState, useCallback } from "react";

// 全局复制落地页数据结构
const CopyPageContext = createContext();

/**
 * 复制落地页提供者组件
 */
export const CopyPageProvider = ({ children }) => {
  // 存储复制时记录的account_id和advertiser_id
  const [copiedPageSource, setCopiedPageSource] = useState(null);

  // 复制落地页（存储账户和广告主信息以及选择的落地页数组）
  const copyPageSource = useCallback((accountId, advertiserId, pages = []) => {
    console.log("CopyPageContext - 复制落地页源信息:", {
      accountId,
      advertiserId,
      pages,
    });
    setCopiedPageSource({
      account_id: accountId,
      advertiser_id: advertiserId,
      pages: pages,
    });
    console.log("已复制落地页源信息到全局key:", { accountId, advertiserId, pages });
  }, []);

  // 获取复制的落地页源信息
  const getCopiedPageSource = useCallback(() => {
    console.log("CopyPageContext - 获取复制的落地页源信息:", copiedPageSource);
    return copiedPageSource;
  }, [copiedPageSource]);

  // 清除复制的落地页源信息
  const clearCopiedPageSource = useCallback(() => {
    console.log("CopyPageContext - 清除复制的落地页源信息");
    setCopiedPageSource(null);
    console.log("已清除全局key中的落地页源信息");
  }, []);

  // 检查是否有复制的落地页源信息
  const hasCopiedPageSource = useCallback(() => {
    const hasCopied = copiedPageSource !== null;
    console.log("CopyPageContext - 检查是否有复制的落地页源信息:", hasCopied, {
      copiedPageSource,
    });
    return hasCopied;
  }, [copiedPageSource]);

  const value = {
    copiedPageSource,
    copyPageSource,
    getCopiedPageSource,
    clearCopiedPageSource,
    hasCopiedPageSource,
  };

  return (
    <CopyPageContext.Provider value={value}>
      {children}
    </CopyPageContext.Provider>
  );
};

/**
 * 使用复制落地页上下文的Hook
 */
export const useCopyPage = () => {
  const context = useContext(CopyPageContext);
  if (!context) {
    throw new Error(
      "useCopyPage must be used within a CopyPageProvider"
    );
  }
  return context;
}; 