import React, { createContext, useContext, useState, useCallback } from "react";



// 全局复制项目详情数据结构
const CopyProjectDetailContext = createContext();

/**
 * 复制项目详情提供者组件
 */
export const CopyProjectDetailProvider = ({ children }) => {
  // 存储复制的项目详情（使用简单的key-value存储）
  const [copiedProjectDetail, setCopiedProjectDetail] = useState(null);

  // 存储复制的项目基本信息
  const [copiedProjectInfo, setCopiedProjectInfo] = useState(null);

  // 复制项目（存储到全局key）
  const copyProject = useCallback((projectDetail, projectInfo) => {
    console.log("CopyProjectContext - 复制项目:", {
      projectDetail,
      projectInfo,
    });
    setCopiedProjectDetail(projectDetail);
    setCopiedProjectInfo(projectInfo);
    console.log("已复制项目到全局key:", { projectDetail, projectInfo });
  }, []);

  // 粘贴项目（获取全局key中的值）
  const pasteProject = useCallback(() => {
    console.log("CopyProjectContext - 粘贴项目，当前状态:", {
      copiedProjectDetail,
      copiedProjectInfo,
    });
    return {
      projectDetail: copiedProjectDetail,
      projectInfo: copiedProjectInfo,
    };
  }, [copiedProjectDetail, copiedProjectInfo]);

  // 清除复制的项目
  const clearCopiedProject = useCallback(() => {
    console.log("CopyProjectContext - 清除复制的项目");
    setCopiedProjectDetail(null);
    setCopiedProjectInfo(null);
    console.log("已清除全局key中的项目");
  }, []);

  // 检查是否有复制的项目
  const hasCopiedProject = useCallback(() => {
    const hasCopied =
      copiedProjectDetail !== null && copiedProjectInfo !== null;
    console.log("CopyProjectContext - 检查是否有复制的项目:", hasCopied, {
      copiedProjectDetail,
      copiedProjectInfo,
    });
    return hasCopied;
  }, [copiedProjectDetail, copiedProjectInfo]);

  const value = {
    copiedProjectDetail,
    copiedProjectInfo,
    copyProject,
    pasteProject,
    clearCopiedProject,
    hasCopiedProject,
  };

  return (
    <CopyProjectDetailContext.Provider value={value}>
      {children}
    </CopyProjectDetailContext.Provider>
  );
};

/**
 * 使用复制项目详情上下文的Hook
 */
export const useCopyProjectDetail = () => {
  const context = useContext(CopyProjectDetailContext);
  if (!context) {
    throw new Error(
      "useCopyProjectDetail must be used within a CopyProjectDetailProvider"
    );
  }
  return context;
};
