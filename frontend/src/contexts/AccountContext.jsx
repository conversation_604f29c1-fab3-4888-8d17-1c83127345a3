import React, { createContext, useContext, useState, useCallback } from 'react';

// 创建AccountContext
const AccountContext = createContext(null);

// 账户状态提供者组件
export function AccountProvider({ children }) {
  const [sharedAccount, setSharedAccount] = useState(null);
  const [sharedSubAccount, setSharedSubAccount] = useState(null);
  
  // 设置主账户
  const setAccount = useCallback((account) => {
    console.log('AccountContext: 设置主账户', account);
    setSharedAccount(account);
  }, []);
  
  // 设置子账户（广告主）
  const setSubAccount = useCallback((subAccount) => {
    console.log('AccountContext: 设置子账户', subAccount);
    setSharedSubAccount(subAccount);
  }, []);
  
  // 清空账户信息
  const clearAccounts = useCallback(() => {
    console.log('AccountContext: 清空账户信息');
    setSharedAccount(null);
    setSharedSubAccount(null);
  }, []);
  
  // 清空子账户信息
  const clearSubAccount = useCallback(() => {
    console.log('AccountContext: 清空子账户信息');
    setSharedSubAccount(null);
  }, []);

  const value = {
    sharedAccount,
    sharedSubAccount,
    setAccount,
    setSubAccount,
    clearAccounts,
    clearSubAccount
  };

  return (
    <AccountContext.Provider value={value}>
      {children}
    </AccountContext.Provider>
  );
}

// 自定义Hook用于访问账户状态
export function useAccountContext() {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccountContext must be used within an AccountProvider');
  }
  return context;
}

export default AccountContext; 