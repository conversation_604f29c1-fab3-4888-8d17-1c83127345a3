import React, { createContext, useContext, useState, useEffect } from 'react';
import { loginOA, handleResponse } from '../services/authService';

// 创建认证上下文
const AuthContext = createContext();

// 认证提供者组件
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 检查本地存储中的用户信息
  useEffect(() => {
    const savedUser = localStorage.getItem('oa_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        localStorage.removeItem('oa_user');
      }
    }
  }, []);

  // 登录方法
  const login = async (username, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 调用后端OA登录接口
      const response = await loginOA(username, password);
      
      if (response && response.code === 0) {
        const userData = response.data.user;
        setUser(userData);
        setIsAuthenticated(true);
        
        // 保存用户信息到本地存储
        localStorage.setItem('oa_user', JSON.stringify(userData));
        
        return { success: true, user: userData };
      } else {
        // 处理登录失败的情况
        const errorMessage = response?.message || '登录失败';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = error.message || '登录失败，请检查网络连接';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  // 登出方法
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    setError(null);
    localStorage.removeItem('oa_user');
  };

  // 清除错误
  const clearError = () => {
    setError(null);
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的Hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
}; 