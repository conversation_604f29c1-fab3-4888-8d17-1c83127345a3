import React, { createContext, useContext, useState, useCallback } from "react";

// 创建策略映射Context
const StrategyMapContext = createContext();

// 策略映射Provider
export const StrategyMapProvider = ({ children }) => {
  // 策略映射：{ strategyId: strategyName }
  const [strategyMap, setStrategyMap] = useState({});

  // 更新策略映射
  const updateStrategyMap = useCallback((strategies) => {
    if (!Array.isArray(strategies)) return;

    const newMap = {};
    strategies.forEach((strategy) => {
      if (strategy && strategy.id && strategy.name) {
        newMap[strategy.id] = strategy.name;
      }
    });

    setStrategyMap(newMap);
  }, []);

  // 添加单个策略到映射
  const addStrategyToMap = useCallback((strategy) => {
    if (strategy && strategy.id && strategy.name) {
      setStrategyMap((prev) => ({
        ...prev,
        [strategy.id]: strategy.name,
      }));
    }
  }, []);

  // 从映射中移除策略
  const removeStrategyFromMap = useCallback((strategyId) => {
    setStrategyMap((prev) => {
      const newMap = { ...prev };
      delete newMap[strategyId];
      return newMap;
    });
  }, []);

  // 根据策略ID获取策略名称
  const getStrategyName = useCallback(
    (strategyId) => {
      if (!strategyId) return "";
      return strategyMap[strategyId] || `策略#${strategyId}`;
    },
    [strategyMap]
  );

  // 根据策略ID数组获取策略名称数组
  const getStrategyNames = useCallback(
    (strategyIds) => {
      if (!Array.isArray(strategyIds)) return [];
      return strategyIds.map((id) => getStrategyName(id)).filter(Boolean);
    },
    [getStrategyName]
  );

  // 检查策略是否存在
  const hasStrategy = useCallback(
    (strategyId) => {
      return strategyId && strategyMap.hasOwnProperty(strategyId);
    },
    [strategyMap]
  );

  const value = {
    strategyMap,
    updateStrategyMap,
    addStrategyToMap,
    removeStrategyFromMap,
    getStrategyName,
    getStrategyNames,
    hasStrategy,
  };

  return (
    <StrategyMapContext.Provider value={value}>
      {children}
    </StrategyMapContext.Provider>
  );
};

// Hook for using strategy map context
export const useStrategyMap = () => {
  const context = useContext(StrategyMapContext);
  if (!context) {
    throw new Error("useStrategyMap must be used within a StrategyMapProvider");
  }
  return context;
};
