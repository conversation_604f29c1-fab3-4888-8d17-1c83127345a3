import React, { createContext, useContext, useState, useCallback } from "react";

// 全局复制广告详情数据结构
const CopyPromotionDetailContext = createContext();

/**
 * 复制广告详情提供者组件
 */
export const CopyPromotionDetailProvider = ({ children }) => {
  // 存储复制的广告详情（使用简单的key-value存储）
  const [copiedPromotionDetail, setCopiedPromotionDetail] = useState(null);

  // 存储复制的广告基本信息
  const [copiedPromotionInfo, setCopiedPromotionInfo] = useState(null);

  // 复制广告（存储到全局key）
  const copyPromotion = useCallback((promotionDetail, promotionInfo) => {
    console.log("CopyPromotionContext - 复制广告:", {
      promotionDetail,
      promotionInfo,
    });
    setCopiedPromotionDetail(promotionDetail);
    setCopiedPromotionInfo(promotionInfo);
    console.log("已复制广告到全局key:", { promotionDetail, promotionInfo });
  }, []);

  // 粘贴广告（获取全局key中的值）
  const pastePromotion = useCallback(() => {
    console.log("CopyPromotionContext - 粘贴广告，当前状态:", {
      copiedPromotionDetail,
      copiedPromotionInfo,
    });
    return {
      promotionDetail: copiedPromotionDetail,
      promotionInfo: copiedPromotionInfo,
    };
  }, [copiedPromotionDetail, copiedPromotionInfo]);

  // 清除复制的广告
  const clearCopiedPromotion = useCallback(() => {
    console.log("CopyPromotionContext - 清除复制的广告");
    setCopiedPromotionDetail(null);
    setCopiedPromotionInfo(null);
    console.log("已清除全局key中的广告");
  }, []);

  // 检查是否有复制的广告
  const hasCopiedPromotion = useCallback(() => {
    const hasCopied =
      copiedPromotionDetail !== null && copiedPromotionInfo !== null;
    console.log("CopyPromotionContext - 检查是否有复制的广告:", hasCopied, {
      copiedPromotionDetail,
      copiedPromotionInfo,
    });
    return hasCopied;
  }, [copiedPromotionDetail, copiedPromotionInfo]);

  const value = {
    copiedPromotionDetail,
    copiedPromotionInfo,
    copyPromotion,
    pastePromotion,
    clearCopiedPromotion,
    hasCopiedPromotion,
  };

  return (
    <CopyPromotionDetailContext.Provider value={value}>
      {children}
    </CopyPromotionDetailContext.Provider>
  );
};

/**
 * 使用复制广告详情上下文的Hook
 */
export const useCopyPromotionDetail = () => {
  const context = useContext(CopyPromotionDetailContext);
  if (!context) {
    throw new Error(
      "useCopyPromotionDetail must be used within a CopyPromotionDetailProvider"
    );
  }
  return context;
};
