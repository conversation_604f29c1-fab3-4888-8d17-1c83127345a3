/**
 * 通知工具函数
 * 用于管理企业微信消息通知相关的设置和功能
 */

import ConfigService from "../services/configService";

// 本地存储键名（作为备用）
const NOTIFICATION_EMAIL_KEY = "strategyNotificationEmail";

/**
 * 获取保存的通知邮箱
 * @returns {Promise<string>} 保存的邮箱地址，如果没有则返回空字符串
 */
export const getNotificationEmail = async () => {
  try {
    // 优先从数据库获取
    const result = await ConfigService.getStrategyNotificationEmail();
    if (result.code === 0 && result.data) {
      return result.data;
    }

    // 如果数据库中没有，尝试从localStorage获取（兼容旧版本）
    const localEmail = localStorage.getItem(NOTIFICATION_EMAIL_KEY);
    if (localEmail) {
      // 将localStorage中的邮箱迁移到数据库
      await saveNotificationEmail(localEmail);
      localStorage.removeItem(NOTIFICATION_EMAIL_KEY);
      return localEmail;
    }

    return "";
  } catch (error) {
    console.error("获取通知邮箱失败:", error);
    // 降级到localStorage
    return localStorage.getItem(NOTIFICATION_EMAIL_KEY) || "";
  }
};

/**
 * 保存通知邮箱
 * @param {string} email - 邮箱地址
 * @returns {Promise<boolean>} 是否保存成功
 */
export const saveNotificationEmail = async (email) => {
  try {
    console.log("notificationUtils: 开始保存通知邮箱:", email);

    if (!email || typeof email !== "string") {
      console.log("notificationUtils: 邮箱格式无效");
      return false;
    }

    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      console.log("notificationUtils: 邮箱格式验证失败");
      return false;
    }

    // 保存到数据库
    console.log(
      "notificationUtils: 调用ConfigService.setStrategyNotificationEmail"
    );
    const result = await ConfigService.setStrategyNotificationEmail(
      email.trim()
    );
    console.log("notificationUtils: ConfigService返回结果:", result);

    if (result.code === 0) {
      // 同时保存到localStorage作为备用
      localStorage.setItem(NOTIFICATION_EMAIL_KEY, email.trim());
      console.log("notificationUtils: 保存成功");
      return true;
    }

    console.log("notificationUtils: 保存失败，返回码:", result.code);
    return false;
  } catch (error) {
    console.error("保存通知邮箱失败:", error);
    // 降级到localStorage
    try {
      localStorage.setItem(NOTIFICATION_EMAIL_KEY, email.trim());
      return true;
    } catch (localError) {
      console.error("保存到localStorage也失败:", localError);
      return false;
    }
  }
};

/**
 * 清除通知邮箱设置
 * @returns {Promise<boolean>} 是否清除成功
 */
export const clearNotificationEmail = async () => {
  try {
    // 从数据库删除
    const result = await ConfigService.deleteConfigByKey(
      "strategy_notification_email"
    );
    if (result.code === 0) {
      // 同时清除localStorage
      localStorage.removeItem(NOTIFICATION_EMAIL_KEY);
      return true;
    }

    return false;
  } catch (error) {
    console.error("清除通知邮箱失败:", error);
    // 降级到localStorage
    try {
      localStorage.removeItem(NOTIFICATION_EMAIL_KEY);
      return true;
    } catch (localError) {
      console.error("清除localStorage也失败:", localError);
      return false;
    }
  }
};

/**
 * 检查是否有设置通知邮箱
 * @returns {Promise<boolean>} 是否已设置邮箱
 */
export const hasNotificationEmail = async () => {
  const email = await getNotificationEmail();
  return email.trim().length > 0;
};

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效的邮箱格式
 */
export const validateEmail = (email) => {
  if (!email || typeof email !== "string") {
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * 格式化邮箱显示（隐藏部分字符）
 * @param {string} email - 邮箱地址
 * @returns {string} 格式化后的邮箱地址
 */
export const formatEmailForDisplay = (email) => {
  if (!email || typeof email !== "string") {
    return "";
  }

  const trimmedEmail = email.trim();
  const atIndex = trimmedEmail.indexOf("@");

  if (atIndex === -1 || atIndex === 0) {
    return trimmedEmail;
  }

  const username = trimmedEmail.substring(0, atIndex);
  const domain = trimmedEmail.substring(atIndex);

  if (username.length <= 2) {
    return trimmedEmail;
  }

  const maskedUsername =
    username.charAt(0) +
    "*".repeat(username.length - 2) +
    username.charAt(username.length - 1);
  return maskedUsername + domain;
};
