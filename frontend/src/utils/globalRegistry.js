/**
 * 全局组件和服务注册
 * 将经常复用的组件、服务和工具放在全局对象中，避免重复import
 */

// 导入Lucide图标
import * as lucideReact from "lucide-react";

// 导入核心UI组件（只导入确实存在的）
import VirtualDataTable from "../components/ui/VirtualDataTable";
import Button from "../components/ui/Button";
import SearchInput from "../components/ui/SearchInput";
import Tag from "../components/ui/Tag";
import { Toast } from "../components/ui";

// 导入主要服务
import strategyService from "../services/strategyService";
import strategyBindingService from "../services/strategyBindingService";

// 导入主要hooks
import { useContextMenu } from "../hooks/useContextMenu";

// 导入主要contexts
import { useStrategyMap } from "../contexts/StrategyMapContext";

// 导入核心工具函数
import {
  formatDateTime,
  formatCurrency,
  formatPercentage,
} from "../utils/index";

/**
 * 初始化全局对象
 */
export function initGlobalRegistry() {
  // 确保globalThis对象存在相应的命名空间
  globalThis.lucideReact = globalThis.lucideReact || {};
  globalThis.components = globalThis.components || {};
  globalThis.services = globalThis.services || {};
  globalThis.hooks = globalThis.hooks || {};
  globalThis.utils = globalThis.utils || {};

  // 注册Lucide图标
  Object.assign(globalThis.lucideReact, lucideReact);

  // 注册核心UI组件
  Object.assign(globalThis.components, {
    VirtualDataTable,
    Button,
    SearchInput,
    Tag,
    Toast,
  });

  // 注册主要服务
  Object.assign(globalThis.services, {
    strategyService,
    strategyBindingService,
  });

  // 注册主要hooks
  Object.assign(globalThis.hooks, {
    useContextMenu,
    useStrategyMap,
  });

  // 注册核心工具函数
  Object.assign(globalThis.utils, {
    formatDateTime,
    formatCurrency,
    formatPercentage,
  });

  console.log("全局组件和服务注册完成");
}

/**
 * 获取全局组件
 * @param {string} name - 组件名称
 * @returns {React.Component} 组件
 */
export function getGlobalComponent(name) {
  return globalThis.components?.[name];
}

/**
 * 获取全局服务
 * @param {string} name - 服务名称
 * @returns {Object} 服务实例
 */
export function getGlobalService(name) {
  return globalThis.services?.[name];
}

/**
 * 获取Lucide图标
 * @param {string} name - 图标名称
 * @returns {React.Component} 图标组件
 */
export function getLucideIcon(name) {
  return globalThis.lucideReact?.[name];
}

// 默认导出初始化函数
export default initGlobalRegistry;
