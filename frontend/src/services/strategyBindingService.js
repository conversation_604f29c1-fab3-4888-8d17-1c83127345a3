import * as StrategyBindingService from "@services/strategybindingservice.js";

// 策略绑定服务类
class StrategyBindingServiceClass {
  // 获取策略绑定列表
  async getStrategyBindingList(params = {}) {
    try {
      const req = {
        page: params.page || 1,
        page_size: params.pageSize || 10,
        strategy_id: params.strategyId || 0,
        binding_type: params.bindingType || "",
        binding_id: params.bindingId || "",
        binding_name: params.bindingName || "",
        enabled: params.enabled,
        order_by: params.orderBy || "",
        order_desc: params.orderDesc || false,
        extra_filters: params.extraFilters || {},
      };

      const result = await StrategyBindingService.GetStrategyBindingList(req);
      return result;
    } catch (error) {
      console.error("获取策略绑定列表失败:", error);
      return {
        code: -1,
        msg: "获取策略绑定列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 绑定策略到实体
  async bindStrategyToEntity(params) {
    try {
      const req = {
        strategy_ids: params.strategy_ids || [],
        binding_type: params.binding_type || "",
        binding_id: params.binding_id || "",
        binding_name: params.binding_name || "",
        priority: params.priority || 0,
        description: params.description || "",
      };

      const result = await StrategyBindingService.BindStrategyToEntity(req);
      return result;
    } catch (error) {
      console.error("绑定策略到实体失败:", error);
      return {
        code: -1,
        msg: "绑定策略到实体失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量绑定策略到多个实体
  async batchBindStrategyToEntities(params) {
    try {
      const req = {
        strategy_ids: params.strategy_ids || [],
        binding_type: params.binding_type || "",
        entities: params.entities || [],
        priority: params.priority || 0,
        description: params.description || "",
      };

      const result = await StrategyBindingService.BatchBindStrategyToEntities(
        req
      );
      return result;
    } catch (error) {
      console.error("批量绑定策略到多个实体失败:", error);
      return {
        code: -1,
        msg: "批量绑定策略到多个实体失败: " + error.message,
        data: null,
      };
    }
  }

  // 从实体解绑策略
  async unbindStrategyFromEntity(params) {
    try {
      const req = {
        strategy_ids: params.strategy_ids || [],
        binding_type: params.binding_type || "",
        binding_id: params.binding_id || "",
      };

      const result = await StrategyBindingService.UnbindStrategyFromEntity(req);
      return result;
    } catch (error) {
      console.error("从实体解绑策略失败:", error);
      return {
        code: -1,
        msg: "从实体解绑策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据绑定实体获取策略列表
  async getStrategiesByBinding(bindingType, bindingId) {
    try {
      const req = {
        binding_type: bindingType,
        binding_id: bindingId,
      };

      console.log("调用GetStrategiesByBinding，参数:", req);
      const result = await StrategyBindingService.GetStrategiesByBinding(req);
      console.log("GetStrategiesByBinding返回结果:", result);
      return result;
    } catch (error) {
      console.error("获取绑定策略列表失败:", error);
      console.error("错误详情:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      return {
        code: -1,
        msg: "获取绑定策略列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 创建策略绑定
  async createStrategyBinding(params) {
    try {
      const req = {
        strategy_id: params.strategy_id || 0,
        binding_type: params.binding_type || "",
        binding_id: params.binding_id || "",
        binding_name: params.binding_name || "",
        enabled: params.enabled !== false,
        priority: params.priority || 0,
        description: params.description || "",
      };

      const result = await StrategyBindingService.CreateStrategyBinding(req);
      return result;
    } catch (error) {
      console.error("创建策略绑定失败:", error);
      return {
        code: -1,
        msg: "创建策略绑定失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新策略绑定
  async updateStrategyBinding(params) {
    try {
      const req = {
        id: params.id || 0,
        strategy_id: params.strategy_id || 0,
        binding_type: params.binding_type || "",
        binding_id: params.binding_id || "",
        binding_name: params.binding_name || "",
        enabled: params.enabled !== false,
        priority: params.priority || 0,
        description: params.description || "",
      };

      const result = await StrategyBindingService.UpdateStrategyBinding(req);
      return result;
    } catch (error) {
      console.error("更新策略绑定失败:", error);
      return {
        code: -1,
        msg: "更新策略绑定失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除策略绑定
  async deleteStrategyBinding(id) {
    try {
      const req = { id: id };
      const result = await StrategyBindingService.DeleteStrategyBinding(req);
      return result;
    } catch (error) {
      console.error("删除策略绑定失败:", error);
      return {
        code: -1,
        msg: "删除策略绑定失败: " + error.message,
        data: null,
      };
    }
  }

  // 切换策略绑定状态
  async toggleStrategyBindingStatus(id) {
    try {
      const req = { id: id };
      const result = await StrategyBindingService.ToggleStrategyBindingStatus(
        req
      );
      return result;
    } catch (error) {
      console.error("切换策略绑定状态失败:", error);
      return {
        code: -1,
        msg: "切换策略绑定状态失败: " + error.message,
        data: null,
      };
    }
  }

  /**
   * 获取实体的所有策略（包括继承的策略）
   * @param {Object} params - 参数对象
   * @param {string} params.bindingType - 绑定类型 (advertiser/project/promotion)
   * @param {string} params.bindingId - 绑定实体ID
   * @param {string} params.bindingName - 绑定实体名称
   * @param {string} params.advertiserId - 广告主ID（可选）
   * @param {string} params.projectId - 项目ID（可选）
   * @returns {Promise<Object>} API响应
   */
  async getInheritedStrategiesByBinding(params) {
    try {
      const result =
        await StrategyBindingService.GetInheritedStrategiesByBinding({
          binding_type: params.bindingType,
          binding_id: params.bindingId,
          binding_name: params.bindingName,
          advertiser_id: params.advertiserId || "",
          project_id: params.projectId || "",
        });
      return result;
    } catch (error) {
      console.error("获取继承策略失败:", error);
      throw error;
    }
  }
}

// 创建默认实例
const strategyBindingService = new StrategyBindingServiceClass();

// 导出默认实例
export default strategyBindingService;

// 同时导出类，以便需要时可以创建新实例
export { StrategyBindingServiceClass };
