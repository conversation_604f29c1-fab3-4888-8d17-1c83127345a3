// 导入Wails生成的绑定文件
import * as RetargetingTagService from '@services/retargetingtagservice.js';

// 项目可用人群包服务类
class RetargetingTagServiceClass {
    // 获取项目可用人群包列表
    async getRetargetingTags(advertiserId, req = {}) {
        try {
            const response = await RetargetingTagService.GetRetargetingTags(advertiserId, req);
            return this.handleResponse(response);
        } catch (error) {
            throw new Error(`获取项目可用人群包列表失败: ${error.message}`);
        }
    }
    
    // 根据账户ID获取项目可用人群包列表
    async getRetargetingTagsByAccount(accountId, advertiserId) {
        try {
            console.log('调用人群包服务，参数:', { accountId, advertiserId });
            const response = await RetargetingTagService.GetRetargetingTagsByAccount(accountId, advertiserId);
            console.log('人群包服务原始响应:', response);
            return this.handleResponse(response);
        } catch (error) {
            console.error('人群包服务调用失败:', error);
            throw new Error(`获取项目可用人群包列表失败: ${error.message}`);
        }
    }
    
    // 统一响应处理
    handleResponse(response) {
        console.log('处理人群包响应:', response);
        
        // 检查响应格式
        if (!response) {
            throw new Error('响应为空');
        }
        
        // 如果是Wails的Result格式
        if (response.code !== undefined) {
            if (response.code !== 0) {
                throw new Error(response.msg || response.message || '操作失败');
            }
            return response.data;
        }
        
        // 如果是直接的响应数据
        return response;
    }
}

// 导出服务实例
export const retargetingTagService = new RetargetingTagServiceClass(); 