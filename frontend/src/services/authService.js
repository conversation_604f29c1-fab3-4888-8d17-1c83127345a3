// 认证服务 - 处理OA登录相关API调用
import * as AccountService from '@services/accountservice.js';

/**
 * OA系统登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise<Object>} 登录响应
 */
export const loginOA = async (username, password) => {
  try {
    // 首先尝试使用Wails生成的绑定文件
    if (AccountService && AccountService.LoginOA) {
      const response = await AccountService.LoginOA(username, password);
      return response;
    }
    
    // 如果绑定文件中没有LoginOA方法，尝试使用window.go方式
    if (window.go && window.go.main && window.go.main.AccountService && window.go.main.AccountService.LoginOA) {
      const response = await window.go.main.AccountService.LoginOA(username, password);
      return response;
    }
    
    // 如果都不可用，抛出错误
    throw new Error('LoginOA方法不可用，请确保后端服务已正确启动并重新生成前端绑定文件');
  } catch (error) {
    console.error('OA登录API调用失败:', error);
    throw error;
  }
};

/**
 * 统一的响应处理
 * @param {Object} response - 后端响应
 * @returns {Object} 处理后的响应
 */
export const handleResponse = (response) => {
  if (response.code !== 0) {
    throw new Error(response.message || '操作失败');
  }
  return response.data;
}; 