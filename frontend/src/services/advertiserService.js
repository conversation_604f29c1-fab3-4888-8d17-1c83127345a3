import * as AdvertiserService from "@services/advertiserservice.js";

// 广告主服务类
class AdvertiserServiceClass {
  // 获取广告主列表
  async getAdvertiserList(params = {}) {
    try {
      const req = {
        page: params.page,
        page_size: params.pageSize,
        account_id: params.accountId || 0,
        advertiser_name: params.advertiserName || "",
        group_name: params.groupName || "",
        status: params.status || 0,
        order_by: params.orderBy || "",
        order_desc: params.orderDesc || false,
        extra_filters: params.extraFilters || {},
      };

      console.log("调用GetAdvertiserList，参数:", req);
      const result = await AdvertiserService.GetAdvertiserList(req);
      console.log("GetAdvertiserList返回结果:", result);

      // 解析数据结构
      if (result && result.code === 0 && result.data) {
        return {
          code: result.code,
          msg: result.msg,
          data: {
            list: result.data.list || [],
            total: result.data.total || 0,
            page: result.data.page || 1,
            pageSize: result.data.page_size || 10,
          },
        };
      }

      return result;
    } catch (error) {
      console.error("获取广告主列表失败:", error);
      return {
        code: -1,
        msg: "获取广告主列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据ID获取广告主详情
  async getAdvertiserById(id) {
    try {
      return await AdvertiserService.GetAdvertiserById(id);
    } catch (error) {
      console.error("获取广告主详情失败:", error);
      return {
        code: -1,
        msg: "获取广告主详情失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据广告主ID获取广告主详情
  async getAdvertiserByAdvertiserId(advertiserId) {
    try {
      return await AdvertiserService.GetAdvertiserByAdvertiserId(advertiserId);
    } catch (error) {
      console.error("获取广告主详情失败:", error);
      return {
        code: -1,
        msg: "获取广告主详情失败: " + error.message,
        data: null,
      };
    }
  }

  // 创建广告主
  async createAdvertiser(advertiser) {
    try {
      return await AdvertiserService.CreateAdvertiser(advertiser);
    } catch (error) {
      console.error("创建广告主失败:", error);
      return {
        code: -1,
        msg: "创建广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新广告主
  async updateAdvertiser(advertiser) {
    try {
      return await AdvertiserService.UpdateAdvertiser(advertiser);
    } catch (error) {
      console.error("更新广告主失败:", error);
      return {
        code: -1,
        msg: "更新广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 使用Map更新广告主
  async updateAdvertiserWithMap(id, data) {
    try {
      return await AdvertiserService.UpdateAdvertiserWithMap(id, data);
    } catch (error) {
      console.error("更新广告主失败:", error);
      return {
        code: -1,
        msg: "更新广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除广告主
  async deleteAdvertiser(id) {
    try {
      return await AdvertiserService.DeleteAdvertiser(id);
    } catch (error) {
      console.error("删除广告主失败:", error);
      return {
        code: -1,
        msg: "删除广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量删除广告主
  async batchDeleteAdvertisers(ids) {
    try {
      return await AdvertiserService.BatchDeleteAdvertisers(ids);
    } catch (error) {
      console.error("批量删除广告主失败:", error);
      return {
        code: -1,
        msg: "批量删除广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 刷新指定账户下的广告主数据
  async refreshAdvertisers(accountId) {
    try {
      return await AdvertiserService.RefreshAdvertisers(accountId);
    } catch (error) {
      console.error("刷新广告主失败:", error);
      return {
        code: -1,
        msg: "刷新广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 同步指定账户下的广告主
  async syncAdvertisers(accountId, advertisers) {
    try {
      return await AdvertiserService.SyncAdvertisers(accountId, advertisers);
    } catch (error) {
      console.error("同步广告主失败:", error);
      return {
        code: -1,
        msg: "同步广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据账户ID获取广告主列表
  async getAdvertisersByAccountId(accountId) {
    try {
      return await AdvertiserService.GetAdvertisersByAccountId(accountId);
    } catch (error) {
      console.error("获取广告主列表失败:", error);
      return {
        code: -1,
        msg: "获取广告主列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新广告主自动删除评论开关
  async updateAutoDeleteComment(id, autoDeleteComment) {
    try {
      const result = await AdvertiserService.UpdateAutoDeleteComment(
        id,
        autoDeleteComment
      );
      return {
        success: result.code === 0,
        message: result.msg || "操作完成",
        data: result.data,
      };
    } catch (error) {
      console.error("更新自动删除评论开关失败:", error);
      return {
        success: false,
        message: "更新自动删除评论开关失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新广告主关注状态
  async updateIsFollowed(accountId, advertiserId, isFollowed) {
    try {
      const result = await AdvertiserService.UpdateIsFollowed(accountId, advertiserId, isFollowed);
      return {
        success: result.code === 0,
        message: result.msg || "操作完成",
        data: result.data,
      };
    } catch (error) {
      console.error("更新关注状态失败:", error);
      return {
        success: false,
        message: "更新关注状态失败: " + error.message,
        data: null,
      };
    }
  }

  // 上传视频作为素材
  async UploadVideoAsMaterial(accountId, advertiserId, filePath) {
    try {
      console.log("调用UploadVideoAsMaterial，参数:", {
        accountId,
        advertiserId,
        filePath,
      });
      const result = await AdvertiserService.UploadVideoAsMaterial(
        accountId,
        advertiserId,
        filePath
      );
      console.log("UploadVideoAsMaterial返回结果:", result);
      return result;
    } catch (error) {
      console.error("上传视频素材失败:", error);
      return {
        code: -1,
        msg: "上传视频素材失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取视频素材信息
  async getMaterialVideoInfo(params) {
    try {
      const req = {
        account_id: params.accountId,
        advertiser_id: params.advertiserId,
        vids: params.vids,
      };

      console.log("调用GetMaterialVideoInfo，参数:", req);
      const result = await AdvertiserService.GetMaterialVideoInfo(req);
      console.log("GetMaterialVideoInfo返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取视频素材信息失败:", error);
      return {
        code: -1,
        msg: "获取视频素材信息失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取素材标题
  async getMaterialTitle(params) {
    try {
      const req = {
        account_id: params.accountId,
        advertiser_id: params.advertiserId,
        video_materials: params.videoMaterials || [],
        image_materials: params.imageMaterials || [],
        aweme_photo_materials: params.awemePhotoMaterials || [],
      };

      console.log("调用GetMaterialTitle，参数:", req);
      const result = await AdvertiserService.GetMaterialTitle(req);
      console.log("GetMaterialTitle返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取素材标题失败:", error);
      return {
        code: -1,
        msg: "获取素材标题失败: " + error.message,
        data: null,
      };
    }
  }

  // 绑定策略到广告主
  async bindStrategyToEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.bindStrategyToEntity(params);
    } catch (error) {
      console.error("绑定策略到广告主失败:", error);
      return {
        code: -1,
        msg: "绑定策略到广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量绑定策略到多个广告主
  async batchBindStrategyToEntities(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.batchBindStrategyToEntities(
        params
      );
    } catch (error) {
      console.error("批量绑定策略到多个广告主失败:", error);
      return {
        code: -1,
        msg: "批量绑定策略到多个广告主失败: " + error.message,
        data: null,
      };
    }
  }

  // 从广告主解绑策略
  async unbindStrategyFromEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.unbindStrategyFromEntity(
        params
      );
    } catch (error) {
      console.error("从广告主解绑策略失败:", error);
      return {
        code: -1,
        msg: "从广告主解绑策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取广告主绑定的策略列表
  async getAdvertiserStrategies(advertiserId) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.getStrategiesByBinding(
        "advertiser",
        advertiserId
      );
    } catch (error) {
      console.error("获取广告主策略列表失败:", error);
      return {
        code: -1,
        msg: "获取广告主策略列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 上传广告主头像
  async uploadAvatar(params) {
    try {
      const req = {
        account_id: params.accountId,
        advertiser_id: params.advertiserId.toString(),
        file_name: params.fileName,
        base64_data: params.base64Data,
        width: params.width,
        height: params.height,
      };

      console.log("调用UploadAvatar，参数:", {
        ...req,
        base64_data: req.base64_data.substring(0, 50) + "...", // 只显示前50个字符
      });

      const result = await AdvertiserService.UploadAvatar(req);
      console.log("UploadAvatar返回结果:", result);

      return result;
    } catch (error) {
      console.error("上传头像失败:", error);
      return {
        code: -1,
        msg: "上传头像失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量上传广告主头像
  async batchUploadAvatar(params) {
    try {
      const req = {
        account_id: params.accountId,
        file_name: params.fileName,
        base64_data: params.base64Data,
        width: params.width,
        height: params.height,
        advertiser_ids: params.advertiserIds.map((id) => id.toString()),
      };

      console.log("调用BatchUploadAvatar，参数:", {
        ...req,
        base64_data: req.base64_data.substring(0, 50) + "...", // 只显示前50个字符
        advertiser_ids: req.advertiser_ids,
      });

      const result = await AdvertiserService.BatchUploadAvatar(req);
      console.log("BatchUploadAvatar返回结果:", result);

      return result;
    } catch (error) {
      console.error("批量上传头像失败:", error);
      return {
        code: -1,
        msg: "批量上传头像失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取页面列表
  async GetPageList(params) {
    try {
      const req = {
        account_id: params.account_id,
        advertiser_id: params.advertiser_id,
        limit: params.limit || 10,
        page: params.page || 1,
        status: params.status || 'all',
        search: params.search || '',
      };

      console.log("调用GetPageList，参数:", req);
      const result = await AdvertiserService.GetPageList(req);
      console.log("GetPageList返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取页面列表失败:", error);
      return {
        code: -1,
        msg: "获取页面列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取落地页列表
  async GetOrangeNormalList(params) {
    try {
      const req = {
        account_id: params.account_id,
        advertiser_id: params.advertiser_id,
        search_mode: 1,  // 固定为1
        order_mode: 1,   // 固定为1
        page: 1,         // 页码从1开始
        size: 50,        // 固定为50
      };

      console.log("调用GetOrangeNormalList，参数:", req);
      const result = await AdvertiserService.GetOrangeNormalList(req);
      console.log("GetOrangeNormalList返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取落地页列表失败:", error);
      return {
        code: -1,
        msg: "获取落地页列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 复制落地页
  async CopyPage(params) {
    try {
      const req = {
        source_account_id: params.source_account_id,
        source_advertiser_id: params.source_advertiser_id,
        target_account_id: params.target_account_id,
        target_advertiser_id: params.target_advertiser_id,
        pages: params.pages,
      };

      console.log("调用CopyPage，参数:", req);
      const result = await AdvertiserService.CopyPage(req);
      console.log("CopyPage返回结果:", result);

      return result;
    } catch (error) {
      console.error("复制落地页失败:", error);
      return {
        code: -1,
        msg: "复制落地页失败: " + error.message,
        data: null,
      };
    }
  }
}

// 导出单例
export default new AdvertiserServiceClass();
