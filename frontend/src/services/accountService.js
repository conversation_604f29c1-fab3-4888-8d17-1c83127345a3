import * as AccountService from '@services/accountservice.js';

// 账户服务类
class AccountServiceClass {
  // 获取账户列表
  async getAccountList(params = {}) {
    try {
      const req = {
        page: params.page || 1,
        page_size: params.pageSize || 10,
        account_name: params.accountName || '',
        login_type: params.loginType || '',
        remark: params.remark || '',
        order_by: params.orderBy || '',
        order_desc: params.orderDesc || false,
        extra_filters: params.extraFilters || {}
      };
      return await AccountService.GetAccountList(req);
    } catch (error) {
      console.error('获取账户列表失败:', error);
      return {
        code: -1,
        msg: '获取账户列表失败: ' + error.message,
        data: null
      };
    }
  }

  // 根据ID获取账户详情
  async getAccountById(id) {
    try {
      return await AccountService.GetAccountById(id);
    } catch (error) {
      console.error('获取账户详情失败:', error);
      return {
        code: -1,
        msg: '获取账户详情失败: ' + error.message,
        data: null
      };
    }
  }

  // 创建账户
  async createAccount(account) {
    try {
      return await AccountService.CreateAccount(account);
    } catch (error) {
      console.error('创建账户失败:', error);
      return {
        code: -1,
        msg: '创建账户失败: ' + error.message,
        data: null
      };
    }
  }

  // 更新账户
  async updateAccount(account) {
    try {
      return await AccountService.UpdateAccount(account);
    } catch (error) {
      console.error('更新账户失败:', error);
      return {
        code: -1,
        msg: '更新账户失败: ' + error.message,
        data: null
      };
    }
  }

  // 使用Map更新账户
  async updateAccountWithMap(id, data) {
    try {
      return await AccountService.UpdateAccountWithMap(id, data);
    } catch (error) {
      console.error('更新账户失败:', error);
      return {
        code: -1,
        msg: '更新账户失败: ' + error.message,
        data: null
      };
    }
  }

  // 删除账户
  async deleteAccount(id) {
    try {
      return await AccountService.DeleteAccount(id);
    } catch (error) {
      console.error('删除账户失败:', error);
      return {
        code: -1,
        msg: '删除账户失败: ' + error.message,
        data: null
      };
    }
  }

  // 批量删除账户
  async batchDeleteAccounts(ids) {
    try {
      return await AccountService.BatchDeleteAccounts(ids);
    } catch (error) {
      console.error('批量删除账户失败:', error);
      return {
        code: -1,
        msg: '批量删除账户失败: ' + error.message,
        data: null
      };
    }
  }

  // 邮箱登录
  async loginWithEmail(email, password) {
    try {
      return await AccountService.LoginWithEmailDirectly(email, password);
    } catch (error) {
      console.error('邮箱登录失败:', error);
      return {
        code: -1,
        msg: '邮箱登录失败: ' + error.message,
        data: null
      };
    }
  }

  // 发送手机验证码
  async sendLoginCode(phone) {
    try {
      console.log('accountService.sendLoginCode 开始调用，手机号：', phone);
      const result = await AccountService.SendLoginCode(phone);
      console.log('AccountService.SendLoginCode 返回结果：', result);
      return result;
    } catch (error) {
      console.error('发送验证码异常:', error);
      return {
        code: -1,
        msg: '发送验证码失败: ' + error.message,
        data: null
      };
    }
  }

  // 手机验证码登录
  async loginWithPhoneCode(phone, code) {
    try {
      return await AccountService.LoginWithPhoneCode(phone, code);
    } catch (error) {
      console.error('手机验证码登录失败:', error);
      return {
        code: -1,
        msg: '手机验证码登录失败: ' + error.message,
        data: null
      };
    }
  }

  // Cookie登录
  async loginWithCookie(cookie) {
    try {
      return await AccountService.LoginWithCookie(cookie);
    } catch (error) {
      console.error('Cookie登录失败:', error);
      return {
        code: -1,
        msg: 'Cookie登录失败: ' + error.message,
        data: null
      };
    }
  }

  // 关注子账户
  async concernAddSubAccount(req) {
    try {
      return await AccountService.ConcernAddSubAccount(req);
    } catch (error) {
      console.error('关注子账户失败:', error);
      return {
        code: -1,
        msg: '关注子账户失败: ' + error.message,
        data: null,
        success: false
      };
    }
  }

  // 取消关注子账户
  async concernRemoveSubAccount(req) {
    try {
      return await AccountService.ConcernRemoveSubAccount(req);
    } catch (error) {
      console.error('取消关注子账户失败:', error);
      return {
        code: -1,
        msg: '取消关注子账户失败: ' + error.message,
        data: null,
        success: false
      };
    }
  }
}

// 导出单例
export default new AccountServiceClass(); 