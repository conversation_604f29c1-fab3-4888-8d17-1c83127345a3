import * as PromotionService from "@services/promotionservice.js";

// 广告服务类
class PromotionServiceClass {
  // 获取广告列表（从本地数据库）
  async GetPromotionList(params = {}) {
    try {
      const req = {
        page: params.page,
        page_size: params.page_size,
        account_id: params.account_id || 0,
        promotion_name: params.promotion_name || "",
        advertiser_id: params.advertiser_id || 0,
        project_id: params.project_id || "",
        status: params.status || "",
        remark: params.remark || "",
        order_by: params.order_by || "",
        order_desc: params.order_desc || false,
        extra_filters: params.extra_filters || {},
      };

      console.log("调用GetPromotionList，参数:", req);
      const result = await PromotionService.GetPromotionList(req);
      console.log("GetPromotionList返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取广告列表失败:", error);
      return {
        code: -1,
        msg: "获取广告列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取广告列表（从API）
  async GetPromotionListAsync(params = {}) {
    try {
      const req = {
        page: params.page || 1,
        page_size: params.page_size || 10,
        account_id: params.account_id || 0,
        keyword: params.keyword || "",
        project_id: params.project_id || "",
        promotion_id: params.promotion_id || "",
      };

      console.log("调用GetPromotionListAsync，参数:", req);
      const result = await PromotionService.GetPromotionListAsync(req);
      console.log("GetPromotionListAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取广告列表失败:", error);
      return {
        code: -1,
        msg: "获取广告列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除广告
  async DeletePromotionAsync(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || "",
        promotion_ids: params.promotion_ids || [],
      };

      console.log("调用DeletePromotionAsync，参数:", req);
      const result = await PromotionService.DeletePromotionAsync(req);
      console.log("DeletePromotionAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("删除广告失败:", error);
      return {
        code: -1,
        msg: "删除广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量删除广告
  async BatchDeletePromotionAsync(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        account_detail_list: params.account_detail_list || [],
      };

      console.log("调用BatchDeletePromotionAsync，参数:", req);
      const result = await PromotionService.BatchDeletePromotionAsync(req);
      console.log("BatchDeletePromotionAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("批量删除广告失败:", error);
      return {
        code: -1,
        msg: "批量删除广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新广告状态
  async UpdatePromotionStatus(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        status_map: params.status_map || {},
      };

      console.log("调用UpdatePromotionStatus，参数:", req);
      const result = await PromotionService.UpdatePromotionStatus(req);
      console.log("UpdatePromotionStatus返回结果:", result);

      return result;
    } catch (error) {
      console.error("更新广告状态失败:", error);
      return {
        code: -1,
        msg: "更新广告状态失败: " + error.message,
        data: null,
      };
    }
  }

  // 修改广告名称
  async UpdatePromotionName(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        promotion_id: params.promotion_id || "",
        name: params.name || "",
      };

      console.log("调用UpdatePromotionName，参数:", req);
      const result = await PromotionService.UpdatePromotionName(req);
      console.log("UpdatePromotionName返回结果:", result);

      return result;
    } catch (error) {
      console.error("修改广告名称失败:", error);
      return {
        code: -1,
        msg: "修改广告名称失败: " + error.message,
        data: null,
      };
    }
  }

  // 刷新指定账户下的广告数据
  async RefreshPromotions(accountId) {
    try {
      console.log("调用RefreshPromotions，参数:", { accountId });
      const result = await PromotionService.RefreshPromotions(accountId);
      console.log("RefreshPromotions返回结果:", result);

      return result;
    } catch (error) {
      console.error("刷新广告数据失败:", error);
      return {
        code: -1,
        msg: "刷新广告数据失败: " + error.message,
        data: null,
      };
    }
  }

  // 复制广告
  async CopyPromotion(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        source_promotion_id: params.source_promotion_id || "",
        new_promotion_name: params.new_promotion_name || "",
        new_project_id: params.new_project_id || "",
        video_material_info: params.video_material_info || [],
      };

      console.log("调用CopyPromotion，参数:", req);
      const result = await PromotionService.CopyPromotion(req);
      console.log("CopyPromotion返回结果:", result);

      return result;
    } catch (error) {
      console.error("复制广告失败:", error);
      return {
        code: -1,
        msg: "复制广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量复制广告
  async BatchCopyPromotion(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        source_promotion_ids: params.source_promotion_ids || [],
        name_prefix: params.name_prefix || "",
        target_project_id: params.target_project_id || "",
      };

      console.log("调用BatchCopyPromotion，参数:", req);
      const result = await PromotionService.BatchCopyPromotion(req);
      console.log("BatchCopyPromotion返回结果:", result);

      return result;
    } catch (error) {
      console.error("批量复制广告失败:", error);
      return {
        code: -1,
        msg: "批量复制广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取广告信息
  async GetPromotionInfo(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        promotion_ids: params.promotion_ids || "",
      };

      console.log("调用GetPromotionInfo，参数:", req);
      const result = await PromotionService.GetPromotionInfo(req);
      console.log("GetPromotionInfo返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取广告信息失败:", error);
      return {
        code: -1,
        msg: "获取广告信息失败: " + error.message,
        data: null,
      };
    }
  }

  // 绑定策略到广告
  async bindStrategyToEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.bindStrategyToEntity(params);
    } catch (error) {
      console.error("绑定策略到广告失败:", error);
      return {
        code: -1,
        msg: "绑定策略到广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量绑定策略到多个广告
  async batchBindStrategyToEntities(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.batchBindStrategyToEntities(
        params
      );
    } catch (error) {
      console.error("批量绑定策略到多个广告失败:", error);
      return {
        code: -1,
        msg: "批量绑定策略到多个广告失败: " + error.message,
        data: null,
      };
    }
  }

  // 从广告解绑策略
  async unbindStrategyFromEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.unbindStrategyFromEntity(
        params
      );
    } catch (error) {
      console.error("从广告解绑策略失败:", error);
      return {
        code: -1,
        msg: "从广告解绑策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取广告绑定的策略列表
  async getPromotionStrategies(promotionId) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.getStrategiesByBinding(
        "promotion",
        promotionId
      );
    } catch (error) {
      console.error("获取广告策略列表失败:", error);
      return {
        code: -1,
        msg: "获取广告策略列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 修改广告出价
  async UpdatePromotionBid(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        bids: params.bids || {},
      };

      console.log("调用UpdatePromotionBid，参数:", req);
      const result = await PromotionService.UpdatePromotionBid(req);
      console.log("UpdatePromotionBid返回结果:", result);

      return result;
    } catch (error) {
      console.error("修改广告出价失败:", error);
      return {
        code: -1,
        msg: "修改广告出价失败: " + error.message,
        data: null,
      };
    }
  }
}

// 导出单例
export default new PromotionServiceClass();

// 同时导出类中的方法，保持与现有代码的兼容性
export const GetPromotionList = (params) =>
  new PromotionServiceClass().GetPromotionList(params);
export const GetPromotionListAsync = (params) =>
  new PromotionServiceClass().GetPromotionListAsync(params);
export const DeletePromotionAsync = (params) =>
  new PromotionServiceClass().DeletePromotionAsync(params);
export const BatchDeletePromotionAsync = (params) =>
  new PromotionServiceClass().BatchDeletePromotionAsync(params);
export const UpdatePromotionStatus = (params) =>
  new PromotionServiceClass().UpdatePromotionStatus(params);
export const UpdatePromotionName = (params) =>
  new PromotionServiceClass().UpdatePromotionName(params);
export const RefreshPromotions = (accountId) =>
  new PromotionServiceClass().RefreshPromotions(accountId);
export const CopyPromotion = (params) =>
  new PromotionServiceClass().CopyPromotion(params);
export const BatchCopyPromotion = (params) =>
  new PromotionServiceClass().BatchCopyPromotion(params);
export const GetPromotionInfo = (params) =>
  new PromotionServiceClass().GetPromotionInfo(params);
export const bindStrategyToEntity = (params) =>
  new PromotionServiceClass().bindStrategyToEntity(params);
export const batchBindStrategyToEntities = (params) =>
  new PromotionServiceClass().batchBindStrategyToEntities(params);
export const unbindStrategyFromEntity = (params) =>
  new PromotionServiceClass().unbindStrategyFromEntity(params);
export const getPromotionStrategies = (promotionId) =>
  new PromotionServiceClass().getPromotionStrategies(promotionId);
export const UpdatePromotionBid = (params) =>
  new PromotionServiceClass().UpdatePromotionBid(params);
