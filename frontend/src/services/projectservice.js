import * as ProjectService from "../../bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service/projectservice.js";

// 项目服务类
class ProjectServiceClass {
  // 获取项目列表（从本地数据库）
  async GetProjectList(params = {}) {
    try {
      const req = {
        page: params.page,
        page_size: params.page_size,
        account_id: params.account_id || 0,
        project_name: params.project_name || "",
        status: params.status || "",
        remark: params.remark || "",
        order_by: params.order_by || "",
        order_desc: params.order_desc || false,
        extra_filters: params.extra_filters || {},
      };

      console.log("调用GetProjectList，参数:", req);
      const result = await ProjectService.GetProjectList(req);
      console.log("GetProjectList返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取项目列表失败:", error);
      return {
        code: -1,
        msg: "获取项目列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取项目列表（从API）
  async GetProjectListAsync(params = {}) {
    try {
      const req = {
        page: params.page || 1,
        page_size: params.page_size || 10,
        account_id: params.account_id || 0,
        keyword: params.keyword || "",
        project_id: params.project_id || "",
        promotion_id: params.promotion_id || "",
      };

      console.log("调用GetProjectListAsync，参数:", req);
      const result = await ProjectService.GetProjectListAsync(req);
      console.log("GetProjectListAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("获取项目列表失败:", error);
      return {
        code: -1,
        msg: "获取项目列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除项目
  async DeleteProjectAsync(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || "",
        project_ids: params.project_ids || [],
      };

      console.log("调用DeleteProjectAsync，参数:", req);
      const result = await ProjectService.DeleteProjectAsync(req);
      console.log("DeleteProjectAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("删除项目失败:", error);
      return {
        code: -1,
        msg: "删除项目失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量删除项目
  async BatchDeleteProjectAsync(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        account_detail_list: params.account_detail_list || [],
      };

      console.log("调用BatchDeleteProjectAsync，参数:", req);
      const result = await ProjectService.BatchDeleteProjectAsync(req);
      console.log("BatchDeleteProjectAsync返回结果:", result);

      return result;
    } catch (error) {
      console.error("批量删除项目失败:", error);
      return {
        code: -1,
        msg: "批量删除项目失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新项目状态
  async UpdateProjectStatus(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        status_map: params.status_map || {},
      };

      console.log("调用UpdateProjectStatus，参数:", req);
      const result = await ProjectService.UpdateProjectStatus(req);
      console.log("UpdateProjectStatus返回结果:", result);

      return result;
    } catch (error) {
      console.error("更新项目状态失败:", error);
      return {
        code: -1,
        msg: "更新项目状态失败: " + error.message,
        data: null,
      };
    }
  }

  // 修改项目名称
  async UpdateProjectName(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        project_id: params.project_id || "",
        name: params.name || "",
      };

      console.log("调用UpdateProjectName，参数:", req);
      const result = await ProjectService.UpdateProjectName(req);
      console.log("UpdateProjectName返回结果:", result);

      return result;
    } catch (error) {
      console.error("修改项目名称失败:", error);
      return {
        code: -1,
        msg: "修改项目名称失败: " + error.message,
        data: null,
      };
    }
  }

  // 刷新指定账户下的项目数据
  async RefreshProjects(accountId) {
    try {
      console.log("调用RefreshProjects，参数:", { accountId });
      const result = await ProjectService.RefreshProjects(accountId);
      console.log("RefreshProjects返回结果:", result);

      return result;
    } catch (error) {
      console.error("刷新项目数据失败:", error);
      return {
        code: -1,
        msg: "刷新项目数据失败: " + error.message,
        data: null,
      };
    }
  }

  // 复制项目
  async CopyProject(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        source_project_id: params.source_project_id || "",
        source_advertiser_id: params.source_advertiser_id || 0,
        advertiser_id: params.advertiser_id || 0,
        new_project_name: params.new_project_name || "",
        copy_count: params.copy_count || 1,
        override_params: params.override_params || {},
        source_project_detail: params.source_project_detail || null,
      };

      console.log("调用CopyProject，参数:", req);
      const result = await ProjectService.CopyProject(req);
      console.log("CopyProject返回结果:", result);

      return result;
    } catch (error) {
      console.error("复制项目失败:", error);
      return {
        code: -1,
        msg: "复制项目失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新项目策略
  async UpdateProjectStrategy(params = {}) {
    try {
      const req = {
        id: params.id || 0,
        account_id: params.account_id || 0,
        project_id: params.project_id || "",
        strategy_ids: params.strategy_ids || [],
      };

      console.log("调用UpdateProjectStrategy，参数:", req);
      const result = await ProjectService.UpdateProjectStrategy(req);
      console.log("UpdateProjectStrategy返回结果:", result);

      return result;
    } catch (error) {
      console.error("更新项目策略失败:", error);
      return {
        code: -1,
        msg: "更新项目策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 绑定策略到项目
  async bindStrategyToEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.bindStrategyToEntity(params);
    } catch (error) {
      console.error("绑定策略到项目失败:", error);
      return {
        code: -1,
        msg: "绑定策略到项目失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量绑定策略到多个项目
  async batchBindStrategyToEntities(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.batchBindStrategyToEntities(
        params
      );
    } catch (error) {
      console.error("批量绑定策略到多个项目失败:", error);
      return {
        code: -1,
        msg: "批量绑定策略到多个项目失败: " + error.message,
        data: null,
      };
    }
  }

  // 从项目解绑策略
  async unbindStrategyFromEntity(params) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.unbindStrategyFromEntity(
        params
      );
    } catch (error) {
      console.error("从项目解绑策略失败:", error);
      return {
        code: -1,
        msg: "从项目解绑策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取项目绑定的策略列表
  async getProjectStrategies(projectId) {
    try {
      // 导入策略绑定服务
      const strategyBindingService = await import(
        "./strategyBindingService.js"
      );
      return await strategyBindingService.default.getStrategiesByBinding(
        "project",
        projectId
      );
    } catch (error) {
      console.error("获取项目策略列表失败:", error);
      return {
        code: -1,
        msg: "获取项目策略列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取项目详情
  async getProjectDetail(accountId, advertiserId, projectId) {
    try {
      const req = {
        account_id: accountId,
        advertiser_id: advertiserId,
        project_id: projectId,
      };

      console.log("2222");
      console.log(
        "检查 ProjectService.GetProjectDetail 是否存在:",
        typeof ProjectService.GetProjectDetail
      );

      if (typeof ProjectService.GetProjectDetail !== "function") {
        throw new Error(
          "ProjectService.GetProjectDetail 方法不存在，可能是Wails绑定未正确生成"
        );
      }

      console.log("开始调用 ProjectService.GetProjectDetail，参数:", req);
      const result = await ProjectService.GetProjectDetail(req);
      console.log("GetProjectDetail返回结果:", result);

      // 检查返回结果
      if (!result) {
        throw new Error("GetProjectDetail返回空结果");
      }

      // 更灵活的返回结果检查
      // 1. 如果result有code字段，检查是否为成功状态
      if (result.code !== undefined) {
        if (result.code !== 0) {
          throw new Error(result.msg || "获取项目详情失败");
        }
        return result;
      }

      // 2. 如果result直接是数据，包装成标准格式
      if (result.data || Array.isArray(result)) {
        return {
          code: 0,
          msg: "获取项目详情成功",
          data: result.data || result,
        };
      }

      // 3. 其他情况，直接返回
      return result;
    } catch (error) {
      console.error("获取项目详情失败:", error);
      console.error("错误堆栈:", error.stack);
      return {
        code: -1,
        msg: "获取项目详情失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量修改项目出价
  async BatchUpdateProjectBid(params = {}) {
    try {
      const req = {
        account_id: params.account_id || 0,
        advertiser_id: params.advertiser_id || 0,
        promotion_bid_map: params.promotion_bid_map || {},
        is_async: params.is_async || false,
      };

      console.log("调用BatchUpdateProjectBid，参数:", req);
      const result = await ProjectService.BatchUpdateProjectBid(req);
      console.log("BatchUpdateProjectBid返回结果:", result);

      return result;
    } catch (error) {
      console.error("批量修改项目出价失败:", error);
      return {
        code: -1,
        msg: "批量修改项目出价失败: " + error.message,
        data: null,
      };
    }
  }
}

// 导出单例
export default new ProjectServiceClass();

// 同时导出类中的方法，保持与现有代码的兼容性
export const GetProjectList = (params) =>
  new ProjectServiceClass().GetProjectList(params);
export const GetProjectListAsync = (params) =>
  new ProjectServiceClass().GetProjectListAsync(params);
export const DeleteProjectAsync = (params) =>
  new ProjectServiceClass().DeleteProjectAsync(params);
export const BatchDeleteProjectAsync = (params) =>
  new ProjectServiceClass().BatchDeleteProjectAsync(params);
export const UpdateProjectStatus = (params) =>
  new ProjectServiceClass().UpdateProjectStatus(params);
export const UpdateProjectName = (params) =>
  new ProjectServiceClass().UpdateProjectName(params);
export const RefreshProjects = (accountId) =>
  new ProjectServiceClass().RefreshProjects(accountId);
export const CopyProject = (params) =>
  new ProjectServiceClass().CopyProject(params);
export const UpdateProjectStrategy = (params) =>
  new ProjectServiceClass().UpdateProjectStrategy(params);
export const bindStrategyToEntity = (params) =>
  new ProjectServiceClass().bindStrategyToEntity(params);
export const batchBindStrategyToEntities = (params) =>
  new ProjectServiceClass().batchBindStrategyToEntities(params);
export const unbindStrategyFromEntity = (params) =>
  new ProjectServiceClass().unbindStrategyFromEntity(params);
export const getProjectStrategies = (projectId) =>
  new ProjectServiceClass().getProjectStrategies(projectId);

export const getProjectDetail = (accountId, advertiserId, projectId) =>
  new ProjectServiceClass().getProjectDetail(
    accountId,
    advertiserId,
    projectId
  );

export const BatchUpdateProjectBid = (params) =>
  new ProjectServiceClass().BatchUpdateProjectBid(params);

// 导出服务实例
export const projectService = new ProjectServiceClass();
