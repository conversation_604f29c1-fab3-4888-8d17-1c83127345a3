import * as StrategyLogService from "@services/strategylogservice.js";

/**
 * 策略日志服务类
 */
class StrategyLogServiceClass {
  /**
   * 获取策略日志列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 响应结果
   */
  async getStrategyLogList(params = {}) {
    try {
      const response = await StrategyLogService.GetStrategyLogList(params);
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取策略日志列表失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取策略日志详情
   * @param {number} id - 日志ID
   * @returns {Promise<Object>} 响应结果
   */
  async getStrategyLogById(id) {
    try {
      const response = await StrategyLogService.GetStrategyLogById({ id });
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取策略日志详情失败: ${error.message}`);
    }
  }

  /**
   * 获取策略日志统计
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 响应结果
   */
  async getStrategyLogStats(params = {}) {
    try {
      const response = await StrategyLogService.GetStrategyLogStats(params);
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取策略日志统计失败: ${error.message}`);
    }
  }

  /**
   * 根据时间删除策略日志
   * @param {Date} beforeTime - 删除此时间之前的日志
   * @returns {Promise<Object>} 响应结果
   */
  async deleteStrategyLogByTime(beforeTime) {
    try {
      const response = await StrategyLogService.DeleteStrategyLogByTime(
        beforeTime
      );
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`删除策略日志失败: ${error.message}`);
    }
  }

  /**
   * 统一响应处理
   * @param {Object} response - 响应数据
   * @returns {Object} 处理后的响应
   */
  handleResponse(response) {
    if (response.code !== 0) {
      throw new Error(response.message || "操作失败");
    }
    return response;
  }
}

// 导出服务实例
export default new StrategyLogServiceClass();
