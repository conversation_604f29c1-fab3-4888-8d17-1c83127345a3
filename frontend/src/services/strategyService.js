import * as StrategyService from "@services/strategyservice.js";

// 防抖机制
let getStrategyListTimer = null;
let lastGetStrategyListCall = 0;
const DEBOUNCE_DELAY = 500; // 500ms防抖，减少延迟时间

// 策略服务类
class StrategyServiceClass {
  // 获取策略列表
  async getStrategyList(params = {}) {
    // 防抖机制：如果距离上次调用时间太短，则跳过
    const now = Date.now();
    if (now - lastGetStrategyListCall < DEBOUNCE_DELAY) {
      console.log("策略列表API调用过于频繁，跳过本次调用");
      return {
        code: 0,
        msg: "调用过于频繁",
        data: { list: [], total: 0 },
      };
    }

    // 清除之前的定时器
    if (getStrategyListTimer) {
      clearTimeout(getStrategyListTimer);
    }

    return new Promise((resolve) => {
      getStrategyListTimer = setTimeout(async () => {
        lastGetStrategyListCall = Date.now();

        try {
          const req = {
            page: params.page || 1,
            page_size: params.pageSize || 10,
            name: params.name || "",
            logic_type: params.logicType || "",
            order_by: params.orderBy || "",
            order_desc: params.orderDesc || false,
            extra_filters: params.extraFilters || {},
          };
          
          // 只有当enabled参数明确指定时才添加到请求中
          if (params.enabled !== undefined && params.enabled !== null) {
            req.enabled = params.enabled;
          }
          
          const result = await StrategyService.GetStrategyList(req);
          resolve(result);
        } catch (error) {
          console.error("获取策略列表失败:", error);
          resolve({
            code: -1,
            msg: "获取策略列表失败: " + error.message,
            data: null,
          });
        }
      }, 50); // 减少延迟到50ms
    });
  }

  // 根据ID获取策略详情
  async getStrategyById(id) {
    try {
      const req = { id };
      return await StrategyService.GetStrategyById(req);
    } catch (error) {
      console.error("获取策略详情失败:", error);
      return {
        code: -1,
        msg: "获取策略详情失败: " + error.message,
        data: null,
      };
    }
  }

  // 创建策略
  async createStrategy(strategyData) {
    try {
      return await StrategyService.CreateStrategy(strategyData);
    } catch (error) {
      console.error("创建策略失败:", error);
      return {
        code: -1,
        msg: "创建策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新策略
  async updateStrategy(strategyData) {
    try {
      return await StrategyService.UpdateStrategy(strategyData);
    } catch (error) {
      console.error("更新策略失败:", error);
      return {
        code: -1,
        msg: "更新策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除策略
  async deleteStrategy(id) {
    try {
      console.log("strategyService.deleteStrategy 被调用，ID:", id);
      const req = { id };
      console.log("删除策略请求参数:", req);
      const result = await StrategyService.DeleteStrategy(req);
      console.log("删除策略API返回结果:", result);
      return result;
    } catch (error) {
      console.error("删除策略失败:", error);
      return {
        code: -1,
        msg: "删除策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 批量删除策略
  async batchDeleteStrategies(ids) {
    try {
      const req = { ids };
      return await StrategyService.BatchDeleteStrategies(req);
    } catch (error) {
      console.error("批量删除策略失败:", error);
      return {
        code: -1,
        msg: "批量删除策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 切换策略状态
  async toggleStrategyStatus(id) {
    try {
      const req = { id };
      return await StrategyService.ToggleStrategyStatus(req);
    } catch (error) {
      console.error("切换策略状态失败:", error);
      return {
        code: -1,
        msg: "切换策略状态失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取所有启用的策略（供监控系统使用）
  async getEnabledStrategies() {
    try {
      return await StrategyService.GetEnabledStrategies();
    } catch (error) {
      console.error("获取启用策略失败:", error);
      return {
        code: -1,
        msg: "获取启用策略失败: " + error.message,
        data: null,
      };
    }
  }

  // 更新策略触发信息（供监控系统使用）
  async updateStrategyTriggerInfo(id) {
    try {
      return await StrategyService.UpdateStrategyTriggerInfo(id);
    } catch (error) {
      console.error("更新策略触发信息失败:", error);
      return {
        code: -1,
        msg: "更新策略触发信息失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取策略执行引擎状态
  async getStrategyEngineStatus() {
    try {
      return await StrategyService.GetStrategyEngineStatus();
    } catch (error) {
      console.error("获取策略引擎状态失败:", error);
      return {
        code: -1,
        msg: "获取策略引擎状态失败: " + error.message,
        data: null,
      };
    }
  }

  // 启动策略执行引擎
  async startStrategyEngine() {
    try {
      return await StrategyService.StartStrategyEngine();
    } catch (error) {
      console.error("启动策略引擎失败:", error);
      return {
        code: -1,
        msg: "启动策略引擎失败: " + error.message,
        data: null,
      };
    }
  }

  // 停止策略执行引擎
  async stopStrategyEngine() {
    try {
      return await StrategyService.StopStrategyEngine();
    } catch (error) {
      console.error("停止策略引擎失败:", error);
      return {
        code: -1,
        msg: "停止策略引擎失败: " + error.message,
        data: null,
      };
    }
  }

  // 重启策略执行引擎
  async restartStrategyEngine() {
    try {
      return await StrategyService.RestartStrategyEngine();
    } catch (error) {
      console.error("重启策略引擎失败:", error);
      return {
        code: -1,
        msg: "重启策略引擎失败: " + error.message,
        data: null,
      };
    }
  }

  // 立即执行指定策略
  async executeStrategyNow(strategyId) {
    try {
      return await StrategyService.ExecuteStrategyNow(strategyId);
    } catch (error) {
      console.error("立即执行策略失败:", error);
      return {
        code: -1,
        msg: "立即执行策略失败: " + error.message,
        data: null,
      };
    }
  }
}

// 导出单例
export default new StrategyServiceClass();
