// 配置服务 - 使用Wails绑定调用后端API
import * as ConfigServiceBinding from "@services/configservice.js";

// 本地存储键名
const CONFIG_PREFIX = "config_";
const STRATEGY_NOTIFICATION_EMAIL_KEY = "strategy_notification_email";
const SYSTEM_EMAIL_KEY = "system_email";
const WECHAT_WEBHOOK_URL_KEY = "wechat_webhook_url";

class ConfigServiceClass {
  // 获取策略通知邮箱
  async getStrategyNotificationEmail() {
    try {
      const result = await ConfigServiceBinding.GetStrategyNotificationEmail();
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || "",
      };
    } catch (error) {
      console.error("获取策略通知邮箱失败:", error);
      return {
        code: -1,
        msg: "获取策略通知邮箱失败: " + error.message,
        data: null,
      };
    }
  }

  // 设置策略通知邮箱
  async setStrategyNotificationEmail(email) {
    try {
      console.log("ConfigService: 开始设置策略通知邮箱:", email);
      const result = await ConfigServiceBinding.SetStrategyNotificationEmail(
        email
      );
      console.log("ConfigService: 设置策略通知邮箱结果:", result);
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || true,
      };
    } catch (error) {
      console.error("设置策略通知邮箱失败:", error);
      return {
        code: -1,
        msg: "设置策略通知邮箱失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取系统邮箱
  async getSystemEmail() {
    try {
      const result = await ConfigServiceBinding.GetSystemEmail();
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || "",
      };
    } catch (error) {
      console.error("获取系统邮箱失败:", error);
      return {
        code: -1,
        msg: "获取系统邮箱失败: " + error.message,
        data: null,
      };
    }
  }

  // 设置系统邮箱
  async setSystemEmail(email) {
    try {
      const result = await ConfigServiceBinding.SetSystemEmail(email);
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || true,
      };
    } catch (error) {
      console.error("设置系统邮箱失败:", error);
      return {
        code: -1,
        msg: "设置系统邮箱失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取配置列表
  async listConfigs(page = 1, pageSize = 10, conditions = {}) {
    try {
      const result = await ConfigServiceBinding.ListConfigs(
        page,
        pageSize,
        conditions
      );
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || {
          list: [],
          total: 0,
          page: page,
          size: pageSize,
        },
      };
    } catch (error) {
      console.error("获取配置列表失败:", error);
      return {
        code: -1,
        msg: "获取配置列表失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据分类获取配置
  async getConfigsByCategory(category) {
    try {
      const result = await ConfigServiceBinding.GetConfigsByCategory(category);
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || [],
      };
    } catch (error) {
      console.error("根据分类获取配置失败:", error);
      return {
        code: -1,
        msg: "根据分类获取配置失败: " + error.message,
        data: null,
      };
    }
  }

  // 获取所有配置
  async getAllConfigs() {
    try {
      const result = await ConfigServiceBinding.GetAllConfigs();
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || [],
      };
    } catch (error) {
      console.error("获取所有配置失败:", error);
      return {
        code: -1,
        msg: "获取所有配置失败: " + error.message,
        data: null,
      };
    }
  }

  // 删除配置
  async deleteConfig(id) {
    try {
      const result = await ConfigServiceBinding.DeleteConfig(id);
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || true,
      };
    } catch (error) {
      console.error("删除配置失败:", error);
      return {
        code: -1,
        msg: "删除配置失败: " + error.message,
        data: null,
      };
    }
  }

  // 根据键删除配置
  async deleteConfigByKey(key) {
    try {
      const result = await ConfigServiceBinding.DeleteConfigByKey(key);
      return {
        code: result.code || 0,
        msg: result.msg || "success",
        data: result.data || true,
      };
    } catch (error) {
      console.error("根据键删除配置失败:", error);
      return {
        code: -1,
        msg: "根据键删除配置失败: " + error.message,
        data: null,
      };
    }
  }
}

const ConfigService = new ConfigServiceClass();
export default ConfigService;
