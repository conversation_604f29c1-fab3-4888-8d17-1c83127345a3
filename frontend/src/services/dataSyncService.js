import * as DataSyncService from "@services/datasyncservice.js";

class DataSyncServiceClass {
  constructor() {
    this.serviceName = "DataSyncService";
  }

  /**
   * 同步广告主项目信息
   * @param {number} advertiserId - 广告主ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAdvertiserProjectInfo(advertiserId) {
    try {
      console.log("调用SyncAdvertiserProjectInfoAPI，参数:", advertiserId);
      const response = await DataSyncService.SyncAdvertiserProjectInfoAPI(
        advertiserId
      );
      console.log("SyncAdvertiserProjectInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步广告主项目信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步广告主广告信息
   * @param {number} advertiserId - 广告主ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAdvertiserPromotionInfo(advertiserId) {
    try {
      console.log("调用SyncAdvertiserPromotionInfoAPI，参数:", advertiserId);
      const response = await DataSyncService.SyncAdvertiserPromotionInfoAPI(
        advertiserId
      );
      console.log("SyncAdvertiserPromotionInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步广告主广告信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步广告主所有信息（项目和广告）
   * @param {number} advertiserId - 广告主ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAdvertiserAllInfo(advertiserId) {
    try {
      console.log("调用SyncAdvertiserAllInfoAPI，参数:", advertiserId);
      const response = await DataSyncService.SyncAdvertiserAllInfoAPI(
        advertiserId
      );
      console.log("SyncAdvertiserAllInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步广告主所有信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步账户数据
   * @param {number} accountId - 账户ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAccountData(accountId) {
    try {
      console.log("调用SyncAccountDataAPI，参数:", accountId);
      const response = await DataSyncService.SyncAccountDataAPI(accountId);
      console.log("SyncAccountDataAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步账户数据失败:", error);
      throw error;
    }
  }

  /**
   * 同步所有数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncAllData() {
    try {
      console.log("调用SyncAllDataAPI");
      const response = await DataSyncService.SyncAllDataAPI();
      console.log("SyncAllDataAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步所有数据失败:", error);
      throw error;
    }
  }

  /**
   * 批量同步广告主数据
   * @param {number[]} advertiserIds - 广告主ID数组
   * @returns {Promise<Object>} 同步结果
   */
  async batchSyncAdvertiserData(advertiserIds) {
    try {
      console.log("调用BatchSyncAdvertiserData，参数:", advertiserIds);
      const response = await DataSyncService.BatchSyncAdvertiserData(
        advertiserIds
      );
      console.log("BatchSyncAdvertiserData返回结果:", response);
      return response;
    } catch (error) {
      console.error("批量同步广告主数据失败:", error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   * @returns {Promise<Object>} 同步状态
   */
  async getSyncStatus() {
    try {
      console.log("调用GetSyncStatus");
      const response = await DataSyncService.GetSyncStatus();
      console.log("GetSyncStatus返回结果:", response);
      return response;
    } catch (error) {
      console.error("获取同步状态失败:", error);
      throw error;
    }
  }

  /**
   * 同步项目下的广告信息（新方法，使用GetPromotionList API）
   * @param {number} accountId - 账户ID
   * @param {string} projectId - 项目ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncPromotionInfoByProject(accountId, projectId) {
    try {
      console.log("调用SyncPromotionInfoByProjectAPI，参数:", {
        accountId,
        projectId,
      });
      const response = await DataSyncService.SyncPromotionInfoByProjectAPI(
        accountId,
        projectId
      );
      console.log("SyncPromotionInfoByProjectAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步项目广告信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步项目下的广告信息（旧方法，使用GetSubAccountPromotionListByProject API）
   * @param {number} accountId - 账户ID
   * @param {number} advertiserId - 广告主ID
   * @param {string} projectId - 项目ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncProjectPromotionInfo(accountId, advertiserId, projectId) {
    try {
      console.log("调用SyncProjectPromotionInfoAPI，参数:", {
        accountId,
        advertiserId,
        projectId,
      });
      const response = await DataSyncService.SyncProjectPromotionInfoAPI(
        accountId,
        advertiserId,
        projectId
      );
      console.log("SyncProjectPromotionInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步项目广告信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步所有项目信息（使用GetProjectList API）
   * @param {number} accountId - 账户ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAllProjectInfo(accountId) {
    try {
      console.log("调用SyncAllProjectInfoAPI，参数:", accountId);
      const response = await DataSyncService.SyncAllProjectInfoAPI(accountId);
      console.log("SyncAllProjectInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步所有项目信息失败:", error);
      throw error;
    }
  }

  /**
   * 同步所有广告信息（账户级别）
   * @param {number} accountId - 账户ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncAllPromotionInfo(accountId) {
    try {
      console.log("调用SyncAllPromotionInfoAPI，参数:", accountId);
      const response = await DataSyncService.SyncAllPromotionInfoAPI(accountId);
      console.log("SyncAllPromotionInfoAPI返回结果:", response);
      return response;
    } catch (error) {
      console.error("同步所有广告信息失败:", error);
      throw error;
    }
  }

  /**
   * 智能同步项目数据
   * 根据选中的广告主决定同步范围
   * @param {Object} options - 同步选项
   * @param {number} options.accountId - 账户ID
   * @param {number} [options.advertiserId] - 广告主ID（可选）
   * @param {string} [options.syncType] - 同步类型：'project' | 'promotion' | 'all'
   * @returns {Promise<Object>} 同步结果
   */
  async smartSyncProjectData(options = {}) {
    const { accountId, advertiserId, syncType = "project" } = options;

    try {
      console.log("智能同步项目数据，选项:", options);

      if (advertiserId) {
        // 如果指定了广告主，使用GetSubAccountProjectList和GetSubAccountPromotionList
        console.log(
          "使用GetSubAccountProjectList和GetSubAccountPromotionList同步广告主数据"
        );
        switch (syncType) {
          case "project":
            return await this.syncAdvertiserProjectInfo(advertiserId);
          case "promotion":
            return await this.syncAdvertiserPromotionInfo(advertiserId);
          case "all":
            return await this.syncAdvertiserAllInfo(advertiserId);
          default:
            return await this.syncAdvertiserAllInfo(advertiserId);
        }
      } else if (accountId) {
        // 如果没有指定广告主，使用GetProjectList和GetPromotionList
        console.log("使用GetProjectList和GetPromotionList同步账户数据");
        switch (syncType) {
          case "project":
            return await this.syncAllProjectInfo(accountId);
          case "promotion":
            return await this.syncAllPromotionInfo(accountId);
          case "all":
            return await this.syncAccountData(accountId);
          default:
            return await this.syncAccountData(accountId);
        }
      } else {
        // 如果都没有指定，同步所有数据
        console.log("同步所有账户的数据");
        return await this.syncAllData();
      }
    } catch (error) {
      console.error("智能同步项目数据失败:", error);
      throw error;
    }
  }

  /**
   * 智能同步广告数据
   * 根据选中的项目决定同步范围
   * @param {Object} options - 同步选项
   * @param {number} options.accountId - 账户ID
   * @param {number} [options.advertiserId] - 广告主ID（可选）
   * @param {string} [options.projectId] - 项目ID（可选）
   * @param {string} [options.syncType] - 同步类型：'project' | 'promotion' | 'all'
   * @returns {Promise<Object>} 同步结果
   */
  async smartSyncPromotionData(options = {}) {
    const {
      accountId,
      advertiserId,
      projectId,
      syncType = "promotion",
    } = options;

    try {
      console.log("智能同步广告数据，选项:", options);

      if (projectId && advertiserId && accountId) {
        // 如果指定了项目，使用GetSubAccountPromotionListByProject API同步该项目下的广告
        console.log("使用GetSubAccountPromotionListByProject API同步项目广告");
        return await this.syncProjectPromotionInfo(
          accountId,
          advertiserId,
          projectId
        );
      } else if (accountId) {
        // 如果没有指定项目，使用GetPromotionList API同步所有广告
        console.log("使用GetPromotionList API同步所有广告");
        return await this.syncAllPromotionInfo(accountId);
      } else {
        // 如果都没有指定，同步所有广告数据
        console.log("同步所有账户的广告数据");
        return await this.syncAllData();
      }
    } catch (error) {
      console.error("智能同步广告数据失败:", error);
      throw error;
    }
  }
}

// 创建单例实例
const dataSyncService = new DataSyncServiceClass();

export default dataSyncService;

// 导出方法以保持兼容性
export const syncAdvertiserProjectInfo = (advertiserId) =>
  dataSyncService.syncAdvertiserProjectInfo(advertiserId);
export const syncAdvertiserPromotionInfo = (advertiserId) =>
  dataSyncService.syncAdvertiserPromotionInfo(advertiserId);
export const syncAdvertiserAllInfo = (advertiserId) =>
  dataSyncService.syncAdvertiserAllInfo(advertiserId);
export const syncAccountData = (accountId) =>
  dataSyncService.syncAccountData(accountId);
export const syncAllData = () => dataSyncService.syncAllData();
export const batchSyncAdvertiserData = (advertiserIds) =>
  dataSyncService.batchSyncAdvertiserData(advertiserIds);
export const getSyncStatus = () => dataSyncService.getSyncStatus();
export const smartSyncProjectData = (options) =>
  dataSyncService.smartSyncProjectData(options);
export const syncPromotionInfoByProject = (accountId, projectId) =>
  dataSyncService.syncPromotionInfoByProject(accountId, projectId);
export const syncProjectPromotionInfo = (accountId, advertiserId, projectId) =>
  dataSyncService.syncProjectPromotionInfo(accountId, advertiserId, projectId);
export const smartSyncPromotionData = (options) =>
  dataSyncService.smartSyncPromotionData(options);
export const syncAllProjectInfo = (accountId) =>
  dataSyncService.syncAllProjectInfo(accountId);
export const syncAllPromotionInfo = (accountId) =>
  dataSyncService.syncAllPromotionInfo(accountId);
