// API服务文件 - 使用Wails 3绑定直接调用Go后端方法

// 导入Wails绑定
import * as ProxyServiceBinding from "@services/proxyservice.js";
import * as FfmpegServiceBinding from "@services/ffmpegservice.js";
import * as ClipTaskServiceBinding from "@services/cliptaskservice.js";
import * as TextInfoServiceBinding from "@services/textinfoservice.js";
import * as SystemServiceBinding from "@services/systemservice.js";
import * as WindowServiceBinding from "@services/windowservice.js";

// 导入自定义服务
import AdvertiserServiceClass from "./advertiserService.js";
import PromotionServiceClass from "./promotionService.js";

// 导入类型定义
import {
  ProxyListReq,
  ProxyImportReq,
  SetTextInfoReq,
  VideoVariationReq,
  SetVideoCoverReq,
  TextInfoListReq,
  BatchGetVideoInfoReq,
} from "@types/models.js";
import { Proxy, TextInfo } from "@models/models.js";
import { OpenFileReq, ReadFileReq } from "@reqTypes/models.js";

// 导出AdvertiserService
export const AdvertiserService = AdvertiserServiceClass;

// 导出PromotionService
export const PromotionService = PromotionServiceClass;

// 导出WindowService
export const WindowService = {
  // 显示窗口
  async ShowWindow(windowPath) {
    try {
      const result = await WindowServiceBinding.ShowWindow(windowPath);
      return result;
    } catch (error) {
      console.error("显示窗口失败:", error);
      throw error;
    }
  },

  // 隐藏窗口
  async HideWindow(windowPath) {
    try {
      const result = await WindowServiceBinding.HideWindow(windowPath);
      return result;
    } catch (error) {
      console.error("隐藏窗口失败:", error);
      throw error;
    }
  },

  // 测试提醒弹窗
  async TestReminder() {
    try {
      const result = await WindowServiceBinding.TestReminder();
      return result;
    } catch (error) {
      console.error("测试提醒弹窗失败:", error);
      throw error;
    }
  },
};

// 代理服务API
export const ProxyService = {
  // 获取代理列表
  async GetProxyList(params) {
    try {
      const req = new ProxyListReq({
        page: params.page ?? 1,
        page_size: params.pageSize ?? 20,
        ip_address: params.ipAddress || "",
        type: params.type || "",
        status: params.status ?? 0,
        remark: params.remark || "",
        extra_filters: params.extraFilters || {},
      });

      const result = await ProxyServiceBinding.GetProxyList(req);
      return result;
    } catch (error) {
      console.error("获取代理列表失败:", error);
      throw error;
    }
  },

  // 创建代理
  async CreateProxy(proxyData) {
    try {
      const proxy = new Proxy({
        ip_address: proxyData.ipAddress,
        port: proxyData.port, // 不转换为数字，保持字符串类型
        username: proxyData.username || "",
        password: proxyData.password || "",
        type: proxyData.type,
        remark: proxyData.remark || "",
        status: proxyData.status || 1,
      });

      const result = await ProxyServiceBinding.CreateProxy(proxy);
      return result;
    } catch (error) {
      console.error("创建代理失败:", error);
      throw error;
    }
  },

  // 更新代理
  async UpdateProxy(proxyData) {
    try {
      const proxy = new Proxy({
        id: proxyData.id,
        ip_address: proxyData.ipAddress,
        port: proxyData.port, // 不转换为数字，保持字符串类型
        username: proxyData.username || "",
        password: proxyData.password || "",
        type: proxyData.type,
        remark: proxyData.remark || "",
        status: proxyData.status || 1,
      });

      const result = await ProxyServiceBinding.UpdateProxy(proxy);
      return result;
    } catch (error) {
      console.error("更新代理失败:", error);
      throw error;
    }
  },

  // 删除代理
  async DeleteProxy(id) {
    try {
      const result = await ProxyServiceBinding.DeleteProxy(id);
      return result;
    } catch (error) {
      console.error("删除代理失败:", error);
      throw error;
    }
  },

  // 批量删除代理
  async BatchDeleteProxies(ids) {
    try {
      const result = await ProxyServiceBinding.BatchDeleteProxies(ids);
      return result;
    } catch (error) {
      console.error("批量删除代理失败:", error);
      throw error;
    }
  },

  // 根据ID获取代理详情
  async GetProxyById(id) {
    try {
      const result = await ProxyServiceBinding.GetProxyById(id);
      return result;
    } catch (error) {
      console.error("获取代理详情失败:", error);
      throw error;
    }
  },

  // 更新代理状态
  async UpdateProxyStatus(id, status) {
    try {
      const result = await ProxyServiceBinding.UpdateProxyStatus(id, status);
      return result;
    } catch (error) {
      console.error("更新代理状态失败:", error);
      throw error;
    }
  },

  // 导入代理
  async ImportProxies(data) {
    try {
      const req = new ProxyImportReq({
        content: data.content,
        format: data.format || "ip:port:username:password",
      });

      const result = await ProxyServiceBinding.ImportProxies(req);
      return result;
    } catch (error) {
      console.error("导入代理失败:", error);
      throw error;
    }
  },
};

// 视频处理服务API
export const FfmpegService = {
  // 上传视频文件到服务器并获取文件路径
  async UploadVideo(formData) {
    try {
      // 注意：后端需要实现UploadVideo方法，用于接收前端上传的视频文件
      // 建议在后端实现以下功能：
      // 1. 接收FormData中的文件数据
      // 2. 保存为临时文件或正式文件
      // 3. 返回文件的绝对路径，格式为：{ success: true, data: { file_path: '/path/to/video.mp4' } }
      // 4. 确保返回的路径可以被ffmpeg直接访问

      // 后端Go方法参考：
      // func (f *FfmpegService) UploadVideo(file *multipart.FileHeader) (string, error) {
      //   tempDir := os.TempDir()
      //   filePath := filepath.Join(tempDir, file.Filename)
      //   // 保存文件...
      //   return filePath, nil
      // }

      const result = await FfmpegServiceBinding.UploadVideo(formData);
      return result;
    } catch (error) {
      console.error("上传视频文件失败:", error);
      throw error;
    }
  },

  // 设置视频文字信息
  async SetTextInfo(params) {
    try {
      const req = new SetTextInfoReq({
        video_file: params.videoFile,
        text_info: params.textInfo,
        is_origin_sound: params.isOriginSound || true,
        width: params.width || 1920,
        height: params.height || 1080,
        scale_mode: params.scaleMode || "fit",
      });
      console.log("SetTextInfo - 请求对象:", req);
      const result = await FfmpegServiceBinding.SetTextInfo(req);
      console.log("SetTextInfo - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("设置视频文字失败:", error);
      throw error;
    }
  },

  // 生成视频变体
  async GenerateVideoVariations(params) {
    try {
      const req = new VideoVariationReq({
        video_file: params.videoFile,
        count: params.count,
        aspect_ratio: params.aspectRatio || "16:9",
        resolution: params.resolution || { width: 1920, height: 1080 },
        is_origin_sound: params.isOriginSound || true,
        effects: params.effects || [],
      });

      const result = await FfmpegServiceBinding.GenerateVideoVariations(req);
      return result;
    } catch (error) {
      console.error("生成视频变体失败:", error);
      throw error;
    }
  },

  // 设置视频封面
  async SetVideoCover(params) {
    try {
      const req = new SetVideoCoverReq({
        video_file: params.videoFile,
        cover_file: params.coverFile,
      });

      const result = await FfmpegServiceBinding.SetVideoCover(req);
      return result;
    } catch (error) {
      console.error("设置视频封面失败:", error);
      throw error;
    }
  },

  // 批量获取视频信息
  async BatchGetVideoInfo(filePathList) {
    try {
      console.log("BatchGetVideoInfo - 开始调用, 文件列表:", filePathList);
      const req = new BatchGetVideoInfoReq({
        video_file_list: filePathList,
      });
      console.log("BatchGetVideoInfo - 请求对象:", req);

      const result = await FfmpegServiceBinding.BatchGetVideoInfo(req);
      console.log("BatchGetVideoInfo - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("BatchGetVideoInfo - 获取视频信息失败, 错误详情:", error);
      throw error;
    }
  },


};

// 混剪任务服务API
export const ClipTaskService = {
  // 创建混剪任务
  async CreateClipTask(clipTaskData) {
    try {
      console.log("CreateClipTask - 开始调用, 任务数据:", clipTaskData);
      const result = await ClipTaskServiceBinding.CreateClipTask(clipTaskData);
      console.log("CreateClipTask - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("CreateClipTask - 创建混剪任务失败, 错误详情:", error);
      throw error;
    }
  },

  // 启动混剪任务
  async StartClipTask(params) {
    try {
      console.log("StartClipTask - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.StartClipTask(params);
      console.log("StartClipTask - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("StartClipTask - 启动混剪任务失败, 错误详情:", error);
      throw error;
    }
  },

  // 获取混剪任务列表
  async List(params) {
    try {
      console.log("List - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.List(params);
      console.log("List - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("List - 获取混剪任务列表失败, 错误详情:", error);
      throw error;
    }
  },

  // 获取混剪任务详情
  async GetClipTask(params) {
    try {
      console.log("GetClipTask - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.GetClipTask(params);
      console.log("GetClipTask - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("GetClipTask - 获取混剪任务详情失败, 错误详情:", error);
      throw error;
    }
  },

  // 更新混剪任务
  async UpdateClipTask(params) {
    try {
      console.log("UpdateClipTask - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.UpdateClipTask(params);
      console.log("UpdateClipTask - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("UpdateClipTask - 更新混剪任务失败, 错误详情:", error);
      throw error;
    }
  },

  // 更新混剪任务状态
  async UpdateClipTaskStatus(params) {
    try {
      console.log("UpdateClipTaskStatus - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.UpdateClipTaskStatus(params);
      console.log("UpdateClipTaskStatus - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("UpdateClipTaskStatus - 更新混剪任务状态失败, 错误详情:", error);
      throw error;
    }
  },

  // 删除混剪任务
  async DeleteClipTask(params) {
    try {
      console.log("DeleteClipTask - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.DeleteClipTask(params);
      console.log("DeleteClipTask - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("DeleteClipTask - 删除混剪任务失败, 错误详情:", error);
      throw error;
    }
  },

  // 批量删除混剪任务
  async BatchDeleteClipTasks(params) {
    try {
      console.log("BatchDeleteClipTasks - 开始调用, 请求参数:", params);
      const result = await ClipTaskServiceBinding.BatchDeleteClipTasks(params);
      console.log("BatchDeleteClipTasks - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("BatchDeleteClipTasks - 批量删除混剪任务失败, 错误详情:", error);
      throw error;
    }
  },
};

// 文字信息服务API
export const TextInfoService = {
  // 获取文字信息列表
  async GetTextInfoList(params) {
    try {
      const req = new TextInfoListReq({
        page: params.page || 1,
        page_size: params.pageSize || 20,
        text: params.text || "",
        remark: params.remark || "",
        extra_filters: params.extraFilters || {},
      });

      const result = await TextInfoServiceBinding.GetTextInfoList(req);
      return result;
    } catch (error) {
      console.error("获取文字信息列表失败:", error);
      throw error;
    }
  },

  // 创建文字信息
  async CreateTextInfo(textInfoData) {
    try {
      const textInfo = new TextInfo({
        text: textInfoData.text,
        font_file: textInfoData.font_file,
        font_size: textInfoData.font_size,
        font_color: textInfoData.font_color,
        position_x: textInfoData.position_x,
        position_y: textInfoData.position_y,
        box: textInfoData.box,
        box_color: textInfoData.box_color,
        box_border_rw: textInfoData.box_border_rw,
        shadow_wx: textInfoData.shadow_wx,
        shadow_wy: textInfoData.shadow_wy,
        shadow_color: textInfoData.shadow_color,
        border_rw: textInfoData.border_rw,
        border_color: textInfoData.border_color,
        remark: textInfoData.remark || "",
      });

      const result = await TextInfoServiceBinding.CreateTextInfo(textInfo);
      return result;
    } catch (error) {
      console.error("创建文字信息失败:", error);
      throw error;
    }
  },

  // 更新文字信息
  async UpdateTextInfo(textInfoData) {
    try {
      const textInfo = new TextInfo({
        id: textInfoData.id,
        text: textInfoData.text,
        font_file: textInfoData.font_file,
        font_size: textInfoData.font_size,
        font_color: textInfoData.font_color,
        position_x: textInfoData.position_x,
        position_y: textInfoData.position_y,
        box: textInfoData.box,
        box_color: textInfoData.box_color,
        box_border_rw: textInfoData.box_border_rw,
        shadow_wx: textInfoData.shadow_wx,
        shadow_wy: textInfoData.shadow_wy,
        shadow_color: textInfoData.shadow_color,
        border_rw: textInfoData.border_rw,
        border_color: textInfoData.border_color,
        remark: textInfoData.remark || "",
      });

      const result = await TextInfoServiceBinding.UpdateTextInfo(textInfo);
      return result;
    } catch (error) {
      console.error("更新文字信息失败:", error);
      throw error;
    }
  },

  // 删除文字信息
  async DeleteTextInfo(id) {
    try {
      const result = await TextInfoServiceBinding.DeleteTextInfo(id);
      return result;
    } catch (error) {
      console.error("删除文字信息失败:", error);
      throw error;
    }
  },

  // 批量删除文字信息
  async BatchDeleteTextInfo(ids) {
    try {
      const result = await TextInfoServiceBinding.BatchDeleteTextInfo(ids);
      return result;
    } catch (error) {
      console.error("批量删除文字信息失败:", error);
      throw error;
    }
  },

  // 根据ID获取文字信息详情
  async GetTextInfoById(id) {
    try {
      const result = await TextInfoServiceBinding.GetTextInfoById(id);
      return result;
    } catch (error) {
      console.error("获取文字信息详情失败:", error);
      throw error;
    }
  },

  // 根据文本内容获取文字信息
  async GetTextInfoByText(text) {
    try {
      const result = await TextInfoServiceBinding.GetTextInfoByText(text);
      return result;
    } catch (error) {
      console.error("根据文本获取文字信息失败:", error);
      throw error;
    }
  },

  // 获取文字信息统计
  async GetTextInfoStats() {
    try {
      const result = await TextInfoServiceBinding.GetTextInfoStats();
      return result;
    } catch (error) {
      console.error("获取文字信息统计失败:", error);
      throw error;
    }
  },

  // 使用Map更新文字信息
  async UpdateTextInfoWithMap(id, data) {
    try {
      const result = await TextInfoServiceBinding.UpdateTextInfoWithMap(
        id,
        data
      );
      return result;
    } catch (error) {
      console.error("使用Map更新文字信息失败:", error);
      throw error;
    }
  },
};

// 系统服务API
export const SystemService = {
  // 打开文件
  async OpenFile(req) {
    try {
      console.log("OpenFile - 开始调用, 请求参数:", req);
      const result = await SystemServiceBinding.OpenFile(req);
      console.log("OpenFile - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("OpenFile - 打开文件失败, 错误详情:", error);
      throw error;
    }
  },

  // 读取文件
  async ReadFile(req) {
    try {
      console.log("ReadFile - 开始调用, 请求参数:", req);
      const result = await SystemServiceBinding.ReadFile(req);
      console.log("ReadFile - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("ReadFile - 读取文件失败, 错误详情:", error);
      throw error;
    }
  },

  // 删除文件
  async DeleteFile(req) {
    try {
      console.log("DeleteFile - 开始调用, 请求参数:", req);
      const result = await SystemServiceBinding.DeleteFile(req);
      console.log("DeleteFile - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("DeleteFile - 删除文件失败, 错误详情:", error);
      throw error;
    }
  },

  // 下载文件
  async DownloadFile(req) {
    try {
      console.log("DownloadFile - 开始调用, 请求参数:", req);
      const result = await SystemServiceBinding.DownloadFile(req);
      console.log("DownloadFile - 获取到的原始结果:", result);
      return result;
    } catch (error) {
      console.error("DownloadFile - 下载文件失败, 错误详情:", error);
      throw error;
    }
  },
};

// 通用错误处理
export const handleApiError = (error) => {
  console.error("API错误详情:", error);

  // Wails错误通常是字符串或包含message的对象
  if (typeof error === "string") {
    return error;
  } else if (error && error.message) {
    return error.message;
  } else if (error && error.error) {
    return error.error;
  } else {
    return "未知错误";
  }
};

// 结果处理辅助函数
export const handleResult = (result) => {
  console.log("handleResult - 处理API结果:", result);
  if (!result) {
    console.error("handleResult - 服务调用返回空结果");
    throw new Error("服务调用返回空结果");
  }

  // Wails返回的结果通常包含success字段和data字段
  if (result.success === false) {
    console.error("handleResult - 操作失败:", result.message || result.error);
    throw new Error(result.message || result.error || "操作失败");
  }

  return result;
};

export default {
  ProxyService,
  FfmpegService,
  TextInfoService,
  SystemService,
  WindowService,
  AdvertiserService,
  PromotionService,
  handleApiError,
  handleResult,
};
