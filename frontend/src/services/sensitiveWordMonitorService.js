// 敏感词监控服务
import * as SensitiveWordService from "@services/sensitivewordservice.js";
import * as SensitiveWordMonitorService from "@services/sensitivewordmonitorservice.js";

// 敏感词服务类
class SensitiveWordServiceClass {
  // 获取敏感词配置
  async getSensitiveWords() {
    try {
      const response = await SensitiveWordService.GetSensitiveWords();
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取敏感词配置失败: ${error.message}`);
    }
  }

  // 设置敏感词配置
  async setSensitiveWords(words) {
    try {
      console.log("调用设置敏感词配置...", words);
      const response = await SensitiveWordService.SetSensitiveWords(words);
      console.log("设置敏感词配置响应:", response);
      return this.handleResponse(response);
    } catch (error) {
      console.error("设置敏感词配置失败:", error);
      throw new Error(`设置敏感词配置失败: ${error.message}`);
    }
  }

  // 获取被删除的评论列表
  async getDeletedComments(params = {}) {
    try {
      const response = await SensitiveWordService.GetDeletedComments(params);
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取删除评论列表失败: ${error.message}`);
    }
  }

  // 检查文本是否包含敏感词
  async checkSensitiveWord(text) {
    try {
      const response = await SensitiveWordService.CheckSensitiveWord(text);
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`检查敏感词失败: ${error.message}`);
    }
  }

  // 获取删除评论统计信息
  async getDeletedCommentStat() {
    try {
      const response = await SensitiveWordService.GetDeletedCommentStat();
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取删除评论统计失败: ${error.message}`);
    }
  }

  // 处理响应
  handleResponse(response) {
    console.log("处理响应:", response);
    console.log("响应类型:", typeof response);
    console.log("响应是否为对象:", typeof response === "object");

    if (!response) {
      console.error("响应为空");
      throw new Error("响应为空");
    }

    // 检查响应是否有code属性
    if (typeof response === "object" && response.hasOwnProperty("code")) {
      console.log("响应包含code属性:", response.code);
      if (response.code !== 0) {
        console.error("响应错误:", response.msg || response.message);
        throw new Error(response.msg || response.message || "操作失败");
      }
    } else {
      console.log("响应不包含code属性，可能是SimpleResult");
    }

    console.log("响应成功，返回数据:", response.data);
    // 如果data为null，说明是SimpleResult，返回msg作为成功消息
    if (response.data === null || response.data === undefined) {
      console.log("返回SimpleResult消息:", response.msg);
      return response.msg;
    }
    return response.data;
  }
}

// 敏感词监控服务类
class SensitiveWordMonitorServiceClass {
  // 启动监控服务
  async startMonitor() {
    try {
      const response = await SensitiveWordMonitorService.StartMonitor();
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`启动监控服务失败: ${error.message}`);
    }
  }

  // 停止监控服务
  async stopMonitor() {
    try {
      console.log("调用停止监控服务...");
      const response = await SensitiveWordMonitorService.StopMonitor();
      console.log("停止监控服务响应:", response);

      // 检查响应格式
      if (!response) {
        throw new Error("停止监控服务返回空响应");
      }

      console.log("响应类型:", typeof response);
      console.log("响应内容:", JSON.stringify(response, null, 2));

      return this.handleResponse(response);
    } catch (error) {
      console.error("停止监控服务失败:", error);
      console.error("错误类型:", typeof error);
      console.error("错误堆栈:", error.stack);
      throw new Error(`停止监控服务失败: ${error.message}`);
    }
  }

  // 获取监控服务状态
  async getMonitorStatus() {
    try {
      const response = await SensitiveWordMonitorService.GetMonitorStatus();
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`获取监控状态失败: ${error.message}`);
    }
  }

  // 设置监控间隔
  async setMonitorInterval(intervalSeconds) {
    try {
      const response = await SensitiveWordMonitorService.SetMonitorInterval(
        intervalSeconds
      );
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`设置监控间隔失败: ${error.message}`);
    }
  }

  // 手动触发检查
  async triggerManualCheck() {
    try {
      const response = await SensitiveWordMonitorService.TriggerManualCheck();
      return this.handleResponse(response);
    } catch (error) {
      throw new Error(`手动检查失败: ${error.message}`);
    }
  }

  // 处理响应
  handleResponse(response) {
    console.log("处理响应:", response);
    console.log("响应类型:", typeof response);
    console.log("响应是否为对象:", typeof response === "object");

    if (!response) {
      console.error("响应为空");
      throw new Error("响应为空");
    }

    // 检查响应是否有code属性
    if (typeof response === "object" && response.hasOwnProperty("code")) {
      console.log("响应包含code属性:", response.code);
      if (response.code !== 0) {
        console.error("响应错误:", response.msg || response.message);
        throw new Error(response.msg || response.message || "操作失败");
      }
    } else {
      console.log("响应不包含code属性，可能是SimpleResult");
    }

    console.log("响应成功，返回数据:", response.data);
    // 如果data为null，说明是SimpleResult，返回msg作为成功消息
    if (response.data === null || response.data === undefined) {
      console.log("返回SimpleResult消息:", response.msg);
      return response.msg;
    }
    return response.data;
  }
}

// 创建服务实例
const sensitiveWordService = new SensitiveWordServiceClass();
const sensitiveWordMonitorService = new SensitiveWordMonitorServiceClass();

// 导出服务实例
export { sensitiveWordService, sensitiveWordMonitorService };

// 默认导出监控服务（保持向后兼容）
export default sensitiveWordMonitorService;
