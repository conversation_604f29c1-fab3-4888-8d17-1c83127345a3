import { Events } from '@wailsio/runtime';

/**
 * 测试任务进度服务
 * 用于演示和测试任务进度条功能
 */
class TestTaskProgressService {
  
  /**
   * 模拟一个简单的任务进度
   */
  async simulateSimpleTask() {
    const taskId = 'test_' + Date.now();
    
    // 1. 任务开始 - 第1步开始
    this.emitTaskEvent({
      task_id: taskId,
      task_name: '测试任务',
      task_description: '这是一个用于测试进度条功能的模拟任务',
      step_description: '正在初始化...',
      total_steps: 3,
      current_step: 1,
      step_progress: 0,
      next_step_progress: 100,
      step_time: 2000, // 2秒
      status: 'start',
      error_message: ''
    });

    // 1.5. 第1步进行中 - 50%
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '初始化进行中...',
        total_steps: 3,
        current_step: 1,
        step_progress: 50,
        next_step_progress: 100,
        step_time: 1000, // 1秒
        status: 'progress',
        error_message: ''
      });
    }, 1000);

    // 2. 第1步完成 - 第2步开始
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '正在处理数据...',
        total_steps: 3,
        current_step: 2,
        step_progress: 0,
        next_step_progress: 100,
        step_time: 3000, // 3秒
        status: 'progress',
        error_message: ''
      });
    }, 2000);

    // 2.5. 第2步进行中 - 60%
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '数据处理中...',
        total_steps: 3,
        current_step: 2,
        step_progress: 60,
        next_step_progress: 100,
        step_time: 1200, // 1.2秒
        status: 'progress',
        error_message: ''
      });
    }, 3800);

    // 3. 第2步完成 - 第3步开始
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '正在验证结果...',
        total_steps: 3,
        current_step: 3,
        step_progress: 0,
        next_step_progress: 100,
        step_time: 2500, // 2.5秒
        status: 'progress',
        error_message: ''
      });
    }, 5000);

    // 3.5. 第3步进行中 - 80%
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '结果验证中...',
        total_steps: 3,
        current_step: 3,
        step_progress: 80,
        next_step_progress: 100,
        step_time: 1000, // 1秒
        status: 'progress',
        error_message: ''
      });
    }, 6500);

    // 4. 第3步完成 - 任务完成
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '测试任务',
        task_description: '这是一个用于测试进度条功能的模拟任务',
        step_description: '任务已完成',
        total_steps: 3,
        current_step: 3,
        step_progress: 100,
        next_step_progress: 100,
        step_time: 0,
        status: 'end',
        error_message: ''
      });
    }, 7500);
  }

  /**
   * 模拟一个出错的任务
   */
  async simulateErrorTask() {
    const taskId = 'error_' + Date.now();
    
    // 1. 任务开始
    this.emitTaskEvent({
      task_id: taskId,
      task_name: '错误测试任务',
      task_description: '这是一个会出错的测试任务',
      step_description: '正在初始化...',
      total_steps: 2,
      current_step: 1,
      step_progress: 0,
      next_step_progress: 50,
      step_time: 1000,
      status: 'start',
      error_message: ''
    });

    // 2. 进度更新
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '错误测试任务',
        task_description: '这是一个会出错的测试任务',
        step_description: '正在处理数据...',
        total_steps: 2,
        current_step: 1,
        step_progress: 50,
        next_step_progress: 100,
        step_time: 2000,
        status: 'progress',
        error_message: ''
      });
    }, 1000);

    // 3. 任务出错
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '错误测试任务',
        task_description: '这是一个会出错的测试任务',
        step_description: '处理失败',
        total_steps: 2,
        current_step: 1,
        step_progress: 75,
        next_step_progress: 75,
        step_time: 0,
        status: 'error',
        error_message: '模拟错误：网络连接超时，请检查网络设置后重试。'
      });
    }, 3000);
  }

  /**
   * 模拟一个长时间运行的任务
   */
  async simulateLongRunningTask() {
    const taskId = 'long_' + Date.now();
    
    // 任务开始
    this.emitTaskEvent({
      task_id: taskId,
      task_name: '长时间任务',
      task_description: '这是一个长时间运行的任务，用于测试进度条的持久显示',
      step_description: '正在连接服务器...',
      total_steps: 10,
      current_step: 1,
      step_progress: 0,
      next_step_progress: 10,
      step_time: 5000, // 5秒
      status: 'start',
      error_message: ''
    });

    // 模拟10个步骤，每个步骤5秒
    for (let i = 1; i <= 10; i++) {
      setTimeout(() => {
        const isLast = i === 10;
        this.emitTaskEvent({
          task_id: taskId,
          task_name: '长时间任务',
          task_description: '这是一个长时间运行的任务，用于测试进度条的持久显示',
          step_description: `正在处理第${i}步...`,
          total_steps: 10,
          current_step: i,
          step_progress: i * 10,
          next_step_progress: isLast ? 100 : (i + 1) * 10,
          step_time: isLast ? 0 : 5000,
          status: isLast ? 'end' : 'progress',
          error_message: ''
        });
      }, i * 5000);
    }
  }

  /**
   * 专门测试步骤计数逻辑的方法
   */
  async testStepCountLogic() {
    const taskId = 'step_count_test_' + Date.now();
    
    console.log('开始测试步骤计数逻辑...');
    
    // 步骤1: 开始第1步 - 应该显示 0/3
    this.emitTaskEvent({
      task_id: taskId,
      task_name: '步骤计数测试',
      task_description: '测试步骤计数逻辑是否正确',
      step_description: '第1步开始 - 应该显示 0/3',
      total_steps: 3,
      current_step: 1,
      step_progress: 0,
      next_step_progress: 100,
      step_time: 2000,
      status: 'start',
      error_message: ''
    });

    // 步骤2: 第1步进行中 - 应该显示 0/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第1步进行中 - 应该显示 0/3',
        total_steps: 3,
        current_step: 1,
        step_progress: 50,
        next_step_progress: 100,
        step_time: 1000,
        status: 'progress',
        error_message: ''
      });
    }, 1000);

    // 步骤3: 第2步开始 - 应该显示 1/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第2步开始 - 应该显示 1/3',
        total_steps: 3,
        current_step: 2,
        step_progress: 0,
        next_step_progress: 100,
        step_time: 2000,
        status: 'progress',
        error_message: ''
      });
    }, 2000);

    // 步骤4: 第2步进行中 - 应该显示 1/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第2步进行中 - 应该显示 1/3',
        total_steps: 3,
        current_step: 2,
        step_progress: 60,
        next_step_progress: 100,
        step_time: 1000,
        status: 'progress',
        error_message: ''
      });
    }, 3000);

    // 步骤5: 第3步开始 - 应该显示 2/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第3步开始 - 应该显示 2/3',
        total_steps: 3,
        current_step: 3,
        step_progress: 0,
        next_step_progress: 100,
        step_time: 2000,
        status: 'progress',
        error_message: ''
      });
    }, 4000);

    // 步骤6: 第3步进行中 - 应该显示 2/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第3步进行中 - 应该显示 2/3',
        total_steps: 3,
        current_step: 3,
        step_progress: 80,
        next_step_progress: 100,
        step_time: 1000,
        status: 'progress',
        error_message: ''
      });
    }, 5000);

    // 步骤7: 第3步完成 - 应该显示 3/3
    setTimeout(() => {
      this.emitTaskEvent({
        task_id: taskId,
        task_name: '步骤计数测试',
        task_description: '测试步骤计数逻辑是否正确',
        step_description: '第3步完成 - 应该显示 3/3',
        total_steps: 3,
        current_step: 3,
        step_progress: 100,
        next_step_progress: 100,
        step_time: 0,
        status: 'end',
        error_message: ''
      });
    }, 6000);
    
    console.log('步骤计数逻辑测试已启动，请观察进度条中的步骤显示是否正确');
  }

  /**
   * 发送任务事件
   */
  emitTaskEvent(taskData) {
    console.log('发送任务事件:', taskData);
    
    // 模拟后端事件发送
    if (window.runtime && window.runtime.EventsEmit) {
      window.runtime.EventsEmit('main_process', taskData);
    } else {
      // 如果runtime不可用，直接触发Events.On的监听器
      // 这里需要手动触发事件，但由于Events.On的内部机制，
      // 我们需要等待一下再发送
      setTimeout(() => {
        if (window.dispatchEvent) {
          const event = new CustomEvent('main_process', {
            detail: taskData
          });
          window.dispatchEvent(event);
        }
      }, 100);
    }
  }
}

// 创建单例实例
const testTaskProgressService = new TestTaskProgressService();

export default testTaskProgressService; 