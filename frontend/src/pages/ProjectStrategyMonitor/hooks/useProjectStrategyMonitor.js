import { useState, useEffect, useCallback } from "react";
import * as StrategyService from "@services/strategyservice.js";

// 模拟项目级策略监控服务
const projectStrategyService = {
  // 获取项目配置
  getProjectConfigs: async () => {
    try {
      // 这里应该调用后端API
      const response = await StrategyService.GetProjectConfigs();
      return {
        success: true,
        data: response.data || {},
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // 获取项目状态
  getProjectStatus: async (projectId) => {
    try {
      const response = await StrategyService.GetProjectStatus(projectId);
      return {
        success: true,
        data: response.data || {},
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // 启动监控
  startMonitor: async () => {
    try {
      const response = await StrategyService.StartMonitor();
      return {
        success: true,
        message: "监控已启动",
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // 停止监控
  stopMonitor: async () => {
    try {
      const response = await StrategyService.StopMonitor();
      return {
        success: true,
        message: "监控已停止",
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // 获取监控状态
  getMonitorStatus: async () => {
    try {
      const response = await StrategyService.GetMonitorStatus();
      return {
        success: true,
        data: response.data || {
          running: false,
          start_time: null,
          total_projects: 0,
          active_projects: 0,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // 立即执行项目
  executeProjectNow: async (projectId) => {
    try {
      const response = await StrategyService.ExecuteProjectNow(projectId);
      return {
        success: true,
        message: `项目 ${projectId} 执行完成`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
};

export const useProjectStrategyMonitor = () => {
  const [projectConfigs, setProjectConfigs] = useState({});
  const [projectStatuses, setProjectStatuses] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  // 加载项目配置数据
  const loadProjectConfigs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await projectStrategyService.getProjectConfigs();

      if (response.success) {
        setProjectConfigs(response.data);
      } else {
        setError(response.error || "加载项目配置失败");
      }
    } catch (err) {
      setError(err.message || "加载项目配置失败");
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载监控状态
  const loadMonitorStatus = useCallback(async () => {
    try {
      const response = await projectStrategyService.getMonitorStatus();

      if (response.success) {
        setIsRunning(response.data.running);
      }
    } catch (err) {
      console.error("加载监控状态失败:", err);
    }
  }, []);

  // 刷新数据
  const refreshData = useCallback(async () => {
    await Promise.all([loadProjectConfigs(), loadMonitorStatus()]);
  }, [loadProjectConfigs, loadMonitorStatus]);

  // 启动监控
  const startMonitor = useCallback(async () => {
    try {
      const response = await projectStrategyService.startMonitor();

      if (response.success) {
        setIsRunning(true);
        // 显示成功消息
        console.log(response.message);
      } else {
        setError(response.error || "启动监控失败");
      }
    } catch (err) {
      setError(err.message || "启动监控失败");
    }
  }, []);

  // 停止监控
  const stopMonitor = useCallback(async () => {
    try {
      const response = await projectStrategyService.stopMonitor();

      if (response.success) {
        setIsRunning(false);
        // 显示成功消息
        console.log(response.message);
      } else {
        setError(response.error || "停止监控失败");
      }
    } catch (err) {
      setError(err.message || "停止监控失败");
    }
  }, []);

  // 立即执行项目
  const executeProjectNow = useCallback(
    async (projectId) => {
      try {
        const response = await projectStrategyService.executeProjectNow(
          projectId
        );

        if (response.success) {
          // 显示成功消息
          console.log(response.message);
          // 刷新数据
          await refreshData();
        } else {
          setError(response.error || `执行项目 ${projectId} 失败`);
        }
      } catch (err) {
        setError(err.message || `执行项目 ${projectId} 失败`);
      }
    },
    [refreshData]
  );

  // 获取项目状态
  const getProjectStatus = useCallback(async (projectId) => {
    try {
      const response = await projectStrategyService.getProjectStatus(projectId);

      if (response.success) {
        setProjectStatuses((prev) => ({
          ...prev,
          [projectId]: response.data,
        }));
      }
    } catch (err) {
      console.error(`获取项目 ${projectId} 状态失败:`, err);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // 定时刷新监控状态
  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        loadMonitorStatus();
      }, 30000); // 每30秒刷新一次

      return () => clearInterval(interval);
    }
  }, [isRunning, loadMonitorStatus]);

  return {
    projectConfigs,
    projectStatuses,
    loading,
    error,
    isRunning,
    refreshData,
    startMonitor,
    stopMonitor,
    executeProjectNow,
    getProjectStatus,
  };
};
