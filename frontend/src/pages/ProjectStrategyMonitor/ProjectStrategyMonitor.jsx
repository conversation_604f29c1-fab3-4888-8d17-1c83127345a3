import React, { useState, useEffect } from "react";
import { Button, SearchInput, DataTable, Toolbar } from "../../components/ui";
import { useProjectStrategyMonitor } from "./hooks/useProjectStrategyMonitor";
import "./ProjectStrategyMonitor.css";

const ProjectStrategyMonitor = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProjects, setSelectedProjects] = useState([]);
  const [filterStatus, setFilterStatus] = useState("all");

  const {
    projectConfigs,
    projectStatuses,
    loading,
    error,
    refreshData,
    startMonitor,
    stopMonitor,
    isRunning,
    executeProjectNow,
  } = useProjectStrategyMonitor();

  // 过滤项目配置
  const filteredConfigs = Object.values(projectConfigs).filter((config) => {
    const matchesSearch = config.ProjectName.toLowerCase().includes(
      searchTerm.toLowerCase()
    );
    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "enabled" && config.Enabled) ||
      (filterStatus === "disabled" && !config.Enabled);

    return matchesSearch && matchesStatus;
  });

  // 表格列定义
  const columns = [
    {
      key: "project_id",
      title: "项目ID",
      width: 100,
      render: (item) => <span className="project-id">{item.project_id}</span>,
    },
    {
      key: "project_name",
      title: "项目名称",
      width: 200,
      render: (item) => (
        <span className="project-name">{item.project_name}</span>
      ),
    },
    {
      key: "enabled",
      title: "监控状态",
      width: 120,
      render: (item) => (
        <span
          className={`status-badge ${item.enabled ? "enabled" : "disabled"}`}
        >
          {item.enabled ? "已启用" : "已禁用"}
        </span>
      ),
    },
    {
      key: "last_check",
      title: "最后检查",
      width: 180,
      render: (item) => (
        <span className="last-check">
          {item.last_check
            ? new Date(item.last_check).toLocaleString("zh-CN")
            : "未检查"}
        </span>
      ),
    },
    {
      key: "trigger_count",
      title: "触发次数",
      width: 120,
      render: (item) => (
        <span className="trigger-count">{item.trigger_count || 0}</span>
      ),
    },
    {
      key: "last_triggered",
      title: "最后触发",
      width: 180,
      render: (item) => (
        <span className="last-triggered">
          {item.last_triggered
            ? new Date(item.last_triggered).toLocaleString("zh-CN")
            : "未触发"}
        </span>
      ),
    },
    {
      key: "condition_count",
      title: "条件数量",
      width: 100,
      render: (item) => (
        <span className="condition-count">{item.condition_count || 0}</span>
      ),
    },
    {
      key: "action_count",
      title: "动作数量",
      width: 100,
      render: (item) => (
        <span className="action-count">{item.action_count || 0}</span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      width: 200,
      render: (item) => (
        <div className="action-buttons">
          <Button
            size="small"
            onClick={() => executeProjectNow(item.project_id)}
            disabled={!item.enabled}
          >
            立即执行
          </Button>
          <Button size="small" variant="outline" onClick={() => refreshData()}>
            刷新状态
          </Button>
        </div>
      ),
    },
  ];

  // 工具栏操作
  const toolbarActions = [
    {
      label: "启动监控",
      onClick: startMonitor,
      disabled: isRunning,
      variant: "primary",
    },
    {
      label: "停止监控",
      onClick: stopMonitor,
      disabled: !isRunning,
      variant: "outline",
    },
    {
      label: "刷新数据",
      onClick: refreshData,
      variant: "outline",
    },
  ];

  // 状态过滤器选项
  const statusOptions = [
    { value: "all", label: "全部状态" },
    { value: "enabled", label: "已启用" },
    { value: "disabled", label: "已禁用" },
  ];

  if (loading) {
    return (
      <div className="project-strategy-monitor">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载项目监控数据中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="project-strategy-monitor">
        <div className="error-container">
          <h3>加载失败</h3>
          <p>{error}</p>
          <Button onClick={refreshData}>重试</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="project-strategy-monitor">
      <div className="page-header">
        <h2>项目级策略监控</h2>
        <div className="monitor-status">
          <span
            className={`status-indicator ${isRunning ? "running" : "stopped"}`}
          >
            {isRunning ? "●" : "○"}
          </span>
          <span className="status-text">
            {isRunning ? "监控运行中" : "监控已停止"}
          </span>
        </div>
      </div>

      <Toolbar actions={toolbarActions} />

      <div className="filters-section">
        <div className="search-filter">
          <SearchInput
            placeholder="搜索项目名称..."
            value={searchTerm}
            onChange={setSearchTerm}
          />
        </div>
        <div className="status-filter">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="status-select"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="stats-section">
        <div className="stat-card">
          <div className="stat-value">{filteredConfigs.length}</div>
          <div className="stat-label">监控项目</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredConfigs.filter((c) => c.Enabled).length}
          </div>
          <div className="stat-label">已启用</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredConfigs.reduce((sum, c) => sum + (c.TriggerCount || 0), 0)}
          </div>
          <div className="stat-label">总触发次数</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {filteredConfigs.filter((c) => c.LastTriggered).length}
          </div>
          <div className="stat-label">今日触发</div>
        </div>
      </div>

      <DataTable
        data={filteredConfigs}
        columns={columns}
        rowKey="project_id"
        selectedRows={selectedProjects}
        onSelectionChange={setSelectedProjects}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        emptyText="暂无项目监控数据"
      />

      {selectedProjects.length > 0 && (
        <div className="batch-actions">
          <h4>批量操作</h4>
          <div className="batch-buttons">
            <Button
              onClick={() => {
                selectedProjects.forEach((projectId) => {
                  executeProjectNow(projectId);
                });
              }}
            >
              批量执行 ({selectedProjects.length})
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectStrategyMonitor;
