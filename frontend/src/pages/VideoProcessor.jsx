import React, { useState, useEffect, useRef } from 'react';
import { Upload, Play, Pause, Download, Trash2, Settings, FileVideo, Type, Wand2, Save, FolderOpen, Eye, Copy, <PERSON>lette, AlignLeft, AlignCenter, AlignRight, X, HelpCircle, Info, Keyboard, FileText, Folder, RotateCcw, Zap, Users, CheckCircle, Music, Image } from 'lucide-react';
import { Events } from '@wailsio/runtime';
import StatusBar from '../components/StatusBar';
import { ContextMenu, MenuBar } from '../components/ui';
import { toast } from '../components/ui/Toast';

const VideoProcessor = () => {
  const [videos, setVideos] = useState([]);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [activeTab, setActiveTab] = useState('text'); // 新增：当前选择的功能标签
  const [processing, setProcessing] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [textPresets, setTextPresets] = useState([]);
  const videoRef = useRef(null);
  
  // 进度条相关状态
  const [showProgress, setShowProgress] = useState(false);
  const [progressValue, setProgressValue] = useState(0);
  const [progressText, setProgressText] = useState('');
  const [processingComplete, setProcessingComplete] = useState(false);
  const progressIntervalRef = useRef(null);
  
  // 真实进度相关状态
  const [isRealProgress, setIsRealProgress] = useState(false);
  const [totalVideos, setTotalVideos] = useState(0);
  const [completedVideos, setCompletedVideos] = useState(0);
  const [currentVideoProgress, setCurrentVideoProgress] = useState(0);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(1);
  
  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  
  // 视频加载状态
  const [videoLoaded, setVideoLoaded] = useState(false);
  
  // 字体缩放比例状态
  const [fontSizeScale, setFontSizeScale] = useState(0.5);
  
  // 右键菜单相关状态
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    video: null
  });
  
  // 视频列表选项卡状态
  const [videoListTab, setVideoListTab] = useState('uploaded'); // 'uploaded' | 'processed'
  
  // 文字设置 - 直接使用后端字段名
  const [textSettings, setTextSettings] = useState({
    id: 0,
    text: "",
    font_file: "arial.ttf",
    font_size: 24,
    font_color: "#FFFFFF",
    position_x: 50.0,
    position_y: 50.0,
    box: 2, // 2=启用背景框，1=禁用
    box_color: "#000000",
    box_border_rw: 5,
    shadow_wx: 2,
    shadow_wy: 2,
    shadow_color: "#000000",
    border_rw: 0,
    border_color: "#000000",
    remark: null
  });

  // 视频变体设置
  const [variationSettings, setVariationSettings] = useState({
    count: 5,
    aspectRatio: '16:9',
    resolution: { width: 1920, height: 1080 },
    isOriginSound: true,
    effects: []
  });

  // 视频混剪设置
  const [clipSettings, setClipSettings] = useState({
    clipTaskName: '',
    aspectRatio: '16:9',
    scaleMode: 'ScaleToFill',
    musicMixMode: 1,
    genTotal: 10,
    musicUrl: [],
    headCover: [],
    tailCover: [],
    groupList: []
  });

  // 混剪任务列表状态
  const [clipTaskList, setClipTaskList] = useState([]);
  const [clipTaskLoading, setClipTaskLoading] = useState(false);

  // 混剪任务操作状态
  const [selectedClipTask, setSelectedClipTask] = useState(null);
  const [clipGenCount, setClipGenCount] = useState(1);
  const [clipTaskProcessing, setClipTaskProcessing] = useState(false);

  // 文字预设
  const [presetForm, setPresetForm] = useState({
    name: '',
    description: '',
    settings: { ...textSettings }
  });

  // 可用字体
  const fonts = [
    { value: 'arial.ttf', label: 'Arial' },
    { value: 'simhei.ttf', label: '黑体' },
    { value: 'simsun.ttf', label: '宋体' },
    { value: 'simkai.ttf', label: '楷体' },
    { value: 'simfang.ttf', label: '仿宋' },
    { value: 'microsoftyahei.ttf', label: '微软雅黑' }
  ];

  // 视频效果
  const videoEffects = [
    { id: 'crop_scale', name: '随机裁剪和缩放', enabled: true },
    { id: 'rotation', name: '随机旋转', enabled: true },
    { id: 'brightness', name: '随机亮度和对比度调整', enabled: true },
    { id: 'color', name: '随机色彩调整', enabled: true },
    { id: 'sharpen', name: '随机锐化或模糊', enabled: true },
  ];

  // 宽高比选项
  const aspectRatios = [
    { value: '16:9', label: '16:9 (横屏)', resolution: { width: 1920, height: 1080 } },
    { value: '9:16', label: '9:16 (竖屏)', resolution: { width: 1080, height: 1920 } },
    { value: '1:1', label: '1:1 (正方形)', resolution: { width: 1080, height: 1080 } },
    { value: '4:3', label: '4:3 (传统)', resolution: { width: 1440, height: 1080 } }
  ];

  // 初始化
  useEffect(() => {
    loadTextPresets();
    loadClipTaskList();
    setVariationSettings(prev => ({
      ...prev,
      effects: videoEffects
    }));

    // 监听文字预设窗口关闭事件
    const handleTextWindowClose = () => {
      console.log('收到text_window_close事件，刷新文字预设列表');
      loadTextPresets();
    };

    // 监听拖拽上传视频事件
    const handleUploadVideo = async (eventData) => {
      console.log('收到upload_video事件:', eventData);
      await handleUploadVideoEvent(eventData);
    };

    // 使用Wails3的EventsOn监听事件
    Events.On('text_window_close', handleTextWindowClose);
    Events.On('upload_video', handleUploadVideo);

    // 清理事件监听器
    return () => {
      Events.Off('text_window_close', handleTextWindowClose);
      Events.Off('upload_video', handleUploadVideo);
    };
  }, []);

  // 加载文字预设
  const loadTextPresets = async () => {
    try {
      const { TextInfoService, handleResult } = await import('../services/api.js');
      const result = await TextInfoService.GetTextInfoList({ page: 1, pageSize: 100 });
      const data = handleResult(result);
      
      if (data && data.data && data.data.list) {
        // 后端数据直接映射到前端（字段名已一致）
        const presets = data.data.list.map(preset => ({
          ...preset,  // 直接展开所有后端字段
          name: preset.remark || `预设${preset.id}`,
          description: preset.remark || '自定义预设'
        }));
        setTextPresets(presets);
      }
        } catch (error) {
      console.error('加载文字预设失败:', error);
      // 设置空预设列表，用户仍可以手动配置文字设置
      setTextPresets([]);
    }
  };

  // 加载混剪任务列表
  const loadClipTaskList = async () => {
    try {
      setClipTaskLoading(true);
      console.log('加载混剪任务列表 - 开始');

      const { ClipTaskService, handleResult } = await import('../services/api.js');

      // 使用 page=0, pageSize=0 获取全部数据
      const params = {
        page: 0,
        page_size: 0,
        clip_task_name: '',
        gen_status: 0
      };

      console.log('加载混剪任务列表 - 请求参数:', params);
      const result = await ClipTaskService.List(params);
      console.log('加载混剪任务列表 - 原始结果:', result);

      if (result && result.data && result.data.list) {
        const taskList = result.data.list;
        console.log('加载混剪任务列表 - 获取到任务列表:', taskList);
        setClipTaskList(taskList);
      } else {
        console.log('加载混剪任务列表 - 未获取到有效数据');
        setClipTaskList([]);
      }
    } catch (error) {
      console.error('加载混剪任务列表失败:', error);
      setClipTaskList([]);
    } finally {
      setClipTaskLoading(false);
    }
  };

  // 删除混剪任务
  const handleDeleteClipTask = async (taskId) => {
    try {
      console.log('删除混剪任务 - 任务ID:', taskId);

      const { ClipTaskService } = await import('../services/api.js');

      const params = { id: taskId };
      console.log('删除混剪任务 - 请求参数:', params);

      const result = await ClipTaskService.DeleteClipTask(params);
      console.log('删除混剪任务 - 结果:', result);

      if (result && result.success !== false) {
        console.log('删除混剪任务 - 成功');
        // 重新加载任务列表
                  await loadClipTaskList();
          toast.success('混剪任务删除成功！');
        } else {
          console.log('删除混剪任务 - 失败:', result);
          toast.error('删除混剪任务失败：' + (result?.message || '未知错误'));
        }
      } catch (error) {
        console.error('删除混剪任务失败:', error);
        toast.error('删除混剪任务失败：' + error.message);
    }
  };

  // 获取生成状态文本
  const getGenStatusText = (status) => {
    switch (status) {
      case 1: return '暂未生成';
      case 2: return '已开始生成';
      default: return '未知状态';
    }
  };

  // 获取生成状态颜色
  const getGenStatusColor = (status) => {
    switch (status) {
      case 1: return 'text-orange-600 bg-orange-50 border-orange-200';
      case 2: return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-slate-600 bg-slate-50 border-slate-200';
    }
  };

  // 启动混剪任务
      const handleStartClipTask = async () => {
      if (!selectedClipTask) {
        toast.warning('请先选择一个混剪任务');
        return;
      }

      if (clipGenCount < 1 || clipGenCount > selectedClipTask.remaining_gen_count) {
        toast.warning(`生成数量必须在 1 到 ${selectedClipTask.remaining_gen_count} 之间`);
        return;
      }

    setClipTaskProcessing(true);

    try {
      console.log('启动混剪任务 - 开始');
      const { ClipTaskService } = await import('../services/api.js');

      const params = {
        id: selectedClipTask.id,
        count: clipGenCount
      };

      console.log('启动混剪任务 - 请求参数:', params);
      const result = await ClipTaskService.StartClipTask(params);
      console.log('启动混剪任务 - 结果:', result);

      // 检查返回结果的结构：{ code: 0, msg: "success", data: { success: true, message: "...", video_paths: [...], failed_paths: [...] } }
      if (result && result.code === 0 && result.data) {
        console.log('启动混剪任务 - 成功');
        const responseData = result.data;

        if (responseData.success && responseData.video_paths && responseData.video_paths.length > 0) {
          console.log('启动混剪任务 - 获取到的视频路径:', responseData.video_paths);

          // 使用BatchGetVideoInfo获取每个生成视频的详细信息
          const { FfmpegService, handleResult } = await import('../services/api.js');
          console.log('启动混剪任务 - 准备获取视频详细信息');
          const videoInfoResult = await FfmpegService.BatchGetVideoInfo(responseData.video_paths);
          console.log('启动混剪任务 - BatchGetVideoInfo调用结果:', videoInfoResult);

          const videoInfoData = handleResult(videoInfoResult);
          console.log('启动混剪任务 - 处理后的视频信息结果:', videoInfoData);

          // 创建新的视频对象并添加到处理完成列表
          const newVideos = [];
          responseData.video_paths.forEach((videoPath, index) => {
            // 从路径中提取文件名
            const fileName = videoPath.split(/[/\\]/).pop();
            console.log(`启动混剪任务 - 处理视频 #${index + 1}, 路径: ${videoPath}, 文件名: ${fileName}`);

            // 验证文件名的有效性
            let finalFileName;
            if (fileName && fileName.trim() && fileName.includes('.')) {
              finalFileName = fileName;
              console.log(`启动混剪任务 - 使用提取的文件名: ${finalFileName}`);
            } else {
              // 如果无法提取有效文件名，生成一个基于时间戳的唯一名称
              const timestamp = Date.now() + index;
              finalFileName = `clip_video_${timestamp}.mp4`;
              console.log(`启动混剪任务 - 文件名无效，使用生成的名称: ${finalFileName}`);
              console.warn(`启动混剪任务 - 原始路径: ${videoPath}, 提取的文件名: ${fileName}`);
            }

            // 获取对应的视频信息
            let videoInfo = {
              duration: 0,
              width: 0,
              height: 0,
              size: 0,
              format: finalFileName.split('.').pop().toUpperCase()
            };

            if (videoInfoData && videoInfoData.data && Array.isArray(videoInfoData.data) && videoInfoData.data[index]) {
              const info = videoInfoData.data[index];
              console.log(`启动混剪任务 - 获取到视频 #${index+1} 的原始信息:`, info);
              
              if (info && typeof info === 'object') {
                videoInfo = {
                  duration: typeof info.duration === 'number' && info.duration > 0 ? info.duration : 0,
                  width: typeof info.width === 'number' && info.width > 0 ? info.width : 0,
                  height: typeof info.height === 'number' && info.height > 0 ? info.height : 0,
                  size: typeof info.size === 'number' && info.size > 0 ? info.size : 0,
                  format: (info.format && typeof info.format === 'string') ? info.format : videoInfo.format
                };
                console.log(`启动混剪任务 - 处理后视频 #${index+1} 的详细信息:`, videoInfo);
              } else {
                console.warn(`启动混剪任务 - 视频 #${index+1} 的信息对象无效:`, info);
              }
            } else {
              console.warn(`启动混剪任务 - 未能获取视频 #${index+1} 的详细信息，使用默认值`);
              console.warn(`启动混剪任务 - videoInfoData结构:`, videoInfoData);
            }

            // 使用辅助函数格式化视频信息
            const formattedInfo = formatVideoInfo(videoInfo, finalFileName);
            console.log(`启动混剪任务 - 格式化后视频 #${index + 1} 的信息:`, formattedInfo);

            // 创建新视频对象
            const processedVideo = {
              id: Date.now() + index + Math.random(), // 确保每个视频有唯一ID
              name: finalFileName,
              file: null,
              filePath: videoPath,
              size: formattedInfo.size,
              duration: formattedInfo.duration,
              resolution: formattedInfo.resolution,
              format: formattedInfo.format,
              status: '已完成',
              progress: 100,
              createTime: new Date().toLocaleString(),
              processTime: new Date().toLocaleString(),
              url: videoPath
            };

            console.log(`启动混剪任务 - 创建的新视频对象 #${index + 1}:`, processedVideo);
            newVideos.push(processedVideo);
          });

          console.log('启动混剪任务 - 所有创建的新视频对象:', newVideos);
          setVideos([...videos, ...newVideos]);

          // 自动切换到"处理完成"选项卡显示新生成的视频
          if (newVideos.length > 0) {
            setVideoListTab('processed');
          }

                      toast.success(responseData.message || `混剪任务启动成功！已生成 ${responseData.video_paths.length} 个视频文件`);
          } else {
            console.log('启动混剪任务 - 未生成视频或生成失败');
            toast.warning(responseData.message || '混剪任务执行完成，但未生成视频');
        }

        // 重新加载任务列表
        await loadClipTaskList();

        // 重置选择状态
        setSelectedClipTask(null);
        setClipGenCount(1);
      } else {
                  console.log('启动混剪任务 - 失败:', result);
          const errorMessage = result?.msg || result?.message || '未知错误';
          toast.error('启动混剪任务失败：' + errorMessage);
        }
      } catch (error) {
        console.error('启动混剪任务失败:', error);
        toast.error('启动混剪任务失败：' + error.message);
    } finally {
      setClipTaskProcessing(false);
    }
  };

  // 选择混剪任务
  const handleSelectClipTask = (task) => {
    setSelectedClipTask(task);
    setClipGenCount(Math.min(1, task.remaining_gen_count)); // 默认生成1个，但不超过剩余数量
  };

  // 上传视频
  const handleUpload = async () => {
    console.log('开始执行handleUpload函数');
    try {
      console.log('准备导入服务模块');
      const { SystemService, FfmpegService, handleResult } = await import('../services/api.js');
      console.log('服务模块导入成功');
      
      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择视频文件',
        display_name: '视频文件',
        pattern: '*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv',
        is_multiple: false
      };
      
      console.log('准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('SystemService.OpenFile调用结果:', result);
      
      const data = handleResult(result);
      console.log('处理后的OpenFile结果:', data);
      
      // SystemService.OpenFile返回的是字符串数组，单选时取第一个元素
      if (data.data && data.data.length > 0) {
        // 获取文件路径
        const filePath = data.data[0]; // 直接获取数组中的第一个路径
        const fileName = filePath.split(/[/\\]/).pop(); // 从路径中获取文件名
        console.log('获取到的文件路径:', filePath);
        console.log('提取的文件名:', fileName);
        
        // 使用BatchGetVideoInfo获取视频详细信息
        console.log('准备调用FfmpegService.BatchGetVideoInfo', [filePath]);
        const videoInfoResult = await FfmpegService.BatchGetVideoInfo([filePath]);
        console.log('BatchGetVideoInfo调用结果:', videoInfoResult);
        
        const videoInfoData = handleResult(videoInfoResult);
        console.log('处理后的BatchGetVideoInfo结果:', videoInfoData);
        
        let videoInfo = {
          duration: 0,
          width: 0,
          height: 0,
          size: 0,
          format: fileName.split('.').pop().toUpperCase()
        };
        
        // 如果成功获取到视频信息
        if (videoInfoData && videoInfoData.data && Array.isArray(videoInfoData.data) && videoInfoData.data.length > 0) {
          const info = videoInfoData.data[0];
          console.log('上传视频 - 获取到的视频信息:', info);
          
          if (info && typeof info === 'object') {
            videoInfo = {
              duration: typeof info.duration === 'number' && info.duration > 0 ? info.duration : 0,
              width: typeof info.width === 'number' && info.width > 0 ? info.width : 0,
              height: typeof info.height === 'number' && info.height > 0 ? info.height : 0,
              size: typeof info.size === 'number' && info.size > 0 ? info.size : 0,
              format: (info.format && typeof info.format === 'string') ? info.format : videoInfo.format
            };
            console.log('上传视频 - 处理后的视频信息:', videoInfo);
          } else {
            console.warn('上传视频 - 视频信息对象无效:', info);
          }
        } else {
          console.warn('上传视频 - 未获取到有效的视频信息数据');
          console.warn('上传视频 - videoInfoData结构:', videoInfoData);
          console.warn('上传视频 - 原始API结果:', videoInfoResult);
        }
        
        // 使用辅助函数格式化视频信息
        const formattedInfo = formatVideoInfo(videoInfo, fileName);
        console.log('上传视频 - 格式化后的视频信息:', formattedInfo);
        
        // 创建新视频对象
        const newVideo = {
          id: Date.now(),
          name: fileName,
          file: null, // 不再保存File对象，而是保存文件路径
          filePath: filePath, // 保存文件路径用于后续处理
          size: formattedInfo.size,
          duration: formattedInfo.duration,
          resolution: formattedInfo.resolution,
          format: formattedInfo.format,
          status: '已上传',
          progress: 0,
          createTime: new Date().toLocaleString(),
          url: filePath // 直接使用文件路径
        };
        
        console.log('创建的视频对象:', newVideo);
        setVideos([...videos, newVideo]);
        setSelectedVideo(newVideo);
        setPreviewUrl(filePath);
        
        // 自动切换到"原始视频"选项卡显示新上传的视频
        setVideoListTab('uploaded');
        
        console.log('视频上传处理完成');
      } else {
        console.warn('未获取到有效的文件路径，OpenFile返回结果:', data);
      }
    } catch (error) {
      console.error('选择视频文件失败，详细错误:', error);
    }
  };

  // 处理拖拽上传视频事件
  const handleUploadVideoEvent = async (eventData) => {
    console.log('开始处理拖拽上传视频事件:', eventData);
    
    try {
      // 获取视频文件路径数组
      let videoPaths = [];
      
      // 解析事件数据结构: { name: "upload_video", data: [[...]], sender: "" }
      if (eventData && eventData.data && Array.isArray(eventData.data)) {
        // data 是一个数组，包含子数组，取第一个子数组
        if (eventData.data.length > 0 && Array.isArray(eventData.data[0])) {
          videoPaths = eventData.data[0]; // 取第一个子数组中的文件路径
          console.log('拖拽上传 - 解析到的视频路径:', videoPaths);
        } else {
          console.warn('拖拽上传 - data[0] 不是数组或为空:', eventData.data);
          return;
        }
      } else {
        console.warn('拖拽上传 - 未识别的事件数据格式:', eventData);
        console.warn('拖拽上传 - 期望格式: { name: "upload_video", data: [[...]], sender: "" }');
        return;
      }
      
      if (videoPaths.length === 0) {
        console.warn('拖拽上传 - 未获取到视频文件路径');
        return;
      }
      
      console.log('拖拽上传 - 获取到的视频路径列表:', videoPaths);
      
      // 导入服务模块
      const { FfmpegService, handleResult } = await import('../services/api.js');
      
      // 使用BatchGetVideoInfo获取视频详细信息
      console.log('拖拽上传 - 准备调用FfmpegService.BatchGetVideoInfo', videoPaths);
      const videoInfoResult = await FfmpegService.BatchGetVideoInfo(videoPaths);
      console.log('拖拽上传 - BatchGetVideoInfo调用结果:', videoInfoResult);
      
      const videoInfoData = handleResult(videoInfoResult);
      console.log('拖拽上传 - 处理后的BatchGetVideoInfo结果:', videoInfoData);
      
      const newVideos = [];
      videoPaths.forEach((filePath, index) => {
        // 从路径中提取文件名
        const fileName = filePath.split(/[/\\]/).pop();
        console.log(`拖拽上传 - 处理视频 #${index + 1}, 路径: ${filePath}, 文件名: ${fileName}`);
        
        // 验证文件名的有效性
        let finalFileName;
        if (fileName && fileName.trim() && fileName.includes('.')) {
          finalFileName = fileName;
          console.log(`拖拽上传 - 使用提取的文件名: ${finalFileName}`);
        } else {
          // 如果无法提取有效文件名，生成一个基于时间戳的唯一名称
          const timestamp = Date.now() + index;
          finalFileName = `uploaded_video_${timestamp}.mp4`;
          console.log(`拖拽上传 - 文件名无效，使用生成的名称: ${finalFileName}`);
          console.warn(`拖拽上传 - 原始路径: ${filePath}, 提取的文件名: ${fileName}`);
        }
        
        // 获取对应的视频信息
        let videoInfo = {
          duration: 0,
          width: 0,
          height: 0,
          size: 0,
          format: finalFileName.split('.').pop().toUpperCase()
        };
        
        if (videoInfoData && videoInfoData.data && Array.isArray(videoInfoData.data) && videoInfoData.data[index]) {
          const info = videoInfoData.data[index];
          console.log(`拖拽上传 - 获取到视频 #${index + 1} 的信息:`, info);
          
          if (info && typeof info === 'object') {
            videoInfo = {
              duration: typeof info.duration === 'number' && info.duration > 0 ? info.duration : 0,
              width: typeof info.width === 'number' && info.width > 0 ? info.width : 0,
              height: typeof info.height === 'number' && info.height > 0 ? info.height : 0,
              size: typeof info.size === 'number' && info.size > 0 ? info.size : 0,
              format: (info.format && typeof info.format === 'string') ? info.format : videoInfo.format
            };
            console.log(`拖拽上传 - 处理后视频 #${index + 1} 的信息:`, videoInfo);
          } else {
            console.warn(`拖拽上传 - 视频 #${index + 1} 的信息对象无效:`, info);
          }
        } else {
          console.warn(`拖拽上传 - 未获取到视频 #${index + 1} 的详细信息`);
          console.warn(`拖拽上传 - videoInfoData结构:`, videoInfoData);
          if (videoInfoData && videoInfoData.data) {
            console.warn(`拖拽上传 - data数组长度: ${videoInfoData.data.length}, 期望索引: ${index}`);
          }
        }
        
        // 使用辅助函数格式化视频信息
        const formattedInfo = formatVideoInfo(videoInfo, finalFileName);
        console.log(`拖拽上传 - 格式化后视频 #${index + 1} 的信息:`, formattedInfo);
        
        // 创建新视频对象
        const newVideo = {
          id: Date.now() + index, // 确保每个视频有唯一ID
          name: finalFileName,
          file: null,
          filePath: filePath,
          size: formattedInfo.size,
          duration: formattedInfo.duration,
          resolution: formattedInfo.resolution,
          format: formattedInfo.format,
          status: '已上传',
          progress: 0,
          createTime: new Date().toLocaleString(),
          url: filePath
        };
        
        console.log(`拖拽上传 - 创建的新视频对象 #${index + 1}:`, newVideo);
        newVideos.push(newVideo);
      });
      
      console.log('拖拽上传 - 所有创建的新视频对象:', newVideos);
      
      // 添加到视频列表
      setVideos(prevVideos => [...prevVideos, ...newVideos]);
      
      // 自动切换到"原始视频"选项卡显示新上传的视频
      if (newVideos.length > 0) {
        setVideoListTab('uploaded');
      }
      
      // 如果当前没有选中的视频，自动选中第一个上传的视频
      if (!selectedVideo && newVideos.length > 0) {
        const firstVideo = newVideos[0];
        setSelectedVideo(firstVideo);
        setPreviewUrl(firstVideo.url);
        console.log('拖拽上传 - 自动选中第一个视频:', firstVideo.name);
      }
      
      console.log(`拖拽上传 - 成功处理 ${newVideos.length} 个视频文件`);
          } catch (error) {
        console.error('拖拽上传 - 处理失败, 详细错误:', error);
        toast.error(`拖拽上传失败：${error.message || '未知错误'}`);
      }
  };

  // 选择视频
  const handleSelectVideo = (video) => {
    setSelectedVideo(video);
    setPreviewUrl(video.url);
    setVideoLoaded(false); // 重置视频加载状态
  };

  // 预览视频
  const handlePreview = (video) => {
    setPreviewUrl(video.url);
    setVideoLoaded(false); // 重置视频加载状态
    if (videoRef.current) {
      videoRef.current.load();
    }
  };

  // 添加文字
  const handleAddText = async () => {
    if (!selectedVideo) {
      console.warn('添加文字 - 未选择视频');
      return;
    }
    
    console.log('添加文字 - 开始处理, 选中视频:', selectedVideo);
    console.log('添加文字 - 文字设置:', textSettings);
    setProcessing(true);
    
    // 启动进度条 - 预计25秒完成
    startProgress('正在添加文字到视频...', 25000);
    
    try {
      console.log('添加文字 - 准备导入服务模块');
      const { FfmpegService, handleResult } = await import('../services/api.js');
      console.log('添加文字 - 服务模块导入成功');
      
      // 直接使用已保存的文件路径
      const videoFilePath = selectedVideo.filePath;
      console.log('添加文字 - 使用的视频文件路径:', videoFilePath);
      
      // 调用后端API添加文字
      console.log('添加文字 - 准备调用SetTextInfo', {
        videoFile: videoFilePath,
        textInfo: textSettings
      });
      
      // 将前端的 camelCase 字段转换为后端期望的 snake_case 格式
      // 注意：后端期望的是一个 TextInfo 数组，不是单个对象
      const textInfoForBackend = {
        ...textSettings,
        // 确保数据类型正确
        font_size: parseInt(textSettings.font_size),
        position_x: parseFloat(textSettings.position_x),
        position_y: parseFloat(textSettings.position_y),
        box: parseInt(textSettings.box),
        box_border_rw: parseInt(textSettings.box_border_rw),
        shadow_wx: parseInt(textSettings.shadow_wx),
        shadow_wy: parseInt(textSettings.shadow_wy),
        border_rw: parseInt(textSettings.border_rw)
      };
      
      const result = await FfmpegService.SetTextInfo({
        videoFile: videoFilePath,
        textInfo: [textInfoForBackend], // 传递数组而不是单个对象
        isOriginSound: true,
        width: 1920,
        height: 1080,
        scaleMode: 'fit'
      });
      console.log('添加文字 - SetTextInfo调用结果:', result);
      
      const data = handleResult(result);
      console.log('添加文字 - 处理后的结果:', data);
      
      // SetTextInfo返回的是生成的视频路径数组
      if (data.data && Array.isArray(data.data) && data.data.length > 0) {
        console.log('添加文字 - 获取到的视频路径列表:', data.data);
        
        // 使用BatchGetVideoInfo获取每个生成视频的详细信息
        console.log('添加文字 - 准备获取视频详细信息');
        const videoInfoResult = await FfmpegService.BatchGetVideoInfo(data.data);
        console.log('添加文字 - BatchGetVideoInfo调用结果:', videoInfoResult);
        
        const videoInfoData = handleResult(videoInfoResult);
        console.log('添加文字 - 处理后的视频信息结果:', videoInfoData);
        
        const newVideos = [];
        data.data.forEach((videoPath, index) => {
          // 从路径中提取文件名
          const fileName = videoPath.split(/[/\\]/).pop();
          console.log(`添加文字 - 处理视频 #${index + 1}, 路径: ${videoPath}, 文件名: ${fileName}`);
          
          // 验证文件名的有效性
          let finalFileName;
          if (fileName && fileName.trim() && fileName.includes('.')) {
            finalFileName = fileName;
            console.log(`添加文字 - 使用提取的文件名: ${finalFileName}`);
          } else {
            // 如果无法提取有效文件名，生成一个基于时间戳的唯一名称
            const timestamp = Date.now() + index;
            finalFileName = `processed_video_${timestamp}.mp4`;
            console.log(`添加文字 - 文件名无效，使用生成的名称: ${finalFileName}`);
            console.warn(`添加文字 - 原始路径: ${videoPath}, 提取的文件名: ${fileName}`);
          }
          
          // 获取对应的视频信息
          let videoInfo = {
            duration: 0,
            width: 0,
            height: 0,
            size: 0,
            format: finalFileName.split('.').pop().toUpperCase()
          };
          
          if (videoInfoData && videoInfoData.data && Array.isArray(videoInfoData.data) && videoInfoData.data[index]) {
            const info = videoInfoData.data[index];
            console.log(`添加文字 - 获取到视频 #${index + 1} 的信息:`, info);
            
            if (info && typeof info === 'object') {
              videoInfo = {
                duration: typeof info.duration === 'number' && info.duration > 0 ? info.duration : 0,
                width: typeof info.width === 'number' && info.width > 0 ? info.width : 0,
                height: typeof info.height === 'number' && info.height > 0 ? info.height : 0,
                size: typeof info.size === 'number' && info.size > 0 ? info.size : 0,
                format: (info.format && typeof info.format === 'string') ? info.format : videoInfo.format
              };
              console.log(`添加文字 - 处理后视频 #${index + 1} 的信息:`, videoInfo);
            } else {
              console.warn(`添加文字 - 视频 #${index + 1} 的信息对象无效:`, info);
            }
          } else {
            console.warn(`添加文字 - 未获取到视频 #${index + 1} 的详细信息`);
            console.warn(`添加文字 - videoInfoData结构:`, videoInfoData);
          }
          
          // 使用辅助函数格式化视频信息，如果获取失败则使用原视频信息
          const formattedInfo = formatVideoInfo(videoInfo, finalFileName);
          console.log(`添加文字 - 格式化后视频 #${index + 1} 的信息:`, formattedInfo);
          
          // 创建新视频对象
          const processedVideo = {
            id: Date.now() + index, // 确保每个视频有唯一ID
            name: finalFileName,
            file: null,
            filePath: videoPath,
            size: formattedInfo.size !== '未知' ? formattedInfo.size : selectedVideo.size,
            duration: formattedInfo.duration !== '未知' ? formattedInfo.duration : selectedVideo.duration,
            resolution: formattedInfo.resolution !== '未知' ? formattedInfo.resolution : selectedVideo.resolution,
            format: formattedInfo.format !== '未知' ? formattedInfo.format : selectedVideo.format,
            status: '已完成',
            progress: 100,
            createTime: new Date().toLocaleString(),
            processTime: new Date().toLocaleString(),
            url: videoPath
          };
          
          console.log(`添加文字 - 创建的新视频对象 #${index + 1}:`, processedVideo);
          newVideos.push(processedVideo);
        });
        
        console.log('添加文字 - 所有创建的新视频对象:', newVideos);
        setVideos([...videos, ...newVideos]);
        
        // 自动切换到"处理后"选项卡显示新生成的视频
        if (newVideos.length > 0) {
          setVideoListTab('processed');
        }
              } else {
          console.warn('添加文字 - 未获取到有效的视频路径');
          toast.error('添加文字失败：未获取到有效的视频路径');
        }
      
      // 完成进度条
      completeProgress();
      setProcessing(false);
      console.log('添加文字 - 处理完成');
    } catch (error) {
      console.error('添加文字 - 处理失败, 详细错误:', error);
      // 停止进度条
              stopProgress();
        setProcessing(false);
        toast.error(`添加文字失败：${error.message || '未知错误'}`);
      }
  };

  // 选择音乐文件
  const handleSelectMusicFiles = async () => {
    try {
      console.log('选择音乐文件 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('选择音乐文件 - 服务模块导入成功');

      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择音乐文件',
        display_name: '音乐文件',
        pattern: '*.mp3;*.wav;*.flac;*.aac;*.ogg;*.m4a;*.wma',
        is_multiple: true
      };

      console.log('选择音乐文件 - 准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('选择音乐文件 - SystemService.OpenFile调用结果:', result);

      const data = handleResult(result);
      console.log('选择音乐文件 - 处理后的OpenFile结果:', data);

      if (data.data && data.data.length > 0) {
        // 将新选择的文件添加到现有列表中，避免重复
        const newMusicFiles = data.data.filter(filePath =>
          !clipSettings.musicUrl.includes(filePath)
        );

        if (newMusicFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            musicUrl: [...prev.musicUrl, ...newMusicFiles]
          }));
          console.log(`选择音乐文件 - 成功添加 ${newMusicFiles.length} 个音乐文件`);
        } else {
          console.log('选择音乐文件 - 所选文件已存在，未添加新文件');
        }
      } else {
        console.log('选择音乐文件 - 用户取消了文件选择或未选择文件');
      }
          } catch (error) {
        console.error('选择音乐文件失败，详细错误:', error);
        toast.error('选择音乐文件失败：' + error.message);
      }
  };

  // 选择头部封面文件
  const handleSelectHeadCoverFiles = async () => {
    try {
      console.log('选择头部封面 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('选择头部封面 - 服务模块导入成功');

      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择头部封面文件',
        display_name: '图片文件',
        pattern: '*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp;*.tiff',
        is_multiple: true
      };

      console.log('选择头部封面 - 准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('选择头部封面 - SystemService.OpenFile调用结果:', result);

      const data = handleResult(result);
      console.log('选择头部封面 - 处理后的OpenFile结果:', data);

      if (data.data && data.data.length > 0) {
        // 将新选择的文件添加到现有列表中，避免重复
        const newCoverFiles = data.data.filter(filePath =>
          !clipSettings.headCover.includes(filePath)
        );

        if (newCoverFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            headCover: [...prev.headCover, ...newCoverFiles]
          }));
          console.log(`选择头部封面 - 成功添加 ${newCoverFiles.length} 个封面文件`);
        } else {
          console.log('选择头部封面 - 所选文件已存在，未添加新文件');
        }
      } else {
        console.log('选择头部封面 - 用户取消了文件选择或未选择文件');
      }
          } catch (error) {
        console.error('选择头部封面失败，详细错误:', error);
        toast.error('选择头部封面失败：' + error.message);
      }
  };

  // 选择尾部封面文件
  const handleSelectTailCoverFiles = async () => {
    try {
      console.log('选择尾部封面 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('选择尾部封面 - 服务模块导入成功');

      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择尾部封面文件',
        display_name: '图片文件',
        pattern: '*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp;*.tiff',
        is_multiple: true
      };

      console.log('选择尾部封面 - 准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('选择尾部封面 - SystemService.OpenFile调用结果:', result);

      const data = handleResult(result);
      console.log('选择尾部封面 - 处理后的OpenFile结果:', data);

      if (data.data && data.data.length > 0) {
        // 将新选择的文件添加到现有列表中，避免重复
        const newCoverFiles = data.data.filter(filePath =>
          !clipSettings.tailCover.includes(filePath)
        );

        if (newCoverFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            tailCover: [...prev.tailCover, ...newCoverFiles]
          }));
          console.log(`选择尾部封面 - 成功添加 ${newCoverFiles.length} 个封面文件`);
        } else {
          console.log('选择尾部封面 - 所选文件已存在，未添加新文件');
        }
      } else {
        console.log('选择尾部封面 - 用户取消了文件选择或未选择文件');
      }
          } catch (error) {
        console.error('选择尾部封面失败，详细错误:', error);
        toast.error('选择尾部封面失败：' + error.message);
      }
  };

  // 删除音乐文件
  const handleRemoveMusicFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      musicUrl: prev.musicUrl.filter(path => path !== filePath)
    }));
  };

  // 删除头部封面文件
  const handleRemoveHeadCoverFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      headCover: prev.headCover.filter(path => path !== filePath)
    }));
  };

  // 删除尾部封面文件
  const handleRemoveTailCoverFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      tailCover: prev.tailCover.filter(path => path !== filePath)
    }));
  };

  // 添加视频分组
  const handleAddVideoGroup = async () => {
    try {
      console.log('添加视频分组 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('添加视频分组 - 服务模块导入成功');

      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择分组视频文件',
        display_name: '视频文件',
        pattern: '*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm;*.m4v;*.3gp;*.f4v',
        is_multiple: true
      };

      console.log('添加视频分组 - 准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('添加视频分组 - SystemService.OpenFile调用结果:', result);

      const data = handleResult(result);
      console.log('添加视频分组 - 处理后的OpenFile结果:', data);

      if (data.data && data.data.length > 0) {
        // 创建新的视频分组
        const newGroup = {
          id: Date.now(), // 临时ID，用于前端管理
          video_url: data.data,
          is_origin_sound: true // 默认保留原声
        };

        setClipSettings(prev => ({
          ...prev,
          groupList: [...prev.groupList, newGroup]
        }));

        console.log(`添加视频分组 - 成功创建分组，包含 ${data.data.length} 个视频文件`);
      } else {
        console.log('添加视频分组 - 用户取消了文件选择或未选择文件');
      }
          } catch (error) {
        console.error('添加视频分组失败，详细错误:', error);
        toast.error('添加视频分组失败：' + error.message);
      }
  };

  // 删除视频分组
  const handleRemoveVideoGroup = (groupId) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.filter(group => group.id !== groupId)
    }));
  };

  // 切换分组原声设置
  const handleToggleGroupOriginSound = (groupId) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.map(group =>
        group.id === groupId
          ? { ...group, is_origin_sound: !group.is_origin_sound }
          : group
      )
    }));
  };

  // 从分组中删除单个视频
  const handleRemoveVideoFromGroup = (groupId, videoPath) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.map(group =>
        group.id === groupId
          ? { ...group, video_url: group.video_url.filter(path => path !== videoPath) }
          : group
      ).filter(group => group.video_url.length > 0) // 移除空分组
    }));
  };

  // 向现有分组添加视频
  const handleAddVideoToGroup = async (groupId) => {
    try {
      console.log('向分组添加视频 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('向分组添加视频 - 服务模块导入成功');

      // 使用SystemService.OpenFile打开文件选择器
      const req = {
        title: '选择要添加的视频文件',
        display_name: '视频文件',
        pattern: '*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm;*.m4v;*.3gp;*.f4v',
        is_multiple: true
      };

      console.log('向分组添加视频 - 准备调用SystemService.OpenFile', req);
      const result = await SystemService.OpenFile(req);
      console.log('向分组添加视频 - SystemService.OpenFile调用结果:', result);

      const data = handleResult(result);
      console.log('向分组添加视频 - 处理后的OpenFile结果:', data);

      if (data.data && data.data.length > 0) {
        setClipSettings(prev => ({
          ...prev,
          groupList: prev.groupList.map(group => {
            if (group.id === groupId) {
              // 过滤掉已存在的视频文件，避免重复
              const newVideos = data.data.filter(videoPath =>
                !group.video_url.includes(videoPath)
              );

              if (newVideos.length > 0) {
                console.log(`向分组添加视频 - 成功添加 ${newVideos.length} 个视频文件到分组`);
                return {
                  ...group,
                  video_url: [...group.video_url, ...newVideos]
                };
              } else {
                console.log('向分组添加视频 - 所选视频文件已存在于分组中');
                return group;
              }
            }
            return group;
          })
        }));
      } else {
        console.log('向分组添加视频 - 用户取消了文件选择或未选择文件');
      }
          } catch (error) {
        console.error('向分组添加视频失败，详细错误:', error);
        toast.error('向分组添加视频失败：' + error.message);
      }
  };

  // 获取文件名（从完整路径中提取）
  const getFileName = (filePath) => {
    if (!filePath) return '';
    return filePath.split(/[/\\]/).pop() || filePath;
  };

  // 获取文件扩展名
  const getFileExtension = (filePath) => {
    if (!filePath) return '';
    const fileName = getFileName(filePath);
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
  };

  // 判断是否为图片文件
  const isImageFile = (filePath) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'webp', 'tiff'];
    return imageExtensions.includes(getFileExtension(filePath));
  };

  // 判断是否为音频文件
  const isAudioFile = (filePath) => {
    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'];
    return audioExtensions.includes(getFileExtension(filePath));
  };

  // 文件缩略图组件
  const FileThumbnail = ({ filePath, size = 'md', className = '' }) => {
    const [imageError, setImageError] = useState(false);

    const sizeClasses = {
      sm: 'w-8 h-8',
      md: 'w-12 h-12',
      lg: 'w-16 h-16',
      xl: 'w-20 h-20'
    };

    const iconSizes = {
      sm: 16,
      md: 20,
      lg: 24,
      xl: 28
    };

    const sizeClass = sizeClasses[size] || sizeClasses.md;
    const iconSize = iconSizes[size] || iconSizes.md;

    if (isImageFile(filePath) && !imageError) {
      return (
        <div className={`${sizeClass} ${className} bg-slate-100 rounded overflow-hidden border border-slate-200 flex items-center justify-center`}>
          <img
            src={`/api/preview?path=${encodeURIComponent(filePath)}`}
            alt={getFileName(filePath)}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
            onLoad={() => setImageError(false)}
          />
        </div>
      );
    }

    if (isAudioFile(filePath)) {
      return (
        <div className={`${sizeClass} ${className} bg-blue-50 rounded border border-blue-200 flex items-center justify-center`}>
          <Music size={iconSize} className="text-blue-600" />
        </div>
      );
    }

    // 默认文件图标
    return (
      <div className={`${sizeClass} ${className} bg-slate-100 rounded border border-slate-200 flex items-center justify-center`}>
        <FileText size={iconSize} className="text-slate-500" />
      </div>
    );
  };

  // 创建混剪任务
      const handleCreateClipTask = async () => {
      if (!clipSettings.clipTaskName.trim()) {
        toast.warning('请输入混剪任务名称');
        return;
      }

    console.log('创建混剪任务 - 开始处理');
    console.log('创建混剪任务 - 混剪设置:', clipSettings);
    setProcessing(true);

    // 启动进度条
    startProgress('正在创建混剪任务...', 10000);

    try {
      console.log('创建混剪任务 - 准备导入服务模块');
      const { ClipTaskService, handleResult } = await import('../services/api.js');
      console.log('创建混剪任务 - 服务模块导入成功');

      // 准备调用参数，确保 groupList 格式正确
      const formattedGroupList = clipSettings.groupList.map(group => ({
        video_url: group.video_url,
        is_origin_sound: group.is_origin_sound
        // 移除前端临时的 id 字段
      }));

      const clipTaskData = {
        clip_task_name: clipSettings.clipTaskName,
        aspect_ratio: clipSettings.aspectRatio,
        scale_mode: clipSettings.scaleMode,
        music_mix_mode: clipSettings.musicMixMode,
        gen_total: clipSettings.genTotal,
        remaining_gen_count: clipSettings.genTotal,
        gen_status: 1, // 1=暂未生成视频
        music_url: clipSettings.musicUrl,
        head_cover: clipSettings.headCover,
        tail_cover: clipSettings.tailCover,
        group_list: formattedGroupList
      };

      console.log('创建混剪任务 - 准备调用CreateClipTask, 参数:', clipTaskData);

      const result = await ClipTaskService.CreateClipTask(clipTaskData);
      console.log('创建混剪任务 - CreateClipTask调用结果:', result);

      // 停止进度条
      setShowProgress(false);
      setProcessing(false);

      // 改进错误处理逻辑
      if (result && result.success !== false) {
                  // 如果result存在且success不是false，认为是成功的
          console.log('创建混剪任务 - 任务创建成功');
          toast.success('混剪任务创建成功！');

        // 重置表单
        setClipSettings({
          clipTaskName: '',
          aspectRatio: '16:9',
          scaleMode: 'ScaleToFill',
          musicMixMode: 1,
          genTotal: 10,
          musicUrl: [],
          headCover: [],
          tailCover: [],
          groupList: []
        });

        // 重新加载任务列表
        await loadClipTaskList();
      } else {
        // 只有明确返回失败时才显示错误
                  console.log('创建混剪任务 - 任务创建失败, result:', result);
          const errorMessage = result?.message || result?.error || '未知错误';
          toast.error('混剪任务创建失败：' + errorMessage);
        }
      } catch (error) {
        console.error('创建混剪任务 - 发生错误:', error);
        setShowProgress(false);
        setProcessing(false);
        toast.error('创建混剪任务失败：' + error.message);
    }
  };

  // 生成视频变体
  const handleGenerateVariations = async () => {
    if (!selectedVideo) {
      console.warn('生成变体 - 未选择视频');
      return;
    }

    console.log('生成变体 - 开始处理, 选中视频:', selectedVideo);
    console.log('生成变体 - 变体设置:', variationSettings);
    setProcessing(true);
    
    // 启动真实进度条 - 基于实际生成进度
    startRealProgress(`正在生成 ${variationSettings.count} 个视频变体...`, variationSettings.count);
    
    // 监听视频变体生成进度事件
    const progressListener = (eventData) => {
      console.log('生成变体 - 收到进度事件:', eventData);
      updateRealProgress(eventData);
    };
    
    // 添加事件监听器
    Events.On('video_variation_progress', progressListener);
    
    try {
      console.log('生成变体 - 准备导入服务模块');
      const { FfmpegService, handleResult } = await import('../services/api.js');
      console.log('生成变体 - 服务模块导入成功');
      
      // 直接使用已保存的文件路径
      const videoFilePath = selectedVideo.filePath;
      console.log('生成变体 - 使用的视频文件路径:', videoFilePath);
      
      // 准备调用参数
      const requestParams = {
        videoFile: videoFilePath,
        count: variationSettings.count,
        aspectRatio: variationSettings.aspectRatio,
        resolution: variationSettings.resolution,
        isOriginSound: variationSettings.isOriginSound,
        effects: variationSettings.effects.filter(e => e.enabled).map(e => e.id)
      };
      console.log('生成变体 - 准备调用GenerateVideoVariations, 参数:', requestParams);
      
      // 使用后端返回的文件路径进行处理
      const result = await FfmpegService.GenerateVideoVariations(requestParams);
      console.log('生成变体 - GenerateVideoVariations调用结果:', result);
      
      const data = handleResult(result);
      console.log('生成变体 - 处理后的结果:', data);
      console.log('生成变体 - data.data:', data.data);
      console.log('生成变体 - data.data类型:', typeof data.data);
      console.log('生成变体 - data.data的属性:', data.data ? Object.keys(data.data) : 'data.data为空');
      
      // 根据返回的结果创建变体视频
      const variations = [];
      if (data.data && data.data.video_paths) {
        console.log('生成变体 - 获取到的视频路径:', data.data.video_paths);
        
        // 使用BatchGetVideoInfo获取每个生成视频的详细信息
        console.log('生成变体 - 准备获取视频详细信息');
        const videoInfoResult = await FfmpegService.BatchGetVideoInfo(data.data.video_paths);
        console.log('生成变体 - BatchGetVideoInfo调用结果:', videoInfoResult);
        
        const videoInfoData = handleResult(videoInfoResult);
        console.log('生成变体 - 处理后的视频信息结果:', videoInfoData);
        
        data.data.video_paths.forEach((path, index) => {
          // 从路径中提取文件名
          const fileName = path.split(/[/\\]/).pop();
          console.log(`生成变体 - 处理视频 #${index + 1}, 路径: ${path}, 文件名: ${fileName}`);
          
          // 验证文件名的有效性
          let finalFileName;
          if (fileName && fileName.trim() && fileName.includes('.')) {
            finalFileName = fileName;
            console.log(`生成变体 - 使用提取的文件名: ${finalFileName}`);
          } else {
            // 如果无法提取有效文件名，生成一个基于时间戳的唯一名称
            const timestamp = Date.now() + index;
            finalFileName = `variation_video_${timestamp}.mp4`;
            console.log(`生成变体 - 文件名无效，使用生成的名称: ${finalFileName}`);
            console.warn(`生成变体 - 原始路径: ${path}, 提取的文件名: ${fileName}`);
          }
          
          // 获取对应的视频信息
          let videoInfo = {
            duration: 0,
            width: 0,
            height: 0,
            size: 0,
            format: finalFileName.split('.').pop().toUpperCase()
          };
          
          if (videoInfoData.data && videoInfoData.data[index]) {
            const info = videoInfoData.data[index];
            console.log(`生成变体 - 获取到视频 #${index + 1} 的信息:`, info);
            videoInfo = {
              duration: info.duration || 0,
              width: info.width || 0,
              height: info.height || 0,
              size: info.size || 0,
              format: info.format || videoInfo.format
            };
          }
          
          // 格式化时长
          const duration = Math.floor(videoInfo.duration);
          const minutes = Math.floor(duration / 60);
          const seconds = duration % 60;
          const durationStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          
          // 创建变体视频对象
          const variation = {
            id: Date.now() + index,
            name: finalFileName,
            file: null,
            filePath: path,
            size: videoInfo.size ? `${videoInfo.size.toFixed(2)} MB` : selectedVideo.size,
            duration: durationStr,
            resolution: videoInfo.width && videoInfo.height ? `${videoInfo.width}x${videoInfo.height}` : `${variationSettings.resolution.width}x${variationSettings.resolution.height}`,
            format: videoInfo.format,
            status: '已完成',
            progress: 100,
            createTime: new Date().toLocaleString(),
            processTime: new Date().toLocaleString(),
            url: path
          };
          console.log(`生成变体 - 创建的变体视频 #${index+1}:`, variation);
          variations.push(variation);
        });
        
        console.log('生成变体 - 所有创建的变体视频:', variations);
        setVideos([...videos, ...variations]);
        
        // 自动切换到"处理后"选项卡显示新生成的视频
        if (variations.length > 0) {
          setVideoListTab('processed');
        }
      } else {
        console.warn('生成变体 - 未获取到有效的视频路径');
                  console.warn('生成变体 - data结构:', data);
          console.warn('生成变体 - 检查条件: data.data存在?', !!data.data, ', data.data.video_paths存在?', !!(data.data && data.data.video_paths));
          toast.error('生成视频变体失败：未获取到有效的视频路径');
      }
      
      // 完成进度条
      completeProgress();
      setProcessing(false);
      console.log('生成变体 - 处理完成');
    } catch (error) {
      console.error('生成变体 - 处理失败, 详细错误:', error);
      // 停止进度条
              stopProgress();
        setProcessing(false);
        toast.error(`生成视频变体失败：${error.message || '未知错误'}`);
      } finally {
      // 清理事件监听器
      Events.Off('video_variation_progress', progressListener);
      console.log('生成变体 - 已清理进度事件监听器');
    }
  };

  // 保存文字预设
  const handleSavePreset = async () => {
    try {
      const { TextInfoService, handleResult } = await import('../services/api.js');
      
      // 调用后端API保存预设（字段名已一致，直接传递）
      const result = await TextInfoService.CreateTextInfo({
        ...presetForm.settings,
        remark: presetForm.description || null
      });
      
      handleResult(result);
      
      // 重新加载预设列表
      await loadTextPresets();
      setPresetForm({
        name: '',
        description: '',
        settings: { ...textSettings }
      });
    } catch (error) {
      console.error('保存预设失败:', error);
      // 如果API调用失败，使用模拟逻辑作为后备
      const newPreset = {
        id: Date.now(),
        name: presetForm.name,
        description: presetForm.description,
        ...presetForm.settings
      };
      
      setTextPresets([...textPresets, newPreset]);
      setPresetForm({
        name: '',
        description: '',
        settings: { ...textSettings }
      });
    }
  };

  // 应用预设
  const handleApplyPreset = (preset) => {
    setTextSettings({
      ...preset,  // 直接应用所有预设字段，因为字段名已一致
      id: 0  // 重置ID为0，表示新建
    });
  };

  // 删除视频
  const handleDelete = (video) => {
    setVideos(videos.filter(v => v.id !== video.id));
    if (selectedVideo?.id === video.id) {
      setSelectedVideo(null);
      setPreviewUrl('');
    }
  };

  // 右键菜单处理函数
  const handleContextMenu = (e, video) => {
    e.preventDefault();
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      video: video
    });
  };

  const handleCloseContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      video: null
    });
  };

  // 打开文件所在位置
  const handleOpenFileLocation = async (video) => {
    try {
      console.log('发送open_folder事件，路径:', video.filePath);
      await Events.Emit(new Events.WailsEvent('open_folder', video.filePath));
    } catch (error) {
      console.error('发送open_folder事件失败:', error);
    }
  };

  // 删除原文件
  const handleDeleteOriginalFile = async (video) => {
    try {
      const { SystemService, handleResult } = await import('../services/api.js');
      const result = await SystemService.DeleteFile({
        file_path_list: [video.filePath]
      });
      handleResult(result);
      
      // 删除成功后从列表中移除
      handleDelete(video);
    } catch (error) {
      console.error('删除原文件失败:', error);
    }
  };

  // 从列表中删除
  const handleDeleteFromList = (video) => {
    handleDelete(video);
  };

  // 下载视频
  const handleDownload = (video) => {
    if (video.status !== '已完成') return;
    
    // 创建下载链接
    const a = document.createElement('a');
    a.href = video.url;
    a.download = video.name;
    a.click();
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case '已完成': return 'text-green-600 bg-green-50 border-green-200';
      case '处理中': return 'text-blue-600 bg-blue-50 border-blue-200';
      case '已暂停': return 'text-orange-600 bg-orange-50 border-orange-200';
      case '失败': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-slate-600 bg-slate-50 border-slate-200';
    }
  };

  // 根据状态获取文件名颜色
  const getNameColorByStatus = (status) => {
    switch (status) {
      case '已完成': return 'text-green-700'; // 绿色表示已完成
      case '处理中': return 'text-blue-700'; // 蓝色表示处理中
      case '已暂停': return 'text-orange-700'; // 橙色表示已暂停
      case '失败': return 'text-red-700'; // 红色表示失败
      case '已上传': return 'text-slate-700'; // 深灰色表示已上传
      default: return 'text-slate-900'; // 默认黑色
    }
  };

  // 获取字体的CSS名称
  const getFontFamily = (fontFile) => {
    const font = fonts.find(f => f.value === fontFile);
    if (font) {
      if (font.label === 'Arial') return 'Arial, sans-serif';
      if (font.label.includes('黑体')) return '"Microsoft YaHei", "SimHei", sans-serif';
      if (font.label.includes('宋体')) return '"SimSun", serif';
      if (font.label.includes('楷体')) return '"KaiTi", serif';
      if (font.label.includes('仿宋')) return '"FangSong", serif';
      if (font.label.includes('微软雅黑')) return '"Microsoft YaHei", sans-serif';
    }
    return 'sans-serif';
  };

  // 获取文字预览样式
  const getTextPreviewStyle = () => {
    if (!textSettings.text.trim()) return null;
    
    // 计算实际显示的字体大小
    const displayFontSize = Math.max(textSettings.font_size * fontSizeScale, 8); // 最小8px
    
    return {
      position: 'absolute',
      left: `${textSettings.position_x}%`,
      top: `${textSettings.position_y}%`,
      transform: 'translate(-50%, -50%)',
      color: textSettings.font_color,
      fontSize: `${displayFontSize}px`, // 使用计算后的字体大小
      fontFamily: getFontFamily(textSettings.font_file),
      backgroundColor: textSettings.box === 2 ? textSettings.box_color : 'transparent',
      padding: textSettings.box === 2 ? '4px 8px' : '2px 4px',
      borderRadius: '2px',
      textShadow: textSettings.shadow_wx > 0 ? `${textSettings.shadow_wx * fontSizeScale}px ${textSettings.shadow_wy * fontSizeScale}px 2px ${textSettings.shadow_color}` : 'none',
      border: textSettings.border_rw > 0 ? `${textSettings.border_rw * fontSizeScale}px solid ${textSettings.border_color}` : 'none',
      whiteSpace: 'pre-wrap',
      textAlign: 'center',
      maxWidth: '80%',
      wordBreak: 'break-word',
      pointerEvents: 'auto', // 改为可交互
      zIndex: 10,
      cursor: 'move', // 显示拖拽光标
      userSelect: 'none', // 禁止文字选择
      transition: 'opacity 0.2s ease',
      opacity: 0.9
    };
  };

  // 获取无视频时的文字预览样式
  const getTextPreviewStyleNoVideo = () => {
    if (!textSettings.text.trim()) return null;
    
    // 无视频时使用固定的预览容器尺寸 (320x240) 来模拟缩放效果
    const previewContainerWidth = 320;
    const previewContainerHeight = 240;
    
    // 假设一个标准的视频分辨率作为参考 (1920x1080)
    const referenceWidth = 1920;
    const referenceHeight = 1080;
    
    // 计算缩放比例
    const widthScale = previewContainerWidth / referenceWidth;
    const heightScale = previewContainerHeight / referenceHeight;
    const fontSizeScale = Math.min(widthScale, heightScale);
    
    // 计算实际显示的字体大小
    const displayFontSize = Math.max(textSettings.font_size * fontSizeScale, 8); // 最小8px
    
    return {
      position: 'absolute',
      left: `${textSettings.position_x}%`,
      top: `${textSettings.position_y}%`,
      transform: 'translate(-50%, -50%)',
      color: textSettings.font_color,
      fontSize: `${displayFontSize}px`,
      fontFamily: getFontFamily(textSettings.font_file),
      backgroundColor: textSettings.box === 2 ? textSettings.box_color : 'transparent',
      padding: textSettings.box === 2 ? '4px 8px' : '2px 4px',
      borderRadius: '2px',
      textShadow: textSettings.shadow_wx > 0 ? `${textSettings.shadow_wx * fontSizeScale}px ${textSettings.shadow_wy * fontSizeScale}px 2px ${textSettings.shadow_color}` : 'none',
      border: textSettings.border_rw > 0 ? `${textSettings.border_rw * fontSizeScale}px solid ${textSettings.border_color}` : 'none',
      whiteSpace: 'pre-wrap',
      textAlign: 'center',
      maxWidth: '80%',
      wordBreak: 'break-word',
      pointerEvents: 'auto',
      zIndex: 10,
      cursor: 'move',
      userSelect: 'none',
      transition: 'opacity 0.2s ease',
      opacity: 0.9
    };
  };

  // 拖拽事件处理
  const handleMouseDown = (e) => {
    e.preventDefault();
    setIsDragging(true);
    
    // 获取预览容器的尺寸和位置
    const container = e.currentTarget.parentElement;
    const containerRect = container.getBoundingClientRect();
    
    // 计算鼠标相对于容器的位置
    const mouseX = e.clientX - containerRect.left;
    const mouseY = e.clientY - containerRect.top;
    
    // 计算当前文字元素的位置
    const textX = (textSettings.position_x / 100) * containerRect.width;
    const textY = (textSettings.position_y / 100) * containerRect.height;
    
    // 记录拖拽开始时的偏移量
    setDragStart({ x: mouseX, y: mouseY });
    setDragOffset({ x: mouseX - textX, y: mouseY - textY });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    // 获取预览容器的尺寸和位置
    const container = document.querySelector('.video-preview-container');
    if (!container) return;
    
    const containerRect = container.getBoundingClientRect();
    
    // 计算鼠标相对于容器的位置
    const mouseX = e.clientX - containerRect.left;
    const mouseY = e.clientY - containerRect.top;
    
    // 计算新的文字位置（减去偏移量）
    const newX = mouseX - dragOffset.x;
    const newY = mouseY - dragOffset.y;
    
    // 转换为百分比并限制在容器范围内
    const newPositionX = Math.max(0, Math.min(100, (newX / containerRect.width) * 100));
    const newPositionY = Math.max(0, Math.min(100, (newY / containerRect.height) * 100));
    
    // 更新文字位置（保留两位小数）
    setTextSettings({
      ...textSettings,
      position_x: parseFloat(newPositionX.toFixed(2)),
      position_y: parseFloat(newPositionY.toFixed(2))
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragStart({ x: 0, y: 0 });
    setDragOffset({ x: 0, y: 0 });
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset, textSettings]);

  // 计算字体缩放比例
  useEffect(() => {
    const calculateFontScale = () => {
      if (videoRef.current && selectedVideo && videoLoaded) {
        // 延迟一点时间确保视频元素已完全渲染
        setTimeout(() => {
          const videoElement = videoRef.current;
          const videoDisplayWidth = videoElement.offsetWidth;
          const videoDisplayHeight = videoElement.offsetHeight;
          
          // 从selectedVideo获取原始视频尺寸
          const originalResolution = selectedVideo.resolution;
          if (originalResolution && originalResolution !== '未知') {
            const [originalWidth, originalHeight] = originalResolution.split('x').map(Number);
            
            if (originalWidth && originalHeight && videoDisplayWidth && videoDisplayHeight) {
              // 计算宽度和高度的缩放比例，取较小值（因为video元素会保持宽高比）
              const widthScale = videoDisplayWidth / originalWidth;
              const heightScale = videoDisplayHeight / originalHeight;
              const newScale = Math.min(widthScale, heightScale);
              
              // 限制缩放比例在合理范围内
              const limitedScale = Math.max(0.1, Math.min(2.0, newScale));
              
              console.log(`字体缩放比例计算: 原始尺寸=${originalWidth}x${originalHeight}, 显示尺寸=${videoDisplayWidth}x${videoDisplayHeight}, 缩放比例=${limitedScale.toFixed(3)}`);
              setFontSizeScale(limitedScale);
            }
          }
        }, 200); // 增加延迟时间
      } else {
        // 无视频时重置为默认缩放比例
        setFontSizeScale(0.5);
      }
    };

    calculateFontScale();
    
    // 监听窗口大小变化
    const handleResize = () => {
      calculateFontScale();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [videoLoaded, selectedVideo, previewUrl]);

  // 进度条控制函数
  const startProgress = (text = '处理中...', duration = 30000) => {
    setShowProgress(true);
    setProgressValue(0);
    setProgressText(text);
    setProcessingComplete(false);
    
    // 清除之前的定时器
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
    
    // 模拟进度增长（虚假进度条）
    let progress = 0;
    const increment = 100 / (duration / 100); // 每100ms增加的进度
    
    progressIntervalRef.current = setInterval(() => {
      progress += increment;
      
      // 进度条在90%处减缓
      if (progress >= 90 && !processingComplete) {
        progress = 90 + (progress - 90) * 0.1; // 在90%处减缓
      }
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(progressIntervalRef.current);
        setTimeout(() => {
          setShowProgress(false);
          setProgressValue(0);
        }, 500); // 显示100%一会儿再隐藏
      }
      
      setProgressValue(Math.min(progress, 100));
    }, 100);
  };
  
  const completeProgress = () => {
    setProcessingComplete(true);
    // 如果还没到100%，快速完成
    if (progressValue < 100) {
      setProgressValue(100);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      setTimeout(() => {
        setShowProgress(false);
        setProgressValue(0);
        // 重置真实进度状态
        if (isRealProgress) {
          setIsRealProgress(false);
          setTotalVideos(0);
          setCompletedVideos(0);
          setCurrentVideoProgress(0);
          setCurrentVideoIndex(1);
        }
      }, 500);
    }
  };
  
  const stopProgress = () => {
    if (progressIntervalRef.current) {
      // 清理定时器（可能是 interval 或 timeout）
      clearInterval(progressIntervalRef.current);
      clearTimeout(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    setShowProgress(false);
    setProgressValue(0);
    setProcessingComplete(false);
    // 重置真实进度状态
    setIsRealProgress(false);
    setTotalVideos(0);
    setCompletedVideos(0);
    setCurrentVideoProgress(0);
    setCurrentVideoIndex(1);
  };

  // 启动真实进度条
  const startRealProgress = (text, total) => {
    console.log(`启动真实进度条: ${text}, 总数: ${total}`);
    setShowProgress(true);
    setProgressValue(0);
    setProgressText(`正在生成第 1/${total} 个视频变体...`);
    setProcessingComplete(false);
    setIsRealProgress(true);
    setTotalVideos(total);
    setCompletedVideos(0);
    setCurrentVideoProgress(0);
    setCurrentVideoIndex(1);
    
    // 清除任何现有的进度定时器
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      clearTimeout(progressIntervalRef.current);
    }
    
    // 立即开始第一个视频的虚假进度增长
    console.log('立即开始第一个视频的进度增长');
    let progress = 0;
    const increment = 100 / 40; // 每100ms增加2.5%，4秒完成
    
    progressIntervalRef.current = setInterval(() => {
      progress += increment;
      
      if (progress >= 100) {
        progress = 100;
        // 不清除定时器，等待事件触发后处理
      }
      
      setProgressValue(progress);
      setCurrentVideoProgress(progress);
    }, 100);
    
    // 设置安全超时 - 如果30分钟内没有完成，自动停止进度条
    setTimeout(() => {
      console.warn('真实进度条安全超时，可能存在问题');
      if (isRealProgress && showProgress) {
        setProgressText('处理时间过长，请检查后端状态...');
      }
    }, 30 * 60 * 1000); // 30分钟
  };

  // 更新真实进度（视频裂变专用 - 根据视频序号决定是否重置进度条）
  const updateRealProgress = (eventData) => {
    if (!isRealProgress) return;
    
    console.log('更新真实进度 - 原始事件数据:', eventData);
    
    // 事件结构: { name: "video_variation_progress", data: [...], sender: "" }
    // 数据在 data 数组中，取第一个元素
    if (eventData && eventData.data && Array.isArray(eventData.data) && eventData.data.length > 0) {
      const data = eventData.data[0]; // 取数组第一个元素
      console.log('提取的进度数据:', data);
      
      // 后端数据格式: { video_file, progress, count, gen_count, message, video_path }
      const { 
        count = totalVideos, 
        gen_count = 1, 
        message = '',
        video_file = '',
        video_path = ''
      } = data;
      
      console.log(`解析字段: count=${count}, gen_count=${gen_count}, message="${message}"`);
      
      // 清除之前的进度定时器
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      
      // 更新当前视频序号和已完成数量
      setCurrentVideoIndex(gen_count);
      setCompletedVideos(gen_count - 1); // 已完成的数量为当前生成数量减1
      
      // 只有当不是第一个视频时才重置进度条
      if (gen_count > 1) {
        console.log(`第 ${gen_count} 个视频开始，重置进度条`);
        setProgressValue(0);
        setCurrentVideoProgress(0);
      } else {
        console.log('第一个视频，继续当前进度');
      }
      
      // 更新进度文本
      const progressText = message || `正在生成第 ${gen_count}/${count} 个视频变体...`;
      setProgressText(progressText);
      
      console.log(`开始生成第 ${gen_count}/${count} 个视频`);
      
      // 如果是最后一个视频，生成完成后直接隐藏进度条
      if (gen_count >= count) {
        console.log('这是最后一个视频，生成完成后将隐藏进度条');
        
        // 开始虚假增长，3秒内到100%
        let progress = gen_count > 1 ? 0 : progressValue; // 如果是第一个视频，从当前进度开始
        const targetProgress = 100;
        const duration = 3000; // 3秒
        const increment = (targetProgress - progress) / (duration / 100);
        
        progressIntervalRef.current = setInterval(() => {
          progress += increment;
          
          if (progress >= targetProgress) {
            progress = targetProgress;
            clearInterval(progressIntervalRef.current);
            setProgressValue(100);
            setCurrentVideoProgress(100);
            setCompletedVideos(count);
            
            console.log('所有视频生成完成，即将隐藏进度条');
            
            // 显示完成状态1秒后隐藏进度条
            setTimeout(() => {
              setShowProgress(false);
              setProgressValue(0);
              setIsRealProgress(false);
              setTotalVideos(0);
              setCompletedVideos(0);
              setCurrentVideoProgress(0);
              setCurrentVideoIndex(1);
            }, 1000);
          } else {
            setProgressValue(progress);
            setCurrentVideoProgress(progress);
          }
        }, 100);
      } else {
        // 不是最后一个视频，正常虚假增长到100%
        let progress = gen_count > 1 ? 0 : progressValue; // 如果是第一个视频，从当前进度开始
        const targetProgress = 100;
        const duration = 4000; // 4秒
        const increment = (targetProgress - progress) / (duration / 100);
        
        progressIntervalRef.current = setInterval(() => {
          progress += increment;
          
          if (progress >= targetProgress) {
            progress = targetProgress;
            clearInterval(progressIntervalRef.current);
            setProgressValue(100);
            setCurrentVideoProgress(100);
            setCompletedVideos(gen_count); // 当前视频完成
            
            console.log(`第 ${gen_count}/${count} 个视频生成完成`);
          } else {
            setProgressValue(progress);
            setCurrentVideoProgress(progress);
          }
        }, 100);
      }
    } else {
      console.warn('收到无效的进度事件数据格式:', eventData);
      console.warn('期望格式: { name: "video_variation_progress", data: [...], sender: "" }');
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // 根据选项卡过滤视频列表
  const getFilteredVideos = () => {
    if (videoListTab === 'uploaded') {
      // 显示原始上传的视频
      return videos.filter(video => video.status === '已上传');
    } else {
      // 显示处理后的视频（已完成、处理中、失败等）
      return videos.filter(video => video.status !== '已上传');
    }
  };

  // 格式化视频信息的辅助函数
  const formatVideoInfo = (videoInfo, fileName) => {
    // 格式化时长
    const duration = Math.floor(videoInfo.duration || 0);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    const durationStr = duration > 0 
      ? `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      : '未知';
    
    // 格式化文件大小
    const sizeStr = (videoInfo.size && videoInfo.size > 0) 
      ? `${videoInfo.size.toFixed(2)} MB` 
      : '未知';
    
    // 格式化分辨率
    const resolutionStr = (videoInfo.width && videoInfo.height && videoInfo.width > 0 && videoInfo.height > 0) 
      ? `${videoInfo.width}x${videoInfo.height}` 
      : '未知';
    
    // 格式化格式
    const formatStr = (videoInfo.format && videoInfo.format !== '') 
      ? videoInfo.format 
      : (fileName ? fileName.split('.').pop().toUpperCase() : '未知');
    
    return {
      duration: durationStr,
      size: sizeStr,
      resolution: resolutionStr,
      format: formatStr
    };
  };

  // 获取过滤后的视频列表
  const filteredVideos = getFilteredVideos();

  // 当切换选项卡时，检查当前选中的视频是否还在过滤列表中
  useEffect(() => {
    if (selectedVideo && !filteredVideos.find(v => v.id === selectedVideo.id)) {
      setSelectedVideo(null);
      setPreviewUrl('');
    }
  }, [videoListTab, filteredVideos, selectedVideo]);

  // 菜单栏配置
  const menuConfig = [
    {
      key: 'file',
      label: '文件',
      items: [
        {
          key: 'upload',
          label: '上传视频',
          icon: <Upload size={16} />,
          shortcut: 'Ctrl+O',
          onClick: handleUpload
        },
        {
          key: 'open-folder',
          label: '打开输出文件夹',
          icon: <Folder size={16} />,
          onClick: () => {
            // 打开输出文件夹
            console.log('打开输出文件夹');
          }
        },
        { type: 'divider' },
        {
          key: 'clear-list',
          label: '清空视频列表',
          icon: <Trash2 size={16} />,
          onClick: () => {
            setVideos([]);
            setSelectedVideo(null);
            setPreviewUrl('');
          }
        }
      ]
    },
    {
      key: 'edit',
      label: '编辑',
      items: [
        {
          key: 'add-text',
          label: '添加文字',
          icon: <Type size={16} />,
          shortcut: 'Ctrl+T',
          disabled: !selectedVideo || processing,
          onClick: () => {
            setActiveTab('text');
          }
        },
        {
          key: 'generate-variations',
          label: '生成视频变体',
          icon: <Wand2 size={16} />,
          shortcut: 'Ctrl+G',
          disabled: !selectedVideo || processing,
          onClick: () => {
            setActiveTab('variation');
          }
        },
        {
          key: 'video-clip',
          label: '视频混剪',
          icon: <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 2v20M2 12h20M7 7l10 10M7 17l10-10"/>
          </svg>,
          shortcut: 'Ctrl+M',
          disabled: processing,
          onClick: () => {
            setActiveTab('clip');
          }
        },
        { type: 'divider' },
        {
          key: 'reset-text',
          label: '重置文字设置',
          icon: <RotateCcw size={16} />,
          onClick: () => {
            setTextSettings({
              id: 0,
              text: "",
              font_file: "arial.ttf",
              font_size: 24,
              font_color: "#FFFFFF",
              position_x: 50.0,
              position_y: 50.0,
              box: 2,
              box_color: "#000000",
              box_border_rw: 5,
              shadow_wx: 2,
              shadow_wy: 2,
              shadow_color: "#000000",
              border_rw: 0,
              border_color: "#000000",
              remark: null
            });
          }
        }
      ]
    },
    {
      key: 'view',
      label: '视图',
      items: [
        {
          key: 'preview',
          label: '预览当前视频',
          icon: <Eye size={16} />,
          shortcut: 'Space',
          disabled: !selectedVideo,
          onClick: () => {
            if (selectedVideo) {
              handlePreview(selectedVideo);
            }
          }
        },
        { type: 'divider' },
        {
          key: 'text-presets',
          label: '文字预设管理',
          icon: <Settings size={16} />,
          onClick: async () => {
            try {
              const { WindowService, handleResult } = await import('../services/api.js');
              const result = await WindowService.ShowWindow('/text');
              handleResult(result);
            } catch (error) {
              console.error('打开文字预设窗口失败:', error);
            }
          }
        }
      ]
    },
    {
      key: 'tools',
      label: '工具',
      items: [
        {
          key: 'batch-process',
          label: '批量处理',
          icon: <Users size={16} />,
          disabled: videos.length === 0 || processing,
          onClick: () => {
            console.log('批量处理功能');
          }
        },
        {
          key: 'export-settings',
          label: '导出设置',
          icon: <Download size={16} />,
          onClick: () => {
            const settings = {
              textSettings,
              variationSettings
            };
            const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'video-processor-settings.json';
            a.click();
            URL.revokeObjectURL(url);
          }
        },
        {
          key: 'import-settings',
          label: '导入设置',
          icon: <Upload size={16} />,
          onClick: () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
              const file = e.target.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                  try {
                    const settings = JSON.parse(e.target.result);
                    if (settings.textSettings) {
                      setTextSettings(settings.textSettings);
                    }
                    if (settings.variationSettings) {
                                              setVariationSettings(settings.variationSettings);
                      }
                      toast.success('设置导入成功！');
                    } catch (error) {
                      toast.error('设置文件格式错误！');
                  }
                };
                reader.readAsText(file);
              }
            };
            input.click();
          }
        }
      ]
    },
    {
      key: 'help',
      label: '帮助',
      items: [
        {
          key: 'shortcuts',
          label: '快捷键',
          icon: <Keyboard size={16} />,
          onClick: () => {
            toast.info(`快捷键说明：
Ctrl+O - 上传视频
Ctrl+T - 切换到文字编辑
Ctrl+G - 切换到视频变体
Ctrl+M - 切换到视频混剪
Space - 预览当前视频
Ctrl++ - 放大预览
Ctrl+- - 缩小预览
Ctrl+0 - 适应窗口`);
          }
        },
        {
          key: 'documentation',
          label: '使用文档',
          icon: <FileText size={16} />,
          onClick: () => {
            console.log('打开使用文档');
          }
        },
        { type: 'divider' },
        {
          key: 'about',
          label: '关于',
          icon: <Info size={16} />,
          onClick: () => {
            toast.info('视频处理工具 v1.0.0\n\n一款专业的视频编辑和处理工具，支持文字添加、视频变体生成等功能。');
          }
        }
      ]
    }
  ];

  return (
    <div className="h-screen flex flex-col bg-slate-50">
      {/* 顶部菜单栏 */}
      <MenuBar menus={menuConfig} />
      
      {/* 上半部分：视频列表和预览区域 - 自适应高度 */}
      <div className="flex-1 flex bg-white border-b border-slate-200 min-h-0">
        {/* 左侧视频列表 */}
        <div className="w-1/3 border-r border-slate-200 flex flex-col">
          <div className="p-4 border-b border-slate-200 bg-slate-50 flex-shrink-0">
           
            
            <div className="flex items-center justify-between text-sm text-slate-600">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <FileVideo size={14} />
                  <span>已上传: {videos.filter(v => v.status === '已上传').length}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle size={14} />
                  <span>已完成: {videos.filter(v => v.status === '已完成').length}</span>
                </div>
              </div>
              
              {/* 颜色说明 */}
              <div className="flex items-center space-x-2 text-xs">
                <span className="text-slate-500">状态:</span>
                <div className="flex items-center space-x-1">
                  <span className="text-green-700 font-medium">已完成</span>
                  <span className="text-slate-400">•</span>
                  <span className="text-slate-700 font-medium">已上传</span>
                </div>
              </div>
                        </div>
          </div>

          {/* 视频列表选项卡 */}
          <div className="border-b border-slate-200 bg-white flex-shrink-0 h-10">
            <div className="flex h-full">
              <button
                onClick={() => setVideoListTab('uploaded')}
                className={`relative flex-1 px-4 h-full text-sm font-medium transition-all duration-200 flex items-center justify-center ${
                  videoListTab === 'uploaded'
                    ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-500'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                }`}
              >
                <div className="flex items-center space-x-1.5">
                  <Upload size={14} />
                  <span>原始视频 ({videos.filter(v => v.status === '已上传').length})</span>
                </div>
              </button>
              <button
                onClick={() => setVideoListTab('processed')}
                className={`relative flex-1 px-4 h-full text-sm font-medium transition-all duration-200 flex items-center justify-center ${
                  videoListTab === 'processed'
                    ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-500'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                }`}
              >
                <div className="flex items-center space-x-1.5">
                  <CheckCircle size={14} />
                  <span>处理完成 ({videos.filter(v => v.status !== '已上传').length})</span>
                </div>
              </button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto min-h-0">
            {filteredVideos.map((video) => (
              <div
                key={video.id}
                className={`px-4 py-1.5 border-b border-slate-100 cursor-pointer hover:bg-slate-50 transition-colors ${
                  selectedVideo?.id === video.id ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => handleSelectVideo(video)}
                onContextMenu={(e) => handleContextMenu(e, video)}
              >
                <div className="flex items-start space-x-3">
                  <div className="w-16 h-12 bg-slate-200 rounded overflow-hidden relative group">
                    <video
                      src={`/api/preview?path=${encodeURIComponent(video.url)}`}
                      className="w-full h-full object-cover"
                      muted
                      preload="metadata"
                      onError={(e) => {
                        // 视频加载失败时显示默认图标
                        e.target.style.display = 'none';
                        e.target.nextElementSibling.style.display = 'flex';
                      }}
                      onLoadedData={(e) => {
                        // 视频加载成功时隐藏默认图标
                        e.target.nextElementSibling.style.display = 'none';
                      }}
                    />
                    {/* 默认图标 - 视频加载失败时显示 */}
                    <div className="absolute inset-0 flex items-center justify-center bg-slate-200">
                      <FileVideo size={20} className="text-slate-500" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className={`text-sm font-medium truncate transition-colors hover:opacity-80 ${getNameColorByStatus(video.status)}`} title={`${video.name} (${video.status})`}>
                      {video.name}
                    </div>
                    <div className="mt-1 text-xs text-slate-500 space-y-1">
                      <div>{video.duration} • {video.resolution} • {video.size}</div>
                      {video.progress > 0 && video.progress < 100 && (
                        <div className="flex items-center space-x-2">
                          <span className="text-blue-600">{video.progress}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {filteredVideos.length === 0 && (
              <div className="p-8 text-center">
                <FileVideo size={48} className="mx-auto text-slate-400 mb-4" />
                {videoListTab === 'uploaded' ? (
                  <>
                    <h4 className="text-lg font-medium text-slate-900 mb-2">暂无原始视频</h4>
                    <p className="text-slate-500 mb-4">上传您的第一个视频开始处理</p>
                  </>
                ) : (
                  <>
                    <h4 className="text-lg font-medium text-slate-900 mb-2">暂无处理后的视频</h4>
                    <p className="text-slate-500 mb-4">对原始视频进行处理后，结果将显示在这里</p>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 右侧视频预览区域 */}
        <div className="flex-1 flex items-center justify-center min-h-0 relative" style={{ backgroundColor: 'rgb(38,38,38)' }}>
          {previewUrl ? (
            <div className="relative w-full h-full flex items-center justify-center video-preview-container">
              <video
                ref={videoRef}
                src={`/api/preview?path=${encodeURIComponent(previewUrl)}`}
                controls
                className="max-w-full max-h-full"
                style={{ objectFit: 'contain' }}
                onLoadedMetadata={() => setVideoLoaded(true)}
                onLoad={() => setVideoLoaded(true)}
              />
              
              {/* 文字预览覆盖层 */}
              {activeTab === 'text' && textSettings.text.trim() && getTextPreviewStyle() && (
                <div 
                  style={{
                    ...getTextPreviewStyle(),
                    opacity: isDragging ? 0.7 : 0.9
                  }}
                  className="select-none"
                  title="拖拽移动文字位置 - 实际效果可能略有差异"
                  onMouseDown={handleMouseDown}
                  onMouseEnter={(e) => e.currentTarget.style.opacity = '1'}
                  onMouseLeave={(e) => e.currentTarget.style.opacity = isDragging ? '0.7' : '0.9'}
                >
                  {textSettings.text}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-white relative video-preview-container w-full h-full flex items-center justify-center">
              <div className="absolute inset-0 flex items-center justify-center z-0">
                <div>
                  <FileVideo size={64} className="mx-auto mb-4 opacity-50" />
                  <p className="text-lg">选择视频进行预览</p>
                </div>
              </div>
              
              {/* 无视频时的文字预览 */}
              {activeTab === 'text' && textSettings.text.trim() && (
                <div className="absolute inset-0 flex items-center justify-center z-10">
                  <div className="relative w-80 h-60 bg-gray-800 rounded-lg border-2 border-dashed border-gray-600 flex items-center justify-center">
                    <div 
                      style={{
                        ...getTextPreviewStyleNoVideo(),
                        position: 'absolute',
                        opacity: isDragging ? 0.7 : 0.9
                      }}
                      className="select-none"
                      title="拖拽移动文字位置 - 选择视频后查看实际效果"
                      onMouseDown={handleMouseDown}
                      onMouseEnter={(e) => e.currentTarget.style.opacity = '1'}
                      onMouseLeave={(e) => e.currentTarget.style.opacity = isDragging ? '0.7' : '0.9'}
                    >
                      {textSettings.text}
                    </div>
                    <div className="text-gray-500 text-sm text-center p-4">
                      <p>文字预览</p>
                      <p className="text-xs mt-1">拖拽文字调整位置</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 下半部分：固定高度的处理工具区域 */}
      <div className="h-85 bg-white border-t border-slate-200 flex-shrink-0">
        <div className="h-full flex flex-col">
          {/* 工具栏标签和信息 */}
          <div className="border-b border-slate-200 bg-white flex-shrink-0 h-10">
            <div className="px-4 h-full flex items-center justify-between">
              {/* 左侧：功能选择标签 */}
              <div className="flex h-full">
                <button
                  onClick={() => setActiveTab('text')}
                  className={`relative px-4 h-full text-sm font-medium transition-all duration-200 flex items-center ${
                    activeTab === 'text'
                      ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-500'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                  }`}
                >
                  <div className="flex items-center space-x-1.5">
                    <Type size={14} />
                    <span>添加文字</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('variation')}
                  className={`relative px-4 h-full text-sm font-medium transition-all duration-200 flex items-center ${
                    activeTab === 'variation'
                      ? 'text-purple-600 bg-purple-50 border-b-2 border-purple-500'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                  }`}
                >
                  <div className="flex items-center space-x-1.5">
                    <Wand2 size={14} />
                    <span>视频裂变</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('clip')}
                  className={`relative px-4 h-full text-sm font-medium transition-all duration-200 flex items-center ${
                    activeTab === 'clip'
                      ? 'text-green-600 bg-green-50 border-b-2 border-green-500'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                  }`}
                >
                  <div className="flex items-center space-x-1.5">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2v20M2 12h20M7 7l10 10M7 17l10-10"/>
                    </svg>
                    <span>视频混剪</span>
                  </div>
                </button>
              </div>

              {/* 右侧：当前视频信息 */}
              {selectedVideo && (
                <div className="flex items-center space-x-2 text-xs text-slate-600 bg-slate-50 px-3 py-1.5 rounded">
                  <div className="flex items-center space-x-1">
                    <FileVideo size={12} className="text-slate-500" />
                    <span className="font-medium text-slate-700">当前视频:</span>
                  </div>
                  <span className="text-slate-900 font-medium max-w-[200px] truncate">{selectedVideo.name}</span>
                  <span className="text-slate-400">•</span>
                  <span>{selectedVideo.duration}</span>
                  <span className="text-slate-400">•</span>
                  <span>{selectedVideo.resolution}</span>
                </div>
              )}
            </div>
          </div>

          {/* 配置内容区域 */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="p-4">
              {activeTab === 'text' && (
                <div className="max-w-6xl">
                  <div className="mb-4">
                    <p className="text-xs text-slate-500 mt-0.5">选择文字预设后，您可以修改文字内容和位置。样式设置将使用预设中的配置。</p>
                  </div>
                  
                  <div className="grid grid-cols-12 gap-4">
                    {/* 左侧：预设选择 */}
                    <div className="col-span-4">
                      <label className="block text-xs font-medium text-slate-700 mb-2">选择预设</label>
                      <div className="space-y-1.5 max-h-32 overflow-y-auto">
                        {textPresets.map((preset) => (
                          <button
                            key={preset.id}
                            onClick={() => handleApplyPreset(preset)}
                            className="w-full p-2.5 border border-slate-300 rounded text-xs hover:border-blue-500 hover:bg-blue-50 transition-colors text-left"
                          >
                            <div className="font-medium text-slate-900">{preset.name}</div>
                            <div className="text-xs text-slate-500 truncate">{preset.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* 中间：配置选项 */}
                    <div className="col-span-5">
                      <div className="space-y-3">
                        {/* 文字内容 */}
                        <div>
                          <label className="block text-xs font-medium text-slate-700 mb-1.5">文字内容</label>
                          <textarea
                            value={textSettings.text}
                            onChange={(e) => setTextSettings({ ...textSettings, text: e.target.value })}
                            className="w-full px-2.5 py-2 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                            rows="2"
                            placeholder="输入要添加的文字"
                          />
                        </div>
                        
                        {/* 位置设置 */}
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-slate-700 mb-1.5">水平位置</label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              step="0.01"
                              value={textSettings.position_x}
                              onChange={(e) => setTextSettings({ ...textSettings, position_x: parseFloat(parseFloat(e.target.value).toFixed(2)) })}
                              className="w-full"
                            />
                            <div className="text-center text-xs text-slate-500 mt-0.5">{textSettings.position_x.toFixed(2)}%</div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-slate-700 mb-1.5">垂直位置</label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              step="0.01"
                              value={textSettings.position_y}
                              onChange={(e) => setTextSettings({ ...textSettings, position_y: parseFloat(parseFloat(e.target.value).toFixed(2)) })}
                              className="w-full"
                            />
                            <div className="text-center text-xs text-slate-500 mt-0.5">{textSettings.position_y.toFixed(2)}%</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 右侧：样式预览和操作 */}
                    <div className="col-span-3">
                      {/* 当前预设信息显示 */}
                      {textSettings.font_file && (
                        <div className="bg-slate-50 rounded-lg p-3 mb-3">
                          <h5 className="text-xs font-medium text-slate-700 mb-2">当前样式预览</h5>
                          <div className="space-y-1.5 text-xs text-slate-600">
                            <div className="flex justify-between">
                              <span className="text-slate-500">字体:</span>
                              <span>{fonts.find(f => f.value === textSettings.font_file)?.label || '默认'}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">大小:</span>
                              <span>{textSettings.font_size}px</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">预览大小:</span>
                              <span>{Math.round(textSettings.font_size * fontSizeScale)}px</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">缩放比例:</span>
                              <span>{(fontSizeScale * 100).toFixed(1)}%</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-slate-500">颜色:</span>
                              <div className="flex items-center space-x-1.5">
                                <span className="inline-block w-3 h-3 rounded border border-slate-300" style={{backgroundColor: textSettings.font_color}}></span>
                                <span className="text-xs">{textSettings.font_color}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 操作按钮 */}
                      <button
                        onClick={handleAddText}
                        disabled={!selectedVideo || !textSettings.text.trim() || processing}
                        className="w-full px-3 py-2.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                      >
                        {processing ? '处理中...' : '添加文字'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'variation' && (
                <div className="max-w-6xl">
                  <div className="mb-4">
                    <p className="text-xs text-slate-500 mt-0.5">生成多个不同效果的视频变体，支持调整分辨率、宽高比和视觉效果。</p>
                  </div>
                  
                  <div className="grid grid-cols-12 gap-4">
                    {/* 左侧：视频效果 */}
                    <div className="col-span-4">
                      <label className="block text-xs font-medium text-slate-700 mb-2">视频效果 <span className="text-slate-500">(效果强度由系统随机生成)</span></label>
                      <div className="space-y-1 max-h-44 overflow-y-auto">
                        {variationSettings.effects.map((effect, index) => (
                          <label key={effect.id} className="flex items-center space-x-2 p-1.5 hover:bg-slate-50 rounded cursor-pointer">
                            <input
                              type="checkbox"
                              checked={effect.enabled}
                              onChange={(e) => {
                                const newEffects = [...variationSettings.effects];
                                newEffects[index].enabled = e.target.checked;
                                setVariationSettings({ ...variationSettings, effects: newEffects });
                              }}
                              className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-xs text-slate-700">{effect.name}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* 中间：基础设置 */}
                    <div className="col-span-5">
                      <div className="space-y-3">
                        <div>
                          <label className="block text-xs font-medium text-slate-700 mb-1.5">生成数量</label>
                          <input
                            type="number"
                            value={variationSettings.count}
                            onChange={(e) => setVariationSettings({ ...variationSettings, count: parseInt(e.target.value) })}
                            className="w-full px-2.5 py-2 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                            min="1"
                            max="20"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-slate-700 mb-1.5">宽高比</label>
                          <select
                            value={variationSettings.aspectRatio}
                            onChange={(e) => {
                              const ratio = aspectRatios.find(r => r.value === e.target.value);
                              setVariationSettings({ 
                                ...variationSettings, 
                                aspectRatio: e.target.value,
                                resolution: ratio.resolution
                              });
                            }}
                            className="w-full px-2.5 py-2 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                          >
                            {aspectRatios.map(ratio => (
                              <option key={ratio.value} value={ratio.value}>{ratio.label}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={variationSettings.isOriginSound}
                              onChange={(e) => setVariationSettings({ ...variationSettings, isOriginSound: e.target.checked })}
                              className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-xs text-slate-700">保留原始音频</span>
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* 右侧：操作按钮 */}
                    <div className="col-span-3">
                      <div className="bg-slate-50 rounded-lg p-3 mb-3">
                        <h5 className="text-xs font-medium text-slate-700 mb-2">输出设置</h5>
                        <div className="space-y-1.5 text-xs text-slate-600">
                          <div className="flex justify-between">
                            <span className="text-slate-500">分辨率:</span>
                            <span>{variationSettings.resolution.width}x{variationSettings.resolution.height}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-500">数量:</span>
                            <span>{variationSettings.count} 个变体</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-500">音频:</span>
                            <span>{variationSettings.isOriginSound ? '保留' : '移除'}</span>
                          </div>
                        </div>
                      </div>

                      <button
                        onClick={handleGenerateVariations}
                        disabled={!selectedVideo || processing}
                        className="w-full px-3 py-2.5 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                      >
                        {processing ? '生成中...' : '生成变体'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'clip' && (
                <div className="max-w-6xl">
                  <div className="mb-4">
                    <p className="text-xs text-slate-500 mt-0.5">管理和执行视频混剪任务，选择任务后可以配置生成参数并开始生成。</p>
                  </div>
                  
                  <div className="grid grid-cols-12 gap-4">
                    {/* 左侧：任务列表 */}
                    <div className="col-span-4">
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-xs font-medium text-slate-700">混剪任务列表</label>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={loadClipTaskList}
                            disabled={clipTaskLoading}
                            className="text-xs text-green-600 hover:text-green-700 disabled:text-slate-400"
                          >
                            {clipTaskLoading ? '加载中...' : '刷新'}
                          </button>
                          <button
                            onClick={async () => {
                              try {
                                const { WindowService } = await import('../services/api.js');
                                await WindowService.ShowWindow('/clip');
                              } catch (error) {
                                console.error('打开混剪窗口失败:', error);
                                toast.error('打开混剪窗口失败: ' + error.message);
                              }
                            }}
                            className="text-xs text-blue-600 hover:text-blue-700"
                          >
                            新建
                          </button>
                        </div>
                      </div>
                      <div className="space-y-1.5 max-h-72 overflow-y-auto">
                        {clipTaskLoading ? (
                          <div className="p-4 text-center text-slate-500">
                            <div className="text-xs">加载中...</div>
                          </div>
                        ) : clipTaskList.length === 0 ? (
                          <div className="p-4 text-center text-slate-500">
                            <div className="text-xs">暂无任务</div>
                          </div>
                        ) : (
                          clipTaskList.map((task) => (
                            <button
                              key={task.id}
                              onClick={() => handleSelectClipTask(task)}
                              className={`w-full p-2.5 border rounded text-xs hover:border-green-500 hover:bg-green-50 transition-colors text-left ${
                                selectedClipTask?.id === task.id
                                  ? 'border-green-500 bg-green-50'
                                  : 'border-slate-300'
                              }`}
                            >
                              <div className="font-medium text-slate-900 truncate mb-1" title={task.clip_task_name}>
                                {task.clip_task_name}
                              </div>
                              <div className="text-xs text-slate-500">
                                <div className="flex justify-between items-center">
                                  <span>进度: {task.remaining_gen_count}/{task.gen_total}</span>
                                  <span className="text-xs text-slate-400">
                                    {new Date(task.created_at).toLocaleDateString('zh-CN', {
                                      month: '2-digit',
                                      day: '2-digit',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </span>
                                </div>
                              </div>
                            </button>
                          ))
                        )}
                      </div>
                    </div>

                    {/* 中间：任务详情和配置 */}
                    <div className="col-span-5">
                      {selectedClipTask ? (
                        <div className="space-y-3">
                          {/* 任务信息 */}
                          <div>
                            <label className="block text-xs font-medium text-slate-700 mb-1.5">任务详情</label>
                            <div className="bg-slate-50 rounded-lg p-3">
                              <div className="text-sm font-medium text-slate-900 mb-2">{selectedClipTask.clip_task_name}</div>
                              <div className="grid grid-cols-3 gap-2 text-xs text-slate-600 mb-2">
                                <div className="flex items-center space-x-1">
                                  <span className="text-slate-500">画面比例:</span>
                                  <span>{selectedClipTask.aspect_ratio}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-slate-500">缩放模式:</span>
                                  <span>{selectedClipTask.scale_mode}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-slate-500">音乐混音:</span>
                                  <span>{selectedClipTask.music_mix_mode === 1 ? '启用' : '禁用'}</span>
                                </div>
                              </div>
                              <div className="border-t border-slate-200 pt-2">
                                <div className="flex items-center justify-between text-xs text-slate-600">
                                  <span className="flex items-center space-x-1">
                                    <span>🎵</span>
                                    <span>{selectedClipTask.music_url?.length || 0}个音乐</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <span>🖼️</span>
                                    <span>{(selectedClipTask.head_cover?.length || 0) + (selectedClipTask.tail_cover?.length || 0)}个封面</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <span>📹</span>
                                    <span>{selectedClipTask.group_list?.length || 0}个分组</span>
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 生成配置 */}
                          <div>
                            <label className="block text-xs font-medium text-slate-700 mb-1.5">生成配置</label>
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <label className="block text-xs text-slate-600 mb-1">剩余可生成数量</label>
                                <div className="px-2.5 py-2 bg-slate-50 rounded text-sm font-medium text-slate-700">
                                  {selectedClipTask.remaining_gen_count} 个视频
                                </div>
                              </div>
                              <div>
                                <label className="block text-xs text-slate-600 mb-1">本次生成数量</label>
                                <input
                                  type="number"
                                  min="1"
                                  max={selectedClipTask.remaining_gen_count}
                                  value={clipGenCount}
                                  onChange={(e) => setClipGenCount(Math.max(1, Math.min(parseInt(e.target.value) || 1, selectedClipTask.remaining_gen_count)))}
                                  className="w-full px-2.5 py-2 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex items-center justify-center text-slate-500">
                          <div className="text-center">
                            <div className="text-2xl mb-2">📝</div>
                            <div className="text-sm">请从左侧选择一个混剪任务</div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 右侧：操作按钮和状态 */}
                    <div className="col-span-3">
                      {selectedClipTask && (
                        <div className="bg-slate-50 rounded-lg p-3 mb-3">
                          <h5 className="text-xs font-medium text-slate-700 mb-2">执行状态</h5>
                          <div className="space-y-1.5 text-xs text-slate-600">
                            <div className="flex justify-between">
                              <span className="text-slate-500">任务状态:</span>
                              <span className={`px-1.5 py-0.5 rounded-full ${getGenStatusColor(selectedClipTask.gen_status)}`}>
                                {getGenStatusText(selectedClipTask.gen_status)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">可生成:</span>
                              <span className={selectedClipTask.remaining_gen_count > 0 ? 'text-green-600' : 'text-red-500'}>
                                {selectedClipTask.remaining_gen_count > 0 ? '是' : '否'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">本次数量:</span>
                              <span>{clipGenCount} 个</span>
                            </div>
                          </div>
                        </div>
                      )}

                      <button
                        onClick={handleStartClipTask}
                        disabled={!selectedClipTask || clipTaskProcessing || selectedClipTask?.remaining_gen_count === 0}
                        className="w-full px-3 py-2.5 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed font-medium mb-3"
                      >
                        {clipTaskProcessing ? '生成中...' : '开始生成'}
                      </button>

                      {selectedClipTask && (
                        <button
                          onClick={(e) => {
                            if (confirm(`确定要删除任务"${selectedClipTask.clip_task_name}"吗？`)) {
                              handleDeleteClipTask(selectedClipTask.id);
                              setSelectedClipTask(null);
                            }
                          }}
                          className="w-full px-3 py-2 text-red-600 border border-red-300 rounded text-sm hover:bg-red-50 transition-colors"
                        >
                          删除任务
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* 底部状态栏 */}
      <StatusBar 
        totalVideos={videos.length}
        completedVideos={videos.filter(v => v.status === '已完成').length}
        processingVideos={videos.filter(v => v.status === '处理中').length}
        selectedVideo={selectedVideo}
        showProgress={showProgress}
        progress={progressValue}
        progressText={progressText}
        isRealProgress={isRealProgress}
        currentVideoIndex={currentVideoIndex}
        totalProgressVideos={totalVideos}
        completedProgressVideos={completedVideos}
      />

      {/* 右键菜单 */}
      <ContextMenu
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        onClose={handleCloseContextMenu}
        items={contextMenu.video ? [
          {
            label: '打开文件所在位置',
            icon: <FolderOpen size={16} />,
            onClick: () => handleOpenFileLocation(contextMenu.video)
          },
          { type: 'divider' },
          {
            label: '删除源文件',
            icon: <Trash2 size={16} />,
            color: '#FF4757',
            disabled: contextMenu.video.status === '已上传', // 原始视频禁用，处理后的视频启用
            onClick: () => handleDeleteOriginalFile(contextMenu.video)
          },
          {
            label: '从列表中删除',
            icon: <X size={16} />,
            color: '#FF4757',
            onClick: () => handleDeleteFromList(contextMenu.video)
          }
        ] : []}
      />
    </div>
  );
  };

export default VideoProcessor; 