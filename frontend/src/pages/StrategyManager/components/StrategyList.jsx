import React from "react";
import {
  <PERSON>,
  Settings,
  <PERSON><PERSON><PERSON>,
  Plus,
  ChevronLeft,
  ChevronRight,
  Building2,
  FolderOpen,
  Smartphone,
  Globe,
  Tag,
} from "lucide-react";
import { frequencyOptions } from "../constants";
import {
  STRATEGY_TYPES,
  STRATEGY_TYPE_LABELS,
  STRATEGY_TYPE_DESCRIPTIONS,
  getStrategyTypeOptions,
} from "../../../constants/strategyConstants";

const StrategyList = ({
  strategies,
  filteredStrategies,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  typeFilter,
  setTypeFilter,
  isLoading,
  pagination,
  toggleStrategyStatus,
  deleteStrategy,
  editStrategy,
  onCreateStrategy,
  onPageChange,
}) => {
  // 策略类型图标映射
  const getStrategyTypeIcon = (type) => {
    switch (type) {
      case STRATEGY_TYPES.ADVERTISER:
        return <Building2 size={16} />;
      case STRATEGY_TYPES.PROJECT:
        return <FolderOpen size={16} />;
      case STRATEGY_TYPES.PROMOTION:
        return <Smartphone size={16} />;
      case STRATEGY_TYPES.GLOBAL:
        return <Globe size={16} />;
      default:
        return <Tag size={16} />;
    }
  };

  // 策略类型颜色主题
  const getStrategyTypeTheme = (type) => {
    switch (type) {
      case STRATEGY_TYPES.ADVERTISER:
        return {
          bg: "rgba(156, 39, 176, 0.1)", // 紫色
          border: "rgba(156, 39, 176, 0.3)",
          text: "#9c27b0",
        };
      case STRATEGY_TYPES.PROJECT:
        return {
          bg: "rgba(33, 150, 243, 0.1)", // 蓝色
          border: "rgba(33, 150, 243, 0.3)",
          text: "#2196f3",
        };
      case STRATEGY_TYPES.PROMOTION:
        return {
          bg: "rgba(76, 175, 80, 0.1)", // 绿色
          border: "rgba(76, 175, 80, 0.3)",
          text: "#4caf50",
        };
      case STRATEGY_TYPES.GLOBAL:
        return {
          bg: "rgba(255, 152, 0, 0.1)", // 橙色
          border: "rgba(255, 152, 0, 0.3)",
          text: "#ff9800",
        };
      default:
        return {
          bg: "rgba(158, 158, 158, 0.1)", // 灰色
          border: "rgba(158, 158, 158, 0.3)",
          text: "#9e9e9e",
        };
    }
  };

  // 分页计算
  const totalPages = Math.ceil(pagination.total / pagination.pageSize);
  const hasNextPage = pagination.page < totalPages;
  const hasPrevPage = pagination.page > 1;

  // 格式化时间
  const formatTime = (timeStr) => {
    if (!timeStr) return "-";
    try {
      const date = new Date(timeStr);
      return date
        .toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        })
        .replace(/\//g, "-");
    } catch {
      return timeStr;
    }
  };

  return (
    <div className="flex-1 flex flex-col min-h-0">
      {/* 搜索和筛选栏 */}
      <div
        className="p-4 border-b"
        style={{ borderColor: "var(--border-color)" }}
      >
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-4">
              <h3
                className="text-lg font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                策略管理
              </h3>
              <span
                className="text-sm"
                style={{ color: "var(--text-secondary)" }}
              >
                共 {pagination.total} 个策略
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* 搜索框 */}
            <div className="relative">
              <Search
                size={16}
                className="absolute left-3 top-1/2 transform -translate-y-1/2"
                style={{ color: "var(--text-secondary)" }}
              />
              <input
                type="text"
                placeholder="搜索策略名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-64 text-sm rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--panel-bg)",
                  border: "1px solid var(--border-color)",
                  color: "var(--text-primary)",
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = "var(--primary-color)";
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = "var(--border-color)";
                }}
              />
            </div>

            {/* 策略类型筛选 */}
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 text-sm rounded-md transition-wechat"
              style={{
                backgroundColor: "var(--panel-bg)",
                border: "1px solid var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              <option value="all">全部类型</option>
              {getStrategyTypeOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* 状态筛选 */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 text-sm rounded-md transition-wechat"
              style={{
                backgroundColor: "var(--panel-bg)",
                border: "1px solid var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              <option value="all">全部状态</option>
              <option value="enabled">已启用</option>
              <option value="disabled">已禁用</option>
            </select>

            {/* 创建策略按钮 */}
            <button
              onClick={onCreateStrategy}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-wechat"
              style={{
                backgroundColor: "var(--primary-color)",
                color: "white",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = "0.9";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = "1";
              }}
            >
              <Plus size={16} />
              <span>创建策略</span>
            </button>
          </div>
        </div>
      </div>

      {/* 策略列表 */}
      <div className="flex-1 overflow-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div
              className="text-center"
              style={{ color: "var(--text-secondary)" }}
            >
              <div className="animate-spin w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mx-auto mb-4"></div>
              <p>加载中...</p>
            </div>
          </div>
        ) : strategies.length === 0 ? (
          <div
            className="flex items-center justify-center h-64"
            style={{ color: "var(--text-secondary)" }}
          >
            <div className="text-center">
              <PieChart size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">还没有策略</p>
              <p className="text-sm mb-4">创建您的第一个自动化策略</p>
              <button
                onClick={onCreateStrategy}
                className="inline-flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--primary-color)",
                  color: "white",
                }}
              >
                <Plus size={16} />
                <span>创建策略</span>
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {strategies.map((strategy) => {
              const theme = getStrategyTypeTheme(strategy.type);
              return (
                <div
                  key={strategy.id}
                  className="p-4 rounded-lg transition-wechat cursor-pointer"
                  style={{
                    backgroundColor: "var(--panel-bg)",
                    border: "1px solid var(--border-color)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = theme.border;
                    e.currentTarget.style.backgroundColor = theme.bg;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = "var(--border-color)";
                    e.currentTarget.style.backgroundColor = "var(--panel-bg)";
                  }}
                >
                  {/* 策略头部 */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      {/* 策略类型标签 */}
                      <div
                        className="inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium mb-2"
                        style={{
                          backgroundColor: theme.bg,
                          color: theme.text,
                          border: `1px solid ${theme.border}`,
                        }}
                      >
                        {getStrategyTypeIcon(strategy.type)}
                        <span>
                          {STRATEGY_TYPE_LABELS[strategy.type] || "未知类型"}
                        </span>
                      </div>

                      <h4
                        className="font-medium text-sm truncate"
                        style={{ color: "var(--text-primary)" }}
                        title={strategy.name}
                      >
                        {strategy.name}
                      </h4>
                      <p
                        className="text-xs mt-1 line-clamp-2"
                        style={{ color: "var(--text-secondary)" }}
                        title={strategy.description}
                      >
                        {strategy.description ||
                          STRATEGY_TYPE_DESCRIPTIONS[strategy.type] ||
                          "无描述"}
                      </p>
                    </div>

                    {/* 状态切换 */}
                    <div className="ml-2 flex-shrink-0">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleStrategyStatus(strategy.id);
                        }}
                        className={`w-10 h-6 rounded-full transition-wechat relative ${
                          strategy.enabled ? "bg-green-500" : "bg-gray-300"
                        }`}
                        title={strategy.enabled ? "点击禁用" : "点击启用"}
                      >
                        <div
                          className={`w-4 h-4 bg-white rounded-full transition-transform absolute top-1 ${
                            strategy.enabled ? "translate-x-5" : "translate-x-1"
                          }`}
                        />
                      </button>
                    </div>
                  </div>

                  {/* 策略信息 */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-xs">
                      <span style={{ color: "var(--text-secondary)" }}>
                        条件/动作:
                      </span>
                      <span style={{ color: "var(--text-primary)" }}>
                        {strategy.conditions_count || 0}/
                        {strategy.actions_count || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span style={{ color: "var(--text-secondary)" }}>
                        逻辑关系:
                      </span>
                      <span
                        className="px-1 py-0.5 rounded text-xs"
                        style={{
                          backgroundColor:
                            strategy.logic_type === "AND"
                              ? "rgba(76, 175, 80, 0.1)"
                              : "rgba(255, 152, 0, 0.1)",
                          color:
                            strategy.logic_type === "AND"
                              ? "#4caf50"
                              : "#ff9800",
                        }}
                      >
                        {strategy.logic_type === "AND" ? "且" : "或"}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span style={{ color: "var(--text-secondary)" }}>
                        监测频率:
                      </span>
                      <span style={{ color: "var(--text-primary)" }}>
                        {frequencyOptions.find(
                          (f) => f.value === strategy.monitor_frequency
                        )?.label || `每${strategy.monitor_frequency}秒`}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span style={{ color: "var(--text-secondary)" }}>
                        触发次数:
                      </span>
                      <span
                        className="px-1 py-0.5 rounded text-xs"
                        style={{
                          backgroundColor:
                            strategy.trigger_count > 0
                              ? "rgba(33, 150, 243, 0.1)"
                              : "rgba(158, 158, 158, 0.1)",
                          color:
                            strategy.trigger_count > 0 ? "#2196f3" : "#9e9e9e",
                        }}
                      >
                        {strategy.trigger_count || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span style={{ color: "var(--text-secondary)" }}>
                        最后触发:
                      </span>
                      <span style={{ color: "var(--text-primary)" }}>
                        {formatTime(strategy.last_triggered)}
                      </span>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        editStrategy(strategy.id);
                      }}
                      className="flex-1 px-3 py-1.5 text-xs rounded transition-wechat"
                      style={{
                        backgroundColor: "var(--hover-bg)",
                        color: "var(--text-primary)",
                        border: "1px solid var(--border-color)",
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = theme.text;
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor =
                          "var(--hover-bg)";
                        e.currentTarget.style.color = "var(--text-primary)";
                      }}
                    >
                      编辑
                    </button>

                    <button
                      onClick={(e) => {
                        console.log("删除按钮被点击，策略ID:", strategy.id);
                        e.stopPropagation();
                        deleteStrategy(strategy.id);
                      }}
                      className="px-3 py-1.5 text-xs rounded transition-wechat"
                      style={{
                        backgroundColor: "var(--hover-bg)",
                        color: "var(--text-primary)",
                        border: "1px solid var(--border-color)",
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = "#ff4757";
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor =
                          "var(--hover-bg)";
                        e.currentTarget.style.color = "var(--text-primary)";
                      }}
                    >
                      删除
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 分页 */}
      {!isLoading && strategies.length > 0 && totalPages > 1 && (
        <div
          className="p-4 border-t flex items-center justify-between"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="text-sm" style={{ color: "var(--text-secondary)" }}>
            第 {pagination.page} 页，共 {totalPages} 页，共 {pagination.total}{" "}
            条记录
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={!hasPrevPage}
              className="flex items-center px-3 py-1.5 text-sm rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: "var(--panel-bg)",
                border: "1px solid var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              <ChevronLeft size={16} />
              上一页
            </button>

            <span
              className="text-sm"
              style={{ color: "var(--text-secondary)" }}
            >
              {pagination.page} / {totalPages}
            </span>

            <button
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={!hasNextPage}
              className="flex items-center px-3 py-1.5 text-sm rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: "var(--panel-bg)",
                border: "1px solid var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              下一页
              <ChevronRight size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategyList;
