import React from "react";
import { Plus, Info } from "lucide-react";
import { Card } from "../../../components/ui";
import {
  conditionTypes,
  operators,
  actionTypes,
  frequencyOptions,
  adStatusOptions,
  advertiserStatusOptions,
  diagnosisStatusOptions,
  materialLabelOptions,
  projectStatusOptions,
  getAvailableOperators,
  getConditionTypesByStrategyType,
  getActionTypesByStrategyType,
} from "../constants";
import { formatUnit, createDefaultCondition } from "../utils";
import {
  STRATEGY_TYPES,
  STRATEGY_TYPE_LABELS,
  STRATEGY_TYPE_DESCRIPTIONS,
  getStrategyTypeOptions,
} from "../../../constants/strategyConstants";

const StrategyForm = ({
  strategyForm,
  setStrategyForm,
  isSaving,
  isEditing,
  onSave,
  onCancel,
  onFillExample,
  addCondition,
  removeCondition,
  updateCondition,
  addAction,
  removeAction,
  updateAction,
  updateActionConfig,
}) => {
  // 获取当前策略类型可用的条件类型
  const getAvailableConditionTypes = () => {
    return getConditionTypesByStrategyType(strategyForm.type);
  };

  // 获取当前策略类型可用的动作类型
  const getAvailableActionTypes = () => {
    return getActionTypesByStrategyType(strategyForm.type);
  };

  // 获取枚举选项的辅助函数
  const getEnumOptions = (conditionType) => {
    switch (conditionType) {
      case "promotion_status":
        return adStatusOptions;
      case "project_status":
        return projectStatusOptions;
      case "advertiser_status":
        return advertiserStatusOptions;
      case "diagnosis_status":
        return diagnosisStatusOptions;
      case "material_label":
        return materialLabelOptions;
      default:
        return [];
    }
  };

  // 检查是否为枚举类型
  const isEnumType = (conditionType) => {
    const condition = conditionTypes.find((c) => c.value === conditionType);
    return condition?.unit === "enum";
  };

  // 处理策略类型变化
  const handleStrategyTypeChange = (newType) => {
    setStrategyForm((prev) => {
      const availableConditionTypes = getConditionTypesByStrategyType(newType);
      const availableConditionValues = availableConditionTypes.map(
        (t) => t.value
      );

      // 检查现有条件是否需要重置
      const updatedConditions = prev.conditions.map((condition) => {
        // 如果当前条件类型在新策略类型中不被支持，重置为默认条件
        if (!availableConditionValues.includes(condition.type)) {
          const defaultCondition = createDefaultCondition(newType);
          return {
            ...condition, // 保持ID
            type: defaultCondition.type,
            field: defaultCondition.type,
            field_name: defaultCondition.label || defaultCondition.type,
            operator: defaultCondition.operator,
            value: "",
            values: [],
            min: 0,
            max: 0,
            unit: defaultCondition.unit,
          };
        }
        return condition;
      });

      return {
        ...prev,
        type: newType,
        conditions: updatedConditions,
      };
    });
  };

  // 获取可用操作符的辅助函数
  // const getAvailableOperators = (conditionType) => {
  //   return operators.filter((op) => op.type === conditionType);
  // };

  return (
    <div
      className="h-full flex flex-col p-4"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <Card className="overflow-hidden h-full flex flex-col">
        {/* 头部 */}
        <div
          className="px-6 py-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center justify-between">
            <h3
              className="text-lg font-semibold"
              style={{ color: "var(--text-primary)" }}
            >
              {isEditing ? "编辑策略" : "创建策略"}
            </h3>
            <div className="flex items-center space-x-3">
              {!isEditing && (
                <button
                  onClick={onFillExample}
                  className="px-3 py-1.5 text-sm rounded-md transition-wechat"
                  style={{
                    backgroundColor: "var(--hover-bg)",
                    color: "var(--text-primary)",
                    border: "1px solid var(--border-color)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--primary-color)";
                    e.currentTarget.style.color = "white";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                    e.currentTarget.style.color = "var(--text-primary)";
                  }}
                >
                  填充示例
                </button>
              )}
              <button
                onClick={onCancel}
                className="px-4 py-2 text-sm rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--hover-bg)",
                  color: "var(--text-primary)",
                  border: "1px solid var(--border-color)",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#ff4757";
                  e.currentTarget.style.color = "white";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                  e.currentTarget.style.color = "var(--text-primary)";
                }}
              >
                取消
              </button>
              <button
                onClick={onSave}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium rounded-md transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: "var(--primary-color)",
                  color: "white",
                }}
                onMouseEnter={(e) => {
                  if (!isSaving) {
                    e.currentTarget.style.opacity = "0.9";
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSaving) {
                    e.currentTarget.style.opacity = "1";
                  }
                }}
              >
                {isSaving ? "保存中..." : isEditing ? "更新策略" : "创建策略"}
              </button>
            </div>
          </div>
        </div>

        {/* 表单内容 */}
        <div className="flex-1 overflow-auto p-6">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div>
              <h4
                className="text-base font-medium mb-4"
                style={{ color: "var(--text-primary)" }}
              >
                基本信息
              </h4>

              {/* 策略类型选择 */}
              <div className="mb-4">
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  策略类型 *
                </label>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {getStrategyTypeOptions().map((option) => (
                    <div
                      key={option.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        strategyForm.type === option.value
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-gray-400"
                      }`}
                      onClick={() => handleStrategyTypeChange(option.value)}
                      style={{
                        backgroundColor:
                          strategyForm.type === option.value
                            ? "rgba(59, 130, 246, 0.1)"
                            : "var(--panel-bg)",
                        borderColor:
                          strategyForm.type === option.value
                            ? "var(--primary-color)"
                            : "var(--border-color)",
                      }}
                    >
                      <div className="flex items-start space-x-2">
                        <input
                          type="radio"
                          name="strategyType"
                          checked={strategyForm.type === option.value}
                          onChange={() =>
                            handleStrategyTypeChange(option.value)
                          }
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-1">
                            <span
                              className="font-medium text-sm"
                              style={{ color: "var(--text-primary)" }}
                            >
                              {option.label}
                            </span>
                            <Info
                              size={12}
                              style={{ color: "var(--text-secondary)" }}
                            />
                          </div>
                          <p
                            className="text-xs mt-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            {option.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--text-primary)" }}
                  >
                    策略名称 *
                  </label>
                  <input
                    type="text"
                    value={strategyForm.name}
                    onChange={(e) =>
                      setStrategyForm((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    placeholder="请输入策略名称"
                    className="w-full px-3 py-2 text-sm rounded-md transition-wechat"
                    style={{
                      backgroundColor: "var(--panel-bg)",
                      border: "1px solid var(--border-color)",
                      color: "var(--text-primary)",
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor =
                        "var(--primary-color)";
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = "var(--border-color)";
                    }}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--text-primary)" }}
                  >
                    监测频率
                  </label>
                  <select
                    value={strategyForm.monitorFrequency}
                    onChange={(e) =>
                      setStrategyForm((prev) => ({
                        ...prev,
                        monitorFrequency: parseInt(e.target.value),
                      }))
                    }
                    className="w-full px-3 py-2 text-sm rounded-md transition-wechat"
                    style={{
                      backgroundColor: "var(--panel-bg)",
                      border: "1px solid var(--border-color)",
                      color: "var(--text-primary)",
                    }}
                  >
                    {frequencyOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="mt-4">
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  策略描述
                </label>
                <textarea
                  value={strategyForm.description}
                  onChange={(e) =>
                    setStrategyForm((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="请输入策略描述（可选）"
                  rows={3}
                  className="w-full px-3 py-2 text-sm rounded-md transition-wechat resize-none"
                  style={{
                    backgroundColor: "var(--panel-bg)",
                    border: "1px solid var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = "var(--primary-color)";
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = "var(--border-color)";
                  }}
                />
              </div>
              <div className="mt-4">
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  逻辑关系
                </label>
                <select
                  value={strategyForm.logicType}
                  onChange={(e) =>
                    setStrategyForm((prev) => ({
                      ...prev,
                      logicType: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 text-sm rounded-md transition-wechat"
                  style={{
                    backgroundColor: "var(--panel-bg)",
                    border: "1px solid var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                >
                  <option value="AND">所有条件都满足（AND）</option>
                  <option value="OR">任一条件满足（OR）</option>
                </select>
              </div>
              <div className="mt-4 flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  checked={strategyForm.enabled}
                  onChange={(e) =>
                    setStrategyForm((prev) => ({
                      ...prev,
                      enabled: e.target.checked,
                    }))
                  }
                  className="mr-2"
                />
                <label
                  htmlFor="enabled"
                  className="text-sm"
                  style={{ color: "var(--text-primary)" }}
                >
                  创建后立即启用
                </label>
              </div>
            </div>

            {/* 触发条件 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4
                  className="text-base font-medium"
                  style={{ color: "var(--text-primary)" }}
                >
                  触发条件 ({strategyForm.conditions.length})
                </h4>
                <button
                  onClick={addCondition}
                  className="flex items-center space-x-1 px-3 py-1.5 text-sm rounded-md transition-wechat"
                  style={{
                    backgroundColor: "var(--primary-color)",
                    color: "white",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.opacity = "0.9";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.opacity = "1";
                  }}
                >
                  <Plus size={14} />
                  <span>添加条件</span>
                </button>
              </div>

              {strategyForm.conditions.length === 0 ? (
                <div
                  className="text-center py-8 border-2 border-dashed rounded-lg"
                  style={{ borderColor: "var(--border-color)" }}
                >
                  <p
                    className="text-sm mb-3"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    还没有添加触发条件
                  </p>
                  <button
                    onClick={addCondition}
                    className="px-4 py-2 text-sm rounded-md transition-wechat"
                    style={{
                      backgroundColor: "var(--primary-color)",
                      color: "white",
                    }}
                  >
                    添加第一个条件
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {strategyForm.conditions.map((condition, index) => (
                    <div
                      key={index}
                      className="p-4 rounded-md"
                      style={{
                        backgroundColor: "var(--panel-bg)",
                        border: "1px solid var(--border-color)",
                      }}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <span
                          className="text-sm font-medium"
                          style={{ color: "var(--text-primary)" }}
                        >
                          条件 {index + 1}
                        </span>
                        {strategyForm.conditions.length > 1 && (
                          <button
                            onClick={() => removeCondition(index)}
                            className="text-sm px-2 py-1 rounded transition-wechat"
                            style={{
                              color: "#ff4757",
                              backgroundColor: "var(--hover-bg)",
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = "#ff4757";
                              e.currentTarget.style.color = "white";
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor =
                                "var(--hover-bg)";
                              e.currentTarget.style.color = "#ff4757";
                            }}
                          >
                            删除
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                        <div>
                          <label
                            className="block text-xs font-medium mb-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            监测指标
                          </label>
                          <select
                            value={condition.type}
                            onChange={(e) =>
                              updateCondition(index, "type", e.target.value)
                            }
                            className="w-full px-3 py-2 text-sm rounded border"
                            style={{
                              backgroundColor: "var(--sidebar-bg)",
                              borderColor: "var(--border-color)",
                              color: "var(--text-primary)",
                            }}
                          >
                            {getAvailableConditionTypes().map((type) => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label
                            className="block text-xs font-medium mb-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            操作符
                          </label>
                          <select
                            value={condition.operator}
                            onChange={(e) =>
                              updateCondition(index, "operator", e.target.value)
                            }
                            className="w-full px-3 py-2 text-sm rounded border"
                            style={{
                              backgroundColor: "var(--sidebar-bg)",
                              borderColor: "var(--border-color)",
                              color: "var(--text-primary)",
                            }}
                          >
                            {getAvailableOperators(condition.type).map((op) => (
                              <option key={op.value} value={op.value}>
                                {op.label}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label
                            className="block text-xs font-medium mb-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            {condition.operator === "between"
                              ? "最小值"
                              : "阈值"}
                          </label>
                          {isEnumType(condition.type) ? (
                            <select
                              value={condition.value}
                              onChange={(e) =>
                                updateCondition(index, "value", e.target.value)
                              }
                              className="w-full px-3 py-2 text-sm rounded border"
                              style={{
                                backgroundColor: "var(--sidebar-bg)",
                                borderColor: "var(--border-color)",
                                color: "var(--text-primary)",
                              }}
                            >
                              <option value="">请选择</option>
                              {getEnumOptions(condition.type).map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          ) : condition.operator === "between" ? (
                            <div className="relative">
                              <input
                                type="number"
                                value={condition.min}
                                onChange={(e) =>
                                  updateCondition(
                                    index,
                                    "min",
                                    parseFloat(e.target.value) || 0
                                  )
                                }
                                placeholder="请输入最小值"
                                className="w-full px-3 py-2 text-sm rounded border pr-12"
                                style={{
                                  backgroundColor: "var(--sidebar-bg)",
                                  borderColor: "var(--border-color)",
                                  color: "var(--text-primary)",
                                }}
                              />
                              <span
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                {formatUnit(
                                  conditionTypes.find(
                                    (t) => t.value === condition.type
                                  )?.unit
                                )}
                              </span>
                            </div>
                          ) : condition.operator === "between" ? (
                            // Between操作符：显示最小值输入框
                            <div className="relative">
                              <input
                                type="number"
                                value={condition.min}
                                onChange={(e) =>
                                  updateCondition(
                                    index,
                                    "min",
                                    parseFloat(e.target.value) || 0
                                  )
                                }
                                placeholder="请输入最小值"
                                className="w-full px-3 py-2 text-sm rounded border pr-12"
                                style={{
                                  backgroundColor: "var(--sidebar-bg)",
                                  borderColor: "var(--border-color)",
                                  color: "var(--text-primary)",
                                }}
                              />
                              <span
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                {formatUnit(
                                  conditionTypes.find(
                                    (t) => t.value === condition.type
                                  )?.unit
                                )}
                              </span>
                            </div>
                          ) : (
                            // 其他操作符：显示普通数值输入框
                            <div className="relative">
                              <input
                                type="number"
                                value={condition.value}
                                onChange={(e) =>
                                  updateCondition(
                                    index,
                                    "value",
                                    e.target.value
                                  )
                                }
                                placeholder="请输入数值"
                                className="w-full px-3 py-2 text-sm rounded border pr-12"
                                style={{
                                  backgroundColor: "var(--sidebar-bg)",
                                  borderColor: "var(--border-color)",
                                  color: "var(--text-primary)",
                                }}
                              />
                              <span
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                {formatUnit(
                                  conditionTypes.find(
                                    (t) => t.value === condition.type
                                  )?.unit
                                )}
                              </span>
                            </div>
                          )}
                        </div>

                        {condition.operator === "between" &&
                          !isEnumType(condition.type) && (
                            <div>
                              <label
                                className="block text-xs font-medium mb-1"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                最大值
                              </label>
                              <div className="relative">
                                <input
                                  type="number"
                                  value={condition.max}
                                  onChange={(e) =>
                                    updateCondition(
                                      index,
                                      "max",
                                      parseFloat(e.target.value) || 0
                                    )
                                  }
                                  placeholder="请输入最大值"
                                  className="w-full px-3 py-2 text-sm rounded border pr-12"
                                  style={{
                                    backgroundColor: "var(--sidebar-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                                <span
                                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  {formatUnit(
                                    conditionTypes.find(
                                      (t) => t.value === condition.type
                                    )?.unit
                                  )}
                                </span>
                              </div>
                            </div>
                          )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 触发动作 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4
                  className="text-base font-medium"
                  style={{ color: "var(--text-primary)" }}
                >
                  触发动作 (
                  {strategyForm.actions.filter((a) => a.enabled).length})
                </h4>
                <button
                  onClick={addAction}
                  className="flex items-center space-x-1 px-3 py-1.5 text-sm rounded-md transition-wechat"
                  style={{
                    backgroundColor: "var(--primary-color)",
                    color: "white",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.opacity = "0.9";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.opacity = "1";
                  }}
                >
                  <Plus size={14} />
                  <span>添加动作</span>
                </button>
              </div>

              {strategyForm.actions.length === 0 ? (
                <div
                  className="text-center py-8 border-2 border-dashed rounded-lg"
                  style={{ borderColor: "var(--border-color)" }}
                >
                  <p
                    className="text-sm mb-3"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    还没有添加触发动作
                  </p>
                  <button
                    onClick={addAction}
                    className="px-4 py-2 text-sm rounded-md transition-wechat"
                    style={{
                      backgroundColor: "var(--primary-color)",
                      color: "white",
                    }}
                  >
                    添加第一个动作
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {strategyForm.actions.map((action, index) => (
                    <div
                      key={index}
                      className="p-4 rounded-md"
                      style={{
                        backgroundColor: "var(--panel-bg)",
                        border: "1px solid var(--border-color)",
                      }}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <span
                            className="text-sm font-medium"
                            style={{ color: "var(--text-primary)" }}
                          >
                            动作 {index + 1}
                          </span>
                          <input
                            type="checkbox"
                            checked={action.enabled}
                            onChange={(e) =>
                              updateAction(index, "enabled", e.target.checked)
                            }
                            className="rounded"
                          />
                          <span
                            className="text-xs"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            启用
                          </span>
                        </div>
                        {strategyForm.actions.length > 1 && (
                          <button
                            onClick={() => removeAction(index)}
                            className="text-sm px-2 py-1 rounded transition-wechat"
                            style={{
                              color: "#ff4757",
                              backgroundColor: "var(--hover-bg)",
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = "#ff4757";
                              e.currentTarget.style.color = "white";
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor =
                                "var(--hover-bg)";
                              e.currentTarget.style.color = "#ff4757";
                            }}
                          >
                            删除
                          </button>
                        )}
                      </div>

                      <div className="space-y-3">
                        <div>
                          <label
                            className="block text-xs font-medium mb-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            动作类型
                          </label>
                          <select
                            value={action.type}
                            onChange={(e) =>
                              updateAction(index, "type", e.target.value)
                            }
                            className="w-full px-3 py-2 text-sm rounded border"
                            style={{
                              backgroundColor: "var(--sidebar-bg)",
                              borderColor: "var(--border-color)",
                              color: "var(--text-primary)",
                            }}
                          >
                            {getAvailableActionTypes().map((type) => (
                              <option key={type.value} value={type.value}>
                                {type.icon} {type.label}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* 动作配置 */}
                        {action.enabled && (
                          <div
                            className="mt-3 p-3 rounded"
                            style={{ backgroundColor: "var(--sidebar-bg)" }}
                          >
                            {(action.type === "sms" ||
                              action.type === "email") && (
                              <div>
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  接收人 (每行一个)
                                </label>
                                <textarea
                                  value={
                                    action.config.recipients?.join("\n") || ""
                                  }
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "recipients",
                                      e.target.value
                                        .split("\n")
                                        .filter((r) => r.trim())
                                    )
                                  }
                                  placeholder={
                                    action.type === "sms"
                                      ? "请输入手机号，每行一个"
                                      : "请输入邮箱地址，每行一个"
                                  }
                                  rows={3}
                                  className="w-full px-3 py-2 text-sm rounded border resize-none"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                              </div>
                            )}

                            {action.type === "wechat_work" && (
                              <div>
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  企业微信机器人 Webhook URL
                                </label>
                                <input
                                  type="url"
                                  value={action.config.webhook_url || ""}
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "webhook_url",
                                      e.target.value
                                    )
                                  }
                                  placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=..."
                                  className="w-full px-3 py-2 text-sm rounded border"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                              </div>
                            )}

                            {action.type === "copy_ad" && (
                              <div>
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  复制数量
                                </label>
                                <input
                                  type="number"
                                  min="1"
                                  max="10"
                                  value={action.config.copy_count || 1}
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "copy_count",
                                      parseInt(e.target.value)
                                    )
                                  }
                                  className="w-full px-3 py-2 text-sm rounded border"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                                <p
                                  className="text-xs mt-1"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  复制到原项目，无需指定项目ID
                                </p>
                              </div>
                            )}

                            {action.type === "copy_multiple_ads" && (
                              <div>
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  复制数量
                                </label>
                                <input
                                  type="number"
                                  min="1"
                                  max="10"
                                  value={action.config.copy_count || 1}
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "copy_count",
                                      parseInt(e.target.value)
                                    )
                                  }
                                  className="w-full px-3 py-2 text-sm rounded border"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                                <p
                                  className="text-xs mt-1"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  批量复制到原项目，无需指定项目ID
                                </p>
                              </div>
                            )}

                            {action.type === "adjust_bid" && (
                              <div>
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  出价调整幅度 (%)
                                </label>
                                <input
                                  type="number"
                                  min="-100"
                                  max="100"
                                  value={action.config.bid_adjustment || 0}
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "bid_adjustment",
                                      parseInt(e.target.value)
                                    )
                                  }
                                  className="w-full px-3 py-2 text-sm rounded border"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                                <p
                                  className="text-xs mt-1"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  正数表示提高出价，负数表示降低出价
                                </p>
                              </div>
                            )}

                            {/* 自定义消息 */}
                            {(action.type === "sms" ||
                              action.type === "email" ||
                              action.type === "wechat_work" ||
                              action.type === "wechat_notification") && (
                              <div className="mt-3">
                                <label
                                  className="block text-xs font-medium mb-2"
                                  style={{ color: "var(--text-secondary)" }}
                                >
                                  自定义消息 (可选)
                                </label>
                                <textarea
                                  value={action.config.message || ""}
                                  onChange={(e) =>
                                    updateActionConfig(
                                      index,
                                      "message",
                                      e.target.value
                                    )
                                  }
                                  placeholder={
                                    action.type === "wechat_notification"
                                      ? "自定义策略提醒消息内容"
                                      : "自定义通知消息内容"
                                  }
                                  rows={2}
                                  className="w-full px-3 py-2 text-sm rounded border resize-none"
                                  style={{
                                    backgroundColor: "var(--panel-bg)",
                                    borderColor: "var(--border-color)",
                                    color: "var(--text-primary)",
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StrategyForm;
