import React, { useState, useEffect } from "react";
import { Pause, RefreshCw, Activity, RotateCcw, TestTube } from "lucide-react";
import { <PERSON><PERSON>, Card } from "../../../components/ui";
import { toast } from "../../../components/ui/Toast";
import strategyService from "../../../services/strategyService";

/**
 * 策略执行控制组件
 * 用于控制策略执行引擎
 */
const StrategyExecutionControl = () => {
  const [engineStatus, setEngineStatus] = useState({
    running: false,
    error: null,
  });
  const [loading, setLoading] = useState(false);

  // 加载引擎状态
  useEffect(() => {
    loadEngineStatus();
    // 定期刷新状态
    const interval = setInterval(loadEngineStatus, 10000); // 每10秒刷新一次
    return () => clearInterval(interval);
  }, []);

  // 加载引擎状态
  const loadEngineStatus = async () => {
    try {
      const response = await strategyService.getStrategyEngineStatus();
      if (response.code === 0) {
        setEngineStatus({
          running: response.data.running || false,
          error: response.data.error || null,
        });
      }
    } catch (error) {
      console.error("获取引擎状态失败:", error);
      setEngineStatus({
        running: false,
        error: error.message,
      });
    }
  };

  // 停止引擎
  const stopEngine = async () => {
    setLoading(true);
    try {
      const response = await strategyService.stopStrategyEngine();
      if (response.code === 0) {
        toast.success("策略执行引擎停止成功");
        await loadEngineStatus();
      } else {
        toast.error(response.msg || "停止失败");
      }
    } catch (error) {
      console.error("停止引擎失败:", error);
      toast.error("停止失败");
    } finally {
      setLoading(false);
    }
  };

  // 重启引擎
  const restartEngine = async () => {
    setLoading(true);
    try {
      const response = await strategyService.restartStrategyEngine();
      if (response.code === 0) {
        toast.success("策略执行引擎重启成功");
        await loadEngineStatus();
      } else {
        toast.error(response.msg || "重启失败");
      }
    } catch (error) {
      console.error("重启引擎失败:", error);
      toast.error("重启失败");
    } finally {
      setLoading(false);
    }
  };


  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Activity className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">策略执行引擎</h3>
        </div>
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              engineStatus.running ? "bg-green-500" : "bg-red-500"
            }`}
          />
          <span className="text-sm text-gray-600">
            {engineStatus.running ? "运行中" : "已停止"}
          </span>
        </div>
      </div>

      {engineStatus.error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">错误: {engineStatus.error}</p>
        </div>
      )}

      <div className="flex space-x-3 flex-wrap">
        <Button
          onClick={stopEngine}
          disabled={loading || !engineStatus.running}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Pause className="h-4 w-4" />
          <span>停止引擎</span>
        </Button>

        <Button
          onClick={restartEngine}
          disabled={loading}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <RotateCcw className="h-4 w-4" />
          <span>重启引擎</span>
        </Button>

        <Button
          onClick={loadEngineStatus}
          disabled={loading}
          variant="ghost"
          className="flex items-center space-x-2"
        >
          <RefreshCw className="h-4 w-4" />
          <span>刷新状态</span>
        </Button>

      </div>

    </Card>
  );
};

export default StrategyExecutionControl;
