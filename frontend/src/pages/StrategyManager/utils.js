// 格式化单位显示
export const formatUnit = (unit) => {
  switch (unit) {
    case "yuan":
      return "元";
    case "percent":
      return "%";
    case "count":
      return "次";
    case "minutes":
      return "分钟";
    case "enum":
      return ""; // 枚举类型不显示单位
    default:
      return "";
  }
};

// 创建默认条件
export const createDefaultCondition = (strategyType = "global") => {
  // 根据策略类型设置合适的默认条件
  const getDefaultConditionType = (type) => {
    switch (type) {
      case "promotion":
      case "global":
        return {
          type: "promotion_status",
          operator: "==",
          unit: "enum",
          label: "广告状态",
        };
      case "project":
        return {
          type: "project_status",
          operator: "==",
          unit: "enum",
          label: "项目状态",
        };
      case "advertiser":
        return {
          type: "advertiser_status",
          operator: "==",
          unit: "enum",
          label: "广告主状态",
        };
      default:
        return {
          type: "stat_cost",
          operator: ">",
          unit: "yuan",
          label: "消耗数",
        };
    }
  };

  const defaultConfig = getDefaultConditionType(strategyType);

  return {
    id: Date.now(),
    type: defaultConfig.type,
    field: defaultConfig.type, // 字段名通常与类型相同
    field_name: defaultConfig.label || defaultConfig.type, // 字段显示名称
    operator: defaultConfig.operator,
    value: "", // 用于非between操作符
    values: [], // 用于in操作符
    min: 0, // 用于between操作符的最小值
    max: 0, // 用于between操作符的最大值
    unit: defaultConfig.unit,
  };
};

// 创建默认动作
export const createDefaultAction = () => ({
  id: Date.now(),
  type: "wechat_notification",
  enabled: true,
  config: {
    recipients: [],
    message: "",
    webhook_url: "",
    copy_count: 1,
    bid_adjustment: 10,
    target_project_id: "",
  },
});

// 创建默认表单
export const createDefaultStrategyForm = () => {
  const timestamp = Date.now();
  const defaultType = "global"; // 默认为全局策略，最灵活的选择
  const defaultCondition = createDefaultCondition(defaultType);
  defaultCondition.id = timestamp; // 设置特定的ID

  return {
    name: "",
    description: "",
    type: defaultType,
    enabled: true,
    logicType: "AND",
    monitorFrequency: 300,
    conditions: [defaultCondition],
    actions: [
      {
        id: timestamp + 1,
        type: "wechat_notification",
        enabled: true,
        config: {
          recipients: [],
          message: "",
          webhook_url: "",
          copy_count: 1,
          bid_adjustment: 10,
          target_project_id: "",
        },
      },
    ],
  };
};

// 创建示例策略表单
export const createExampleStrategyForm = () => {
  const timestamp = Date.now();
  return {
    name: "示例策略 - 高消费告警",
    description: "当日消费超过500元时发送企业微信通知并暂停广告投放",
    type: "promotion", // 示例策略设置为广告策略
    enabled: true,
    logicType: "AND",
    monitorFrequency: 1800,
    conditions: [
      {
        id: timestamp,
        type: "stat_cost",
        field: "stat_cost",
        field_name: "消耗数",
        operator: ">",
        value: "500",
        values: [],
        min: 0,
        max: 0,
        unit: "yuan",
      },
    ],
    actions: [
      {
        id: timestamp + 1,
        type: "wechat_notification",
        enabled: true,
        config: {
          recipients: [],
          message: "广告消费超标，已自动暂停投放",
          webhook_url: "",
          copy_count: 1,
          bid_adjustment: 10,
          target_project_id: "",
        },
      },
      {
        id: timestamp + 2,
        type: "pause_ad",
        enabled: true,
        config: {
          recipients: [],
          message: "",
          webhook_url: "",
          copy_count: 1,
          bid_adjustment: 10,
          target_project_id: "",
        },
      },
    ],
  };
};
