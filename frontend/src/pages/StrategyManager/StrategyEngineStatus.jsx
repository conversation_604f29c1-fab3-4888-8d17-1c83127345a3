import React, { useState, useEffect } from "react";
import strategyService from "../../services/strategyService";
import { toast } from "../../components/ui/Toast";

const StrategyEngineStatus = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取策略引擎状态
  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await strategyService.getStrategyEngineStatus();
      if (result && result.code === 0) {
        setStatus(result.data);
      } else {
        setError(result?.msg || "获取状态失败");
      }
    } catch (err) {
      setError(err.message || "获取状态失败");
    } finally {
      setLoading(false);
    }
  };

  // 重启策略引擎
  const restartEngine = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await strategyService.restartStrategyEngine();
      if (result && result.code === 0) {
        toast.success("策略引擎重启成功");
        fetchStatus(); // 刷新状态
      } else {
        setError(result?.msg || "重启失败");
      }
    } catch (err) {
      setError(err.message || "重启失败");
    } finally {
      setLoading(false);
    }
  };

  // 停止策略引擎
  const stopEngine = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await strategyService.stopStrategyEngine();
      if (result && result.code === 0) {
        toast.success("策略引擎停止成功");
        fetchStatus(); // 刷新状态
      } else {
        setError(result?.msg || "停止失败");
      }
    } catch (err) {
      setError(err.message || "停止失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    // 定期刷新状态
    const interval = setInterval(fetchStatus, 10000); // 每10秒刷新一次
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">策略引擎状态</h1>
          <div className="flex gap-2">
            <button
              onClick={fetchStatus}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              刷新状态
            </button>
            <button
              onClick={restartEngine}
              disabled={loading}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
            >
              重启引擎
            </button>
            <button
              onClick={stopEngine}
              disabled={loading}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
            >
              停止引擎
            </button>
          </div>
        </div>

        {/* 自动启动说明 */}
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <p className="text-blue-800 text-sm">
            <strong>注意：</strong>
            策略引擎会在应用启动时自动开启，无需手动启动。创建、修改、删除策略时会自动刷新引擎。
          </p>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {loading && (
          <div className="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
            加载中...
          </div>
        )}

        {status && (
          <div className="space-y-4">
            {/* 基本状态 */}
            <div className="bg-gray-50 p-4 rounded">
              <h2 className="text-lg font-semibold mb-2">基本状态</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">运行状态:</span>
                  <span
                    className={`ml-2 px-2 py-1 rounded text-sm ${
                      status.running
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {status.running ? "运行中" : "已停止"}
                  </span>
                </div>
              </div>
            </div>

            {/* 引擎详细信息 */}
            {status.engine && (
              <div className="bg-gray-50 p-4 rounded">
                <h2 className="text-lg font-semibold mb-2">引擎详细信息</h2>
                <pre className="bg-white p-4 rounded border text-sm overflow-auto">
                  {JSON.stringify(status.engine, null, 2)}
                </pre>
              </div>
            )}

            {/* 完整状态信息 */}
            <div className="bg-gray-50 p-4 rounded">
              <h2 className="text-lg font-semibold mb-2">完整状态信息</h2>
              <pre className="bg-white p-4 rounded border text-sm overflow-auto">
                {JSON.stringify(status, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StrategyEngineStatus;
