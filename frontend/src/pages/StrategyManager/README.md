# 策略管理模块

## 概述

策略管理模块是一个独立的功能模块，用于管理和配置广告投放策略。该模块从主页面（MainPage.jsx）中分离出来，提供更好的代码组织和可维护性。

## 文件结构

```
StrategyManager/
├── README.md                     # 模块文档
├── index.js                     # 模块导出入口
├── StrategyManager.jsx          # 主组件
├── constants.js                 # 常量定义
├── utils.js                     # 工具函数
├── hooks/                       # 自定义Hooks
│   ├── useStrategyManager.js    # 策略管理主要逻辑
│   └── useStrategySave.js       # 策略保存验证逻辑
└── components/                  # 子组件
    ├── StrategyForm.jsx         # 策略表单组件
    └── StrategyList.jsx         # 策略列表组件
```

## 主要功能

### 1. 策略列表管理

- 显示所有策略的网格布局
- 支持策略搜索和状态筛选
- 策略启用/禁用切换
- 策略编辑和删除操作

### 2. 策略创建/编辑

- 基本信息配置（名称、描述、监测频率）
- 触发条件配置（支持多个条件和逻辑关系）
- 触发动作配置（支持多种动作类型）
- 实时预览功能
- 表单验证

### 3. 条件类型支持

- 消费金额（元）
- 点击率（%）
- 转化率（%）
- 展现量（次）
- 点击量（次）
- 平均点击价格（元）
- 千次展现价格（元）

### 4. 动作类型支持

- 短信通知
- 邮件通知
- 企业微信通知
- 暂停广告
- 关闭广告
- 复制广告
- 调整出价

## 技术特点

### 1. 模块化设计

- 采用单一职责原则
- 清晰的文件组织结构
- 便于维护和扩展

### 2. 自定义 Hooks

- `useStrategyManager`: 管理策略状态和业务逻辑
- `useStrategySave`: 处理策略保存和验证

### 3. 组件拆分

- `StrategyForm`: 策略创建/编辑表单
- `StrategyList`: 策略列表展示

### 4. 配置化

- 常量集中管理
- 工具函数复用
- 易于配置和修改

## 使用方法

### 1. 导入组件

```javascript
import StrategyManager from "./pages/StrategyManager";
```

### 2. 使用组件

```javascript
<StrategyManager />
```

## 扩展指南

### 1. 添加新的条件类型

在 `constants.js` 中的 `conditionTypes` 数组添加新项：

```javascript
{ value: "new_type", label: "新条件类型", unit: "yuan" }
```

### 2. 添加新的动作类型

在 `constants.js` 中的 `actionTypes` 数组添加新项：

```javascript
{ value: "new_action", label: "新动作", icon: "🔥" }
```

在 `StrategyForm.jsx` 中添加对应的配置 UI。

### 3. 修改验证规则

在 `useStrategySave.js` 中的 `saveStrategy` 函数中添加新的验证逻辑。

## 重构历史

- **2024-01-XX**: 从 MainPage.jsx 中分离策略管理功能
- 将约 1600 行的策略管理代码拆分为多个文件
- 提高代码可读性和可维护性
- 减少主页面文件的复杂度

## 注意事项

1. 该模块使用了与主应用相同的设计系统变量
2. 依赖 `Card` 组件从 `../../components/ui`
3. 使用 `lucide-react` 图标库
4. 支持中文界面
