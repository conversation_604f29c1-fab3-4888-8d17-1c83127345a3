import React, { useState, useEffect } from "react";
import {
  Settings,
  Plus,
  Search,
  PieChart,
  Copy,
  Upload,
  TestTube,
  Mail,
  Save,
  X,
} from "lucide-react";
import { Card, Input, Button, Toast } from "../../components/ui";
import { useStrategyManager } from "./hooks/useStrategyManager";
import { useStrategySave } from "./hooks/useStrategySave";
import {
  conditionTypes,
  operators,
  actionTypes,
  frequencyOptions,
} from "./constants";
import { formatUnit } from "./utils";
import StrategyForm from "./components/StrategyForm";
import StrategyList from "./components/StrategyList";
import StrategyExecutionControl from "./components/StrategyExecutionControl";
import ConfirmDialog from "../../components/ui/ConfirmDialog";
import { toast } from "../../components/ui/Toast";
import {
  getNotificationEmail,
  saveNotificationEmail,
  validateEmail,
} from "../../utils/notificationUtils";

// 策略管理组件
const StrategyManager = ({ onTabChange }) => {
  // 邮箱设置状态
  const [notificationEmail, setNotificationEmail] = useState("");
  const [isSavingEmail, setIsSavingEmail] = useState(false);
  const [showEmailSettings, setShowEmailSettings] = useState(false);

  // 加载保存的邮箱设置
  useEffect(() => {
    const loadEmail = async () => {
      try {
        const savedEmail = await getNotificationEmail();
        if (savedEmail) {
          setNotificationEmail(savedEmail);
        }
      } catch (error) {
        console.error("加载邮箱设置失败:", error);
      }
    };

    loadEmail();
  }, []);

  // 保存邮箱设置
  const handleSaveEmail = async () => {
    if (!notificationEmail.trim()) {
      toast.error("请输入有效的邮箱地址");
      return;
    }

    if (!validateEmail(notificationEmail)) {
      toast.error("请输入正确的邮箱格式");
      return;
    }

    setIsSavingEmail(true);
    try {
      const success = await saveNotificationEmail(notificationEmail);

      if (success) {
        toast.success("邮箱设置保存成功");
        setShowEmailSettings(false);
      } else {
        toast.error("保存邮箱设置失败");
      }
    } catch (error) {
      console.error("保存邮箱设置失败:", error);
      toast.error("保存邮箱设置失败");
    } finally {
      setIsSavingEmail(false);
    }
  };

  const {
    showCreateStrategy,
    setShowCreateStrategy,
    isSaving,
    setIsSaving,
    isLoading,
    strategies,
    setStrategies,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    strategyForm,
    setStrategyForm,
    editingStrategy,
    pagination,
    filteredStrategies,
    confirmDialog,
    closeConfirmDialog,
    addCondition,
    removeCondition,
    updateCondition,
    addAction,
    removeAction,
    updateAction,
    updateActionConfig,
    toggleStrategyStatus,
    deleteStrategy,
    editStrategy,
    createStrategy,
    fillExampleForm,
    resetForm,
    loadStrategies,
    handlePageChange,
  } = useStrategyManager();

  const { saveStrategy } = useStrategySave(
    strategyForm,
    setStrategyForm,
    setStrategies,
    setIsSaving,
    setShowCreateStrategy,
    editingStrategy,
    loadStrategies,
    pagination
  );

  // 如果显示创建策略页面
  if (showCreateStrategy) {
    return (
      <StrategyForm
        strategyForm={strategyForm}
        setStrategyForm={setStrategyForm}
        isSaving={isSaving}
        isEditing={!!editingStrategy}
        onSave={saveStrategy}
        onCancel={resetForm}
        onFillExample={fillExampleForm}
        addCondition={addCondition}
        removeCondition={removeCondition}
        updateCondition={updateCondition}
        addAction={addAction}
        removeAction={removeAction}
        updateAction={updateAction}
        updateActionConfig={updateActionConfig}
      />
    );
  }

  // 默认策略管理主页面
  return (
    <div
      className="h-full flex flex-col p-4"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <Card className="overflow-hidden h-full flex flex-col">
        {/* 策略引擎控制区 */}
        <div
          className="p-6 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <StrategyExecutionControl />
        </div>

        {/* 顶部功能区域 */}
        <div
          className="p-6 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
            {/* 新建策略 */}
            <div
              className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
              onClick={() => setShowCreateStrategy(true)}
            >
              <Plus size={24} className="mx-auto mb-2 text-green-500" />
              <h3
                className="font-medium mb-1 text-center"
                style={{ color: "var(--text-primary)" }}
              >
                新建策略
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                创建新的投放策略
              </p>
            </div>

            {/* 策略模板 */}
            <div
              className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              <Copy size={24} className="mx-auto mb-2 text-blue-500" />
              <h3
                className="font-medium mb-1 text-center"
                style={{ color: "var(--text-primary)" }}
              >
                策略模板
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                使用预设模板
              </p>
            </div>

            {/* 导入策略 */}
            <div
              className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              <Upload size={24} className="mx-auto mb-2 text-purple-500" />
              <h3
                className="font-medium mb-1 text-center"
                style={{ color: "var(--text-primary)" }}
              >
                导入策略
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                从文件导入
              </p>
            </div>

            {/* 通知设置 */}
            <div
              className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
              onClick={() => setShowEmailSettings(true)}
            >
              <Mail size={24} className="mx-auto mb-2 text-orange-500" />
              <h3
                className="font-medium mb-1 text-center"
                style={{ color: "var(--text-primary)" }}
              >
                通知设置
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                {notificationEmail ? "已配置邮箱" : "配置消息通知邮箱"}
              </p>
              {notificationEmail && (
                <p
                  className="text-xs text-center mt-1"
                  style={{ color: "var(--primary-color)" }}
                >
                  {notificationEmail}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* 策略列表区域 */}
        <StrategyList
          strategies={strategies}
          filteredStrategies={filteredStrategies}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          typeFilter={typeFilter}
          setTypeFilter={setTypeFilter}
          isLoading={isLoading}
          pagination={pagination}
          toggleStrategyStatus={toggleStrategyStatus}
          deleteStrategy={deleteStrategy}
          editStrategy={editStrategy}
          onCreateStrategy={createStrategy}
          onPageChange={handlePageChange}
        />
      </Card>

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={closeConfirmDialog}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />

      {/* 邮箱设置对话框 */}
      {showEmailSettings && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 遮罩层 */}
          <div
            className="absolute inset-0 bg-black/50 transition-opacity"
            onClick={() => setShowEmailSettings(false)}
          />

          {/* 对话框 */}
          <div
            className="relative bg-white rounded-lg shadow-xl w-full max-w-md mx-4"
            style={{
              backgroundColor: "var(--panel-bg)",
              border: "1px solid var(--border-color)",
            }}
          >
            {/* 标题栏 */}
            <div
              className="flex items-center justify-between p-4 border-b"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center space-x-2">
                <Mail size={20} style={{ color: "var(--primary-color)" }} />
                <h3
                  className="text-lg font-semibold"
                  style={{ color: "var(--text-primary)" }}
                >
                  通知邮箱设置
                </h3>
              </div>
              <button
                onClick={() => setShowEmailSettings(false)}
                className="p-1 rounded transition-wechat"
                style={{ color: "var(--text-secondary)" }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                <X size={20} />
              </button>
            </div>

            {/* 内容区域 */}
            <div className="p-6">
              <div className="mb-4">
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  企业微信消息通知邮箱
                </label>
                <Input
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  value={notificationEmail}
                  onChange={(e) => setNotificationEmail(e.target.value)}
                  className="w-full"
                  style={{
                    backgroundColor: "var(--sidebar-bg)",
                    borderColor: "var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                />
                <p
                  className="text-xs mt-2"
                  style={{ color: "var(--text-secondary)" }}
                >
                  此邮箱将用于接收企业微信消息通知，确保您能及时了解策略执行情况
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-end space-x-2">
                <Button
                  onClick={() => setShowEmailSettings(false)}
                  variant="outline"
                  size="small"
                >
                  取消
                </Button>
                <Button
                  onClick={handleSaveEmail}
                  disabled={isSavingEmail || !notificationEmail.trim()}
                  size="small"
                  loading={isSavingEmail}
                  icon={isSavingEmail ? null : <Save size={14} />}
                >
                  {isSavingEmail ? "保存中..." : "保存"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategyManager;
