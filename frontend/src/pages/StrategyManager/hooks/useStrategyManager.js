import { useState, useCallback, useEffect, useRef } from "react";
import strategyService from "../../../services/strategyService";
import { toast } from "../../../components/ui/Toast";
import {
  conditionTypes,
  frequencyOptions,
  getAvailableOperators,
} from "../constants";
import {
  createDefaultStrategyForm,
  createExampleStrategyForm,
  createDefaultCondition,
  createDefaultAction,
} from "../utils";

export const useStrategyManager = () => {
  const [showCreateStrategy, setShowCreateStrategy] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [strategies, setStrategies] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [strategyForm, setStrategyForm] = useState(createDefaultStrategyForm);
  const [editingStrategy, setEditingStrategy] = useState(null);

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    message: "",
    onConfirm: null,
  });

  // 分页状态
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
  });

  // 防抖和加载状态管理
  const loadingRef = useRef(false);
  const debounceTimerRef = useRef(null);
  const lastLoadParamsRef = useRef(null);

  // 加载策略列表
  const loadStrategies = useCallback(
    async (page = 1, pageSize = 20) => {
      // 如果正在加载，则跳过
      if (loadingRef.current) {
        console.log("策略列表正在加载中，跳过本次调用");
        return;
      }

      // 构建查询参数
      const params = {
        page,
        page_size: pageSize,
        name: searchTerm,
      };

      // 处理状态筛选
      if (statusFilter !== "all") {
        params.enabled = statusFilter === "enabled";
      }

      // 处理类型筛选
      if (typeFilter !== "all") {
        params.extraFilters = { type: typeFilter };
      }

      // 检查参数是否与上次相同，避免重复调用
      const paramsKey = JSON.stringify(params);
      // if (lastLoadParamsRef.current === paramsKey) {
      // console.log("查询参数未变化，跳过本次调用");
      // return;
      // }

      // 清除之前的防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // 设置防抖延迟
      debounceTimerRef.current = setTimeout(async () => {
        loadingRef.current = true;
        setIsLoading(true);
        lastLoadParamsRef.current = paramsKey;

        try {
          const response = await strategyService.getStrategyList(params);

          if (response.code === 0) {
            setStrategies(response.data.list || []);
            setPagination({
              page: response.data.page,
              pageSize: response.data.page_size,
              total: response.data.total,
            });
          } else {
            console.error("获取策略列表失败:", response.msg);
            toast.error(response.msg || "获取策略列表失败");
          }
        } catch (error) {
          console.error("加载策略列表失败:", error);
          toast.error("加载策略列表失败");
        } finally {
          loadingRef.current = false;
          setIsLoading(false);
        }
      }, 300); // 300ms防抖延迟
    },
    [searchTerm, statusFilter, typeFilter]
  );

  // 初始加载和搜索/筛选变化时重新加载
  useEffect(() => {
    loadStrategies(1, pagination.pageSize);
  }, [searchTerm, statusFilter, typeFilter, loadStrategies]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 分页处理
  const handlePageChange = useCallback(
    (page) => {
      loadStrategies(page, pagination.pageSize);
    },
    [loadStrategies, pagination.pageSize]
  );

  // 添加条件
  const addCondition = useCallback(() => {
    setStrategyForm((prev) => ({
      ...prev,
      conditions: [...prev.conditions, createDefaultCondition(prev.type)],
    }));
  }, []);

  // 移除条件
  const removeCondition = useCallback((index) => {
    setStrategyForm((prev) => ({
      ...prev,
      conditions: prev.conditions.filter((_, i) => i !== index),
    }));
  }, []);

  // 更新条件
  const updateCondition = useCallback((index, field, value) => {
    setStrategyForm((prev) => ({
      ...prev,
      conditions: prev.conditions.map((condition, i) => {
        if (i === index) {
          const updatedCondition = { ...condition, [field]: value };

          // 当更新条件类型时，同时更新对应的单位字段和操作符
          if (field === "type") {
            const conditionType = conditionTypes.find((t) => t.value === value);
            if (conditionType) {
              updatedCondition.unit = conditionType.unit;
              updatedCondition.field = value; // 字段名通常与类型相同
              updatedCondition.field_name = conditionType.label; // 字段显示名称

              // 检查当前操作符是否在新条件类型的允许操作符列表中
              const currentOperator = updatedCondition.operator;
              const isOperatorAllowed =
                conditionType.allowedOperators.includes(currentOperator);

              // 如果当前操作符不被允许，重置为第一个允许的操作符
              if (!isOperatorAllowed) {
                updatedCondition.operator = conditionType.allowedOperators[0];
              }

              // 清空值字段，因为条件类型变化了
              updatedCondition.value = "";
              updatedCondition.values = [];
              updatedCondition.min = 0;
              updatedCondition.max = 0;
            }
          }

          // 当更新操作符时，检查是否需要清空不兼容的值
          if (field === "operator") {
            // 如果新操作符不是 "between"，清空min/max值
            if (value !== "between") {
              updatedCondition.min = 0;
              updatedCondition.max = 0;
            }
            // 如果新操作符不是 "in"，清空values数组
            if (value !== "in") {
              updatedCondition.values = [];
            }
          }

          return updatedCondition;
        }
        return condition;
      }),
    }));
  }, []);

  // 添加动作
  const addAction = useCallback(() => {
    setStrategyForm((prev) => ({
      ...prev,
      actions: [...prev.actions, createDefaultAction()],
    }));
  }, []);

  // 移除动作
  const removeAction = useCallback((index) => {
    setStrategyForm((prev) => ({
      ...prev,
      actions: prev.actions.filter((_, i) => i !== index),
    }));
  }, []);

  // 更新动作
  const updateAction = useCallback((index, field, value) => {
    setStrategyForm((prev) => ({
      ...prev,
      actions: prev.actions.map((action, i) =>
        i === index ? { ...action, [field]: value } : action
      ),
    }));
  }, []);

  // 更新动作配置
  const updateActionConfig = useCallback((actionIndex, configField, value) => {
    setStrategyForm((prev) => ({
      ...prev,
      actions: prev.actions.map((action, i) =>
        i === actionIndex
          ? {
              ...action,
              config: { ...action.config, [configField]: value },
            }
          : action
      ),
    }));
  }, []);

  // 切换策略状态
  const toggleStrategyStatus = useCallback(
    async (strategyId) => {
      try {
        const response = await strategyService.toggleStrategyStatus(strategyId);

        if (response.code === 0) {
          // 重新加载策略列表
          await loadStrategies(pagination.page, pagination.pageSize);
          toast.success(response.msg || "操作成功");
        } else {
          console.error("切换策略状态失败:", response.msg);
          toast.error(response.msg || "操作失败");
        }
      } catch (error) {
        console.error("切换策略状态失败:", error);
        toast.error("操作失败");
      }
    },
    [loadStrategies, pagination.page, pagination.pageSize]
  );

  // 删除策略
  const deleteStrategy = useCallback(
    async (strategyId) => {
      console.log("删除策略被调用，策略ID:", strategyId);

      // 显示确认对话框
      setConfirmDialog({
        isOpen: true,
        title: "确认删除策略",
        message: "确定要删除这个策略吗？此操作无法撤销。",
        onConfirm: async () => {
          try {
            console.log("开始调用删除策略API...");
            const response = await strategyService.deleteStrategy(strategyId);
            console.log("删除策略API响应:", response);

            if (response.code === 0) {
              // 重新加载策略列表
              console.log("删除成功，重新加载策略列表...");
              await loadStrategies(pagination.page, pagination.pageSize);
              toast.success(response.msg || "删除成功");
            } else {
              console.error("删除策略失败:", response.msg);
              toast.error(response.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除策略失败:", error);
            toast.error("删除失败");
          } finally {
            // 关闭确认对话框
            setConfirmDialog({
              isOpen: false,
              title: "",
              message: "",
              onConfirm: null,
            });
          }
        },
      });
    },
    [loadStrategies, pagination.page, pagination.pageSize]
  );

  // 编辑策略
  const editStrategy = useCallback(async (strategyId) => {
    try {
      const response = await strategyService.getStrategyById(strategyId);

      if (response.code === 0) {
        const strategy = response.data;

        // 处理条件数据，确保包含正确的unit字段
        const processedConditions = (strategy.parsed_conditions || []).map(
          (condition) => {
            // 如果条件缺少unit字段，根据type自动补充
            if (!condition.unit) {
              const conditionType = conditionTypes.find(
                (t) => t.value === condition.type
              );
              if (conditionType) {
                condition.unit = conditionType.unit;
              }
            }

            // 确保所有必要字段都存在
            if (!condition.field) {
              condition.field = condition.type; // 如果没有field字段，使用type作为默认值
            }
            if (!condition.field_name) {
              // 如果没有field_name字段，根据type查找对应的label
              const conditionType = conditionTypes.find(
                (t) => t.value === condition.type
              );
              condition.field_name = conditionType
                ? conditionType.label
                : condition.type;
            }
            if (!condition.values) {
              condition.values = [];
            }
            if (condition.min === undefined) {
              condition.min = 0;
            }
            if (condition.max === undefined) {
              condition.max = 0;
            }

            return condition;
          }
        );

        // 处理监测频率：如果是旧的分钟值，转换为秒值
        let monitorFrequency = strategy.monitor_frequency;
        // 如果频率值小于3600且不在frequencyOptions中，认为是旧的分钟值，需要转换
        const isFrequencyInOptions = frequencyOptions.some(
          (option) => option.value === monitorFrequency
        );
        if (monitorFrequency < 3600 && !isFrequencyInOptions) {
          monitorFrequency = monitorFrequency * 60; // 分钟转秒
        }

        // 转换后端数据为表单格式
        const formData = {
          id: strategy.id,
          name: strategy.name,
          description: strategy.description,
          type: strategy.type || "global", // 如果后端没有类型字段，默认为全局策略
          enabled: strategy.enabled,
          logicType: strategy.logic_type,
          monitorFrequency: monitorFrequency,
          conditions: processedConditions,
          actions: strategy.parsed_actions || [],
        };

        setStrategyForm(formData);
        setEditingStrategy(strategy);
        setShowCreateStrategy(true);
      } else {
        console.error("获取策略详情失败:", response.msg);
        toast.error(response.msg || "获取策略详情失败");
      }
    } catch (error) {
      console.error("编辑策略失败:", error);
      toast.error("编辑策略失败");
    }
  }, []);

  // 创建新策略
  const createStrategy = useCallback(() => {
    setStrategyForm(createDefaultStrategyForm());
    setEditingStrategy(null);
    setShowCreateStrategy(true);
  }, []);

  // 使用示例填充表单
  const fillExampleForm = useCallback(() => {
    setStrategyForm(createExampleStrategyForm());
  }, []);

  // 重置表单
  const resetForm = useCallback(() => {
    setStrategyForm(createDefaultStrategyForm());
    setEditingStrategy(null);
    setShowCreateStrategy(false);
  }, []);

  // 筛选策略列表
  const filteredStrategies = strategies.filter((strategy) => {
    const matchesSearch = strategy.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "enabled" && strategy.enabled) ||
      (statusFilter === "disabled" && !strategy.enabled);
    const matchesType = typeFilter === "all" || strategy.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  return {
    // 状态
    showCreateStrategy,
    setShowCreateStrategy,
    isSaving,
    setIsSaving,
    isLoading,
    strategies,
    setStrategies,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    strategyForm,
    setStrategyForm,
    editingStrategy,
    pagination,
    filteredStrategies,
    confirmDialog,
    closeConfirmDialog: () =>
      setConfirmDialog({
        isOpen: false,
        title: "",
        message: "",
        onConfirm: null,
      }),

    // 条件操作
    addCondition,
    removeCondition,
    updateCondition,

    // 动作操作
    addAction,
    removeAction,
    updateAction,
    updateActionConfig,

    // 策略操作
    toggleStrategyStatus,
    deleteStrategy,
    editStrategy,
    createStrategy,
    fillExampleForm,
    resetForm,
    loadStrategies,

    // 分页
    handlePageChange,
  };
};
