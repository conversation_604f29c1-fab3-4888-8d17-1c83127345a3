import { useCallback } from "react";
import strategyService from "../../../services/strategyService";
import { actionTypes, frequencyOptions, conditionTypes } from "../constants";
import { createDefaultStrategyForm } from "../utils";
import { toast } from "../../../components/ui/Toast";

export const useStrategySave = (
  strategyForm,
  setStrategyForm,
  setStrategies,
  setIsSaving,
  setShowCreateStrategy,
  editingStrategy,
  loadStrategies,
  pagination
) => {
  const saveStrategy = useCallback(async () => {
    setIsSaving(true);

    try {
      // 验证基本信息
      if (!strategyForm.name.trim()) {
        toast.warning("请输入策略名称");
        setIsSaving(false);
        return;
      }

      // 验证触发条件
      for (let i = 0; i < strategyForm.conditions.length; i++) {
        const condition = strategyForm.conditions[i];

        // 获取条件类型定义
        const conditionTypeDef = conditionTypes.find(
          (c) => c.value === condition.type
        );

        // 检查条件值（between操作符使用min/max，其他操作符使用value）
        if (condition.operator === "between") {
          // between操作符验证min/max
          if (conditionTypeDef?.unit !== "enum") {
            if (condition.min === undefined || condition.max === undefined) {
              toast.warning(`请填写第${i + 1}个条件的最小值和最大值`);
              setIsSaving(false);
              return;
            }
          }
        } else if (condition.operator === "in") {
          // in操作符验证values数组
          if (!condition.values || condition.values.length === 0) {
            toast.warning(`请选择第${i + 1}个条件的值`);
            setIsSaving(false);
            return;
          }
        } else {
          // 其他操作符验证value
          if (!condition.value || condition.value === "") {
            toast.warning(`请填写第${i + 1}个条件的值`);
            setIsSaving(false);
            return;
          }
        }

        // 根据条件类型和操作符进行不同的验证
        if (
          condition.operator === "between" &&
          conditionTypeDef?.unit !== "enum"
        ) {
          // between操作符的数值验证
          const min = parseFloat(condition.min);
          const max = parseFloat(condition.max);

          if (conditionTypeDef?.unit === "percent") {
            if (
              isNaN(min) ||
              min < 0 ||
              min > 100 ||
              isNaN(max) ||
              max < 0 ||
              max > 100
            ) {
              toast.warning(`第${i + 1}个条件的百分比值应在0-100之间`);
              setIsSaving(false);
              return;
            }
          } else if (
            conditionTypeDef?.unit === "yuan" ||
            conditionTypeDef?.unit === "count" ||
            conditionTypeDef?.unit === "minutes"
          ) {
            if (isNaN(min) || min < 0 || isNaN(max) || max < 0) {
              toast.warning(`第${i + 1}个条件的数值应为非负数`);
              setIsSaving(false);
              return;
            }
          }
        } else if (condition.operator === "in") {
          // in操作符无需额外验证，values数组已在上面检查
        } else {
          // 其他操作符的验证
          if (conditionTypeDef?.unit === "enum") {
            // 枚举类型无需额外验证，value已在上面检查
          } else if (conditionTypeDef?.unit === "percent") {
            // 百分比验证
            const value = parseFloat(condition.value);
            if (isNaN(value) || value < 0 || value > 100) {
              toast.warning(`第${i + 1}个条件的百分比值应在0-100之间`);
              setIsSaving(false);
              return;
            }
          } else if (
            conditionTypeDef?.unit === "yuan" ||
            conditionTypeDef?.unit === "count" ||
            conditionTypeDef?.unit === "minutes"
          ) {
            // 数值验证
            const value = parseFloat(condition.value);
            if (isNaN(value) || value < 0) {
              toast.warning(`第${i + 1}个条件的值应为非负数`);
              setIsSaving(false);
              return;
            }
          }
        }

        // 检查区间值的范围逻辑
        if (
          condition.operator === "between" &&
          conditionTypeDef?.unit !== "enum"
        ) {
          const min = parseFloat(condition.min);
          const max = parseFloat(condition.max);
          if (min >= max) {
            toast.warning(`第${i + 1}个条件的最小值应小于最大值`);
            setIsSaving(false);
            return;
          }
        }
      }

      // 验证触发动作
      const enabledActions = strategyForm.actions.filter(
        (action) => action.enabled
      );
      if (enabledActions.length === 0) {
        toast.warning("至少需要启用一个触发动作");
        setIsSaving(false);
        return;
      }

      for (let i = 0; i < strategyForm.actions.length; i++) {
        const action = strategyForm.actions[i];

        if (!action.enabled) continue; // 跳过未启用的动作

        // 验证动作配置
        switch (action.type) {
          case "sms":
          case "email":
            if (
              !action.config.recipients ||
              action.config.recipients.length === 0
            ) {
              const actionType = actionTypes.find(
                (t) => t.value === action.type
              );
              toast.warning(
                `请配置${actionType?.label || action.type}的接收人`
              );
              setIsSaving(false);
              return;
            }
            break;
          case "wechat_work":
            if (
              !action.config.webhook_url ||
              action.config.webhook_url.trim() === ""
            ) {
              toast.warning("请配置企业微信机器人的Webhook URL");
              setIsSaving(false);
              return;
            }
            break;
          case "copy_ad":
            if (!action.config.copy_count || action.config.copy_count <= 0) {
              toast.warning("复制广告数量应大于0");
              setIsSaving(false);
              return;
            }
            break;
          case "copy_multiple_ads":
            if (!action.config.copy_count || action.config.copy_count <= 0) {
              toast.warning("批量复制广告数量应大于0");
              setIsSaving(false);
              return;
            }
            if (action.config.copy_count > 10) {
              toast.warning("批量复制广告数量不能超过10个");
              setIsSaving(false);
              return;
            }
            break;
          case "delete_and_create_ad":
            // 删除并新建广告无需额外验证
            break;
          case "adjust_bid":
            if (
              !action.config.bid_adjustment ||
              action.config.bid_adjustment === 0
            ) {
              toast.warning("请设置出价调整幅度");
              setIsSaving(false);
              return;
            }
            break;
        }
      }

      // 验证监测频率
      const frequency = frequencyOptions.find(
        (f) => f.value === strategyForm.monitorFrequency
      );
      if (!frequency) {
        toast.warning("请选择有效的监测频率");
        setIsSaving(false);
        return;
      }

      // 准备保存数据
      const saveData = {
        name: strategyForm.name.trim(),
        description: strategyForm.description.trim(),
        type: strategyForm.type, // 添加策略类型字段
        enabled: strategyForm.enabled,
        logic_type: strategyForm.logicType,
        monitor_frequency: strategyForm.monitorFrequency,
        // 确保数据类型正确，特别是数值字段
        conditions: strategyForm.conditions.map((condition) => ({
          type: condition.type,
          field: condition.field || condition.type,
          field_name: condition.field_name || condition.type, // 字段显示名称
          operator: condition.operator,
          // 根据操作符选择合适的字段
          value:
            condition.operator === "between" || condition.operator === "in"
              ? null
              : condition.value,
          values: condition.operator === "in" ? condition.values || [] : [],
          min:
            condition.operator === "between"
              ? parseFloat(condition.min) || 0
              : 0,
          max:
            condition.operator === "between"
              ? parseFloat(condition.max) || 0
              : 0,
          unit: condition.unit,
        })),
        actions: strategyForm.actions,
      };

      let response;

      if (editingStrategy) {
        // 更新现有策略
        saveData.id = editingStrategy.id;
        response = await strategyService.updateStrategy(saveData);
      } else {
        // 创建新策略
        response = await strategyService.createStrategy(saveData);
      }

      if (response.code === 0) {
        toast.success(
          response.msg || (editingStrategy ? "更新策略成功" : "创建策略成功")
        );

        // 关闭创建面板
        setShowCreateStrategy(false);

        // 重置表单
        setStrategyForm(createDefaultStrategyForm());

        // 重新加载策略列表
        await loadStrategies(pagination.page, pagination.pageSize);
      } else {
        console.error("保存策略失败:", response.msg);
        toast.error(response.msg || "保存策略失败");
      }
    } catch (error) {
      console.error("保存策略异常:", error);
      toast.error("保存策略失败: " + error.message);
    } finally {
      setIsSaving(false);
    }
  }, [
    strategyForm,
    setIsSaving,
    setShowCreateStrategy,
    setStrategyForm,
    editingStrategy,
    loadStrategies,
    pagination,
  ]);

  return {
    saveStrategy,
  };
};
