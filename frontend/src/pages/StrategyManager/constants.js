// 基础条件类型定义
const baseConditionTypes = {
  // 数据指标类条件
  stat_cost: {
    value: "stat_cost",
    label: "消耗数",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  convert_cnt: {
    value: "convert_cnt",
    label: "转化数",
    unit: "count",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  conversion_cost: {
    value: "conversion_cost",
    label: "平均转化成本",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  show_cnt: {
    value: "show_cnt",
    label: "展示数",
    unit: "count",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  conversion_rate: {
    value: "conversion_rate",
    label: "转化率",
    unit: "percent",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  ctr: {
    value: "ctr",
    label: "点击率",
    unit: "percent",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  click_cnt: {
    value: "click_cnt",
    label: "点击量",
    unit: "count",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },

  // 状态类条件
  project_status: {
    value: "project_status",
    label: "项目状态",
    unit: "enum",
    allowedOperators: ["==", "!=", "in"],
    multiSelect: true,
  },
  promotion_status: {
    value: "promotion_status",
    label: "广告状态",
    unit: "enum",
    allowedOperators: ["=="],
    multiSelect: false,
  },
  advertiser_status: {
    value: "advertiser_status",
    label: "广告主状态",
    unit: "enum",
    allowedOperators: ["=="],
    multiSelect: false,
  },

  // 资金类条件
  account_balance: {
    value: "account_balance",
    label: "账户余额",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },

  // 广告属性条件
  ad_budget: {
    value: "ad_budget",
    label: "广告预算",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  ad_bid: {
    value: "ad_bid",
    label: "出价",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },

  // 原有广告条件
  cpc_platform: {
    value: "cpc_platform",
    label: "平均点击价格",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  cpm_platform: {
    value: "cpm_platform",
    label: "千次展现价格",
    unit: "yuan",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  diagnosis_status: {
    value: "diagnosis_status",
    label: "诊断状态(未完成)",
    unit: "enum",
    allowedOperators: ["==", "!=", "in"],
    multiSelect: true,
  },
  material_label: {
    value: "material_label",
    label: "素材标签(未完成)",
    unit: "enum",
    allowedOperators: ["==", "!=", "in"],
    multiSelect: true,
  },
  learning_timeout: {
    value: "learning_timeout",
    label: "学保超时时间(未完成)",
    unit: "minutes",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
  ad_time_special: {
    value: "ad_time_special",
    label: "广告时间/新建后特殊时间(未完成)",
    unit: "minutes",
    allowedOperators: [">", "<", ">=", "<=", "==", "between"],
    multiSelect: false,
  },
};

// 基础动作类型定义
const baseActionTypes = {
  // 通知类动作
  wechat_notification: {
    value: "wechat_notification",
    label: "企业微信通知",
    icon: "📱",
  },

  // 项目操作动作
  copy_project: {
    value: "copy_project",
    label: "复制项目(未完成)",
    icon: "📋",
  },
  enable_project: {
    value: "enable_project",
    label: "启用项目",
    icon: "▶️",
  },
  pause_project: {
    value: "pause_project",
    label: "暂停项目",
    icon: "⏸️",
  },
  delete_project: {
    value: "delete_project",
    label: "删除项目",
    icon: "🗑️",
  },

  // 广告操作动作
  pause_ad: {
    value: "pause_ad",
    label: "暂停广告",
    icon: "⏸️",
  },
  resume_ad: {
    value: "resume_ad",
    label: "启用广告",
    icon: "▶️",
  },
  delete_ad: {
    value: "delete_ad",
    label: "删除广告",
    icon: "❌",
  },
  copy_ad: {
    value: "copy_ad",
    label: "复制广告",
    icon: "📋",
  },
  delete_and_create_ad: {
    value: "delete_and_create_ad",
    label: "删除并新建广告",
    icon: "🔄",
  },
  copy_multiple_ads: {
    value: "copy_multiple_ads",
    label: "批量复制广告",
    icon: "📋📋",
  },
};

// 根据策略类型获取可用的条件类型
export const getConditionTypesByStrategyType = (strategyType) => {
  switch (strategyType) {
    case "project":
      return [
        baseConditionTypes.project_status, //项目状态
        baseConditionTypes.conversion_cost,
        baseConditionTypes.stat_cost,
        baseConditionTypes.show_cnt,
        baseConditionTypes.convert_cnt,
        baseConditionTypes.conversion_rate,
      ];

    case "advertiser":
      return [
        baseConditionTypes.advertiser_status,
        baseConditionTypes.account_balance,
        baseConditionTypes.conversion_cost,
        baseConditionTypes.stat_cost,
        baseConditionTypes.show_cnt,
        baseConditionTypes.convert_cnt,
        baseConditionTypes.conversion_rate,
        baseConditionTypes.click_cnt,
        baseConditionTypes.ctr,
        baseConditionTypes.cpm_platform,
      ];

    case "promotion":
    case "global":
      return [
        baseConditionTypes.promotion_status,
        baseConditionTypes.conversion_cost,
        baseConditionTypes.stat_cost,
        baseConditionTypes.show_cnt,
        baseConditionTypes.convert_cnt,
        baseConditionTypes.conversion_rate,
        baseConditionTypes.ad_budget,
        baseConditionTypes.ad_bid,
        // 原有广告条件
        baseConditionTypes.ctr,
        baseConditionTypes.click_cnt,
        baseConditionTypes.cpc_platform,
        baseConditionTypes.cpm_platform,
        baseConditionTypes.diagnosis_status,
        baseConditionTypes.material_label,
        baseConditionTypes.learning_timeout,
        baseConditionTypes.ad_time_special,
      ];

    default:
      return Object.values(baseConditionTypes);
  }
};

// 根据策略类型获取可用的动作类型
export const getActionTypesByStrategyType = (strategyType) => {
  switch (strategyType) {
    case "project":
      return [
        baseActionTypes.wechat_notification,
        baseActionTypes.copy_project,
        baseActionTypes.enable_project,
        baseActionTypes.pause_project,
        baseActionTypes.delete_project,
      ];

    case "advertiser":
      return [baseActionTypes.wechat_notification];

    case "promotion":
    case "global":
      return [
        baseActionTypes.wechat_notification,
        baseActionTypes.delete_ad,
        baseActionTypes.resume_ad,
        baseActionTypes.copy_ad,
        baseActionTypes.delete_and_create_ad,
        baseActionTypes.pause_ad,
        baseActionTypes.copy_multiple_ads,
      ];

    default:
      return Object.values(baseActionTypes);
  }
};

// 导出条件类型和动作类型（用于全类型支持的场景）
export const conditionTypes = Object.values(baseConditionTypes);
export const actionTypes = Object.values(baseActionTypes);

// 操作符选项
export const operators = [
  { value: ">", label: "大于" },
  { value: "<", label: "小于" },
  { value: ">=", label: "大于等于" },
  { value: "<=", label: "小于等于" },
  { value: "==", label: "等于" },
  { value: "!=", label: "不等于" },
  { value: "between", label: "介于" },
  { value: "in", label: "属于" },
];

// 获取条件类型允许的操作符
export const getAvailableOperators = (conditionType) => {
  const condition = baseConditionTypes[conditionType];
  if (!condition) return operators;

  return operators.filter((op) =>
    condition.allowedOperators.includes(op.value)
  );
};

// 检查条件类型是否支持多选
export const isMultiSelectCondition = (conditionType) => {
  const condition = baseConditionTypes[conditionType];
  return condition ? condition.multiSelect : false;
};

// 项目状态选项
export const projectStatusOptions = [
  { value: "active", label: "启用" },
  { value: "paused", label: "暂停" },
];

// 广告状态选项
export const adStatusOptions = [
  { value: "active", label: "投放中" },
  { value: "paused", label: "已暂停" },
  { value: "audit_failed", label: "审核不通过" },
  { value: "insufficient_funds", label: "账户余额不足" },
  { value: "audit_pending", label: "新建审核中" },
  { value: "terminated", label: "已终止" },
];

// 广告主状态选项
export const advertiserStatusOptions = [
  { value: "active", label: "正常" },
  { value: "paused", label: "暂停" },
  { value: "deleted", label: "删除" },
];

// 诊断状态选项
export const diagnosisStatusOptions = [
  { value: "normal", label: "正常" },
  { value: "low_activity", label: "活跃度低" },
  { value: "similar_squeeze", label: "相似广告挤压严重" },
  { value: "ineffective", label: "低效素材" },
  { value: "queue_squeeze", label: "挤压严重" },
  { value: "queue_delivery", label: "排队投放" },
  { value: "undelivered_queue", label: "未投放预计排队" },
];

// 素材标签选项
export const materialLabelOptions = [
  { value: "copy_material", label: "搬运素材" },
  { value: "original_material", label: "原创素材" },
  { value: "optimized_material", label: "优化素材" },
  { value: "queue_material", label: "排队素材" },
];

// 监测频率选项（以秒为单位）
export const frequencyOptions = [
  { value: 10, label: "每10秒" },
  { value: 30, label: "每30秒" },
  { value: 60, label: "每1分钟" },
  { value: 120, label: "每2分钟" },
  { value: 300, label: "每5分钟" },
  { value: 600, label: "每10分钟" },
  { value: 900, label: "每15分钟" },
  { value: 1800, label: "每30分钟" },
  { value: 3600, label: "每1小时" },
  { value: 7200, label: "每2小时" },
  { value: 14400, label: "每4小时" },
  { value: 28800, label: "每8小时" },
  { value: 43200, label: "每12小时" },
  { value: 86400, label: "每24小时" },
];
