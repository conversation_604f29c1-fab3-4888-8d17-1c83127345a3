import React, { useState, useEffect } from "react";
import {
  Settings,
  User,
  BarChart3,
  Target,
  History,
  ChevronUp,
  ChevronDown,
  ChevronRight,
  Clock,
  FileText,
  Download,
  Upload,
  Plus,
  Copy,
  Search,
  Filter,
  Lightbulb,
  PieChart,
  TrendingUp,
  Shield,
} from "lucide-react";
import { useAccountContext } from "../contexts/AccountContext";
import { useStrategyPreloader } from "../hooks/useStrategyPreloader";
import useTaskProgress from "../hooks/useTaskProgress";
import { Events } from "@wailsio/runtime";
import { Card, CompactTaskProgressBar } from "../components/ui";
import AccountSelector from "../components/ui/AccountSelector";
import UserInfo from "../components/auth/UserInfo";
import AccountManager from "./AccountManager";
import AdvertiserManager from "./AdvertiserManager";
import ProjectManager from "./ProjectManager";
import PromotionManager from "./PromotionManager";
import StrategyManager from "./StrategyManager";
import StrategyLogPage from "./StrategyLogPage";
import SensitiveWordMonitor from "./SensitiveWordMonitor";
import TestProgressPage from "./TestProgressPage";
import testTaskProgressService from "../services/testTaskProgressService";
// 独立的时间显示组件 - 避免影响主组件渲染
const TimeDisplay = React.memo(() => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <span className="text-timestamp" style={{ color: "var(--text-secondary)" }}>
      当前时间: {currentTime.toLocaleString()}
    </span>
  );
});

// 常量定义
const TABS = [
  { name: "账户管理", icon: User, component: AccountManager },
  { name: "广告主管理", icon: User, component: AdvertiserManager },
  { name: "项目管理", icon: BarChart3, component: ProjectManager },
  { name: "项目广告", icon: Target, component: PromotionManager },
  { name: "策略管理", icon: PieChart, component: StrategyManager },
  { name: "策略日志", icon: History, component: StrategyLogPage },
  { name: "自动删评", icon: Shield, component: SensitiveWordMonitor },
  { name: "进度条测试", icon: Settings, component: TestProgressPage },
];

// 操作类型映射 - 使用设计系统颜色
const OPERATION_TYPE_MAP = {
  add: { label: "添加", color: "var(--primary-color)", bgColor: "#E9F8F1" },
  update: { label: "更新", color: "#1E90FF", bgColor: "#E6F3FF" },
  delete: { label: "删除", color: "#FF4747", bgColor: "#FFE6E6" },
  select: {
    label: "查询",
    color: "var(--text-secondary)",
    bgColor: "var(--hover-bg)",
  },
  login: { label: "登录", color: "#8A2BE2", bgColor: "#F0E6FF" },
  logout: { label: "登出", color: "#FF8C00", bgColor: "#FFF0E6" },
  send_code: { label: "发送验证码", color: "#FFD700", bgColor: "#FFFACD" },
};

// 模块映射
const MODULE_MAP = {
  account: "账户",
  advertiser: "广告主",
  project: "项目",
  promotion: "广告",
  proxy: "代理",
};

// 主页面组件
function MainPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedProject, setSelectedProject] = useState(null);
  const [showOperationLog, setShowOperationLog] = useState(false);
  const [operationLogs, setOperationLogs] = useState([]);
  const [expandedDebugLogId, setExpandedDebugLogId] = useState(null);

  // 调试：监控渲染次数
  const renderCount = React.useRef(0);
  renderCount.current += 1;
  // console.log(`MainPage 渲染次数: ${renderCount.current}`);

  // 使用全局账户状态
  const {
    sharedAccount,
    sharedSubAccount,
    setAccount,
    setSubAccount,
    clearAccounts,
    clearSubAccount,
  } = useAccountContext();

  // 使用策略预加载器
  const strategyPreloader = useStrategyPreloader();

  // 使用任务进度管理
  const { currentTask, isVisible: isTaskVisible } = useTaskProgress();

  // 监听操作日志事件
  useEffect(() => {
    const unsubscribe = Events.On("operation_log", (eventData) => {
      // console.log("接收到操作日志事件:", eventData);
      // console.log("数据类型:", typeof eventData);
      // console.log("数据结构:", JSON.stringify(eventData, null, 2));

      // 从事件数据中提取日志数组
      let logs = [];
      if (eventData && eventData.data && Array.isArray(eventData.data)) {
        logs = eventData.data;
      } else if (Array.isArray(eventData)) {
        logs = eventData;
      } else if (eventData) {
        // 如果是单个日志对象，包装成数组
        logs = [eventData];
      }

      // console.log("解析出的日志数组:", logs);

      if (logs.length > 0) {
        setOperationLogs((prevLogs) => {
          const newLogs = [...logs, ...prevLogs];
          // 保留最近100条日志
          return newLogs.slice(0, 100);
        });
      }
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // 监听菜单导航事件
  useEffect(() => {
    const unsubscribe = Events.On("navigate_to_sensitive_word_monitor", () => {
      // console.log("接收到导航到自动删评页面事件");
      setActiveTab(6); // 自动删评页面的索引
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // 格式化时间
  const formatTime = (timeStr) => {
    if (!timeStr) return "-";
    try {
      const date = new Date(timeStr);
      return date
        .toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/\//g, "-");
    } catch {
      return timeStr;
    }
  };

  // 清空日志
  const clearLogs = () => {
    setOperationLogs([]);
  };

  // 切换调试信息展开状态
  const toggleDebugInfo = (logId) => {
    setExpandedDebugLogId((prev) => {
      if (prev === logId) {
        return null;
      } else {
        return logId;
      }
    });
  };

  // 主页面的账户选择处理
  const handleMainAccountChange = React.useCallback(
    (account) => {
      console.log("主页面账户选择:", account);
      setAccount(account);
      // 清空子账户和项目
      clearSubAccount();
      setSelectedProject(null);
      // 不自动跳转页面，保持当前页面
    },
    [setAccount, clearSubAccount]
  );

  // 处理广告主选择并跳转到项目管理
  const handleSubAccountSelect = React.useCallback(
    (subAccount) => {
      console.log("subAccount", subAccount);
      setSubAccount(subAccount);
      setActiveTab(2); // 跳转到项目管理页面（索引为2）
    },
    [setSubAccount]
  );

  // 处理从账户管理页面导航到广告主管理页面
  const handleNavigateToAdvertiser = React.useCallback(
    (account) => {
      console.log("导航到广告主管理页面，账户:", account);
      // 设置选中的账户
      setAccount(account);
      // 清空子账户和项目
      clearSubAccount();
      setSelectedProject(null);
      // 跳转到广告主管理页面（索引为1）
      setActiveTab(1);
    },
    [setAccount, clearSubAccount]
  );

  // 处理项目选择并跳转到广告管理
  const handleProjectSelect = React.useCallback(
    (project) => {
      console.log("选中项目:", project);

      // 从项目信息中提取主账户ID，构建主账户信息
      // 注意：项目列表中不包含account_id，需要从其他地方获取
      // 这里我们使用当前已选择的主账户，如果没有则需要用户先选择账户
      if (!sharedAccount) {
        console.warn("没有选择主账户，无法跳转到广告管理");
        return;
      }

      // 确保主账户信息正确设置
      const account = {
        account_id: sharedAccount.account_id,
        account_name: sharedAccount.account_name,
        id: sharedAccount.id,
      };
      setAccount(account);

      // 从项目信息构建广告主信息
      const subAccount = {
        account_id: sharedAccount.account_id,
        advertiser_id: project.advertiser_id,
        advertiser_name:
          project.advertiser_name || `广告主(${project.advertiser_id})`,
        main_account_name: sharedAccount.account_name,
      };
      setSubAccount(subAccount);

      // 设置项目信息，传递给广告管理页面
      const projectWithAccountInfo = {
        ...project,
        accountId: sharedAccount.account_id,
        accountName: sharedAccount.account_name,
      };

      setSelectedProject(projectWithAccountInfo);
      setActiveTab(3); // 跳转到广告管理页面（索引为3）
    },
    [sharedAccount, setAccount, setSubAccount]
  );

  // 处理标签页切换
  const handleTabChange = React.useCallback(
    (index, props = {}) => {
      // 如果从项目管理或广告管理切换到账户管理，清空广告主和项目
      if ((activeTab === 2 || activeTab === 3) && index === 0) {
        clearAccounts();
        setSelectedProject(null);
      }

      setActiveTab(index);
    },
    [activeTab, clearAccounts]
  );



  // 优化：简化页面组件渲染逻辑
  const activeComponent = React.useMemo(() => {
    const ActiveComponent = TABS[activeTab]?.component;
    if (!ActiveComponent) return null;

    // 统一的组件props配置
    const componentProps = {
      0: {
        // 账户管理
        onAccountSelect: handleMainAccountChange,
        onNavigateToAdvertiser: handleNavigateToAdvertiser,
      },
      1: {
        // 广告主管理
        preSelectedAccount: sharedAccount,
        onSubAccountSelect: handleSubAccountSelect,
      },
      2: {
        // 项目管理
        preSelectedAccount: sharedAccount,
        preSelectedSubAccount: sharedSubAccount,
        onSubAccountSelect: handleSubAccountSelect,
        onProjectSelect: handleProjectSelect,
      },
      3: {
        // 广告管理
        preSelectedAccount: sharedAccount,
        selectedProject: selectedProject,
      },
      4: { onTabChange: handleTabChange }, // 策略管理
      5: {}, // 策略日志
      6: {}, // 自动删评
      7: {}, // 进度条测试
    };

    return <ActiveComponent {...(componentProps[activeTab] || {})} />;
  }, [
    activeTab,
    sharedAccount,
    sharedSubAccount,
    selectedProject,
    handleMainAccountChange,
    handleNavigateToAdvertiser,
    handleSubAccountSelect,
    handleProjectSelect,
    handleTabChange,
  ]);

  return (
    <div
      className="h-screen w-screen flex flex-col"
      style={{
        backgroundColor: "var(--sidebar-bg)",
        fontFamily: "Microsoft YaHei UI, Segoe UI, sans-serif",
      }}
    >
      {/* 导航栏 */}
      <div
        className="transition-wechat"
        style={{
          backgroundColor: "var(--sidebar-bg)",
          borderBottom: "1px solid var(--border-color)",
        }}
      >
        <div className="pr-6">
          <div className="flex items-center justify-between">
            {/* 左侧：标签页导航 */}
            <div className="flex items-center space-x-0">
              {TABS.map((tab, index) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={index}
                    className="sidebar-item flex items-center space-x-2 px-4 py-3 font-medium text-sm transition-wechat relative border-none outline-none"
                    style={{
                      backgroundColor:
                        index === activeTab ? "var(--panel-bg)" : "transparent",
                      color:
                        index === activeTab
                          ? "var(--primary-color)"
                          : "var(--text-primary)",
                      fontWeight: index === activeTab ? "600" : "500",
                      border: "none",
                      borderRadius: "4px 4px 0 0",
                    }}
                    onMouseEnter={(e) => {
                      if (index !== activeTab) {
                        e.currentTarget.style.backgroundColor =
                          "var(--hover-bg)";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (index !== activeTab) {
                        e.currentTarget.style.backgroundColor = "transparent";
                      }
                    }}
                    onClick={() => handleTabChange(index)}
                  >
                    <IconComponent size={16} />
                    <span>{tab.name}</span>
                    {index === activeTab && (
                      <div
                        className="absolute bottom-0 left-0 right-0 h-0.5"
                        style={{ backgroundColor: "var(--primary-color)" }}
                      ></div>
                    )}
                  </button>
                );
              })}
            </div>

            {/* 右侧：状态信息、账户选择器和用户信息 */}
            <div className="flex items-center space-x-4 text-sm">
              {/* 状态信息 */}
              <div className="flex items-center space-x-4">
                {sharedSubAccount && (
                  <div style={{ color: "var(--primary-color)" }}>
                    广告主: {sharedSubAccount.advertiser_name}
                  </div>
                )}
              </div>

              {/* 账户选择器 */}
              <AccountSelector
                selectedAccount={sharedAccount}
                onAccountChange={handleMainAccountChange}
                className="w-64"
              />

              {/* 用户信息 - 放在最右侧 */}
              <UserInfo className="ml-4" />
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 min-h-0 ">{activeComponent}</div>

        {/* 操作日志面板 */}
        {showOperationLog && (
          <Card
            className="h-80 flex flex-col"
            style={{
              borderTop: "1px solid var(--border-color)",
              borderRadius: "0",
              boxShadow: "0 -2px 8px rgba(0, 0, 0, 0.08)",
              flexShrink: 0,
            }}
          >
            <div
              className="px-4 py-2 flex items-center justify-between"
              style={{
                backgroundColor: "var(--sidebar-bg)",
                borderBottom: "1px solid var(--border-color)",
              }}
            >
              <div className="flex items-center space-x-2">
                <History size={16} style={{ color: "var(--text-secondary)" }} />
                <span
                  className="text-sm font-medium"
                  style={{ color: "var(--text-primary)" }}
                >
                  操作日志
                </span>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  ({operationLogs.length}条)
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={clearLogs}
                  className="text-xs px-2 py-1 rounded transition-wechat"
                  style={{
                    color: "var(--text-secondary)",
                    backgroundColor: "transparent",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                    e.currentTarget.style.color = "var(--text-primary)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                    e.currentTarget.style.color = "var(--text-secondary)";
                  }}
                >
                  清空
                </button>
              </div>
            </div>
            <div className="flex-1 overflow-auto">
              {operationLogs.length === 0 ? (
                <div
                  className="flex items-center justify-center h-full"
                  style={{ color: "var(--text-secondary)" }}
                >
                  <div className="text-center">
                    <History size={32} className="mx-auto mb-2 opacity-50" />
                    <p className="text-sm">暂无操作日志</p>
                  </div>
                </div>
              ) : (
                <div style={{ borderColor: "var(--border-color)" }}>
                  {operationLogs.map((log, index) => {
                    // 安全地获取字段值
                    const operationType =
                      log?.operation_type || log?.OperationType || "unknown";
                    const module = log?.module || log?.Module || "unknown";
                    const operation =
                      log?.operation || log?.Operation || "未知操作";
                    const description =
                      log?.description || log?.Description || "无描述";
                    const operationTime =
                      log?.operation_time ||
                      log?.OperationTime ||
                      log?.operation_time;
                    const accountName =
                      log?.account_name || log?.AccountName || "";
                    const accountId = log?.account_id || log?.AccountId || "";
                    const logId = log?.id || log?.Id || index;

                    const operationTypeConfig = OPERATION_TYPE_MAP[
                      operationType
                    ] || {
                      label: operationType,
                      color: "var(--text-secondary)",
                      bgColor: "var(--hover-bg)",
                    };
                    const moduleText = MODULE_MAP[module] || module;

                    return (
                      <div
                        key={logId}
                        className="px-4 py-2 transition-wechat"
                        style={{
                          borderBottom: "1px solid var(--border-color)",
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor =
                            "var(--hover-bg)";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = "transparent";
                        }}
                      >
                        <div className="flex items-center space-x-2 text-sm">
                          <Clock
                            size={12}
                            className="flex-shrink-0"
                            style={{ color: "var(--text-secondary)" }}
                          />
                          <span
                            className="text-xs flex-shrink-0 w-28 whitespace-nowrap"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            {formatTime(operationTime)}
                          </span>
                          {accountName && (
                            <span
                              className="text-xs flex-shrink-0"
                              style={{ color: "var(--primary-color)" }}
                            >
                              {accountName}
                            </span>
                          )}
                          <span
                            className="inline-flex items-center px-1.5 py-0.5 text-xs font-medium flex-shrink-0"
                            style={{
                              backgroundColor: operationTypeConfig.bgColor,
                              color: operationTypeConfig.color,
                              borderRadius: "4px",
                            }}
                          >
                            {operationTypeConfig.label}
                          </span>
                          <span
                            className="text-xs px-1.5 py-0.5 flex-shrink-0"
                            style={{
                              backgroundColor: "var(--hover-bg)",
                              color: "var(--text-secondary)",
                              borderRadius: "4px",
                            }}
                          >
                            {moduleText}
                          </span>
                          <span
                            className="font-medium flex-shrink-0"
                            style={{ color: "var(--text-primary)" }}
                          >
                            {operation}
                          </span>
                          <span style={{ color: "var(--text-secondary)" }}>
                            -
                          </span>
                          <div
                            className="truncate flex-1"
                            style={{ color: "var(--text-secondary)" }}
                          >
                            {description}
                          </div>
                          {/* 调试按钮放在右侧 */}
                          {process.env.NODE_ENV === "development" && (
                            <div
                              onClick={() => toggleDebugInfo(logId)}
                              className="flex items-center space-x-1 text-xs cursor-pointer flex-shrink-0 transition-wechat"
                              style={{ color: "var(--primary-color)" }}
                              title={
                                expandedDebugLogId === logId
                                  ? "收起调试信息"
                                  : "展开调试信息"
                              }
                              onMouseEnter={(e) => {
                                e.currentTarget.style.opacity = "0.8";
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.opacity = "1";
                              }}
                            >
                              {expandedDebugLogId === logId ? (
                                <ChevronDown size={12} />
                              ) : (
                                <ChevronRight size={12} />
                              )}
                              <span>调试信息</span>
                            </div>
                          )}
                        </div>
                        {/* 调试信息内容 - 展开时显示在下方 */}
                        {process.env.NODE_ENV === "development" &&
                          expandedDebugLogId === logId && (
                            <div
                              className="mt-2 p-3"
                              style={{
                                backgroundColor: "var(--sidebar-bg)",
                                border: "1px solid var(--border-color)",
                                borderRadius: "4px",
                              }}
                            >
                              <div
                                className="text-xs mb-2 font-medium"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                调试信息:
                              </div>
                              <pre
                                className="text-xs overflow-auto max-h-40 whitespace-pre-wrap"
                                style={{ color: "var(--text-primary)" }}
                              >
                                {JSON.stringify(log, null, 2)}
                              </pre>
                            </div>
                          )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </Card>
        )}
      </div>

      {/* 底部状态栏 */}
      <div
        className="px-6 py-2"
        style={{
          backgroundColor: "var(--panel-bg)",
          borderTop: "1px solid var(--border-color)",
        }}
      >
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <TimeDisplay />
            <span style={{ color: "var(--text-secondary)" }}>•</span>
            <span style={{ color: "var(--text-secondary)" }}>
              系统状态: 正常运行
            </span>
          </div>
          <div className="flex items-center space-x-4">
            {/* 任务进度条 - 放在操作日志按钮左侧 */}
            {isTaskVisible && currentTask && (
              <CompactTaskProgressBar
                taskData={currentTask}
              />
            )}
            
            <button
              onClick={() => setShowOperationLog(!showOperationLog)}
              className="flex items-center space-x-1 px-3 py-1 text-xs rounded transition-wechat"
              style={{
                color: "var(--text-secondary)",
                backgroundColor: "transparent",
              }}
              title={showOperationLog ? "隐藏操作日志" : "显示操作日志"}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                e.currentTarget.style.color = "var(--primary-color)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
                e.currentTarget.style.color = "var(--text-secondary)";
              }}
            >
              <History size={12} />
              <span>操作日志</span>
              {operationLogs.length > 0 && (
                <span
                  className="text-xs px-1.5 py-0.5 rounded-full min-w-[16px] text-center"
                  style={{
                    backgroundColor: "#E9F8F1",
                    color: "var(--primary-color)",
                  }}
                >
                  {operationLogs.length > 99 ? "99+" : operationLogs.length}
                </span>
              )}
              {showOperationLog ? (
                <ChevronDown size={12} />
              ) : (
                <ChevronUp size={12} />
              )}
            </button>
            <span style={{ color: "var(--text-secondary)" }}>版本: v1.0.0</span>
            <span style={{ color: "var(--text-secondary)" }}>•</span>
            <span style={{ color: "var(--text-secondary)" }}>
              © 2024 巨量引擎管理工具
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MainPage;
