import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, TestTube, RefreshCw, Download, Upload, Settings, Network, CheckCircle, XCircle, Clock, ToggleLeft, ToggleRight, Zap } from 'lucide-react';
import SearchInput from '../components/ui/SearchInput';

const ProxyManager = () => {
  const [proxies, setProxies] = useState([]);
  const [selectedProxies, setSelectedProxies] = useState([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [total, setTotal] = useState(0);
  
  const [formData, setFormData] = useState({
    ipAddress: '',
    port: '',
    username: '',
    password: '',
    type: 'HTTP',
    remark: '',
    status: 2
  });

  const [quickInput, setQuickInput] = useState(''); // 用于快速输入代理字符串

  const [importData, setImportData] = useState({
    content: '',
    format: 'ip:port:username:password'
  });

  // 代理类型选项
  const proxyTypes = [
    { value: 'HTTP', label: 'HTTP' },
    { value: 'HTTPS', label: 'HTTPS' },
    { value: 'SOCKS4', label: 'SOCKS4' },
    { value: 'SOCKS5', label: 'SOCKS5' }
  ];

  // 状态选项
  const statusOptions = [
    { value: 2, label: '启用', color: 'text-green-600 bg-green-50 border-green-200' },
    { value: 1, label: '禁用', color: 'text-red-600 bg-red-50 border-red-200' },
    { value: 0, label: '测试中', color: 'text-blue-600 bg-blue-50 border-blue-200' }
  ];

  // 解析代理字符串
  const parseProxyString = (proxyString) => {
    if (!proxyString || !proxyString.trim()) {
      return null;
    }

    const trimmed = proxyString.trim();
    
    try {
      // 格式1: URL格式 - socks5://username:password@host:port
      if (trimmed.includes('://')) {
        const url = new URL(trimmed);
        const type = url.protocol.replace(':', '').toUpperCase();
        const host = url.hostname;
        const port = url.port;
        const username = url.username || '';
        const password = url.password || '';
        
        return {
          ipAddress: host,
          port: port,
          username: username,
          password: password,
          type: type,
          remark: `从URL解析: ${trimmed}`
        };
      }
      
      // 格式2: 冒号分隔格式 - host:port:username:password:date:remark
      const parts = trimmed.split(':');
      if (parts.length >= 2) {
        const result = {
          ipAddress: parts[0],
          port: parts[1],
          username: parts[2] || '',
          password: parts[3] || '',
          type: 'HTTP', // 默认类型
          remark: ''
        };
        
        // 如果有更多部分，构建备注信息
        if (parts.length > 4) {
          const extraParts = parts.slice(4); // 从第5个部分开始的所有内容
          result.remark = extraParts.join(':'); // 用冒号重新连接，保持原始格式
        }
        
        return result;
      }
    } catch (error) {
      console.error('解析代理字符串失败:', error);
    }
    
    return null;
  };

  // 应用解析结果到表单
  const handleQuickParse = () => {
    const parsed = parseProxyString(quickInput);
    if (parsed) {
      setFormData(prev => ({
        ...prev,
        ...parsed,
        status: prev.status // 保持当前状态设置
      }));
      setQuickInput(''); // 清空输入框
      
      // 显示解析成功的提示
      console.log('代理字符串解析成功:', parsed);
    } else {
      alert('无法解析代理字符串，请检查格式是否正确\n\n支持的格式：\n1. URL格式：socks5://username:password@host:port\n2. 冒号分隔：host:port:username:password:date:remark');
    }
  };

  // 获取代理列表
  const fetchProxies = async () => {
    setLoading(true);
    try {
      const { ProxyService, handleResult } = await import('../services/api.js');
      const result = await ProxyService.GetProxyList({
        page: 0,
        pageSize: 0,
        ipAddress: searchTerm,
        type: '',
        status: 0,
        remark: ''
      });
      
      const data = handleResult(result);
      if (data && data.data) {
        // 转换后端数据格式到前端格式
        const proxyList = data.data.list.map(proxy => ({
          id: proxy.id,
          ipAddress: proxy.ip_address,
          port: proxy.port,
          username: proxy.username,
          password: proxy.password,
          type: proxy.type,
          status: proxy.status,
          remark: proxy.remark,
          addTime: proxy.add_time || new Date().toLocaleString()
        }));
        
        setProxies(proxyList);
        setTotal(data.data.total);
      }
    } catch (error) {
      console.error('获取代理列表失败:', error);
      // 如果API调用失败，使用模拟数据作为后备
      const mockData = [
        {
          id: 1,
          ipAddress: '*************',
          port: 8080,
          username: 'user1',
          password: 'pass1',
          type: 'HTTP',
          status: 1,
          remark: '主要代理服务器',
          addTime: '2024-01-01 09:00:00'
        },
        {
          id: 2,
          ipAddress: '*************',
          port: 1080,
          username: 'user2',
          password: 'pass2',
          type: 'SOCKS5',
          status: 0,
          remark: '备用代理服务器',
          addTime: '2024-01-02 14:20:00'
        }
      ];
      setProxies(mockData);
      setTotal(mockData.length);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProxies();
  }, [searchTerm]);

  // 过滤代理
  const filteredProxies = proxies.filter(proxy =>
    proxy.ipAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
    proxy.remark.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 添加代理
  const handleAdd = () => {
    setFormData({
      ipAddress: '',
      port: '',
      username: '',
      password: '',
      type: 'HTTP',
      remark: '',
      status: 2
    });
    setQuickInput(''); // 清空快速输入
    setShowAddDialog(true);
  };

  // 编辑代理
  const handleEdit = (proxyId) => {
    // 如果不传ID，则使用已选中的代理
    if (!proxyId && selectedProxies.length !== 1) return;
    
    const id = proxyId || selectedProxies[0];
    const proxy = proxies.find(p => p.id === id);
    
    setFormData({
      id: proxy.id,
      ipAddress: proxy.ipAddress,
      port: proxy.port.toString(),
      username: proxy.username,
      password: proxy.password,
      type: proxy.type,
      remark: proxy.remark,
      status: proxy.status
    });
    setQuickInput(''); // 清空快速输入
    setShowEditDialog(true);
  };

  // 删除代理
  const handleDelete = (proxyId) => {
    // 如果不传ID，则使用已选中的代理
    if (!proxyId && selectedProxies.length === 0) return;
    
    // 如果传入了proxyId，则设置为当前选中的代理
    if (proxyId) {
      setSelectedProxies([proxyId]);
    }
    
    setShowDeleteDialog(true);
  };

  // 保存代理
  const handleSave = async () => {
    try {
      const { ProxyService, handleResult } = await import('../services/api.js');
      
      if (showAddDialog) {
        // 调用后端API创建代理
        const result = await ProxyService.CreateProxy(formData);
        // 检查返回结果
        if (result && result.code !== 0) {
          throw new Error(result.msg || '创建失败');
        }
        
        // 重新获取列表
        await fetchProxies();
      } else if (showEditDialog) {
        // 调用后端API更新代理
        const result = await ProxyService.UpdateProxy(formData);
        // 检查返回结果
        if (result && result.code !== 0) {
          throw new Error(result.msg || '更新失败');
        }
        
        // 重新获取列表
        await fetchProxies();
      }
      
      setShowAddDialog(false);
      setShowEditDialog(false);
    } catch (error) {
      console.error('保存代理失败:', error);
      alert('操作失败: ' + (error.message || '未知错误'));
      
      // 保持对话框打开状态，让用户可以修改错误数据
      // 不关闭对话框
    }
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    try {
      const { ProxyService, handleResult } = await import('../services/api.js');
      
      if (selectedProxies.length === 1) {
        // 单个删除
        const result = await ProxyService.DeleteProxy(selectedProxies[0]);
        handleResult(result);
      } else {
        // 批量删除
        const result = await ProxyService.BatchDeleteProxies(selectedProxies);
        handleResult(result);
      }
      
      // 重新获取列表
      await fetchProxies();
      setSelectedProxies([]);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('删除代理失败:', error);
      // 如果API调用失败，使用模拟逻辑作为后备
      setProxies(proxies.filter(proxy => !selectedProxies.includes(proxy.id)));
      setSelectedProxies([]);
      setShowDeleteDialog(false);
    }
  };

  // 移除测试代理函数，保留函数结构但改为空函数以避免影响其他代码
  const handleTestProxy = async () => {};
  const handleTestAll = async () => {};
  
  // 切换代理状态
  const handleToggleProxy = async (proxyId, currentStatus) => {
    try {
      const newStatus = currentStatus === 2 ? 1 : 2; // 如果是启用(2)则改为禁用(1)，反之亦然
      
      const { ProxyService, handleResult } = await import('../services/api.js');
      
      // 调用后端API更新代理状态
      const proxy = proxies.find(p => p.id === proxyId);
      const updatedProxy = {
        ...proxy,
        status: newStatus
      };
      
      const result = await ProxyService.UpdateProxy(updatedProxy);
      // 检查返回结果
      if (result && result.code !== 0) {
        throw new Error(result.msg || '状态切换失败');
      }
      
      // 重新获取列表
      await fetchProxies();
    } catch (error) {
      console.error('切换代理状态失败:', error);
      alert('操作失败: ' + (error.message || '未知错误'));
      
      // 如果API调用失败，使用本地状态更新作为后备
      setProxies(proxies.map(proxy => 
        proxy.id === proxyId 
          ? { ...proxy, status: proxy.status === 2 ? 1 : 2 }
          : proxy
      ));
    }
  };
  
  // 导入代理
  const handleImport = async () => {
    try {
      const { ProxyService, handleResult } = await import('../services/api.js');
      
      // 调用后端API批量导入
      const result = await ProxyService.ImportProxies({
        content: importData.content,
        format: importData.format
      });
      handleResult(result);
      
      // 重新获取列表
      await fetchProxies();
      setShowImportDialog(false);
      setImportData({ content: '', format: 'ip:port:username:password' });
    } catch (error) {
      console.error('导入代理失败:', error);
      // 如果API调用失败，使用模拟逻辑作为后备
      const lines = importData.content.split('\n').filter(line => line.trim());
      const importedProxies = [];
      
      lines.forEach(line => {
        const parts = line.trim().split(':');
        if (parts.length >= 2) {
          importedProxies.push({
            ipAddress: parts[0],
            port: parts[1],
            username: parts[2] || '',
            password: parts[3] || '',
            type: 'HTTP',
            remark: '批量导入',
            status: 2
          });
        }
      });
      
      const newProxies = importedProxies.map((proxy, index) => ({
        ...proxy,
        id: Date.now() + index,
        port: parseInt(proxy.port),
        addTime: new Date().toLocaleString()
      }));
      
      setProxies([...proxies, ...newProxies]);
      setShowImportDialog(false);
      setImportData({ content: '', format: 'ip:port:username:password' });
    }
  };

  // 导出代理
  const handleExport = () => {
    const exportData = proxies.map(proxy => 
      `${proxy.ipAddress}:${proxy.port}:${proxy.username}:${proxy.password}`
    ).join('\n');
    
    const blob = new Blob([exportData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'proxies.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  // 选择代理
  const handleSelectProxy = (proxyId) => {
    setSelectedProxies(prev => {
      if (prev.includes(proxyId)) {
        return prev.filter(id => id !== proxyId);
      } else {
        return [...prev, proxyId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedProxies.length === filteredProxies.length) {
      setSelectedProxies([]);
    } else {
      setSelectedProxies(filteredProxies.map(proxy => proxy.id));
    }
  };

  // 获取状态样式
  const getStatusStyle = (status) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option ? option.color : 'text-gray-600 bg-gray-50 border-gray-200';
  };

  // 获取状态图标
  const getStatusIcon = (status) => {
    switch (status) {
      case 2: return <CheckCircle size={16} className="text-green-600" />;
      case 1: return <XCircle size={16} className="text-red-600" />;
      case 0: return <Clock size={16} className="text-blue-600" />;
      default: return <XCircle size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* 工具栏 */}
      <div className="border-b border-slate-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleAdd}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} />
              <span>添加代理</span>
            </button>
            <button
              onClick={handleDelete}
              disabled={selectedProxies.length === 0}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <Trash2 size={16} />
              <span>删除</span>
            </button>
          </div>
          
          <div className="flex items-center space-x-3">
            <SearchInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索IP地址"
              className="w-64"
              showClearButton={true}
              onClear={() => setSearchTerm('')}
            />
            <button
              onClick={fetchProxies}
              disabled={loading}
              className="p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded transition-colors"
            >
              <RefreshCw size={18} className={loading ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="border-b border-slate-200 p-4 bg-slate-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Network size={16} className="text-blue-600" />
              <span className="text-sm text-slate-600">总数: {proxies.length}</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle size={16} className="text-green-600" />
              <span className="text-sm text-slate-600">可用: {proxies.filter(p => p.status === 2).length}</span>
            </div>
            <div className="flex items-center space-x-2">
              <XCircle size={16} className="text-red-600" />
              <span className="text-sm text-slate-600">不可用: {proxies.filter(p => p.status === 1).length}</span>
            </div>
            {selectedProxies.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-blue-600">已选择: {selectedProxies.length}</span>
              </div>
            )}
          </div>
          <div className="text-sm text-slate-500">
            显示 {filteredProxies.length} / {proxies.length} 个代理
          </div>
        </div>
      </div>

      {/* 代理列表 */}
      <div className="flex-1 overflow-auto">
        <table className="w-full">
          <thead className="bg-slate-50 sticky top-0">
            <tr>
              <th className="px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedProxies.length === filteredProxies.length && filteredProxies.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">状态</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">代理地址</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">类型</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">账号</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">密码</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">备注</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {filteredProxies.map((proxy) => (
              <tr key={proxy.id} className="hover:bg-slate-50 transition-colors">
                <td className="px-4 py-4">
                  <input
                    type="checkbox"
                    checked={selectedProxies.includes(proxy.id)}
                    onChange={() => handleSelectProxy(proxy.id)}
                    className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(proxy.status)}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusStyle(proxy.status)}`}>
                      {statusOptions.find(opt => opt.value === proxy.status)?.label}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm font-mono text-slate-900">
                    {proxy.ipAddress}:{proxy.port}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {proxy.type}
                  </span>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm text-slate-600">
                    {proxy.username || '-'}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm text-slate-600">
                    {proxy.password ? '******' : '-'}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm text-slate-600 max-w-32 truncate" title={proxy.remark}>
                    {proxy.remark || '-'}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleToggleProxy(proxy.id, proxy.status)}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                        proxy.status === 2 
                          ? 'text-amber-600 hover:text-amber-800 bg-amber-50 hover:bg-amber-100' 
                          : 'text-emerald-600 hover:text-emerald-800 bg-emerald-50 hover:bg-emerald-100'
                      }`}
                      title={proxy.status === 2 ? '点击关闭代理' : '点击开启代理'}
                    >
                      {proxy.status === 2 ? '关闭' : '开启'}
                    </button>
                    <span className="text-slate-300">|</span>
                    <button
                      onClick={() => handleEdit(proxy.id)}
                      className="text-emerald-600 hover:text-emerald-800 text-sm font-medium"
                    >
                      编辑
                    </button>
                    <span className="text-slate-300">|</span>
                    <button
                      onClick={() => handleDelete(proxy.id)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredProxies.length === 0 && (
          <div className="text-center py-12">
            <Network size={48} className="mx-auto text-slate-400 mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">暂无代理</h3>
            <p className="text-slate-500 mb-4">开始添加您的第一个代理服务器</p>
            <button
              onClick={handleAdd}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} />
              <span>添加代理</span>
            </button>
          </div>
        )}
      </div>

      {/* 添加/编辑对话框 */}
      {(showAddDialog || showEditDialog) && (
        <div 
          className="fixed inset-0 flex items-center justify-center z-50" 
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowAddDialog(false);
              setShowEditDialog(false);
              setQuickInput('');
            }
          }}
        >
          <div className="bg-white rounded shadow-xl w-[500px] max-h-[90vh] overflow-y-auto border border-slate-200 relative">
            <div className="px-6 py-4 border-b border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900">
                {showAddDialog ? '添加代理' : '编辑代理'}
              </h3>
            </div>
            <div className="px-6 py-4 space-y-4">
              {/* 快速输入区域 */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <label className="block text-sm font-medium text-blue-800 mb-2">
                  <Zap className="inline w-4 h-4 mr-1" />
                  快速输入代理字符串
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={quickInput}
                    onChange={(e) => setQuickInput(e.target.value)}
                    className="flex-1 px-3 py-2 border border-blue-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                    placeholder="支持格式: socks5://user:pass@host:port 或 host:port:user:pass:date:remark"
                  />
                  <button
                    type="button"
                    onClick={handleQuickParse}
                    disabled={!quickInput.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
                  >
                    解析
                  </button>
                </div>
                <div className="mt-2 text-xs text-blue-600">
                  <p>支持格式：</p>
                  <p>• URL格式：socks5://username:password@host:port</p>
                  <p>• 冒号分隔：host:port:username:password:date:remark</p>
                </div>
              </div>

              {/* 分隔线 */}
              <div className="flex items-center">
                <div className="flex-1 border-t border-gray-200"></div>
                <div className="px-4 text-sm text-gray-500">或手动填写</div>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">IP地址</label>
                <input
                  type="text"
                  value={formData.ipAddress}
                  onChange={(e) => setFormData({ ...formData, ipAddress: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                  placeholder="*************"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">端口</label>
                <input
                  type="number"
                  value={formData.port}
                  onChange={(e) => setFormData({ ...formData, port: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                  placeholder="8080"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">类型</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                >
                  {proxyTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">用户名</label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                  placeholder="可选"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">密码</label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                  placeholder="可选"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">备注</label>
                <input
                  type="text"
                  value={formData.remark}
                  onChange={(e) => setFormData({ ...formData, remark: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                  placeholder="代理服务器描述"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">状态</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                >
                  {statusOptions.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="px-6 py-4 border-t border-slate-200 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowAddDialog(false);
                  setShowEditDialog(false);
                  setQuickInput(''); // 清空快速输入
                }}
                className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteDialog && (
        <div 
          className="fixed inset-0 flex items-center justify-center z-50" 
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowDeleteDialog(false);
            }
          }}
        >
          <div className="bg-white rounded shadow-xl w-96 border border-slate-200 relative">
            <div className="px-6 py-4 border-b border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900">确认删除</h3>
            </div>
            <div className="px-6 py-4">
              <p className="text-slate-600">
                确定要删除选中的 {selectedProxies.length} 个代理吗？此操作不可撤销。
              </p>
            </div>
            <div className="px-6 py-4 border-t border-slate-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteDialog(false)}
                className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 导入对话框 */}
      {showImportDialog && (
        <div 
          className="fixed inset-0 flex items-center justify-center z-50" 
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowImportDialog(false);
              setImportData({ content: '', format: 'ip:port:username:password' });
            }
          }}
        >
          <div className="bg-white rounded shadow-xl w-[600px] max-h-[90vh] overflow-y-auto border border-slate-200 relative">
            <div className="px-6 py-4 border-b border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900">批量导入代理</h3>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">格式</label>
                <select
                  value={importData.format}
                  onChange={(e) => setImportData({ ...importData, format: e.target.value })}
                  className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                >
                  <option value="ip:port:username:password">IP:端口:用户名:密码</option>
                  <option value="ip:port">IP:端口</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">代理列表</label>
                <textarea
                  value={importData.content}
                  onChange={(e) => setImportData({ ...importData, content: e.target.value })}
                  className="w-full h-64 px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none resize-none"
                  placeholder="每行一个代理，格式如下：&#10;*************:8080:username:password&#10;*************:1080:user2:pass2"
                />
              </div>
              <div className="text-sm text-slate-500">
                <p>• 每行一个代理服务器</p>
                <p>• 支持格式：IP:端口:用户名:密码 或 IP:端口</p>
                <p>• 用户名和密码可选，留空表示无认证</p>
              </div>
            </div>
            <div className="px-6 py-4 border-t border-slate-200 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowImportDialog(false);
                  setImportData({ content: '', format: 'ip:port:username:password' });
                }}
                className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleImport}
                disabled={!importData.content.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                导入
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProxyManager; 