import React, { useState, useEffect, useCallback } from "react";
import {
  Shield,
  Play,
  Pause,
  RefreshCw,
  Settings,
  Search,
  Trash2,
  Alert<PERSON>riangle,
  Save,
  Eye,
} from "lucide-react";
import {
  Button,
  Card,
  Switch,
  Input,
  Tag,
  DataTable,
  Toast,
  Pagination,
} from "../../components/ui";
import {
  sensitiveWordService,
  sensitiveWordMonitorService,
} from "../../services/sensitiveWordMonitorService";

const SensitiveWordMonitor = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [monitorStatus, setMonitorStatus] = useState({
    running: false,
    interval: "5m0s",
  });
  const [sensitiveWords, setSensitiveWords] = useState("");
  const [deletedComments, setDeletedComments] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 初始化
  useEffect(() => {
    loadInitialData();
  }, []);

  // 监听分页和搜索参数变化，重新加载删除评论列表
  useEffect(() => {
    loadDeletedComments();
  }, [currentPage, pageSize]);

  // 加载初始数据
  const loadInitialData = useCallback(async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadMonitorStatus(),
        loadSensitiveWords(),
        loadDeletedComments(),
      ]);
    } catch (error) {
      Toast.error("加载数据失败: " + error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载监控状态
  const loadMonitorStatus = useCallback(async () => {
    try {
      const status = await sensitiveWordMonitorService.getMonitorStatus();
      setMonitorStatus(status);
    } catch (error) {
      console.error("加载监控状态失败:", error);
    }
  }, []);

  // 加载敏感词配置
  const loadSensitiveWords = useCallback(async () => {
    try {
      const words = await sensitiveWordService.getSensitiveWords();
      setSensitiveWords(words || "");
    } catch (error) {
      console.error("加载敏感词配置失败:", error);
    }
  }, []);

  // 加载删除评论列表
  const loadDeletedComments = useCallback(async () => {
    try {
      const params = {
        page: currentPage,
        page_size: pageSize,
      };
      const result = await sensitiveWordService.getDeletedComments(params);
      setDeletedComments(result.list || []);
      setTotal(result.total || 0);
    } catch (error) {
      console.error("加载删除评论列表失败:", error);
    }
  }, [currentPage, pageSize]);

  // 启动监控
  const handleStartMonitor = useCallback(async () => {
    try {
      setLoading(true);

      // 检查是否有敏感词配置
      if (
        monitorStatus.sensitive_words &&
        !monitorStatus.sensitive_words.configured
      ) {
        Toast.error("请先配置敏感词，再启动监控");
        return;
      }

      await sensitiveWordMonitorService.startMonitor();
      await loadMonitorStatus();
      Toast.success("监控服务启动成功");
    } catch (error) {
      Toast.error("启动监控失败: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [loadMonitorStatus, monitorStatus.sensitive_words]);

  // 停止监控
  const handleStopMonitor = useCallback(async () => {
    try {
      setLoading(true);
      await sensitiveWordMonitorService.stopMonitor();
      await loadMonitorStatus();
      Toast.success("监控服务停止成功");
    } catch (error) {
      Toast.error("停止监控失败: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [loadMonitorStatus]);

  // 手动检查
  const handleManualCheck = useCallback(async () => {
    try {
      setLoading(true);
      await sensitiveWordMonitorService.triggerManualCheck();
      Toast.success("手动检查已触发");
    } catch (error) {
      Toast.error("手动检查失败: " + error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // 保存敏感词配置
  const handleSaveSensitiveWords = useCallback(async () => {
    try {
      setLoading(true);
      await sensitiveWordService.setSensitiveWords(sensitiveWords);
      await loadMonitorStatus(); // 重新加载监控状态以更新敏感词配置状态
      Toast.success("敏感词配置保存成功");
    } catch (error) {
      Toast.error("保存敏感词配置失败: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [sensitiveWords, loadMonitorStatus]);

  // 分页处理
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  // 页面大小变化处理
  const handlePageSizeChange = useCallback((newPageSize, newCurrentPage) => {
    setPageSize(newPageSize);
    setCurrentPage(newCurrentPage);
  }, []);

  // 表格列定义
  const columns = [
    {
      key: "comment_id",
      title: "评论ID",
      width: 140,
      render: (item) => (
        <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
          {item.comment_id}
        </span>
      ),
    },
    {
      key: "comment_text",
      title: "评论内容",
      width: 500,
      render: (item) => (
        <div className="max-w-md break-words" title={item.comment_text}>
          {item.comment_text}
        </div>
      ),
    },
    {
      key: "sensitive_word",
      title: "敏感词",
      width: 120,
      render: (item) => (
        <Tag color="red" className="text-xs">
          {item.sensitive_word}
        </Tag>
      ),
    },
    {
      key: "account_name",
      title: "账户",
      width: 120,
      render: (item) => (
        <span className="font-medium">{item.account_name}</span>
      ),
    },
    {
      key: "project_name",
      title: "项目",
      width: 150,
      render: (item) => (
        <span className="font-medium">{item.project_name}</span>
      ),
    },
    {
      key: "promotion_name",
      title: "推广计划",
      width: 150,
      render: (item) => (
        <span className="font-medium">{item.promotion_name}</span>
      ),
    },
    {
      key: "delete_time",
      title: "删除时间",
      width: 180,
      render: (item) => (
        <span className="text-sm text-gray-500 font-mono">
          {new Date(item.delete_time).toLocaleString()}
        </span>
      ),
    },
  ];

  return (
    <div
      className="w-full flex flex-col h-full overflow-hidden"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <Card className="flex flex-col h-full">
        {/* 监控控制区域 */}
        <div
          className="p-4 border-b flex-shrink-0"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={monitorStatus.running}
                  onChange={
                    monitorStatus.running
                      ? handleStopMonitor
                      : handleStartMonitor
                  }
                  disabled={
                    loading ||
                    (monitorStatus.sensitive_words &&
                      !monitorStatus.sensitive_words.configured)
                  }
                />
                <span
                  className={`font-medium text-sm px-2 py-1 rounded-full ${
                    monitorStatus.running
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  }`}
                >
                  {monitorStatus.running ? "监控运行中" : "监控已停止"}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <RefreshCw size={14} className="text-gray-500" />
                <span className="text-sm text-gray-600">
                  间隔: {monitorStatus.interval}
                </span>
              </div>

              {/* 敏感词配置状态提示 */}
              {monitorStatus.sensitive_words &&
                !monitorStatus.sensitive_words.configured && (
                  <div className="flex items-center space-x-1">
                    <AlertTriangle size={12} className="text-orange-500" />
                    <span className="text-xs text-orange-600">
                      未配置敏感词，监控不会执行
                    </span>
                  </div>
                )}
            </div>
            <Button
              onClick={handleManualCheck}
              disabled={
                loading ||
                !monitorStatus.running ||
                (monitorStatus.sensitive_words &&
                  !monitorStatus.sensitive_words.configured)
              }
              loading={loading}
              size="small"
              icon={<RefreshCw size={14} />}
            >
              手动检查
            </Button>
          </div>
        </div>

        {/* 页面标题和功能区域 */}
        <div
          className="p-3 border-b flex-shrink-0"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="mb-3 text-center">
            <Shield size={24} className="mx-auto mb-1 text-red-500" />
            <h2
              className="text-base font-bold mb-1"
              style={{ color: "var(--text-primary)" }}
            >
              自动删评管理
            </h2>
            <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
              监控项目评论，自动删除包含敏感词的评论
            </p>
          </div>

          {/* 功能卡片区域 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-w-4xl mx-auto">
            {/* 监控控制 */}
            <div
              className="p-2 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onClick={
                monitorStatus.running ? handleStopMonitor : handleStartMonitor
              }
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              {monitorStatus.running ? (
                <Pause size={16} className="mx-auto mb-1 text-red-500" />
              ) : (
                <Play size={16} className="mx-auto mb-1 text-green-500" />
              )}
              <h3
                className="font-medium mb-1 text-center text-xs"
                style={{ color: "var(--text-primary)" }}
              >
                {monitorStatus.running ? "停止监控" : "启动监控"}
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                {monitorStatus.running
                  ? "点击停止自动删评监控"
                  : "点击启动自动删评监控"}
              </p>
            </div>

            {/* 敏感词配置 */}
            <div
              className="p-2 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              <Settings size={16} className="mx-auto mb-1 text-blue-500" />
              <h3
                className="font-medium mb-1 text-center text-xs"
                style={{ color: "var(--text-primary)" }}
              >
                敏感词配置
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                配置需要监控的敏感词
              </p>
            </div>

            {/* 删除记录 */}
            <div
              className="p-2 border rounded-lg cursor-pointer hover:shadow-md transition-all"
              style={{ borderColor: "var(--border-color)" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "var(--hover-bg)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              <Trash2 size={16} className="mx-auto mb-1 text-orange-500" />
              <h3
                className="font-medium mb-1 text-center text-xs"
                style={{ color: "var(--text-primary)" }}
              >
                删除记录
              </h3>
              <p
                className="text-xs text-center"
                style={{ color: "var(--text-secondary)" }}
              >
                查看已删除的评论记录
              </p>
            </div>
          </div>
        </div>

        {/* 敏感词配置区域 */}
        <div
          className="p-3 border-b flex-shrink-0"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <AlertTriangle size={14} className="text-orange-500" />
              <h3
                className="text-sm font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                敏感词配置
              </h3>
            </div>

            {/* 敏感词配置状态 */}
            {monitorStatus.sensitive_words && (
              <div className="flex items-center space-x-2">
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    monitorStatus.sensitive_words.configured
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  }`}
                >
                  {monitorStatus.sensitive_words.configured
                    ? `已配置 (${monitorStatus.sensitive_words.word_count}个敏感词)`
                    : "未配置敏感词"}
                </span>
                {monitorStatus.sensitive_words.error && (
                  <span className="text-xs text-red-500">
                    配置错误: {monitorStatus.sensitive_words.error}
                  </span>
                )}
              </div>
            )}
          </div>

          <div className="max-w-2xl">
            <div className="mb-2">
              <label
                className="block text-sm font-medium mb-1"
                style={{ color: "var(--text-primary)" }}
              >
                敏感词列表
              </label>
              <textarea
                value={sensitiveWords}
                onChange={(e) => setSensitiveWords(e.target.value)}
                placeholder="请输入敏感词，多个敏感词用逗号分隔，例如：敏感词1,敏感词2,敏感词3"
                rows={2}
                className="w-full p-2 border rounded-lg resize-vertical"
                style={{
                  backgroundColor: "var(--sidebar-bg)",
                  borderColor: "var(--border-color)",
                  color: "var(--text-primary)",
                }}
              />
              <p
                className="text-xs mt-1"
                style={{ color: "var(--text-secondary)" }}
              >
                多个敏感词请用逗号分隔，系统将自动检测包含这些敏感词的评论并删除
              </p>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={handleSaveSensitiveWords}
                disabled={loading}
                loading={loading}
                icon={<Save size={12} />}
                size="small"
              >
                保存配置
              </Button>
            </div>
          </div>
        </div>

        {/* 删除记录列表区域 */}
        <div className="p-3 flex-1 min-h-0 flex flex-col">
          <div className="flex items-center space-x-2 mb-2 flex-shrink-0">
            <Eye size={14} className="text-blue-500" />
            <h3
              className="text-sm font-semibold"
              style={{ color: "var(--text-primary)" }}
            >
              删除记录
            </h3>
            <span className="text-xs text-gray-500">({total} 条记录)</span>
          </div>

          <div className="bg-white rounded-lg border overflow-hidden shadow-sm flex flex-col flex-1 min-h-0">
            <div className="flex-1 min-h-0 overflow-auto">
              <DataTable
                data={deletedComments}
                columns={columns}
                loading={loading}
                rowKey="id"
                className="h-full"
              />
            </div>

            {/* 分页组件 */}
            <div
              className="border-t p-2 flex-shrink-0"
              style={{ borderColor: "var(--border-color)" }}
            >
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                onChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                showSizeChanger={true}
                showQuickJumper={true}
                showTotal={true}
                pageSizeOptions={[10, 20, 50, 100]}
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SensitiveWordMonitor;
