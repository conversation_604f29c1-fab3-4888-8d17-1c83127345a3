import React, { useState, useEffect } from "react";
import {
  Card,
  DataTable,
  SearchInput,
  Button,
  StatusBadge,
  Toolbar,
  Pagination,
} from "../../components/ui";
import { toast } from "../../components/ui/Toast";
import strategyLogService from "../../services/strategyLogService";
import { formatDate } from "../../utils";
import {
  Search,
  RefreshCw,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  Filter,
  Download,
  Eye,
  Info,
  X,
} from "lucide-react";

/**
 * 策略日志页面
 * 显示策略执行日志列表
 */
const StrategyLogPage = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [searchValue, setSearchValue] = useState("");
  const [filters, setFilters] = useState({
    strategyName: "",
    targetType: "",
    targetID: "",
    conditionMet: null,
    actionCompleted: null,
  });

  // 统计数据
  const [stats, setStats] = useState({
    totalExecutions: 0,
    successCount: 0,
    failureCount: 0,
    conditionMetCount: 0,
    conditionNotMetCount: 0,
  });

  // 实时更新相关状态
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5秒刷新间隔
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const [isUpdating, setIsUpdating] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 详情对话框状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);

  // 表格列定义
  const columns = [
    {
      key: "strategy_name",
      title: "策略名称",
      render: (value, item) => (
        <div className="flex items-center space-x-2">
          <FileText size={14} style={{ color: "var(--primary-color)" }} />
          <span
            className="font-medium"
            style={{ color: "var(--text-primary)" }}
          >
            {value || ""}
          </span>
        </div>
      ),
    },
    {
      key: "target_type",
      title: "目标类型",
      width: 100,
      render: (value, item) => {
        const typeMap = {
          advertiser: "广告主",
          project: "项目",
          promotion: "广告",
          global: "全局",
        };
        const typeColors = {
          advertiser: "rgba(156, 39, 176, 0.1)",
          project: "rgba(33, 150, 243, 0.1)",
          promotion: "rgba(76, 175, 80, 0.1)",
          global: "rgba(255, 152, 0, 0.1)",
        };
        const typeTextColors = {
          advertiser: "#9c27b0",
          project: "#2196f3",
          promotion: "#4caf50",
          global: "#ff9800",
        };

        return (
          <span
            className="px-2 py-1 rounded text-xs font-medium"
            style={{
              backgroundColor: typeColors[value] || "rgba(158, 158, 158, 0.1)",
              color: typeTextColors[value] || "#9e9e9e",
            }}
          >
            {typeMap[value] || value || ""}
          </span>
        );
      },
    },
    {
      key: "advertiser_name",
      title: "广告主",
      width: 150,
      render: (value, item) => (
        <span style={{ color: "var(--text-primary)" }}>{value || "-"}</span>
      ),
    },
    {
      key: "project_name",
      title: "项目名称",
      render: (value, item) => (
        <span style={{ color: "var(--text-primary)" }}>{value || "-"}</span>
      ),
    },
    {
      key: "promotion_name",
      title: "广告名称",
      render: (value, item) => (
        <span style={{ color: "var(--text-primary)" }}>{value || "-"}</span>
      ),
    },
    {
      key: "target_name",
      title: "目标名称",
      width: 150,
      render: (value, item) => (
        <span style={{ color: "var(--text-primary)" }}>{value || "-"}</span>
      ),
    },
    {
      key: "condition_met",
      title: "条件评估",
      width: 140,
      render: (value, item) => (
        <div className="flex items-center space-x-2">
          {value ? (
            <CheckCircle
              size={16}
              style={{ color: "var(--success-color, #52C41A)" }}
            />
          ) : (
            <XCircle
              size={16}
              style={{ color: "var(--error-color, #FF4D4F)" }}
            />
          )}
          <div className="flex flex-col">
            <span
              className="text-sm font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              {value ? "条件满足" : "条件不满足"}
            </span>
            <span
              className="text-xs"
              style={{ color: "var(--text-secondary)" }}
            >
              {value ? "策略将被执行" : "跳过执行"}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: "action_completed",
      title: "执行结果",
      width: 140,
      render: (value, item) => (
        <div className="flex items-center space-x-2">
          {value ? (
            <CheckCircle
              size={16}
              style={{ color: "var(--success-color, #52C41A)" }}
            />
          ) : (
            <XCircle
              size={16}
              style={{ color: "var(--error-color, #FF4D4F)" }}
            />
          )}
          <div className="flex flex-col">
            <span
              className="text-sm font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              {value ? "执行成功" : "执行失败"}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: "execution_time",
      title: "执行时间",
      width: 160,
      render: (value, item) => (
        <div className="flex items-center space-x-2">
          <Clock size={14} style={{ color: "var(--text-secondary)" }} />
          <span style={{ color: "var(--text-primary)" }}>
            {value ? formatDate(value, "YYYY-MM-DD HH:mm:ss") : "-"}
          </span>
        </div>
      ),
    },

    {
      key: "conditions",
      title: "触发条件",
      render: (value, item) => (
        <div className="max-w-xs">
          <div
            className="text-sm whitespace-pre-line leading-relaxed"
            style={{ color: "var(--text-primary)" }}
            title={value || ""}
          >
            {value ? (
              <div className="text-sm" style={{ color: "var(--text-primary)" }}>
                {value.includes(" 且 ") ? (
                  // 多个条件时分行显示
                  <div className="space-y-1">
                    {value.split(" 且 ").map((condition, index) => (
                      <div key={index} className="flex items-center">
                        <span className="text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 font-medium mr-2">
                          {index + 1}
                        </span>
                        <span>{condition}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  // 单个条件直接显示
                  <span>{value}</span>
                )}
              </div>
            ) : (
              <span style={{ color: "var(--text-secondary)" }}>无条件</span>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "actions",
      title: "执行动作",
      render: (value, item) => (
        <div className="max-w-xs">
          <div
            className="text-sm whitespace-pre-line leading-relaxed"
            style={{ color: "var(--text-primary)" }}
            title={value || ""}
          >
            {value ? (
              <div className="text-sm" style={{ color: "var(--text-primary)" }}>
                {value.includes(" + ") ? (
                  // 多个动作时分行显示
                  <div className="space-y-1">
                    {value.split(" + ").map((action, index) => (
                      <div key={index} className="flex items-center">
                        <span className="text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-700 font-medium mr-2">
                          {index + 1}
                        </span>
                        <span>{action}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  // 单个动作直接显示
                  <span>{value}</span>
                )}
              </div>
            ) : (
              <span style={{ color: "var(--text-secondary)" }}>无动作</span>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "error_message",
      title: "错误信息",
      width: 150,
      render: (value, item) => (
        <span
          className="text-sm max-w-xs truncate block"
          style={{
            color: value
              ? "var(--error-color, #FF4D4F)"
              : "var(--text-secondary)",
          }}
          title={value || ""}
        >
          {value || "-"}
        </span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      width: 80,
      render: (value, item) => (
        <div className="flex items-center space-x-2">
          <Button
            size="small"
            variant="ghost"
            onClick={() => handleViewDetail(item)}
            title="查看详情"
          >
            <Eye size={14} />
          </Button>
        </div>
      ),
    },
  ];

  // 加载日志数据
  const loadLogs = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    } else {
      setIsUpdating(true);
    }

    try {
      // 获取当前页数据用于显示
      const response = await strategyLogService.getStrategyLogList({
        page,
        page_size: pageSize,
        ...filters,
      });

      if (response.code === 0) {
        setLogs(response.data.list);
        setTotal(response.data.total);

        // 更新最后刷新时间
        setLastUpdateTime(new Date());
      } else {
        if (showLoading) {
          toast.error(response.msg || "获取日志失败");
        }
      }
    } catch (error) {
      console.error("获取策略日志失败:", error);
      if (showLoading) {
        toast.error("获取日志失败");
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      } else {
        // 延迟关闭更新状态，让用户看到更新效果
        setTimeout(() => setIsUpdating(false), 500);
      }
    }
  };

  // 加载统计数据（使用专门的统计接口）
  const loadStats = async () => {
    try {
      console.log("开始加载统计数据...");

      // 先测试不使用时间条件的统计
      console.log("测试不使用时间条件的统计...");
      const testResponse = await strategyLogService.getStrategyLogStats({});
      console.log("测试统计响应:", testResponse);

      const startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 最近7天
      const endTime = new Date();

      console.log("查询时间范围:", {
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
      });

      const response = await strategyLogService.getStrategyLogStats({
        start_time: startTime,
        end_time: endTime,
      });

      console.log("统计数据响应:", response);

      if (response.code === 0) {
        // 检查响应数据结构
        console.log("响应数据结构:", {
          total_count: response.data.total_count,
          success_count: response.data.success_count,
          error_count: response.data.error_count,
          condition_met_count: response.data.condition_met_count,
          condition_not_met_count: response.data.condition_not_met_count,
          start_time: response.data.start_time,
          end_time: response.data.end_time,
        });
        const newStats = {
          totalExecutions: response.data.total_count || 0,
          successCount: response.data.success_count || 0,
          failureCount: response.data.error_count || 0,
          conditionMetCount: response.data.condition_met_count || 0,
          conditionNotMetCount: response.data.condition_not_met_count || 0,
        };
        console.log("设置统计数据:", newStats);

        // 验证数据一致性
        if (
          newStats.totalExecutions !==
          newStats.successCount + newStats.failureCount
        ) {
          console.warn("数据不一致: 总执行次数 != 成功次数 + 失败次数", {
            total: newStats.totalExecutions,
            success: newStats.successCount,
            failure: newStats.failureCount,
            sum: newStats.successCount + newStats.failureCount,
          });
        }

        if (
          newStats.totalExecutions !==
          newStats.conditionMetCount + newStats.conditionNotMetCount
        ) {
          console.warn(
            "数据不一致: 总执行次数 != 条件满足次数 + 条件不满足次数",
            {
              total: newStats.totalExecutions,
              conditionMet: newStats.conditionMetCount,
              conditionNotMet: newStats.conditionNotMetCount,
              sum: newStats.conditionMetCount + newStats.conditionNotMetCount,
            }
          );
        }

        setStats(newStats);
      } else {
        console.error("统计数据响应错误:", response);
      }
    } catch (error) {
      console.error("获取统计数据失败:", error);
      // 设置默认值
      setStats({
        totalExecutions: 0,
        successCount: 0,
        failureCount: 0,
        conditionMetCount: 0,
        conditionNotMetCount: 0,
      });
    }
  };

  // 处理搜索
  const handleSearch = (searchTerm) => {
    setSearchValue(searchTerm);
    setFilters((prev) => ({ ...prev, strategyName: searchTerm }));
    setPage(1);
  };

  // 处理筛选
  const handleFilter = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPage(1);
  };

  // 处理分页
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // 清空筛选
  const handleClearFilters = () => {
    setSearchValue("");
    setFilters({
      strategyName: "",
      targetType: "",
      targetID: "",
      conditionMet: null,
      actionCompleted: null,
    });
    setPage(1);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadLogs();
  };

  // 设置刷新间隔
  const handleIntervalChange = (interval) => {
    setRefreshInterval(interval);
  };

  // 查看详情
  const handleViewDetail = (log) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };

  // 关闭详情对话框
  const handleCloseDetail = () => {
    setDetailModalVisible(false);
    setSelectedLog(null);
  };

  // 初始加载
  useEffect(() => {
    loadLogs();
    loadStats(); // 同时加载统计数据
  }, [page, pageSize, filters]);

  // 自动刷新定时器
  useEffect(() => {
    let intervalId;
    let countdownId;

    // 设置倒计时
    setCountdown(refreshInterval / 1000);

    countdownId = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          return refreshInterval / 1000;
        }
        return prev - 1;
      });
    }, 1000);

    intervalId = setInterval(() => {
      loadLogs(false); // 静默刷新，不显示loading
      loadStats(); // 同时刷新统计数据
    }, refreshInterval);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      if (countdownId) {
        clearInterval(countdownId);
      }
    };
  }, [refreshInterval, page, pageSize, filters]);

  return (
    <div
      className="h-full flex flex-col p-4"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <Card className="overflow-hidden h-full flex flex-col">
        {/* 统计卡片区域 */}
        <div
          className="p-6 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* 总执行次数 */}
            <div
              className={`p-4 border rounded-lg transition-all duration-300 ${
                isUpdating ? "bg-opacity-80" : ""
              }`}
              style={{
                borderColor: "var(--border-color)",
                backgroundColor: isUpdating
                  ? "rgba(7, 193, 96, 0.05)"
                  : "transparent",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    总执行次数
                  </p>
                  <p
                    className={`text-2xl font-bold mt-1 transition-all duration-300 ${
                      isUpdating ? "scale-105" : ""
                    }`}
                    style={{ color: "var(--text-primary)" }}
                  >
                    {stats.totalExecutions}
                  </p>
                </div>
                <BarChart3
                  size={24}
                  className={isUpdating ? "animate-spin" : ""}
                  style={{ color: "var(--primary-color)" }}
                />
              </div>
            </div>

            {/* 执行成功 */}
            <div
              className={`p-4 border rounded-lg transition-all duration-300 ${
                isUpdating ? "bg-opacity-80" : ""
              }`}
              style={{
                borderColor: "var(--border-color)",
                backgroundColor: isUpdating
                  ? "rgba(82, 196, 26, 0.05)"
                  : "transparent",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    执行成功
                  </p>
                  <p
                    className={`text-2xl font-bold mt-1 transition-all duration-300 ${
                      isUpdating ? "scale-105" : ""
                    }`}
                    style={{ color: "var(--success-color, #52C41A)" }}
                  >
                    {stats.successCount}
                  </p>
                </div>
                <CheckCircle
                  size={24}
                  className={isUpdating ? "animate-bounce" : ""}
                  style={{ color: "var(--success-color, #52C41A)" }}
                />
              </div>
            </div>

            {/* 执行失败 */}
            <div
              className="p-4 border rounded-lg"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    执行失败
                  </p>
                  <p
                    className="text-2xl font-bold mt-1"
                    style={{ color: "var(--error-color, #FF4D4F)" }}
                  >
                    {stats.failureCount}
                  </p>
                </div>
                <XCircle
                  size={24}
                  style={{ color: "var(--error-color, #FF4D4F)" }}
                />
              </div>
            </div>

            {/* 条件满足 */}
            <div
              className="p-4 border rounded-lg"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    条件满足
                  </p>
                  <p
                    className="text-2xl font-bold mt-1"
                    style={{ color: "var(--success-color, #52C41A)" }}
                  >
                    {stats.conditionMetCount}
                  </p>
                </div>
                <CheckCircle
                  size={24}
                  style={{ color: "var(--success-color, #52C41A)" }}
                />
              </div>
            </div>

            {/* 条件不满足 */}
            <div
              className="p-4 border rounded-lg"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    条件不满足
                  </p>
                  <p
                    className="text-2xl font-bold mt-1"
                    style={{ color: "var(--warning-color, #FAAD14)" }}
                  >
                    {stats.conditionNotMetCount}
                  </p>
                </div>
                <AlertTriangle
                  size={24}
                  style={{ color: "var(--warning-color, #FAAD14)" }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和筛选区域 */}
        <div
          className="p-6 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-4">
                <h3
                  className="text-lg font-semibold"
                  style={{ color: "var(--text-primary)" }}
                >
                  策略执行日志
                </h3>
                <span
                  className="text-sm"
                  style={{ color: "var(--text-secondary)" }}
                >
                  共 {total} 条记录
                </span>
                <span
                  className={`text-xs px-2 py-1 rounded transition-all duration-300 ${
                    isUpdating ? "animate-pulse" : ""
                  }`}
                  style={{
                    backgroundColor: "rgba(7, 193, 96, 0.1)",
                    color: "var(--primary-color)",
                  }}
                >
                  {isUpdating ? "更新中..." : "实时更新中"}
                </span>
                <span
                  className="text-xs"
                  style={{ color: "var(--text-secondary)" }}
                >
                  最后更新: {lastUpdateTime.toLocaleTimeString()}
                  <span style={{ color: "var(--primary-color)" }}>
                    {" "}
                    · {countdown}s后更新
                  </span>
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* 搜索框 */}
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2"
                  style={{ color: "var(--text-secondary)" }}
                />
                <input
                  type="text"
                  placeholder="搜索策略名称..."
                  value={searchValue}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 text-sm rounded-md transition-wechat"
                  style={{
                    backgroundColor: "var(--panel-bg)",
                    border: "1px solid var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = "var(--primary-color)";
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = "var(--border-color)";
                  }}
                />
              </div>

              {/* 目标类型筛选 */}
              <select
                value={filters.targetType}
                onChange={(e) => handleFilter("targetType", e.target.value)}
                className="px-3 py-2 text-sm rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--panel-bg)",
                  border: "1px solid var(--border-color)",
                  color: "var(--text-primary)",
                }}
              >
                <option value="">所有目标类型</option>
                <option value="advertiser">广告主</option>
                <option value="project">项目</option>
                <option value="promotion">广告</option>
                <option value="global">全局</option>
              </select>

              {/* 条件状态筛选 */}
              <select
                value={
                  filters.conditionMet === null
                    ? ""
                    : filters.conditionMet.toString()
                }
                onChange={(e) =>
                  handleFilter(
                    "conditionMet",
                    e.target.value === "" ? null : e.target.value === "true"
                  )
                }
                className="px-3 py-2 text-sm rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--panel-bg)",
                  border: "1px solid var(--border-color)",
                  color: "var(--text-primary)",
                }}
              >
                <option value="">所有条件状态</option>
                <option value="true">条件满足</option>
                <option value="false">条件不满足</option>
              </select>

              {/* 执行状态筛选 */}
              <select
                value={
                  filters.actionCompleted === null
                    ? ""
                    : filters.actionCompleted.toString()
                }
                onChange={(e) =>
                  handleFilter(
                    "actionCompleted",
                    e.target.value === "" ? null : e.target.value === "true"
                  )
                }
                className="px-3 py-2 text-sm rounded-md transition-wechat"
                style={{
                  backgroundColor: "var(--panel-bg)",
                  border: "1px solid var(--border-color)",
                  color: "var(--text-primary)",
                }}
              >
                <option value="">所有执行状态</option>
                <option value="true">执行成功</option>
                <option value="false">执行失败</option>
              </select>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2">
                {/* 刷新间隔设置 */}
                <div
                  className="flex items-center space-x-2 border-r pr-3"
                  style={{ borderColor: "var(--border-color)" }}
                >
                  <span
                    className="text-xs"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    刷新间隔:
                  </span>
                  <select
                    value={refreshInterval}
                    onChange={(e) =>
                      handleIntervalChange(Number(e.target.value))
                    }
                    className="px-2 py-1 text-xs rounded border"
                    style={{
                      backgroundColor: "var(--panel-bg)",
                      borderColor: "var(--border-color)",
                      color: "var(--text-primary)",
                    }}
                  >
                    <option value={3000}>3秒</option>
                    <option value={5000}>5秒</option>
                    <option value={10000}>10秒</option>
                    <option value={30000}>30秒</option>
                  </select>
                </div>

                <Button
                  variant="outline"
                  size="small"
                  onClick={handleClearFilters}
                  icon={<Filter size={14} />}
                >
                  清空筛选
                </Button>
                <Button
                  variant="outline"
                  size="small"
                  onClick={handleRefresh}
                  icon={<RefreshCw size={14} />}
                  loading={loading}
                >
                  刷新
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 数据表格区域 */}
        <div className="flex-1 flex flex-col min-h-0">
          {isUpdating && (
            <div
              className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-green-500 to-transparent animate-pulse"
              style={{ zIndex: 10 }}
            />
          )}
          <div className="flex-1 overflow-auto">
            <DataTable
              data={logs}
              columns={columns}
              loading={loading}
              rowKey="id"
              className={`transition-opacity duration-300 ${
                isUpdating ? "opacity-90" : "opacity-100"
              }`}
            />
          </div>

          {/* 分页组件 */}
          <Pagination
            current={page}
            total={total}
            pageSize={pageSize}
            onChange={handlePageChange}
            onPageSizeChange={(newPageSize, newCurrent) => {
              setPageSize(newPageSize);
              setPage(newCurrent);
            }}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={true}
            pageSizeOptions={[20, 50, 100, 200]}
            className="border-t"
          />
        </div>
      </Card>

      {/* 详情对话框 */}
      {detailModalVisible && selectedLog && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.4)" }}
        >
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                策略执行详情
              </h3>
              <button
                onClick={handleCloseDetail}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* 内容 */}
            <div className="p-6 space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    策略名称
                  </label>
                  <div
                    className="mt-1 text-sm"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {selectedLog.strategy_name}
                  </div>
                </div>
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    目标类型
                  </label>
                  <div
                    className="mt-1 text-sm"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {selectedLog.target_type === "advertiser"
                      ? "广告主"
                      : selectedLog.target_type === "project"
                      ? "项目"
                      : selectedLog.target_type === "promotion"
                      ? "广告"
                      : selectedLog.target_type === "global"
                      ? "全局"
                      : selectedLog.target_type}
                  </div>
                </div>
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    目标名称
                  </label>
                  <div
                    className="mt-1 text-sm"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {selectedLog.target_name}
                  </div>
                </div>
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    执行时间
                  </label>
                  <div
                    className="mt-1 text-sm"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {formatDate(
                      selectedLog.execution_time,
                      "YYYY-MM-DD HH:mm:ss"
                    )}
                  </div>
                </div>
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    执行耗时
                  </label>
                  <div
                    className="mt-1 text-sm"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {selectedLog.duration}ms
                  </div>
                </div>
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    执行状态
                  </label>
                  <div className="mt-1">
                    <StatusBadge
                      status={
                        selectedLog.action_completed ? "success" : "error"
                      }
                      text={
                        selectedLog.action_completed ? "执行成功" : "执行失败"
                      }
                    />
                  </div>
                </div>
              </div>

              {/* 条件评估 */}
              <div>
                <label
                  className="text-sm font-medium"
                  style={{ color: "var(--text-secondary)" }}
                >
                  触发条件
                </label>
                <div
                  className="mt-2 p-3 rounded-md"
                  style={{
                    backgroundColor: "var(--bg-secondary)",
                    border: "1px solid var(--border-color)",
                  }}
                >
                  <div className="flex items-center space-x-2 mb-3">
                    {selectedLog.condition_met ? (
                      <CheckCircle
                        size={16}
                        style={{ color: "var(--success-color)" }}
                      />
                    ) : (
                      <XCircle
                        size={16}
                        style={{ color: "var(--error-color)" }}
                      />
                    )}
                    <span
                      className="text-sm font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      {selectedLog.condition_met ? "条件满足" : "条件不满足"}
                    </span>
                    <span
                      className="text-xs"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      {selectedLog.condition_met ? "策略将被执行" : "跳过执行"}
                    </span>
                  </div>
                  <div className="space-y-2">
                    {selectedLog.conditions ? (
                      selectedLog.conditions.includes(" 且 ") ? (
                        // 多个条件时分行显示
                        selectedLog.conditions
                          .split(" 且 ")
                          .map((condition, index) => (
                            <div key={index} className="flex items-center">
                              <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 font-medium mr-3 flex-shrink-0">
                                {index + 1}
                              </span>
                              <span
                                className="text-sm"
                                style={{ color: "var(--text-primary)" }}
                              >
                                {condition}
                              </span>
                            </div>
                          ))
                      ) : (
                        // 单个条件直接显示
                        <div
                          className="text-sm"
                          style={{ color: "var(--text-primary)" }}
                        >
                          {selectedLog.conditions}
                        </div>
                      )
                    ) : (
                      <span
                        className="text-sm"
                        style={{ color: "var(--text-secondary)" }}
                      >
                        无条件
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* 执行动作 */}
              <div>
                <label
                  className="text-sm font-medium"
                  style={{ color: "var(--text-secondary)" }}
                >
                  执行动作
                </label>
                <div
                  className="mt-2 p-3 rounded-md"
                  style={{
                    backgroundColor: "var(--bg-secondary)",
                    border: "1px solid var(--border-color)",
                  }}
                >
                  <div className="space-y-2">
                    {selectedLog.actions ? (
                      selectedLog.actions.includes(" + ") ? (
                        // 多个动作时分行显示
                        selectedLog.actions
                          .split(" + ")
                          .map((action, index) => (
                            <div key={index} className="flex items-center">
                              <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-700 font-medium mr-3 flex-shrink-0">
                                {index + 1}
                              </span>
                              <span
                                className="text-sm"
                                style={{ color: "var(--text-primary)" }}
                              >
                                {action}
                              </span>
                            </div>
                          ))
                      ) : (
                        // 单个动作直接显示
                        <div
                          className="text-sm"
                          style={{ color: "var(--text-primary)" }}
                        >
                          {selectedLog.actions}
                        </div>
                      )
                    ) : (
                      <span
                        className="text-sm"
                        style={{ color: "var(--text-secondary)" }}
                      >
                        无动作
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* 错误信息 */}
              {selectedLog.error_message && (
                <div>
                  <label
                    className="text-sm font-medium"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    错误信息
                  </label>
                  <div
                    className="mt-2 p-3 rounded-md"
                    style={{
                      backgroundColor: "rgba(255, 77, 79, 0.1)",
                      border: "1px solid rgba(255, 77, 79, 0.3)",
                    }}
                  >
                    <div
                      className="text-sm"
                      style={{ color: "var(--error-color)" }}
                    >
                      {selectedLog.error_message}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategyLogPage;
