import React from 'react';
import { Play, AlertCircle, Clock, Bug } from 'lucide-react';
import { Card, Button } from '../components/ui';
import testTaskProgressService from '../services/testTaskProgressService';

/**
 * 测试进度条页面
 * 用于演示和测试任务进度条的各种功能
 */
const TestProgressPage = () => {
  
  // 测试按钮处理函数
  const handleSimpleTest = () => {
    console.log('开始简单任务测试');
    testTaskProgressService.simulateSimpleTask();
  };

  const handleErrorTest = () => {
    console.log('开始错误任务测试');
    testTaskProgressService.simulateErrorTask();
  };

  const handleLongTest = () => {
    console.log('开始长时间任务测试');
    testTaskProgressService.simulateLongRunningTask();
  };

  const handleCustomTest = () => {
    console.log('开始自定义任务测试');
    // 发送一个自定义的任务进度事件
    testTaskProgressService.emitTaskEvent({
      task_id: 'custom_' + Date.now(),
      task_name: '自定义任务',
      task_description: '这是一个自定义的任务进度演示',
      step_description: '正在执行自定义操作...',
      total_steps: 1,
      current_step: 1,
      step_progress: 0,
      next_step_progress: 100,
      step_time: 5000, // 5秒
      status: 'start',
      error_message: ''
    });

    // 5秒后完成任务
    setTimeout(() => {
      testTaskProgressService.emitTaskEvent({
        task_id: 'custom_' + Date.now(),
        task_name: '自定义任务',
        task_description: '这是一个自定义的任务进度演示',
        step_description: '自定义操作完成',
        total_steps: 1,
        current_step: 1,
        step_progress: 100,
        next_step_progress: 100,
        step_time: 0,
        status: 'end',
        error_message: ''
      });
    }, 5000);
  };

  const handleStepCountTest = () => {
    console.log('开始步骤计数逻辑测试');
    testTaskProgressService.testStepCountLogic();
  };

  return (
    <div className="h-full flex flex-col">
      {/* 页面标题 */}
      <div 
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: 'var(--panel-bg)',
          borderBottomColor: 'var(--border-color)',
        }}
      >
        <div className="flex items-center space-x-3">
          <Bug size={20} style={{ color: 'var(--primary-color)' }} />
          <h1 
            className="text-lg font-semibold"
            style={{ color: 'var(--text-primary)' }}
          >
            进度条测试
          </h1>
        </div>
        <p 
          className="text-sm mt-1"
          style={{ color: 'var(--text-secondary)' }}
        >
          测试和演示任务进度条的各种功能和状态
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* 简单任务测试 */}
          <Card
            className="p-6"
            style={{
              backgroundColor: 'var(--panel-bg)',
              border: '1px solid var(--border-color)',
            }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <Play size={20} style={{ color: 'var(--primary-color)' }} />
              <h3 
                className="text-lg font-medium"
                style={{ color: 'var(--text-primary)' }}
              >
                简单任务测试
              </h3>
            </div>
            <p 
              className="text-sm mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              演示一个包含3个步骤的简单任务，展示进度条的基本功能：
            </p>
            <ul 
              className="text-sm space-y-1 mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              <li>• 步骤1：初始化 (0% → 30%，2秒)</li>
              <li>• 步骤2：处理数据 (30% → 60%，3秒)</li>
              <li>• 步骤3：验证结果 (60% → 90%，2.5秒)</li>
              <li>• 步骤4：完成任务 (90% → 100%，1.5秒)</li>
            </ul>
            <Button 
              onClick={handleSimpleTest}
              className="w-full"
            >
              开始简单任务测试
            </Button>
          </Card>

          {/* 错误任务测试 */}
          <Card
            className="p-6"
            style={{
              backgroundColor: 'var(--panel-bg)',
              border: '1px solid var(--border-color)',
            }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <AlertCircle size={20} style={{ color: 'var(--error-color)' }} />
              <h3 
                className="text-lg font-medium"
                style={{ color: 'var(--text-primary)' }}
              >
                错误任务测试
              </h3>
            </div>
            <p 
              className="text-sm mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              演示一个会出错的任务，展示错误状态下的进度条表现：
            </p>
            <ul 
              className="text-sm space-y-1 mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              <li>• 步骤1：初始化 (0% → 50%，1秒)</li>
              <li>• 步骤2：处理数据 (50% → 100%，2秒)</li>
              <li>• 错误：模拟网络连接超时</li>
              <li>• 错误消息显示并5秒后自动隐藏</li>
            </ul>
            <Button 
              onClick={handleErrorTest}
              className="w-full"
              style={{
                backgroundColor: 'var(--error-color)',
                borderColor: 'var(--error-color)',
              }}
            >
              开始错误任务测试
            </Button>
          </Card>

          {/* 长时间任务测试 */}
          <Card
            className="p-6"
            style={{
              backgroundColor: 'var(--panel-bg)',
              border: '1px solid var(--border-color)',
            }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <Clock size={20} style={{ color: 'var(--warning-color)' }} />
              <h3 
                className="text-lg font-medium"
                style={{ color: 'var(--text-primary)' }}
              >
                长时间任务测试
              </h3>
            </div>
            <p 
              className="text-sm mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              演示一个长时间运行的任务，包含10个步骤：
            </p>
            <ul 
              className="text-sm space-y-1 mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              <li>• 总共10个步骤</li>
              <li>• 每个步骤耗时5秒</li>
              <li>• 总耗时约50秒</li>
              <li>• 测试长时间任务的用户体验</li>
            </ul>
            <Button 
              onClick={handleLongTest}
              className="w-full"
              style={{
                backgroundColor: 'var(--warning-color)',
                borderColor: 'var(--warning-color)',
              }}
            >
              开始长时间任务测试
            </Button>
          </Card>

          {/* 自定义任务测试 */}
          <Card
            className="p-6"
            style={{
              backgroundColor: 'var(--panel-bg)',
              border: '1px solid var(--border-color)',
            }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <Bug size={20} style={{ color: 'var(--info-color)' }} />
              <h3 
                className="text-lg font-medium"
                style={{ color: 'var(--text-primary)' }}
              >
                自定义任务测试
              </h3>
            </div>
            <p 
              className="text-sm mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              演示一个简单的自定义任务：
            </p>
            <ul 
              className="text-sm space-y-1 mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              <li>• 单个步骤</li>
              <li>• 进度从0%缓慢增长到100%</li>
              <li>• 耗时5秒</li>
              <li>• 演示基本的动画效果</li>
            </ul>
            <Button 
              onClick={handleCustomTest}
              className="w-full"
              style={{
                backgroundColor: 'var(--info-color)',
                borderColor: 'var(--info-color)',
              }}
            >
              开始自定义任务测试
            </Button>
          </Card>

          {/* 步骤计数逻辑测试 */}
          <Card
            className="p-6"
            style={{
              backgroundColor: 'var(--panel-bg)',
              border: '1px solid var(--border-color)',
            }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <Bug size={20} style={{ color: 'var(--success-color)' }} />
              <h3 
                className="text-lg font-medium"
                style={{ color: 'var(--text-primary)' }}
              >
                步骤计数逻辑测试
              </h3>
            </div>
            <p 
              className="text-sm mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              专门测试步骤计数逻辑的修复效果：
            </p>
            <ul 
              className="text-sm space-y-1 mb-4"
              style={{ color: 'var(--text-secondary)' }}
            >
              <li>• 第1步执行时显示 0/3</li>
              <li>• 第2步执行时显示 1/3</li>
              <li>• 第3步执行时显示 2/3</li>
              <li>• 第3步完成时显示 3/3</li>
            </ul>
            <Button 
              onClick={handleStepCountTest}
              className="w-full"
              style={{
                backgroundColor: 'var(--success-color)',
                borderColor: 'var(--success-color)',
              }}
            >
              开始步骤计数测试
            </Button>
          </Card>
        </div>

        {/* 使用说明 */}
        <Card
          className="mt-6 p-6"
          style={{
            backgroundColor: 'var(--panel-bg)',
            border: '1px solid var(--border-color)',
          }}
        >
          <h3 
            className="text-lg font-medium mb-4"
            style={{ color: 'var(--text-primary)' }}
          >
            使用说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 
                className="font-medium mb-2"
                style={{ color: 'var(--text-primary)' }}
              >
                进度条特性
              </h4>
              <ul 
                className="text-sm space-y-1"
                style={{ color: 'var(--text-secondary)' }}
              >
                <li>• 立即跳转到当前进度</li>
                <li>• 缓慢增长到下一个进度</li>
                <li>• 显示当前步骤描述</li>
                <li>• 显示预估剩余时间</li>
                <li>• 支持展开/收起详细信息</li>
              </ul>
            </div>
            <div>
              <h4 
                className="font-medium mb-2"
                style={{ color: 'var(--text-primary)' }}
              >
                交互操作
              </h4>
              <ul 
                className="text-sm space-y-1"
                style={{ color: 'var(--text-secondary)' }}
              >
                <li>• 点击进度条可展开详细信息</li>
                <li>• 点击关闭按钮可手动关闭进度条</li>
                <li>• 任务完成后3秒自动隐藏</li>
                <li>• 任务出错后5秒自动隐藏</li>
                <li>• 支持多个任务同时运行</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TestProgressPage; 