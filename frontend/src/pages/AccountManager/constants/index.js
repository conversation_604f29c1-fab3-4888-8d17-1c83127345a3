// 账户状态常量
export const ACCOUNT_STATUS = {
  NORMAL: '正常',
  NOT_LOGGED_IN: '未登录',
  NOT_CONFIGURED: '未配置'
};

// 状态对应的UI变体
export const STATUS_VARIANTS = {
  [ACCOUNT_STATUS.NORMAL]: 'success',
  [ACCOUNT_STATUS.NOT_LOGGED_IN]: 'warning',
  [ACCOUNT_STATUS.NOT_CONFIGURED]: 'error'
};

// 初始分页状态
export const INITIAL_PAGINATION = {
  page: 1,
  pageSize: 10,
  total: 0
};

// 初始账户对话框状态
export const INITIAL_ACCOUNT_DIALOG = {
  isOpen: false,
  account: null,
  title: '编辑账户'
};

// 初始确认对话框状态
export const INITIAL_CONFIRM_DIALOG = {
  isOpen: false,
  title: '',
  message: '',
  onConfirm: null
}; 