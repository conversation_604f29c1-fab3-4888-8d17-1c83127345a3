import React from "react";
import { Avatar, StatusBadge } from "../../../components/ui";
import { STATUS_VARIANTS } from "../constants";
import { formatTime, getAccountStatus, isAccountClickable } from "../utils";

/**
 * 生成表格列配置
 * @param {Function} onAccountClick - 账户点击处理函数
 * @param {Function} onContextMenu - 右键菜单处理函数
 * @returns {Array} 表格列配置数组
 */
export const createTableColumns = (onAccountClick, onContextMenu) => [
  {
    title: "账户信息",
    dataIndex: "account_name",
    key: "account_info",
    width: "280px",
    render: (_, record) => {
      const clickable = isAccountClickable(record);

      return (
        <div className="flex items-center space-x-3">
          <Avatar
            name={record.account_name}
            size="md"
            status={clickable ? "online" : "offline"}
          />
          <div
            className={`p-2 transition-wechat ${
              clickable ? "cursor-pointer" : ""
            }`}
            style={{
              borderRadius: "4px",
              ...(clickable && {
                cursor: "pointer",
              }),
            }}
            onMouseEnter={(e) => {
              if (clickable) {
                e.currentTarget.style.backgroundColor = "#E9F8F1";
                e.currentTarget.style.boxShadow = "var(--shadow-card)";
              }
            }}
            onMouseLeave={(e) => {
              if (clickable) {
                e.currentTarget.style.backgroundColor = "transparent";
                e.currentTarget.style.boxShadow = "none";
              }
            }}
            onClick={() => onAccountClick(record)}
            onContextMenu={(e) => onContextMenu && onContextMenu(e, record)}
          >
            <div
              className="text-sm font-medium flex items-center space-x-2"
              style={{
                color: clickable
                  ? "var(--primary-color)"
                  : "var(--text-primary)",
                fontWeight: "500",
              }}
            >
              <span>{record.account_name || "未命名账户"}</span>
              {clickable && (
                <div
                  className="w-2 h-2 rounded-full pulse-animation"
                  style={{ backgroundColor: "var(--primary-color)" }}
                ></div>
              )}
            </div>
            <div
              className="text-xs mt-0.5"
              style={{
                color: "var(--text-secondary)",
                fontSize: "12px",
              }}
            >
              {record.account_id ? `ID: ${record.account_id}` : "未登录"}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: "100px",
    render: (_, record) => {
      const status = getAccountStatus(record);
      const variant = STATUS_VARIANTS[status];
      return <StatusBadge variant={variant}>{status}</StatusBadge>;
    },
  },
  {
    title: "登录类型",
    dataIndex: "login_type",
    key: "login_type",
    width: "120px",
    className: "text-secondary",
    render: (value) => (
      <span style={{ color: "var(--text-secondary)" }}>{value || "-"}</span>
    ),
  },
  {
    title: "子账户数",
    dataIndex: "sub_account_count",
    key: "sub_account_count",
    width: "100px",
    className: "text-secondary",
    render: (value) => (
      <span style={{ color: "var(--text-secondary)" }}>{value || 0}</span>
    ),
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: "200px",
    className: "text-secondary",
    render: (value) => (
      <span style={{ color: "var(--text-secondary)" }}>{value || "-"}</span>
    ),
  },
  {
    title: "创建时间",
    dataIndex: "create_time",
    key: "create_time",
    width: "160px",
    className: "text-secondary",
    render: (value) => (
      <span style={{ color: "var(--text-secondary)" }}>
        {formatTime(value)}
      </span>
    ),
  },
];
