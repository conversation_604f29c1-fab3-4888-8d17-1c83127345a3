# AccountManager 更新日志

## 🔄 API重构 - 直接使用Bindings

### 📅 更新时间
2025年6月4日

### 🎯 更新目标
- 移除中间层服务包装，直接使用Wails bindings
- 提高API调用效率和性能
- 简化依赖关系

### 🔧 主要变更

#### 1. 导入方式变更
```diff
- import accountService from '../../../services/accountService';
+ import * as AccountService from '@services/accountservice.js';
```

#### 2. API调用方式重构

| 原API调用 | 新API调用 | 参数变更 |
|-----------|-----------|----------|
| `accountService.getAccountList(params)` | `AccountService.GetAccountList(req)` | 参数结构调整 |
| `accountService.updateAccount(account)` | `AccountService.UpdateAccount(account)` | 无变更 |
| `accountService.deleteAccount(id)` | `AccountService.DeleteAccount(id)` | 无变更 |
| `accountService.batchDeleteAccounts(ids)` | `AccountService.BatchDeleteAccounts(ids)` | 无变更 |
| `accountService.loginWithEmail()` | `AccountService.LoginWithEmailDirectly()` | 无变更 |
| `accountService.loginWithPhoneCode()` | `AccountService.LoginWithPhoneCode()` | 无变更 |
| `accountService.loginWithCookie()` | `AccountService.LoginWithCookie()` | 无变更 |
| `accountService.sendLoginCode()` | `AccountService.SendLoginCode()` | 无变更 |

#### 3. 请求参数结构调整

**获取账户列表请求参数:**
```diff
// 原参数结构
- {
-   page: 1,
-   pageSize: 10,
-   accountName: 'xxx'
- }

// 新参数结构  
+ {
+   page: 1,
+   page_size: 10,
+   account_name: 'xxx',
+   login_type: '',
+   remark: '',
+   order_by: '',
+   order_desc: false,
+   extra_filters: {}
+ }
```

#### 4. 错误处理增强

- 增加了console.error日志记录
- 添加了空值检查 (`result && result.code === 0`)
- 统一了错误返回格式
- 为登录方法添加了try-catch包装

### 🚀 性能优势

1. **减少中间层开销** - 直接调用binding，减少一层封装
2. **更好的错误处理** - 直接捕获底层异常
3. **类型安全** - 使用自动生成的binding接口
4. **调试友好** - 增加了详细的错误日志

### 🔒 向后兼容性

- API接口保持不变
- 返回数据结构不变
- 错误处理方式保持一致
- 组件使用方式不需要修改

### 📝 注意事项

1. **路径别名**: 使用 `@services/` 别名访问binding文件
2. **错误处理**: 新增了更详细的错误日志，便于调试
3. **参数验证**: 确保传递正确的参数结构
4. **异步处理**: 所有API调用都正确处理了Promise和异常

### 🧪 测试建议

- [ ] 测试账户列表加载
- [ ] 测试账户编辑功能
- [ ] 测试账户删除功能
- [ ] 测试批量删除功能
- [ ] 测试各种登录方式
- [ ] 测试验证码发送功能
- [ ] 测试错误处理场景 