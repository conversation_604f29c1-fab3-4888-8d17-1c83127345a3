import React from "react";
import { LogIn, Refresh<PERSON><PERSON>, Trash2, Edit, User, Copy } from "lucide-react";
import {
  SearchInput,
  AccountDialog,
  LoginDialog,
  ConfirmDialog,
  DataTable,
  Button,
  Card,
  StatsDisplay,
  ContextMenu,
} from "../../components/ui";
import { useAccountManager } from "./hooks";
import { createTableColumns } from "./components";
import { isAccountClickable } from "./utils";

/**
 * 账户管理页面主组件
 * @param {Function} onAccountSelect - 账户选择回调函数
 * @param {Function} onNavigateToAdvertiser - 导航到广告主管理页面的回调函数
 */
function AccountManager({ onAccountSelect, onNavigateToAdvertiser }) {
  // 响应式状态
  const [windowWidth, setWindowWidth] = React.useState(window.innerWidth);

  // 右键菜单状态
  const [contextMenu, setContextMenu] = React.useState({
    visible: false,
    x: 0,
    y: 0,
    account: null,
  });

  // 监听窗口大小变化
  React.useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 使用自定义Hook管理状态和逻辑
  const {
    // 状态
    searchTerm,
    loading,
    data,
    pagination,
    selectedItems,
    accountDialog,
    loginDialog,
    confirmDialog,

    // 操作函数
    handleSearch,
    handleSelectItem,
    handleSelectAll,
    handleEditAccount,
    handleDeleteAccount,
    handleBatchDelete,
    handleAccountSubmit,
    handleLogin,
    handleSendCode,
    loadAccountData,

    // 对话框控制
    setAccountDialog,
    setLoginDialog,
    setConfirmDialog,

    // 常量
    INITIAL_ACCOUNT_DIALOG,
    INITIAL_CONFIRM_DIALOG,
  } = useAccountManager();

  // 处理账户信息点击 - 导航到广告主管理页面
  const handleAccountInfoClick = (account) => {
    if (onNavigateToAdvertiser && isAccountClickable(account)) {
      // 设置当前账户为选中状态
      if (onAccountSelect) {
        onAccountSelect(account);
      }
      // 导航到广告主管理页面
      onNavigateToAdvertiser(account);
    }
  };

  // 处理右键菜单
  const handleContextMenu = (event, account) => {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      account,
    });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      account: null,
    });
  };

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // 可以添加一个toast提示
      console.log("已复制到剪贴板:", text);
    });
  };

  // 创建右键菜单项
  const createContextMenuItems = (account) => {
    if (!account) return [];

    return [
      {
        label: "查看详情",
        icon: <User size={16} />,
        onClick: () => handleAccountInfoClick(account),
        disabled: !isAccountClickable(account),
      },
      {
        label: "编辑账户",
        icon: <Edit size={16} />,
        onClick: () => handleEditAccount(account),
      },
      { type: "divider" },
      {
        label: "复制App ID",
        icon: <Copy size={16} />,
        onClick: () => copyToClipboard(account.app_id),
        disabled: !account.app_id,
      },
      {
        label: "复制用户名",
        icon: <Copy size={16} />,
        onClick: () => copyToClipboard(account.username),
        disabled: !account.username,
      },
      { type: "divider" },
      {
        label: "删除账户",
        icon: <Trash2 size={16} />,
        color: "#FF4757",
        onClick: () => handleDeleteAccount(account),
      },
    ];
  };

  // 生成表格列配置
  const columns = createTableColumns(
    handleAccountInfoClick,
    handleContextMenu // 传递右键菜单处理函数
  );

  // 响应式判断
  const isSmallScreen = windowWidth < 640;
  const isLargeScreen = windowWidth >= 1024;

  return (
    <div
      className="h-full flex flex-col p-4 overflow-hidden hide-scrollbar"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      {/* 统一的卡片容器 */}
      <Card
        className="overflow-hidden h-full flex flex-col hide-scrollbar"
        style={{ overflow: "hidden" }}
      >
        {/* 工具栏 - 作为卡片的顶部区域 */}
        <div
          className="flex items-center justify-between p-4 border-b transition-wechat gap-2"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              onClick={() => setLoginDialog(true)}
              variant="primary"
              size="default"
              icon={<LogIn size={16} />}
              className="tooltip"
              style={{
                padding: "8px 12px",
                minWidth: "40px",
              }}
              data-tooltip="添加新账户"
            >
              {!isSmallScreen && <span>登录账户</span>}
            </Button>
            <Button
              onClick={handleBatchDelete}
              disabled={selectedItems.length === 0}
              variant="outline"
              size="default"
              icon={<Trash2 size={16} />}
              className="tooltip"
              style={{
                padding: "8px 12px",
                minWidth: "40px",
              }}
              data-tooltip={
                selectedItems.length === 0
                  ? "请先选择要删除的账户"
                  : `删除已选择的 ${selectedItems.length} 个账户`
              }
            >
              {!isSmallScreen && <span>批量删除</span>}
            </Button>
          </div>

          <div className="flex items-center gap-2 flex-shrink min-w-0">
            <SearchInput
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder={isSmallScreen ? "搜索..." : "搜索账户..."}
              className="transition-wechat"
              style={{
                width: isSmallScreen
                  ? "120px"
                  : windowWidth < 768
                  ? "180px"
                  : "240px",
                minWidth: "100px",
              }}
              showClearButton={true}
              onClear={() => handleSearch("")}
            />
            <Button
              onClick={loadAccountData}
              disabled={loading}
              variant="ghost"
              size="default"
              className="tooltip flex-shrink-0"
              style={{
                padding: "8px",
                minWidth: "32px",
              }}
              data-tooltip="刷新数据"
            >
              <RefreshCw
                size={16}
                className={`transition-wechat ${
                  loading ? "loading-spinner" : ""
                }`}
                style={{ color: "var(--text-secondary)" }}
              />
            </Button>
            {isLargeScreen && (
              <StatsDisplay
                stats={{
                  total: pagination.total,
                  current: data.length,
                  selected: selectedItems.length,
                  label: "个账户",
                }}
                className="responsive-text flex-shrink-0"
                style={{ whiteSpace: "nowrap" }}
              />
            )}
          </div>
        </div>

        {/* 数据表格 - 作为卡片的主体区域 */}
        <div className="flex-1 min-h-0 overflow-hidden hide-scrollbar">
          <DataTable
            columns={columns}
            data={data}
            loading={loading}
            selectable={true}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            onSelectAll={handleSelectAll}
            onRowContextMenu={handleContextMenu}
            className="h-full"
          />
        </div>

        {/* 移动端统计信息栏 - 作为卡片的底部区域 */}
        <div
          className="md:hidden p-4 border-t"
          style={{
            borderColor: "var(--border-color)",
          }}
        >
          <StatsDisplay
            stats={{
              total: pagination.total,
              current: data.length,
              selected: selectedItems.length,
              label: "个账户",
            }}
            inline={false}
            className="text-sm"
          />
        </div>
      </Card>

      {/* 对话框组件 */}
      <AccountDialog
        isOpen={accountDialog.isOpen}
        onClose={() => setAccountDialog(INITIAL_ACCOUNT_DIALOG)}
        onSubmit={handleAccountSubmit}
        account={accountDialog.account}
        title={accountDialog.title}
      />

      <LoginDialog
        isOpen={loginDialog}
        onClose={() => setLoginDialog(false)}
        onLogin={handleLogin}
        onSendCode={handleSendCode}
      />

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(INITIAL_CONFIRM_DIALOG)}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
      />

      <ContextMenu
        visible={contextMenu.visible}
        onClose={closeContextMenu}
        items={createContextMenuItems(contextMenu.account)}
        x={contextMenu.x}
        y={contextMenu.y}
      />
    </div>
  );
}

export default AccountManager;
