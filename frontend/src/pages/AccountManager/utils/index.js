import { ACCOUNT_STATUS } from '../constants';

/**
 * 格式化时间字符串
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间或'-'
 */
export const formatTime = (timeStr) => {
  if (!timeStr) return '-';
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
  } catch {
    return timeStr;
  }
};

/**
 * 获取账户状态
 * @param {Object} account - 账户对象
 * @returns {string} 账户状态
 */
export const getAccountStatus = (account) => {
  if (account.account_id && account.account_id > 0) {
    return ACCOUNT_STATUS.NORMAL;
  } else if (account.email || account.mobile) {
    return ACCOUNT_STATUS.NOT_LOGGED_IN;
  } else {
    return ACCOUNT_STATUS.NOT_CONFIGURED;
  }
};

/**
 * 检查账户是否可点击（已登录状态）
 * @param {Object} account - 账户对象
 * @returns {boolean} 是否可点击
 */
export const isAccountClickable = (account) => {
  return account.account_id && account.account_id > 0;
}; 