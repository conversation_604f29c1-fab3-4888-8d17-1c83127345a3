import { useState, useEffect, useCallback } from 'react';
import { toast } from '../../../components/ui/Toast';
import * as AccountService from '@services/accountservice.js';
import { 
  INITIAL_PAGINATION, 
  INITIAL_ACCOUNT_DIALOG, 
  INITIAL_CONFIRM_DIALOG 
} from '../constants';

/**
 * AccountManager 自定义Hook
 * 管理账户列表的状态和业务逻辑
 */
export const useAccountManager = () => {
  // 基础状态
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);
  const [selectedItems, setSelectedItems] = useState([]);
  
  // 对话框状态
  const [accountDialog, setAccountDialog] = useState(INITIAL_ACCOUNT_DIALOG);
  const [loginDialog, setLoginDialog] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(INITIAL_CONFIRM_DIALOG);

  // 加载账户数据
  const loadAccountData = useCallback(async () => {
    setLoading(true);
    try {
      const req = {
        page: pagination.page,
        page_size: pagination.pageSize,
        account_name: searchTerm || '',
        login_type: '',
        remark: '',
        order_by: '',
        order_desc: false,
        extra_filters: {}
      };
      
      const result = await AccountService.GetAccountList(req);
      
      if (result && result.code === 0) {
        setData(result.data.list || []);
        setPagination(prev => ({
          ...prev,
          total: result.data.total || 0
        }));
      } else {
        toast.error('加载账户数据失败: ' + (result?.msg || '未知错误'));
      }
    } catch (error) {
      console.error('加载账户数据失败:', error);
      toast.error('加载账户数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, searchTerm]);

  // 防抖加载数据
  useEffect(() => {
    const timer = setTimeout(() => {
      loadAccountData();
    }, 300);
    return () => clearTimeout(timer);
  }, [loadAccountData]);

  // 搜索处理
  const handleSearch = (value) => {
    setSearchTerm(value);
    if (pagination.page !== 1) {
      setPagination(prev => ({ ...prev, page: 1 }));
    }
  };

  // 选择处理
  const handleSelectItem = (id) => {
    setSelectedItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(data.map(item => item.id));
    }
  };

  // 账户操作
  const handleEditAccount = (account) => {
    setAccountDialog({
      isOpen: true,
      account: account,
      title: '编辑账户'
    });
  };

  const handleDeleteAccount = (account) => {
    setConfirmDialog({
      isOpen: true,
      title: '确认删除',
      message: `确定要删除账户"${account.account_name}"吗？此操作无法撤销。`,
      onConfirm: () => confirmDeleteAccount(account.id)
    });
  };

  const handleBatchDelete = () => {
    if (selectedItems.length === 0) {
      toast.warning('请选择要删除的账户');
      return;
    }
    
    setConfirmDialog({
      isOpen: true,
      title: '批量删除',
      message: `确定要删除选中的 ${selectedItems.length} 个账户吗？此操作无法撤销。`,
      onConfirm: () => confirmBatchDelete()
    });
  };

  // 确认删除操作
  const confirmDeleteAccount = async (id) => {
    try {
      const result = await AccountService.DeleteAccount(id);
      if (result && result.code === 0) {
        toast.success(result.msg || '删除成功');
        loadAccountData();
      } else {
        toast.error(result?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除账户失败:', error);
      toast.error('删除失败: ' + error.message);
    }
    setConfirmDialog(INITIAL_CONFIRM_DIALOG);
  };

  const confirmBatchDelete = async () => {
    try {
      const result = await AccountService.BatchDeleteAccounts(selectedItems);
      if (result && result.code === 0) {
        toast.success(result.msg || '批量删除成功');
        setSelectedItems([]);
        loadAccountData();
      } else {
        toast.error(result?.msg || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除账户失败:', error);
      toast.error('批量删除失败: ' + error.message);
    }
    setConfirmDialog(INITIAL_CONFIRM_DIALOG);
  };

  const handleAccountSubmit = async (accountData) => {
    try {
      const result = await AccountService.UpdateAccount(accountData);
      
      if (result && result.code === 0) {
        toast.success(result.msg || '更新成功');
        loadAccountData();
      } else {
        toast.error(result?.msg || '更新失败');
      }
    } catch (error) {
      console.error('更新账户失败:', error);
      toast.error('更新失败: ' + error.message);
    }
  };

  // 登录处理
  const handleLogin = {
    email: async (email, password) => {
      try {
        const result = await AccountService.LoginWithEmailDirectly(email, password);
        if (result && result.code === 0) {
          loadAccountData();
        }
        return result;
      } catch (error) {
        console.error('邮箱登录失败:', error);
        return {
          code: -1,
          msg: '邮箱登录失败: ' + error.message,
          data: null
        };
      }
    },
    phone: async (phone, code) => {
      try {
        const result = await AccountService.LoginWithPhoneCode(phone, code);
        if (result && result.code === 0) {
          loadAccountData();
        }
        return result;
      } catch (error) {
        console.error('手机验证码登录失败:', error);
        return {
          code: -1,
          msg: '手机验证码登录失败: ' + error.message,
          data: null
        };
      }
    },
    cookie: async (cookie) => {
      try {
        const result = await AccountService.LoginWithCookie(cookie);
        if (result && result.code === 0) {
          loadAccountData();
        }
        return result;
      } catch (error) {
        console.error('Cookie登录失败:', error);
        return {
          code: -1,
          msg: 'Cookie登录失败: ' + error.message,
          data: null
        };
      }
    }
  };

  const handleSendCode = async (phone) => {
    try {
      const result = await AccountService.SendLoginCode(phone);
      return result;
    } catch (error) {
      console.error('发送验证码失败:', error);
      return {
        code: -1,
        msg: '发送验证码失败: ' + error.message,
        data: null
      };
    }
  };

  return {
    // 状态
    searchTerm,
    loading,
    data,
    pagination,
    selectedItems,
    accountDialog,
    loginDialog,
    confirmDialog,
    
    // 操作函数
    handleSearch,
    handleSelectItem,
    handleSelectAll,
    handleEditAccount,
    handleDeleteAccount,
    handleBatchDelete,
    handleAccountSubmit,
    handleLogin,
    handleSendCode,
    loadAccountData,
    
    // 对话框控制
    setAccountDialog,
    setLoginDialog,
    setConfirmDialog,
    
    // 常量
    INITIAL_ACCOUNT_DIALOG,
    INITIAL_CONFIRM_DIALOG
  };
}; 