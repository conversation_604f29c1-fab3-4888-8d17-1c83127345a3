# AccountManager 模块

## 📁 目录结构

```
AccountManager/
├── components/           # 页面专用组件
│   ├── TableColumns.jsx    # 表格列配置
│   └── index.js            # 组件统一导出
├── hooks/               # 页面专用hooks
│   ├── useAccountManager.js # 主要业务逻辑hook
│   └── index.js            # hooks统一导出
├── constants/           # 页面常量
│   └── index.js            # 常量定义
├── utils/              # 页面工具函数
│   └── index.js            # 工具函数
├── AccountManager.jsx   # 页面主组件
├── index.js            # 页面统一导出
└── README.md           # 说明文档
```

## 🔧 模块说明

### 📋 常量模块 (constants)
- `ACCOUNT_STATUS` - 账户状态常量
- `STATUS_VARIANTS` - 状态对应的UI变体
- `INITIAL_*` - 各种初始状态配置

### 🛠️ 工具模块 (utils)
- `formatTime()` - 时间格式化工具
- `getAccountStatus()` - 获取账户状态
- `isAccountClickable()` - 判断账户是否可点击

### 🎣 Hooks模块 (hooks)
- `useAccountManager` - 主要业务逻辑管理
  - 状态管理：数据、分页、选择、对话框等
  - API调用：加载数据、增删改操作
  - 事件处理：搜索、选择、操作等

### 🧩 组件模块 (components)
- `createTableColumns()` - 动态生成表格列配置
- 支持自定义操作回调函数

### 🎨 主组件 (AccountManager.jsx)
- 纯UI展示组件
- 专注于视图渲染
- 通过props接收数据和事件处理函数

## 🚀 使用方式

```javascript
import AccountManager from './pages/AccountManager';

// 在父组件中使用
<AccountManager onAccountSelect={handleAccountSelect} />
```

## ✨ 设计优势

1. **关注点分离** - 业务逻辑、UI组件、工具函数分离
2. **可重用性** - hooks和工具函数可在其他页面复用
3. **可测试性** - 每个模块都可独立测试
4. **可维护性** - 清晰的文件结构，易于维护和扩展
5. **类型安全** - 完整的JSDoc注释和参数说明

## 🔄 迁移说明

原来的单文件`AccountManager.jsx`已重构为模块化结构，向后兼容，使用方式保持不变。 