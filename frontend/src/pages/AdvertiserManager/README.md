# AdvertiserManager 广告主管理模块

## 📋 概述

广告主管理模块提供了完整的广告主数据管理功能，包括数据查看、搜索、刷新和同步等操作。该模块采用现代化的 React 架构，注重代码的可读性和维护性。

## 🏗️ 架构设计

### 目录结构

```
AdvertiserManager/
├── hooks/
│   ├── useAdvertiserManager.js # 业务逻辑Hook
│   └── index.js             # Hook导出
├── AdvertiserManager.jsx    # 主组件（包含所有相关代码）
├── index.js                 # 模块导出
└── README.md                # 文档
```

### 设计理念

- ✅ **简洁明了** - 将相关的常量、工具函数、表格配置合并到主文件中
- ✅ **避免过度解耦** - 减少不必要的文件分割，提高代码可读性
- ✅ **单一职责** - 每个文件都有明确的职责边界
- ✅ **易于维护** - 减少文件跳转，方便开发和调试

### 核心特性

- ✅ **无分页设计** - 一次性加载全部数据，提升用户体验
- ✅ **实时搜索** - 300ms 防抖搜索，响应迅速
- ✅ **智能刷新** - 区分数据库刷新和 API 同步
- ✅ **状态管理** - 全局账户状态共享
- ✅ **错误处理** - 完善的错误提示和异常处理
- ✅ **性能优化** - 使用虚拟滚动和 useCallback 避免重复渲染

## 🎯 核心模块

### 🎣 useAdvertiserManager Hook

**功能：** 统一管理广告主列表的状态和业务逻辑

**主要状态：**

```javascript
{
  searchTerm, // 搜索关键词
    loading, // 加载状态
    data, // 广告主列表数据
    confirmDialog, // 确认对话框状态
    selectedAccount; // 当前选中账户
}
```

**主要方法：**

- `handleSearch()` - 搜索处理（防抖）
- `refreshAdvertisers()` - 从数据库刷新数据
- `syncAdvertisers()` - 从 API 同步最新数据
- `handleDeleteAdvertiser()` - 删除广告主
- `handleSubAccountNameClick()` - 处理广告主点击

### 🎨 AdvertiserManager 主组件

**功能：** 主组件包含所有相关代码，便于理解和维护

**内置模块：**

- **常量定义** - 广告主状态、颜色配置、列宽等
- **工具函数** - 格式化函数（金额、百分比、数字、文本）
- **表格配置** - 动态生成表格列配置
- **UI 组件** - 主界面渲染和交互逻辑

**主要区域：**

- 工具栏：操作按钮、搜索框、统计信息
- 数据表格：使用虚拟滚动的广告主列表
- 确认对话框：删除确认等交互

### 📊 表格列配置

**包含列：**

- 序号、账户名称、账户 ID、账户状态
- 账户余额、转化成本、消耗数据
- 展示数、转化数、转化率、备注

### 🛠️ 内置工具函数

- `formatAmount()` - 金额格式化（千分位 + 货币符号）
- `formatNumber()` - 数字格式化（千分位分隔）
- `formatPercentage()` - 百分比格式化
- `truncateText()` - 文本截断处理

### 🎨 内置常量

- `ADVERTISER_STATUS` - 广告主状态枚举
- `STATUS_COLORS` - 状态颜色映射
- `STATUS_VARIANTS` - StatusBadge 变体映射
- `COLUMN_WIDTHS` - 表格列宽配置

## 🚀 使用方式

```javascript
import AdvertiserManager from "./pages/AdvertiserManager";

// 基本用法
<AdvertiserManager
  preSelectedAccount={account}
  onSubAccountSelect={handleSelect}
/>;
```

## 🔧 API 交互

### 数据加载

- **方法：** `AdvertiserService.getAdvertiserList()`
- **参数：** `{ accountId, advertiserName, page: 0, pageSize: 0 }`
- **返回：** `{ code, data: { list, total }, msg }`

### 数据同步

- **方法：** `AdvertiserService.refreshAdvertisers()`
- **参数：** `accountId`
- **返回：** `{ code, msg }`

### 删除操作

- **方法：** `AdvertiserService.deleteAdvertiser()`
- **参数：** `advertiserId`
- **返回：** `{ code, msg }`

## ⚡ 性能优化

1. **虚拟滚动** - VirtualDataTable 处理大量数据
2. **防抖搜索** - 300ms 延迟避免频繁请求
3. **重复调用保护** - 防止同时发起多个相同请求
4. **手动刷新标记** - 避免 useEffect 重复触发
5. **useCallback 优化** - 避免不必要的组件重渲染
6. **缓存机制** - 会话级数据缓存和滚动位置恢复

## 🐛 错误处理

- 网络请求异常：显示友好错误提示
- 数据格式异常：提供默认值和兜底逻辑
- 操作权限检查：账户选择状态验证
- 加载状态管理：防止重复操作

## 📝 更新日志

### v3.0.0 (当前版本) - 代码可读性优化

- ✅ **合并代码结构** - 将 constants、utils、components 合并到主文件
- ✅ **提高可读性** - 减少文件跳转，代码逻辑更清晰
- ✅ **避免过度解耦** - 删除不必要的文件分割
- ✅ **统一代码风格** - 使用清晰的注释分割不同功能区域
- ✅ **保持功能完整** - 所有原有功能保持不变

### v2.0.0 (之前版本)

- ✅ 移除分页功能，改为全量加载
- ✅ 精简 Hook 接口，移除冗余函数
- ✅ 优化性能，减少不必要的渲染
- ✅ 增强错误处理和用户体验

### v1.0.0 (原始版本)

- 基础功能实现
- 分页加载数据
- 基本的 CRUD 操作

## 🔄 迁移说明

从 v2.0.0 到 v3.0.0 的主要变化：

- 合并了 constants、utils、components 到主文件中
- 提高了代码的可读性和维护性
- 减少了文件间的依赖关系
- 使用方式保持完全兼容，无需修改调用代码

## 💡 代码组织原则

1. **可读性优先** - 相关代码集中管理，减少文件跳转
2. **适度抽象** - 只有当代码需要在多处复用时才进行抽离
3. **清晰分区** - 使用注释分割不同功能区域
4. **统一风格** - 保持一致的代码风格和命名规范

## 🤝 贡献指南

1. 保持代码风格一致
2. 添加完整的 JSDoc 注释
3. 优先考虑代码可读性
4. 避免不必要的过度解耦
5. 遵循 React Hooks 最佳实践

## 功能概述

广告主管理页面提供了完整的广告主数据管理功能，包括查看、搜索、筛选、策略绑定和头像上传等功能。

## 主要功能

### 1. 数据展示

- 支持虚拟滚动的大数据量展示
- 多列排序功能
- 状态颜色标识
- 数据格式化显示

### 2. 搜索与筛选

- 实时搜索（支持广告主名称、ID、备注）
- 状态筛选
- 高级筛选对话框
- 浮动搜索框

### 3. 批量操作

- 批量选择（Ctrl+A 全选）
- 批量删除
- 批量策略绑定

### 4. 右键菜单功能

- **更改头像** - 上传广告主头像
- **绑定策略** - 为广告主绑定策略
- **复制信息** - 复制广告主 ID、名称等信息
- **刷新数据** - 重新加载广告主数据
- **同步数据** - 从巨量引擎同步最新数据

### 5. 头像上传功能

#### 使用方法

1. 在广告主列表中右键点击任意广告主
2. 选择"更改头像"选项
3. 在弹出的对话框中：
   - 点击"选择图片"按钮选择图片文件
   - 或直接拖拽图片文件到上传区域
4. 预览图片效果
5. 点击"确认上传"完成头像上传

#### 支持的图片格式

- JPG/JPEG
- PNG
- GIF
- WebP

#### 文件大小限制

- 最大文件大小：5MB

#### 技术实现

- 前端：使用 FileReader API 读取图片文件
- 后端：调用巨量引擎 API 上传头像
- 支持图片尺寸自动检测
- 完整的错误处理和用户反馈

### 6. 策略管理

- 单个广告主策略绑定
- 批量广告主策略绑定
- 策略解绑功能
- 策略状态显示

## 键盘快捷键

- `Ctrl + A` - 全选/取消全选
- `Ctrl + F` - 快速搜索
- `Delete` - 删除选中项

## 状态说明

### 状态颜色标识

- 🟢 绿色 - 正常/成功状态（审核通过、正常）
- 🔵 蓝色 - 待处理状态（审核中、待提交资质等）
- 🔴 红色 - 问题状态（审核不通过、已禁用等）
- 🟠 橙色 - 警告状态（暂停、限制）
- ⚫ 灰色 - 默认/未知状态

## 技术特性

- 虚拟滚动优化大数据量性能
- 响应式设计适配不同屏幕
- 完整的错误处理和用户反馈
- 操作日志记录
- 数据缓存和状态管理

## 依赖组件

- VirtualDataTable - 虚拟滚动表格
- AvatarUploadDialog - 头像上传对话框
- StrategyBindDialog - 策略绑定对话框
- StatusBar - 状态栏
- FloatingSearchBox - 浮动搜索框
- FilterDialog - 筛选对话框

## 更新日志

### v1.1.0

- 新增头像上传功能
- 优化右键菜单体验
- 改进错误处理机制

### v1.0.0

- 基础广告主管理功能
- 策略绑定功能
- 搜索和筛选功能
