import { useState, useEffect, useCallback, useRef } from "react";
import { useAccountContext } from "../../../contexts/AccountContext";
import { useStrategyMap } from "../../../contexts/StrategyMapContext";
import { toast } from "../../../components/ui/Toast";
import AdvertiserService from "../../../services/advertiserService.js";
import strategyService from "../../../services/strategyService.js";

/**
 * AdvertiserManager 自定义Hook
 * 管理广告主列表的状态和业务逻辑
 */
export const useAdvertiserManager = (
  preSelectedAccount,
  onSubAccountSelect
) => {
  // 使用策略映射
  const { updateStrategyMap } = useStrategyMap();

  // 基础状态 - 仅保留必要的状态，搜索筛选由useManagerPage处理
  const [loading, setLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [data, setData] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    message: "",
    confirmText: "确定",
    onConfirm: null,
  });

  // 排序状态
  const SORT_KEY = "advertiserManagerSort";

  const getDefaultSort = () => {
    try {
      const saved = localStorage.getItem(SORT_KEY);
      if (saved) return JSON.parse(saved);
    } catch {}
    return { field: "stat_cost", order: "desc" }; // 默认值
  };

  const [sortConfig, setSortConfig] = useState(getDefaultSort());

  // 用于避免重复调用的标记
  const isManualRefreshRef = useRef(false);

  // 使用全局账户状态
  const { sharedAccount, setAccount } = useAccountContext();

  // 当前使用的账户：优先使用共享状态，其次使用传入的预选账户
  const selectedAccount = sharedAccount || preSelectedAccount;

  // 预加载策略映射
  const preloadStrategyMap = useCallback(async () => {
    try {
      const response = await strategyService.getStrategyList({
        page: 1,
        pageSize: 1000,
        enabled: undefined,
      });

      if (response.code === 0 && response.data && response.data.list) {
        updateStrategyMap(response.data.list);
        console.log(
          `广告主管理页面预加载了 ${response.data.list.length} 个策略的映射`
        );
      }
    } catch (error) {
      console.warn("广告主管理页面预加载策略映射失败:", error);
    }
  }, [updateStrategyMap]);

  // 加载广告主数据的核心函数
  const loadAdvertiserData = useCallback(
    async (forceRefresh = false) => {
      // 如果没有选中账户，清空数据
      if (!selectedAccount?.account_id) {
        setData([]);
        return;
      }

      // 如果是强制刷新，清空选择状态
      if (forceRefresh) {
        setSelectedItems([]);
      }

      setLoading(true);
      try {
        // 先预加载策略映射，确保策略名称能正确显示
        await preloadStrategyMap();

        const params = {
          accountId: selectedAccount.account_id,
          advertiserName: "", // 移除搜索条件，获取所有数据
          page: 0,
          pageSize: 0,
        };

        const result = await AdvertiserService.getAdvertiserList(params);

        if (result && result.code === 0 && result.data) {
          const newData = result.data.list || [];
          setData(newData);
        } else {
          toast.error("加载广告主数据失败: " + (result?.msg || "未知错误"));
        }
      } catch (error) {
        toast.error("加载广告主数据失败: " + error.message);
      } finally {
        setLoading(false);
      }
    },
    [selectedAccount?.account_id, preloadStrategyMap]
  );

  // 保存排序配置到本地存储
  useEffect(() => {
    localStorage.setItem(SORT_KEY, JSON.stringify(sortConfig));
  }, [sortConfig]); // 当 sortConfig 变化时执行

  // 只在preSelectedAccount变化且与当前sharedAccount不同时更新共享状态
  useEffect(() => {
    if (
      preSelectedAccount &&
      (!sharedAccount ||
        sharedAccount.account_id !== preSelectedAccount.account_id)
    ) {
      setAccount(preSelectedAccount);
      // 从账号列表点击"账户信息"进入时自动加载数据
      setTimeout(() => {
        loadAdvertiserData(true);
      }, 100);
    }
  }, [
    preSelectedAccount?.account_id,
    sharedAccount,
    setAccount,
    loadAdvertiserData,
  ]);

  // 监听sharedAccount变化，确保账户选择器改变时能及时更新数据
  useEffect(() => {
    // 当sharedAccount存在且与当前数据不匹配时，重新加载数据
    if (sharedAccount?.account_id) {
      // 检查当前数据是否属于不同的账户
      const currentDataAccountId = data.length > 0 ? data[0]?.account_id : null;
      if (currentDataAccountId !== sharedAccount.account_id) {
        console.log("检测到账户选择器变化，重新加载广告主数据:", sharedAccount);
        // 设置手动刷新标记，避免useEffect重复触发
        isManualRefreshRef.current = true;
        setTimeout(() => {
          loadAdvertiserData(true);
        }, 100);
      }
    }
  }, [sharedAccount?.account_id, data, loadAdvertiserData]);

  // 自动加载数据 - 防抖延迟
  useEffect(() => {
    // 如果是手动刷新，跳过useEffect的自动加载
    if (isManualRefreshRef.current) {
      isManualRefreshRef.current = false;
      return;
    }

    if (selectedAccount?.account_id) {
      const timer = setTimeout(() => {
        loadAdvertiserData(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [selectedAccount?.account_id, loadAdvertiserData]);

  // 刷新广告主数据（从数据库重新加载）
  const refreshAdvertisers = useCallback(async () => {
    if (!selectedAccount) {
      toast.warning("请先选择账户");
      return;
    }

    if (loading) return;

    // 设置手动刷新标记，避免useEffect重复触发
    isManualRefreshRef.current = true;
    await loadAdvertiserData(false);
    toast.success("刷新成功");
  }, [selectedAccount, loading, loadAdvertiserData]);

  // 同步广告主数据（从API获取最新数据）
  const syncAdvertisers = useCallback(async () => {
    if (!selectedAccount) {
      toast.warning("请先选择账户");
      return;
    }

    // 设置同步loading状态
    setSyncLoading(true);

    try {
      const result = await AdvertiserService.refreshAdvertisers(
        selectedAccount.account_id
      );

      if (result && result.code === 0) {
        toast.success("同步成功");
        // 设置手动刷新标记，避免useEffect重复触发
        isManualRefreshRef.current = true;
        await loadAdvertiserData(false);
      } else {
        toast.error("同步失败: " + (result?.msg || "未知错误"));
      }
    } catch (error) {
      toast.error("同步失败: " + error.message);
    } finally {
      // 清除同步loading状态
      setSyncLoading(false);
    }
  }, [selectedAccount, loadAdvertiserData]);

  // 删除广告主
  const handleDeleteAdvertiser = useCallback((advertiser) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认删除",
      message: `确定要删除广告主"${advertiser.advertiser_name}"吗？此操作无法撤销。`,
      confirmText: "删除",
      onConfirm: () => confirmDeleteAdvertiser(advertiser.id),
    });
  }, []);

  const confirmDeleteAdvertiser = async (id) => {
    try {
      const result = await AdvertiserService.deleteAdvertiser(id);

      if (result && result.code === 0) {
        toast.success(result.msg || "删除成功");
        // 设置手动刷新标记，避免useEffect重复触发
        isManualRefreshRef.current = true;
        await loadAdvertiserData(false);
      } else {
        toast.error(result?.msg || "删除失败");
      }
    } catch (error) {
      toast.error("删除失败: " + error.message);
    }
    setConfirmDialog({
      isOpen: false,
      title: "",
      message: "",
      confirmText: "确定",
      onConfirm: null,
    });
  };

  // 处理项目选择
  const handleItemSelect = useCallback(
    (advertiserIdOrIds, isCtrlKey = false) => {
      console.log("handleItemSelect 被调用:", {
        advertiserIdOrIds,
        isCtrlKey,
        type: typeof advertiserIdOrIds,
        isArray: Array.isArray(advertiserIdOrIds),
      });

      // 如果传入的是数组（批量选择）
      if (Array.isArray(advertiserIdOrIds)) {
        const newItems = advertiserIdOrIds;
        if (isCtrlKey) {
          // Ctrl+拖拽：合并选择
          setSelectedItems((prev) => {
            const result = [...new Set([...prev, ...newItems])];
            console.log("Ctrl+拖拽合并选择:", { prev, newItems, result });
            return result;
          });
        } else {
          // 普通拖拽：替换选择
          console.log("普通拖拽替换选择:", newItems);
          setSelectedItems(newItems);
        }
      } else {
        // 单项选择逻辑
        const advertiserId = advertiserIdOrIds;
        setSelectedItems((prev) => {
          let result;
          if (isCtrlKey) {
            // Ctrl+单击：切换选择状态（多选模式）
            if (prev.includes(advertiserId)) {
              result = prev.filter((item) => item !== advertiserId);
              console.log("Ctrl+单击取消选择:", { advertiserId, prev, result });
            } else {
              result = [...prev, advertiserId];
              console.log("Ctrl+单击添加选择:", { advertiserId, prev, result });
            }
          } else {
            // 普通单击：单选模式，清空其他选择，只选中当前项
            if (prev.includes(advertiserId) && prev.length === 1) {
              // 如果当前项已选中且只选中了这一项，则取消选择
              result = [];
              console.log("普通单击取消选择:", { advertiserId, prev, result });
            } else {
              // 否则清空其他选择，只选中当前项
              result = [advertiserId];
              console.log("普通单击单选:", { advertiserId, prev, result });
            }
          }
          return result;
        });
      }
    },
    []
  );

  // 为handleItemSelect添加批量选择标识，供VirtualDataTable识别
  handleItemSelect.isBatchSelect = true;

  // 处理广告主名称点击
  const handleSubAccountNameClick = useCallback(
    (subAccount) => {
      if (onSubAccountSelect && subAccount.advertiser_id && selectedAccount) {
        const subAccountWithMainAccount = {
          ...subAccount,
          account_id: selectedAccount.account_id,
          main_account_name: selectedAccount.account_name,
        };
        onSubAccountSelect(subAccountWithMainAccount);
      }
    },
    [onSubAccountSelect, selectedAccount]
  );

  // 注意：搜索、筛选、对话框控制等功能现在由useManagerPage提供

  // 处理排序
  const handleSort = useCallback((field) => {
    setSortConfig((prev) => {
      // 如果点击的是当前排序字段
      if (prev.field === field) {
        // 循环切换：desc -> asc -> null
        if (prev.order === "desc") {
          return { field, order: "asc" };
        } else if (prev.order === "asc") {
          return { field: null, order: null };
        }
      }
      // 如果点击的是新字段，默认降序
      return { field, order: "desc" };
    });
  }, []);

  // 获取排序后的数据
  const getSortedData = useCallback(
    (dataToSort) => {
      const sorted = [...dataToSort].sort((a, b) => {
        // 首先按关注状态排序：已关注的排在前面
        const aFollowed = a.is_followed === true;
        const bFollowed = b.is_followed === true;

        if (aFollowed && !bFollowed) return -1;
        if (!aFollowed && bFollowed) return 1;

        // 如果关注状态相同，再按其他字段排序
        if (!sortConfig.field || !sortConfig.order) {
          return 0;
        }

        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];

        // 处理空值
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        // 特殊处理备注字段 - 使用拼音排序
        if (sortConfig.field === "advertiser_remark") {
          const aStr = String(aValue || "");
          const bStr = String(bValue || "");

          // 使用中文拼音排序
          const comparison = aStr.localeCompare(bStr, "zh-CN", {
            numeric: true,
            sensitivity: "base",
          });

          return sortConfig.order === "asc" ? comparison : -comparison;
        }

        // 数值比较
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return sortConfig.order === "asc" ? aNum - bNum : bNum - aNum;
        }

        // 字符串比较（备用）
        return sortConfig.order === "asc"
          ? String(aValue).localeCompare(String(bValue))
          : String(bValue).localeCompare(String(aValue));
      });

      return sorted;
    },
    [sortConfig]
  );

  return {
    // 状态
    loading,
    syncLoading, // 同步loading状态
    data, // 原始数据，由useManagerPage进行筛选
    selectedItems,
    selectedAccount,
    sortConfig,
    confirmDialog,

    // 操作函数
    loadAdvertiserData,
    refreshAdvertisers,
    syncAdvertisers,
    handleDeleteAdvertiser,
    handleItemSelect,
    handleSubAccountNameClick,
    handleSort,
    getSortedData,

    // 对话框控制
    setConfirmDialog,

    // 账户管理
    setAccount,

    // 选择项管理
    setSelectedItems,
  };
};
