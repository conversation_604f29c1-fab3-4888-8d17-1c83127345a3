import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import {
  RefreshCw,
  Users,
  Trash2,
  CheckSquare,
  Square,
  Search,
  Filter,
  X,
  Eye,
  EyeOff,
  ChevronUp,
  ChevronDown,
  Copy,
  Target,
  User,
  Heart,
  Globe,
  Clipboard,
  Settings,
  Play,
} from "lucide-react";
import {
  Button,
  Card,
  ConfirmDialog,
  VirtualDataTable,
  EmptyState,
  FilterDialog,
  StatusBar,
  MobileStatsBar,
  HighlightText,
  FloatingSearchBox,
  AvatarUploadDialog,
  BatchAvatarUploadDialog,
  Switch,
  CopyPageDialog,
} from "../../components/ui";
import { useAdvertiserManager } from "./hooks";
import { useManagerPage } from "../../hooks/useManagerPage";
import StrategyBindDialog from "../../components/ui/StrategyBindDialog";
import StrategyNameDisplay from "../../components/strategy/StrategyNameDisplay";
import advertiserService from "../../services/advertiserService";
import accountService from "../../services/accountService";
import { toast } from "../../components/ui/Toast";
import { useAccountContext } from "../../contexts/AccountContext";
import { useCopyPage } from "../../contexts/CopyPageContext";
import testTaskProgressService from "../../services/testTaskProgressService";

// =============================================================================
// 常量定义和工具函数
// =============================================================================

/**
 * 根据状态名称配置颜色 - 智能状态识别
 *
 * 状态分类：
 * 🟢 绿色系 - 正常/成功状态：审核通过、正常
 * 🔵 蓝色系 - 待处理状态：审核中、待提交资质、待合规归档、待缴费、待对公验证
 * 🔴 红色系 - 问题状态：审核不通过、已禁用、暂停投放
 * 🟠 橙色系 - 警告状态：暂停、限制
 * ⚫ 灰色系 - 默认/未知状态
 */
const getStatusColors = (statusName) => {
  // 统一的文字颜色
  const textColor = "#4b5563"; // 深灰色文字，保持一致

  // 正常/成功状态 - 柔和绿色圆点
  if (statusName?.includes("审核通过") || statusName?.includes("正常")) {
    return {
      dot: "#22c55e", // 更柔和的绿色
      bg: "rgba(34, 197, 94, 0.1)",
      text: textColor,
    };
  }

  // 审核中/待处理状态 - 柔和蓝色圆点
  if (
    statusName?.includes("审核中") ||
    statusName?.includes("待提交") ||
    statusName?.includes("待合规") ||
    statusName?.includes("待缴费") ||
    statusName?.includes("待对公验证")
  ) {
    return {
      dot: "#60a5fa", // 更柔和的蓝色
      bg: "rgba(96, 165, 250, 0.1)",
      text: textColor,
    };
  }

  // 问题/禁用状态 - 柔和红色圆点
  if (
    statusName?.includes("审核不通过") ||
    statusName?.includes("已禁用") ||
    statusName?.includes("暂停投放")
  ) {
    return {
      dot: "#f87171", // 更柔和的红色
      bg: "rgba(248, 113, 113, 0.1)",
      text: textColor,
    };
  }

  // 警告状态 - 柔和橙色圆点
  if (statusName?.includes("暂停") || statusName?.includes("限制")) {
    return {
      dot: "#fbbf24", // 更柔和的橙色
      bg: "rgba(251, 191, 36, 0.1)",
      text: textColor,
    };
  }

  // 默认状态 - 柔和灰色圆点
  return {
    dot: "#9ca3af", // 更柔和的灰色
    bg: "rgba(156, 163, 175, 0.1)",
    text: textColor,
  };
};

/**
 * 格式化金额
 */
const formatAmount = (amount) => {
  if (!amount || amount === 0) return "¥0.00";
  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return "¥0.00";

  return `¥${numAmount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

/**
 * 格式化百分比
 */
const formatPercentage = (rate) => {
  if (!rate || rate === 0) return "0%";
  const numRate = typeof rate === "string" ? parseFloat(rate) : rate;
  if (isNaN(numRate)) return "0%";

  if (numRate < 1) {
    return `${(numRate * 100).toFixed(2)}%`;
  }

  return `${numRate.toFixed(2)}%`;
};

/**
 * 格式化数字显示
 */
const formatNumber = (num) => {
  if (!num || num === 0) return "0";
  const numValue = typeof num === "string" ? parseInt(num) : num;
  if (isNaN(numValue)) return "0";

  return numValue.toLocaleString("zh-CN");
};

/**
 * 截断文本
 */
const truncateText = (text, maxLength = 50) => {
  if (!text) return "-";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

/**
 * 格式化时间为 HH:mm:ss 格式
 */
const formatTimeOnly = (timeStr) => {
  if (!timeStr) return "-";
  try {
    const date = new Date(timeStr);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");
    return `${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return "-";
  }
};

/**
 * 状态筛选选项
 */
const STATUS_FILTER_OPTIONS = [
  { value: "all", label: "全部状态" },
  { value: "审核通过", label: "审核通过" },
  { value: "正常", label: "正常" },
  { value: "审核中", label: "审核中" },
  { value: "待提交", label: "待提交资质" },
  { value: "待合规", label: "待合规归档" },
  { value: "待缴费", label: "待缴费" },
  { value: "待对公验证", label: "待对公验证" },
  { value: "审核不通过", label: "审核不通过" },
  { value: "已禁用", label: "已禁用" },
  { value: "暂停投放", label: "暂停投放" },
  { value: "暂停", label: "暂停" },
  { value: "限制", label: "限制" },
];

// =============================================================================
// 表格单元格组件
// =============================================================================

/**
 * 序号单元格
 */
const IndexCell = ({ index }) => (
  <span className="text-xs text-slate-900">{index + 1}</span>
);

/**
 * 广告主名称单元格
 */
const AdvertiserNameCell = ({ record, onClick, searchTerm }) => (
  <div
    className="text-xs font-medium text-blue-600 hover:text-blue-800 cursor-pointer max-w-[280px] truncate"
    title={record.advertiser_name}
    onClick={() => onClick(record)}
    data-clickable="true"
  >
    <HighlightText
      text={record.advertiser_name || "未命名广告主"}
      searchTerm={searchTerm}
    />
  </div>
);

/**
 * 广告主ID单元格
 */
const AdvertiserIdCell = ({ value, searchTerm }) => (
  <div className="text-xs text-slate-500" title={value}>
    <HighlightText text={value || "-"} searchTerm={searchTerm} />
  </div>
);

/**
 * 状态单元格 - 使用圆点设计，根据状态名称判断颜色
 */
const StatusCell = ({ record }) => {
  const statusText = record.advertiser_status_name;
  const colors = getStatusColors(statusText);

  return (
    <div className="flex items-center gap-2">
      {/* 状态圆点 - 简洁设计，无动画 */}
      <div
        className="w-2 h-2 rounded-full flex-shrink-0"
        style={{
          backgroundColor: colors.dot,
          opacity: 0.85,
        }}
      />
      {/* 状态文字 - 统一样式 */}
      <span
        className="text-xs text-slate-600"
        style={{ color: colors.text }}
        title={statusText}
      >
        {statusText || "未知状态"}
      </span>
    </div>
  );
};

/**
 * 金额单元格
 */
const AmountCell = ({ value }) => (
  <span className="text-xs text-slate-500">{formatAmount(value)}</span>
);

/**
 * 数字单元格
 */
const NumberCell = ({ value }) => (
  <span className="text-xs text-slate-500">{formatNumber(value)}</span>
);

/**
 * 百分比单元格
 */
const PercentageCell = ({ value }) => (
  <span className="text-xs text-slate-500">{formatPercentage(value)}</span>
);

/**
 * 备注单元格
 */
const RemarkCell = ({ value, searchTerm }) => (
  <div className="text-xs text-slate-500 max-w-[150px] truncate" title={value}>
    <HighlightText text={truncateText(value, 20)} searchTerm={searchTerm} />
  </div>
);

/**
 * 关注状态单元格
 */
const FollowStatusCell = ({ record, onToggle, selectedAccount }) => {
  const [loading, setLoading] = useState(false);
  const isFollowed = record.is_followed === true;

  const handleClick = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (loading) return;

    if (!selectedAccount || !selectedAccount.account_id) {
      toast.error("请先选择主账户");
      return;
    }

    setLoading(true);
    try {
      const result = await advertiserService.updateIsFollowed(
        selectedAccount.account_id,
        record.advertiser_id,
        !isFollowed
      );

      if (result.success || result.code === 0) {
        // 更新本地数据
        record.is_followed = !isFollowed;
        onToggle && onToggle(record.advertiser_id, !isFollowed);
        toast.success(isFollowed ? "已取消关注" : "已添加关注");
      } else {
        toast.error(result.message || result.msg || "操作失败");
      }
    } catch (error) {
      console.error("关注操作失败:", error);
      toast.error("操作失败：" + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center">
      <Heart
        size={16}
        className={`transition-all duration-200 cursor-pointer ${
          loading
            ? "text-gray-300 animate-pulse"
            : isFollowed
            ? "text-red-500 fill-red-500 drop-shadow-sm hover:text-red-600"
            : "text-gray-400 hover:text-red-400"
        }`}
        fill={isFollowed ? "currentColor" : "none"}
        title={
          loading ? "处理中..." : isFollowed ? "点击取消关注" : "点击添加关注"
        }
        onClick={handleClick}
      />
    </div>
  );
};

/**
 * 更新时间单元格
 */
const UpdateTimeCell = ({ value }) => (
  <span
    className="text-xs text-slate-500"
    title={value ? new Date(value).toLocaleString("zh-CN") : ""}
  >
    {formatTimeOnly(value)}
  </span>
);

/**
 * 自动删除评论开关单元格
 */
const AutoDeleteCommentCell = ({ record, onToggle }) => {
  const [loading, setLoading] = useState(false);

  const handleToggle = async (checked) => {
    if (loading) return;

    setLoading(true);
    try {
      const result = await advertiserService.updateAutoDeleteComment(
        record.id,
        checked ? 1 : 0
      );
      if (result.success) {
        toast.success(result.message);
        // 更新本地数据
        record.auto_delete_comment = checked ? 1 : 0;
        onToggle && onToggle(record.id, checked);
      } else {
        toast.error(result.message || "操作失败");
      }
    } catch (error) {
      toast.error("操作失败：" + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="flex items-center justify-center"
      title="开启后，敏感词监控服务将自动删除该广告主下包含敏感词的评论"
    >
      <Switch
        checked={record.auto_delete_comment === 1}
        onChange={handleToggle}
        disabled={loading}
        size="small"
        className="mx-auto"
      />
    </div>
  );
};

// =============================================================================
// 界面组件
// =============================================================================

/**
 * 复制到剪贴板的工具函数
 */
const copyToClipboard = async (text, fieldName = "值") => {
  try {
    await navigator.clipboard.writeText(text);
    console.log(`已复制${fieldName}: ${text}`);
    // 这里可以添加Toast提示，如果需要的话
  } catch (err) {
    console.error("复制失败:", err);
    // 降级方案
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      console.log(`已复制${fieldName}(降级): ${text}`);
    } catch (fallbackErr) {
      console.error("降级复制也失败:", fallbackErr);
    }
    document.body.removeChild(textArea);
  }
};

/**
 * 测试任务进度条（出错版本） - 模拟在子任务中出现网络错误
 */
const testTaskProgressError = () => {
  console.log('开始测试任务进度条 - 模拟子任务中的网络错误');
  const taskId = 'advertiser_sync_error_' + Date.now();
  
  // 子任务1：连接API（正常完成）
  // 1.1 任务开始
  testTaskProgressService.emitTaskEvent({
    task_id: taskId,
    task_name: '广告主数据同步',
    task_description: '正在同步广告主账户数据（将在第2个子任务出错）',
    step_description: '连接巨量引擎API',
    total_steps: 3,
    current_step: 1,
    step_progress: 0,      // 子任务1开始：0%
    next_step_progress: 60, // 子任务1进度点：60%
    step_time: 1000,
    status: 'start',
    error_message: ''
  });

  // 1.2 子任务1完成
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步广告主账户数据（将在第2个子任务出错）',
      step_description: '连接巨量引擎API',
      total_steps: 3,
      current_step: 1,
      step_progress: 60,      // 当前进度：60%
      next_step_progress: 100, // 子任务1完成：100%
      step_time: 800,
      status: 'progress',
      error_message: ''
    });
  }, 1000);

  // 子任务2：获取数据（在这个子任务中出错）
  // 2.1 子任务2开始
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步广告主账户数据（将在第2个子任务出错）',
      step_description: '获取广告主数据',
      total_steps: 3,
      current_step: 2,
      step_progress: 0,       // 子任务2开始：0%
      next_step_progress: 40, // 子任务2第一个进度点：40%
      step_time: 1200,
      status: 'progress',
      error_message: ''
    });
  }, 1800);

  // 2.2 子任务2进度更新
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步广告主账户数据（将在第2个子任务出错）',
      step_description: '获取广告主数据',
      total_steps: 3,
      current_step: 2,
      step_progress: 40,      // 当前进度：40%
      next_step_progress: 80, // 准备到达80%时出错
      step_time: 1500,
      status: 'progress',
      error_message: ''
    });
  }, 3000);

  // 2.3 在子任务2的75%进度时出现错误
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步广告主账户数据（将在第2个子任务出错）',
      step_description: '获取广告主数据时出错',
      total_steps: 3,
      current_step: 2,
      step_progress: 75,      // 子任务2在75%时出错
      next_step_progress: 75, // 停止在75%
      step_time: 0,
      status: 'error',
      error_message: '网络连接超时：获取广告主数据失败，请检查网络设置后重试。错误代码: API_TIMEOUT_002'
    });
  }, 4500);
};

/**
 * 测试任务进度条 - 模拟广告主数据同步任务（正确的子任务进度）
 */
const testTaskProgress = () => {
  console.log('开始测试任务进度条 - 广告主数据同步（子任务进度）');
  const taskId = 'advertiser_sync_' + Date.now();
  
  // 子任务1：连接API
  // 1.1 任务开始
  testTaskProgressService.emitTaskEvent({
    task_id: taskId,
    task_name: '广告主数据同步',
    task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
    step_description: '连接巨量引擎API',
    total_steps: 3,
    current_step: 1,
    step_progress: 0,      // 子任务1开始：0%
    next_step_progress: 30, // 子任务1第一个进度点：30%
    step_time: 1000,
    status: 'start',
    error_message: ''
  });

  // 1.2 子任务1进度更新
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '连接巨量引擎API',
      total_steps: 3,
      current_step: 1,
      step_progress: 30,     // 当前进度：30%
      next_step_progress: 70, // 下一个进度点：70%
      step_time: 1500,
      status: 'progress',
      error_message: ''
    });
  }, 1000);

  // 1.3 子任务1完成
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '连接巨量引擎API',
      total_steps: 3,
      current_step: 1,
      step_progress: 70,      // 当前进度：70%
      next_step_progress: 100, // 子任务1完成：100%
      step_time: 800,
      status: 'progress',
      error_message: ''
    });
  }, 2500);

  // 子任务2：获取数据
  // 2.1 子任务2开始（进度条重新开始）
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '获取广告主数据',
      total_steps: 3,
      current_step: 2,
      step_progress: 0,       // 子任务2开始：0%
      next_step_progress: 25, // 子任务2第一个进度点：25%
      step_time: 1200,
      status: 'progress',
      error_message: ''
    });
  }, 3300);

  // 2.2 子任务2进度更新
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '获取广告主数据',
      total_steps: 3,
      current_step: 2,
      step_progress: 25,      // 当前进度：25%
      next_step_progress: 60, // 下一个进度点：60%
      step_time: 2000,
      status: 'progress',
      error_message: ''
    });
  }, 4500);

  // 2.3 子任务2继续更新
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '获取广告主数据',
      total_steps: 3,
      current_step: 2,
      step_progress: 60,      // 当前进度：60%
      next_step_progress: 100, // 子任务2完成：100%
      step_time: 1000,
      status: 'progress',
      error_message: ''
    });
  }, 6500);

  // 子任务3：保存数据
  // 3.1 子任务3开始（进度条再次重新开始）
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '保存到数据库',
      total_steps: 3,
      current_step: 3,
      step_progress: 0,       // 子任务3开始：0%
      next_step_progress: 50, // 子任务3第一个进度点：50%
      step_time: 1500,
      status: 'progress',
      error_message: ''
    });
  }, 7500);

  // 3.2 子任务3进度更新
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '保存到数据库',
      total_steps: 3,
      current_step: 3,
      step_progress: 50,      // 当前进度：50%
      next_step_progress: 100, // 子任务3完成：100%
      step_time: 1000,
      status: 'progress',
      error_message: ''
    });
  }, 9000);

  // 3.3 任务完成
  setTimeout(() => {
    testTaskProgressService.emitTaskEvent({
      task_id: taskId,
      task_name: '广告主数据同步',
      task_description: '正在同步选中的广告主账户数据，包括余额、状态、统计信息等',
      step_description: '同步完成',
      total_steps: 3,
      current_step: 3,
      step_progress: 100,     // 最后子任务完成：100%
      next_step_progress: 100,
      step_time: 0,
      status: 'end',
      error_message: ''
    });
  }, 10000);
};

/**
 * 创建右键菜单项配置
 */
const createContextMenuItems = (
  loading,
  syncLoading,
  selectedAccount,
  selectedItems,
  data,
  onRefresh,
  onSync,
  onClearSelection,
  onToggleSelectAll,
  onShowSearch,
  onShowFilter,
  showDeleted,
  onToggleShowDeleted,
  onBatchToggleAutoDelete
) => [
  {
    key: "search",
    label: "搜索 (Ctrl+F)",
    icon: <Search size={14} />,
    color: "#3b82f6",
    disabled: loading,
    onClick: () => onShowSearch(),
  },
  {
    key: "filter",
    label: "状态筛选",
    icon: <Filter size={14} />,
    color: "#8b5cf6",
    disabled: loading,
    onClick: () => onShowFilter(),
  },
  {
    type: "divider",
  },
  {
    key: "refresh",
    label: "刷新数据",
    icon: <RefreshCw size={14} className={loading ? "animate-spin" : ""} />,
    color: "#3b82f6",
    disabled: loading || !selectedAccount,
    onClick: () => onRefresh(),
  },
  {
    key: "sync",
    label: "同步数据",
    icon: <RefreshCw size={14} className={syncLoading ? "animate-spin" : ""} />,
    color: "#10b981",
    disabled: syncLoading || !selectedAccount,
    onClick: () => onSync(),
  },
  {
    type: "divider",
  },
  {
    key: "toggleSelectAll",
    label:
      selectedItems.length === data.length
        ? "取消全选 (Ctrl+A)"
        : "全选 (Ctrl+A)",
    icon:
      selectedItems.length === data.length ? (
        <Square size={14} />
      ) : (
        <CheckSquare size={14} />
      ),
    color: "#8b5cf6",
    disabled: loading || data.length === 0,
    onClick: () => onToggleSelectAll(),
  },
  {
    key: "clearSelection",
    label: `清空选择 (${selectedItems.length})`,
    icon: <Trash2 size={14} />,
    color: "#ef4444",
    disabled: selectedItems.length === 0,
    onClick: () => onClearSelection(),
  },
  {
    type: "divider",
  },
  {
    key: "batchEnableAutoDelete",
    label: `批量开启自动删除评论 (${selectedItems.length})`,
    icon: <Target size={14} />,
    color: "#22c55e",
    disabled: selectedItems.length === 0,
    onClick: () => onBatchToggleAutoDelete(selectedItems, true),
  },
  {
    key: "batchDisableAutoDelete",
    label: `批量关闭自动删除评论 (${selectedItems.length})`,
    icon: <Target size={14} />,
    color: "#f59e0b",
    disabled: selectedItems.length === 0,
    onClick: () => onBatchToggleAutoDelete(selectedItems, false),
  },
  {
    type: "divider",
  },
  {
    key: "toggleDeleted",
    label: showDeleted ? "隐藏已删除" : "显示已删除",
    icon: showDeleted ? <EyeOff size={14} /> : <Eye size={14} />,
    color: "#8b5cf6",
    onClick: () => onToggleShowDeleted(),
  },
  {
    type: "divider",
  },
  {
    key: "testProgress",
    label: "测试子任务进度条（正常）",
    icon: <Play size={14} />,
    color: "#22c55e",
    disabled: false,
    onClick: () => testTaskProgress(),
  },
  {
    key: "testProgressError",
    label: "测试子任务进度条（错误）",
    icon: <Settings size={14} />,
    color: "#ef4444",
    disabled: false,
    onClick: () => testTaskProgressError(),
  },
];

// 移除重复的对话框组件定义，使用通用组件

// 移除本地定义的EmptyState，使用从ui组件库导入的通用组件

// 移除本地定义的MobileStatsBar，使用从CommonDialogs导入的通用组件

// =============================================================================
// 表格配置
// =============================================================================

/**
 * 排序标题组件
 */
const SortableTitle = ({ title, field, sortConfig, handleSort }) => {
  const isActive = sortConfig.field === field;
  const isAsc = isActive && sortConfig.order === "asc";
  const isDesc = isActive && sortConfig.order === "desc";

  return (
    <div
      className="flex items-center gap-1 cursor-pointer select-none hover:text-blue-600 transition-colors"
      onClick={() => handleSort(field)}
    >
      <span>{title}</span>
      <div className="flex flex-col -space-y-1.5">
        <ChevronUp
          size={12}
          className={`transition-colors ${
            isDesc ? "text-blue-600" : "text-slate-300"
          }`}
        />
        <ChevronDown
          size={12}
          className={`transition-colors ${
            isAsc ? "text-blue-600" : "text-slate-300"
          }`}
        />
      </div>
    </div>
  );
};

/**
 * 创建表格列配置 - 优化后的简洁版本
 */
const createTableColumns = (
  onSubAccountClick,
  showDeleted,
  sortConfig,
  handleSort,
  searchTerm,
  onFollowToggle,
  selectedAccount
) => [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 60,
    render: (_, record, index) => <IndexCell index={index} />,
  },
  {
    title: "关注",
    dataIndex: "is_followed",
    key: "is_followed",
    width: 60,
    render: (_, record) => (
      <FollowStatusCell
        record={record}
        onToggle={onFollowToggle}
        selectedAccount={selectedAccount}
      />
    ),
  },
  {
    title: "账户名称",
    dataIndex: "advertiser_name",
    key: "advertiser_name",
    width: 200,
    render: (_, record) => (
      <AdvertiserNameCell
        record={record}
        onClick={onSubAccountClick}
        searchTerm={searchTerm}
      />
    ),
  },
  {
    title: "账户ID",
    dataIndex: "advertiser_id",
    key: "advertiser_id",
    width: 160,
    render: (value) => (
      <AdvertiserIdCell value={value} searchTerm={searchTerm} />
    ),
  },
  {
    title: "账户状态",
    dataIndex: "advertiser_status",
    key: "advertiser_status",
    width: 150,
    render: (_, record) => <StatusCell record={record} />,
  },
  {
    title: (
      <SortableTitle
        title="更新时间"
        field="update_time"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "update_time",
    key: "update_time",
    width: 100,
    render: (value) => <UpdateTimeCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="账户余额"
        field="advertiser_balance"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "advertiser_balance",
    key: "advertiser_balance",
    width: 100,
    render: (value) => <AmountCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="平均转化成本"
        field="conversion_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "conversion_cost",
    key: "conversion_cost",
    width: 120,
    render: (value) => <AmountCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="消耗数"
        field="stat_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "stat_cost",
    key: "stat_cost",
    width: 100,
    render: (value) => <AmountCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="展示数"
        field="show_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "show_cnt",
    key: "show_cnt",
    width: 100,
    render: (value) => <NumberCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="转化数"
        field="convert_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "convert_cnt",
    key: "convert_cnt",
    width: 100,
    render: (value) => <NumberCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="转化率"
        field="conversion_rate"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "conversion_rate",
    key: "conversion_rate",
    width: 100,
    render: (value) => <PercentageCell value={value} />,
  },
  {
    title: (
      <SortableTitle
        title="备注"
        field="advertiser_remark"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    dataIndex: "advertiser_remark",
    key: "advertiser_remark",
    width: 150,
    render: (value) => <RemarkCell value={value} searchTerm={searchTerm} />,
  },
  {
    key: "strategies",
    title: "已绑定策略",
    width: 120,
    render: (item) => (
      <StrategyNameDisplay
        strategyIds={item.strategy_ids || []}
        showAsTags={true}
        maxDisplay={2}
      />
    ),
  },
  {
    title: "自动删除评论",
    dataIndex: "auto_delete_comment",
    key: "auto_delete_comment",
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (_, record) => (
      <AutoDeleteCommentCell
        record={record}
        onToggle={(id, checked) => {
          // 可以在这里添加额外的处理逻辑
        }}
      />
    ),
  },
  // 删除时间列（仅在显示已删除时显示）
  ...(showDeleted
    ? [
        {
          title: "删除时间",
          dataIndex: "delete_time",
          key: "delete_time",
          width: 140,
          render: (value) => {
            if (!value)
              return <span className="text-xs text-slate-500">-</span>;

            const deleteTime = new Date(value * 1000);
            const now = new Date();
            const diffDays = Math.floor(
              (now - deleteTime) / (1000 * 60 * 60 * 24)
            );

            let displayText;

            if (diffDays === 0) {
              // 今天：今天 14:30
              displayText = `今天 ${deleteTime.toLocaleString("zh-CN", {
                hour: "2-digit",
                minute: "2-digit",
              })}`;
            } else if (diffDays === 1) {
              // 昨天：昨天 14:30
              displayText = `昨天 ${deleteTime.toLocaleString("zh-CN", {
                hour: "2-digit",
                minute: "2-digit",
              })}`;
            } else if (diffDays < 30) {
              // 30天内：01/15 14:30
              displayText = deleteTime.toLocaleString("zh-CN", {
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              });
            } else {
              // 更久前：2024/01/15 14:30
              displayText = deleteTime.toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              });
            }

            const fullTime = deleteTime.toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            });

            return (
              <span
                className="text-xs text-red-600 font-bold whitespace-nowrap delete-time-cell"
                title={`删除于: ${fullTime}`}
              >
                {displayText}
              </span>
            );
          },
        },
      ]
    : []),
];

// =============================================================================
// 主组件
// =============================================================================

/**
 * 广告主管理页面主组件
 * @param {Object} preSelectedAccount - 预选账户
 * @param {Function} onSubAccountSelect - 子账户选择回调函数
 */
function AdvertiserManager({ preSelectedAccount, onSubAccountSelect }) {
  const {
    loading,
    syncLoading,
    data,
    searchTerm,
    selectedItems,
    selectedAccount,
    showDeleted,
    showSearchDialog,
    showFilterDialog,
    statusFilter,
    sortConfig,
    confirmDialog,
    loadAdvertiserData,
    refreshAdvertisers,
    syncAdvertisers,
    handleSearch,
    handleStatusFilterChange,
    handleClearAllFilters,
    handleShowDeleted,
    handleShowSearch,
    handleCloseSearch,
    handleShowFilter,
    handleCloseFilter,
    handleSubAccountNameClick,
    handleItemSelect,
    handleSort,
    getSortedData,
    saveScrollPosition,
    restoreScrollPosition,
    setConfirmDialog,
    setSelectedItems,
  } = useAdvertiserManager(preSelectedAccount, onSubAccountSelect);

  // 使用selectedAccount作为当前账户（已经包含正确的account_id）
  // selectedAccount 来自 useAdvertiserManager Hook

  // 选择状态管理
  const [selectedAdvertiserIds, setSelectedAdvertiserIds] = useState([]);
  const [currentStrategyIds, setCurrentStrategyIds] = useState("");
  const [currentAdvertiserName, setCurrentAdvertiserName] = useState("");
  const [showStrategyDialog, setShowStrategyDialog] = useState(false);

  // 头像上传状态管理
  const [showAvatarDialog, setShowAvatarDialog] = useState(false);
  const [currentAdvertiserForAvatar, setCurrentAdvertiserForAvatar] =
    useState(null);

  // 批量头像上传状态管理
  const [showBatchAvatarDialog, setShowBatchAvatarDialog] = useState(false);
  const [
    selectedAdvertisersForBatchAvatar,
    setSelectedAdvertisersForBatchAvatar,
  ] = useState([]);

  // 落地页复制功能
  const {
    copyPageSource,
    getCopiedPageSource,
    clearCopiedPageSource,
    hasCopiedPageSource,
  } = useCopyPage();

  // 落地页复制对话框状态
  const [showCopyPageDialog, setShowCopyPageDialog] = useState(false);
  const [copyPageParams, setCopyPageParams] = useState({
    sourceAccountId: null,
    sourceAdvertiserId: null,
    targetAccountId: null,
    targetAdvertiserId: null,
  });

  // 批量操作自动删除评论
  const handleBatchToggleAutoDelete = useCallback(
    async (selectedItems, checked) => {
      if (selectedItems.length === 0) {
        toast.error("请先选择要操作的广告主");
        return;
      }

      const action = checked ? "开启" : "关闭";
      const confirmMessage = `确定要${action}选中 ${selectedItems.length} 个广告主的自动删除评论功能吗？`;

      // 显示确认对话框
      setConfirmDialog({
        isOpen: true,
        title: `批量${action}自动删除评论`,
        message: confirmMessage,
        confirmText: action, // 确认按钮显示"开启"或"关闭"
        onConfirm: async () => {
          try {
            let successCount = 0;
            let failCount = 0;

            // 批量更新
            for (const itemId of selectedItems) {
              try {
                const result = await advertiserService.updateAutoDeleteComment(
                  itemId,
                  checked ? 1 : 0
                );
                if (result.success) {
                  successCount++;
                  // 更新本地数据 - 使用数据库主键ID查找
                  const item = data.find((d) => d.id === itemId);
                  if (item) {
                    item.auto_delete_comment = checked ? 1 : 0;
                  }
                } else {
                  failCount++;
                }
              } catch (error) {
                failCount++;
                console.error(`更新广告主 ${itemId} 失败:`, error);
              }
            }

            // 显示结果
            if (successCount > 0) {
              toast.success(
                `成功${action} ${successCount} 个广告主的自动删除评论功能`
              );
            }
            if (failCount > 0) {
              toast.error(`${failCount} 个广告主操作失败`);
            }

            // 刷新数据
            await loadAdvertiserData();
          } catch (error) {
            toast.error(`批量操作失败: ${error.message}`);
          } finally {
            setConfirmDialog({
              isOpen: false,
              title: "",
              message: "",
              confirmText: "",
              onConfirm: null,
            });
          }
        },
      });
    },
    [selectedItems, data, loadAdvertiserData, setConfirmDialog]
  );

  // 使用通用管理页面Hook - 纯前端筛选
  const {
    searchTerm: managerSearchTerm,
    statusFilter: managerStatusFilter,
    showDeleted: managerShowDeleted,
    filteredData,
    showSearchDialog: managerShowSearchDialog,
    showFilterDialog: managerShowFilterDialog,
    statusBarFilters,
    handleSearch: managerHandleSearch,
    handleStatusFilterChange: managerHandleStatusFilterChange,
    handleClearAllFilters: managerHandleClearAllFilters,
    handleShowDeleted: managerHandleShowDeleted,
    handleShowSearch: managerHandleShowSearch,
    handleCloseSearch: managerHandleCloseSearch,
    handleShowFilter: managerHandleShowFilter,
    handleCloseFilter: managerHandleCloseFilter,
    handleScroll,
    generateContextMenuItems,
  } = useManagerPage({
    data,
    onSearch: null, // 不触发服务器端搜索，纯前端筛选
    searchFields: ["advertiser_name", "advertiser_id", "advertiser_remark"],
    statusField: "advertiser_status_name",
    filterOptions: STATUS_FILTER_OPTIONS,
    itemLabel: "个广告主",
    pageKey: "advertiser",
    onClearSelection: () => setSelectedItems([]),
    onToggleSelectAll: () => {
      if (selectedItems.length === filteredData.length) {
        setSelectedItems([]);
      } else {
        setSelectedItems(filteredData.map((item) => item.id));
      }
    },
    onBatchDeleteConfirm: () => {
      if (selectedItems.length === 0) return;
      setConfirmDialog({
        isOpen: true,
        title: "批量删除",
        message: `确定要删除选中的 ${selectedItems.length} 个广告主吗？此操作无法撤销。`,
        confirmText: "删除",
        onConfirm: () => {
          // 批量删除逻辑
          setConfirmDialog({
            isOpen: false,
            title: "",
            message: "",
            confirmText: "确定",
            onConfirm: null,
          });
        },
      });
    },
  });

  // 获取排序后的数据
  const sortedData = useMemo(() => {
    // 确保 filteredData 不为 undefined
    const safeFilteredData = filteredData || [];
    return getSortedData(safeFilteredData);
  }, [filteredData, getSortedData]);

  // 计算搜索匹配数量（仅考虑搜索，不考虑其他筛选）
  const searchMatchCount = useMemo(() => {
    if (!managerSearchTerm || !managerSearchTerm.trim()) {
      return data.length;
    }

    const searchLower = managerSearchTerm.toLowerCase().trim();
    const searchFields = [
      "advertiser_name",
      "advertiser_id",
      "advertiser_remark",
    ];

    return data.filter((item) => {
      return searchFields.some((field) => {
        const value = item[field];
        if (!value) return false;
        return value.toString().toLowerCase().includes(searchLower);
      });
    }).length;
  }, [data, managerSearchTerm]);

  // 关注状态切换处理
  const handleFollowToggle = useCallback(async (advertiserId, isFollowed) => {
    // 这里可以添加额外的处理逻辑，比如刷新数据或更新UI
    console.log(`广告主 ${advertiserId} 关注状态变更为: ${isFollowed}`);
  }, []);

  // 生成表格列配置
  const columns = useMemo(() => {
    return createTableColumns(
      handleSubAccountNameClick,
      managerShowDeleted,
      sortConfig,
      handleSort,
      managerSearchTerm,
      handleFollowToggle,
      selectedAccount
    );
  }, [
    handleSubAccountNameClick,
    managerShowDeleted,
    sortConfig,
    handleSort,
    managerSearchTerm,
    handleFollowToggle,
    selectedAccount,
  ]);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+A 全选功能
      if (event.ctrlKey && event.key === "a") {
        event.preventDefault();
        // 确保当前焦点在表格区域内或没有其他输入框聚焦
        const activeElement = document.activeElement;
        const isInputElement =
          activeElement &&
          (activeElement.tagName === "INPUT" ||
            activeElement.tagName === "TEXTAREA" ||
            activeElement.isContentEditable);

        // 如果当前没有输入框聚焦，且有数据可选择，则执行全选
        if (!isInputElement && sortedData.length > 0) {
          if (selectedItems.length === sortedData.length) {
            setSelectedItems([]);
          } else {
            setSelectedItems(sortedData.map((item) => item.id));
          }
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedItems, sortedData]);

  // 处理头像上传确认
  const handleAvatarUploadConfirm = useCallback(
    async (uploadData) => {
      if (!currentAdvertiserForAvatar || !selectedAccount) {
        toast.error("缺少必要信息，无法上传头像");
        return;
      }

      try {
        const result = await advertiserService.uploadAvatar({
          accountId: selectedAccount.account_id,
          advertiserId: currentAdvertiserForAvatar.advertiser_id,
          fileName: uploadData.fileName,
          base64Data: uploadData.base64Data,
          width: uploadData.width,
          height: uploadData.height,
        });

        if (result.code === 0) {
          // 成功提示现在在弹窗内显示，这里不需要toast
          // 刷新广告主数据
          await refreshAdvertisers();
        } else {
          // 错误提示现在在弹窗内显示，这里抛出错误让弹窗处理
          throw new Error(result.msg || result.message || "头像上传失败");
        }
      } catch (error) {
        console.error("头像上传异常:", error);
        // 错误提示现在在弹窗内显示，这里抛出错误让弹窗处理
        throw error;
      }
    },
    [currentAdvertiserForAvatar, selectedAccount, refreshAdvertisers]
  );

  // 处理批量头像上传确认
  const handleBatchAvatarUploadConfirm = useCallback(
    async (uploadData, selectedAdvertisers, progressCallback) => {
      console.log("handleBatchAvatarUploadConfirm 被调用:", {
        uploadData,
        selectedAdvertisers,
      });

      if (!selectedAccount || !selectedAdvertisersForBatchAvatar.length) {
        console.log("缺少必要信息:", {
          selectedAccount,
          selectedAdvertisersForBatchAvatar,
        });
        toast.error("缺少必要信息，无法批量上传头像");
        return;
      }

      try {
        // 初始化进度状态
        const initialProgress = {};
        selectedAdvertisersForBatchAvatar.forEach((advertiser) => {
          initialProgress[advertiser.advertiser_id] = {
            status: "uploading",
            message: "准备上传...",
          };
        });
        progressCallback?.(initialProgress);

        console.log("开始调用批量上传API...");
        const result = await advertiserService.batchUploadAvatar({
          accountId: selectedAccount.account_id,
          advertiserIds: selectedAdvertisersForBatchAvatar.map(
            (a) => a.advertiser_id
          ),
          fileName: uploadData.fileName,
          base64Data: uploadData.base64Data,
          width: uploadData.width,
          height: uploadData.height,
        });

        console.log("批量上传API返回结果:", result);

        if (result.code === 0) {
          // 更新进度状态
          const finalProgress = {};
          let successCount = 0;
          let errorCount = 0;

          selectedAdvertisersForBatchAvatar.forEach((advertiser) => {
            const advertiserResult =
              result.data?.results?.[advertiser.advertiser_id];
            if (advertiserResult?.success) {
              finalProgress[advertiser.advertiser_id] = {
                status: "success",
                message: "上传成功",
              };
              successCount++;
            } else {
              finalProgress[advertiser.advertiser_id] = {
                status: "error",
                message: advertiserResult?.message || "上传失败",
                error: advertiserResult?.message || "上传失败",
              };
              errorCount++;
            }
          });

          progressCallback?.(finalProgress);

          // 等待一小段时间让用户看到进度状态
          await new Promise((resolve) => setTimeout(resolve, 1000));

          console.log("批量上传成功，显示成功提示");

          // 成功提示现在在弹窗内显示，这里不需要toast
          setShowBatchAvatarDialog(false);
          setSelectedAdvertisersForBatchAvatar([]);
          await refreshAdvertisers();
        } else {
          // 更新所有为失败状态
          const errorProgress = {};
          selectedAdvertisersForBatchAvatar.forEach((advertiser) => {
            errorProgress[advertiser.advertiser_id] = {
              status: "error",
              message: result.msg || result.message || "上传失败",
              error: result.msg || result.message || "上传失败",
            };
          });
          progressCallback?.(errorProgress);

          console.log("批量上传失败，显示错误提示");
          // 错误提示现在在弹窗内显示，这里抛出错误让弹窗处理
          throw new Error(result.msg || result.message || "批量更换头像失败");
        }
      } catch (error) {
        // 更新所有为失败状态
        const errorProgress = {};
        selectedAdvertisersForBatchAvatar.forEach((advertiser) => {
          errorProgress[advertiser.advertiser_id] = {
            status: "error",
            message: error.message || "上传异常",
            error: error.message || "上传异常",
          };
        });
        progressCallback?.(errorProgress);

        console.error("批量上传异常:", error);
        // 错误提示现在在弹窗内显示，这里抛出错误让弹窗处理
        throw error;
      }
    },
    [
      selectedAccount,
      selectedAdvertisersForBatchAvatar,
      advertiserService,
      refreshAdvertisers,
    ]
  );

  // 处理头像上传取消
  const handleAvatarUploadCancel = useCallback(() => {
    setShowAvatarDialog(false);
    setCurrentAdvertiserForAvatar(null);
  }, []);

  // 处理批量头像上传取消
  const handleBatchAvatarUploadCancel = useCallback(() => {
    setShowBatchAvatarDialog(false);
    setSelectedAdvertisersForBatchAvatar([]);
  }, []);

  // 处理复制落地页
  const handleCopyPage = useCallback(
    (item) => {
      if (!selectedAccount) {
        toast.error("请先选择主账户");
        return;
      }

      // 设置复制对话框参数 - 复制时显示对话框选择落地页
      setCopyPageParams({
        sourceAccountId: selectedAccount.account_id,
        sourceAdvertiserId: item.advertiser_id,
        targetAccountId: null, // 复制时不设置目标
        targetAdvertiserId: null,
      });
      setShowCopyPageDialog(true);
    },
    [selectedAccount]
  );

  // 处理粘贴落地页
  const handlePastePage = useCallback(
    (item) => {
      if (!selectedAccount) {
        toast.error("请先选择主账户");
        return;
      }

      const copiedSource = getCopiedPageSource();
      if (!copiedSource) {
        toast.error("没有可粘贴的落地页信息");
        return;
      }

      // 直接执行粘贴操作
      const copyParams = {
        source_account_id: copiedSource.account_id,
        source_advertiser_id: copiedSource.advertiser_id,
        target_account_id: selectedAccount.account_id,
        target_advertiser_id: item.advertiser_id,
        pages: copiedSource.pages, // 使用之前选择的落地页数组
      };

      // 执行复制操作
      advertiserService
        .CopyPage(copyParams)
        .then((result) => {
          if (result.code === 0 && result.data) {
            const copyData = result.data;
            const successCount = copyData.success_count || 0;
            const errorCount = copyData.error_count || 0;

            if (successCount > 0) {
              if (errorCount > 0) {
                toast.warning(
                  `粘贴完成：成功 ${successCount} 个，失败 ${errorCount} 个`
                );
              } else {
                toast.success(`成功粘贴 ${successCount} 个落地页`);
              }
              refreshAdvertisers();
            } else {
              throw new Error(copyData.summary || "粘贴落地页失败");
            }
          } else {
            throw new Error(result.msg || result.message || "粘贴落地页失败");
          }
        })
        .catch((error) => {
          console.error("粘贴落地页失败:", error);
          toast.error(`粘贴落地页失败: ${error.message}`);
        });
    },
    [
      selectedAccount,
      getCopiedPageSource,
      advertiserService,
      refreshAdvertisers,
    ]
  );

  // 处理落地页复制成功
  const handleCopyPageSuccess = useCallback(
    async (result) => {
      console.log("落地页复制成功:", result);
      // 可以在这里刷新数据或执行其他操作
      await refreshAdvertisers();
    },
    [refreshAdvertisers]
  );

  // 关闭落地页复制对话框
  const handleCloseCopyPageDialog = useCallback(() => {
    setShowCopyPageDialog(false);
    setCopyPageParams({
      sourceAccountId: null,
      sourceAdvertiserId: null,
      targetAccountId: null,
      targetAdvertiserId: null,
    });
  }, []);

  // 生成右键菜单配置 - 支持动态生成
  const contextMenuItems = (item, cellData) => {
    const base = [
      ...createContextMenuItems(
        loading,
        syncLoading,
        selectedAccount,
        selectedItems,
        sortedData,
        refreshAdvertisers,
        syncAdvertisers,
        () => setSelectedItems([]),
        () => {
          if (selectedItems.length === sortedData.length) {
            setSelectedItems([]);
          } else {
            setSelectedItems(sortedData.map((item) => item.id));
          }
        },
        managerHandleShowSearch,
        managerHandleShowFilter,
        managerShowDeleted,
        managerHandleShowDeleted,
        handleBatchToggleAutoDelete
      ),
      {
        key: "uploadAvatar",
        label: "更改头像",
        icon: <User size={14} />,
        color: "#10b981",
        onClick: () => {
          setCurrentAdvertiserForAvatar(item);
          setShowAvatarDialog(true);
        },
      },
      // 当选中多个广告主时，显示批量上传头像选项
      ...(selectedItems.length > 1
        ? [
            {
              key: "batchUploadAvatar",
              label: `批量更改头像 (${selectedItems.length}个)`,
              icon: <Users size={14} />,
              color: "#10b981",
              onClick: () => {
                const selectedAdvertisers = sortedData.filter((advertiser) =>
                  selectedItems.includes(advertiser.id)
                );
                setSelectedAdvertisersForBatchAvatar(selectedAdvertisers);
                setShowBatchAvatarDialog(true);
              },
            },
          ]
        : []),
      {
        key: "bindStrategy",
        label: "绑定策略",
        icon: <Target size={14} />,
        color: "#3b82f6",
        onClick: () => {
          // 策略绑定需要使用advertiser_id（业务ID）
          const ids =
            selectedItems.length > 0
              ? sortedData
                  .filter((item) => selectedItems.includes(item.id))
                  .map((item) => item.advertiser_id)
              : [item.advertiser_id];
          setSelectedAdvertiserIds(ids);
          setCurrentStrategyIds(
            item.strategy_ids ? item.strategy_ids.join(",") : ""
          );
          setCurrentAdvertiserName(item.advertiser_name || "");
          setShowStrategyDialog(true);
        },
      },
      {
        type: "divider",
      },
      // 落地页复制相关功能 - 仅在选中一项数据时启用
      ...(selectedItems.length <= 1
        ? [
            {
              key: "copyPage",
              label: "复制落地页",
              icon: <Globe size={14} />,
              color: "#f59e0b",
              disabled: !selectedAccount,
              onClick: () => handleCopyPage(item),
            },
            {
              key: "pastePage",
              label: "粘贴落地页",
              icon: <Clipboard size={14} />,
              color: "#10b981",
              disabled: !selectedAccount || !hasCopiedPageSource(),
              onClick: () => handlePastePage(item),
            },
          ]
        : []),
    ];
    return base;
  };

  // 绑定策略回调
  const handleStrategyConfirm = async (strategyIdsStr) => {
    try {
      // 如果策略ID为空，说明用户要清空所有策略绑定
      if (!strategyIdsStr || strategyIdsStr.trim() === "") {
        // 如果选择了多个广告主，批量解绑
        if (selectedAdvertiserIds.length > 1) {
          // 为每个广告主单独调用解绑API
          let successCount = 0;
          let errorCount = 0;

          for (const advertiserId of selectedAdvertiserIds) {
            try {
              const result = await advertiserService.unbindStrategyFromEntity({
                strategy_ids: [], // 空数组表示解绑所有策略
                binding_type: "advertiser",
                binding_id: advertiserId.toString(),
              });

              if (result.code === 0) {
                successCount++;
              } else {
                errorCount++;
                console.error(
                  `广告主 ${advertiserId} 清空策略绑定失败:`,
                  result.msg
                );
              }
            } catch (error) {
              errorCount++;
              console.error(`广告主 ${advertiserId} 清空策略绑定异常:`, error);
            }
          }

          setShowStrategyDialog(false);
          // 先刷新策略映射，再刷新广告主数据
          await refreshAdvertisers();

          if (errorCount === 0) {
            toast.success(`已清空 ${successCount} 个广告主的策略绑定`);
          } else if (successCount > 0) {
            toast.warning(
              `成功清空 ${successCount} 个广告主的策略绑定，${errorCount} 个失败`
            );
          } else {
            toast.error(`清空策略绑定失败，共 ${errorCount} 个错误`);
          }
        } else {
          // 单个广告主解绑
          const result = await advertiserService.unbindStrategyFromEntity({
            strategy_ids: [], // 空数组表示解绑所有策略
            binding_type: "advertiser",
            binding_id: selectedAdvertiserIds[0].toString(),
          });

          if (result.code === 0) {
            setShowStrategyDialog(false);
            // 先刷新策略映射，再刷新广告主数据
            await refreshAdvertisers();
            toast.success("已清空策略绑定");
          } else {
            console.error("清空策略绑定失败:", result.msg);
            toast.error("清空策略绑定失败: " + result.msg);
          }
        }
        return;
      }

      // 如果选择了多个广告主，使用批量绑定
      if (selectedAdvertiserIds.length > 1) {
        const entities = selectedAdvertiserIds.map((id) => {
          const advertiser = data.find((item) => item.advertiser_id === id);
          return {
            binding_id: id.toString(),
            binding_name: advertiser
              ? advertiser.advertiser_name
              : `广告主#${id}`,
          };
        });

        const result = await advertiserService.batchBindStrategyToEntities({
          strategy_ids: strategyIdsStr.split(",").map((id) => Number(id)),
          binding_type: "advertiser",
          entities: entities,
          priority: 0,
          description: "",
        });

        if (result.code === 0) {
          setShowStrategyDialog(false);
          // 先刷新策略映射，再刷新广告主数据
          await refreshAdvertisers();
        } else {
          console.error("批量绑定策略失败:", result.msg);
          toast.error("批量绑定策略失败: " + result.msg);
        }
      } else {
        // 单个绑定
        const result = await advertiserService.bindStrategyToEntity({
          strategy_ids: strategyIdsStr.split(",").map((id) => Number(id)),
          binding_type: "advertiser",
          binding_id: selectedAdvertiserIds[0].toString(), // 转换为字符串
          binding_name: currentAdvertiserName,
        });

        if (result.code === 0) {
          setShowStrategyDialog(false);
          // 先刷新策略映射，再刷新广告主数据
          await refreshAdvertisers();
        } else {
          console.error("绑定策略失败:", result.msg);
          toast.error("绑定策略失败: " + result.msg);
        }
      }
    } catch (error) {
      console.error("绑定策略异常:", error);
      toast.error("绑定策略失败: " + error.message);
    }
  };

  return (
    <div
      className="h-full flex flex-col p-4"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <Card className="overflow-hidden h-full flex flex-col">
        {/* 状态提示栏 */}
        <StatusBar
          filters={statusBarFilters}
          totalCount={data.length}
          filteredCount={sortedData.length}
          selectedCount={selectedItems.length}
          itemLabel="个广告主"
          showDeleted={managerShowDeleted}
          onClearAll={managerHandleClearAllFilters}
          selectedAccount={selectedAccount}
        />

        {/* 数据表格 */}
        <div className="h-full overflow-auto" onScroll={handleScroll}>
          <VirtualDataTable
            columns={columns}
            data={sortedData}
            loading={loading}
            selectable={true}
            selectedItems={selectedItems}
            onSelectItem={handleItemSelect}
            contextMenuItems={contextMenuItems}
            rowKey="id"
            rowClassName={(item) => {
              // 为已删除的项添加特殊样式
              if (item && item.delete_time) {
                return "deleted-row";
              }
              return "";
            }}
            itemHeight={28} // 舒适行高，与项目管理页面保持一致
            className="advertiser-table-virtual"
            emptyState={
              selectedAccount ? (
                <EmptyState
                  type="empty"
                  title="暂无广告主数据"
                  description="💡 右键表格区域进行搜索、筛选等操作 | Ctrl+F 快速搜索"
                />
              ) : (
                <EmptyState
                  type="empty"
                  title="请先选择账户"
                  description="请先在顶部选择一个账户"
                />
              )
            }
          />
        </div>

        {/* 移动端统计信息栏 */}
        <MobileStatsBar
          totalCount={data.length}
          filteredCount={sortedData.length}
          selectedCount={selectedItems.length}
          itemLabel="个广告主"
          showDeleted={managerShowDeleted}
        />
      </Card>

      {/* 浮动搜索框 */}
      <FloatingSearchBox
        isOpen={managerShowSearchDialog}
        onClose={managerHandleCloseSearch}
        searchTerm={managerSearchTerm}
        onSearch={managerHandleSearch}
        placeholder="搜索广告主名称、ID或备注"
        matchCount={searchMatchCount}
        totalCount={data.length}
      />

      {/* 筛选对话框 */}
      <FilterDialog
        isOpen={managerShowFilterDialog}
        onClose={managerHandleCloseFilter}
        filterValue={managerStatusFilter}
        onFilterChange={managerHandleStatusFilterChange}
        filterOptions={STATUS_FILTER_OPTIONS}
        title="状态筛选"
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() =>
          setConfirmDialog({
            isOpen: false,
            title: "",
            message: "",
            confirmText: "确定",
            onConfirm: null,
          })
        }
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText={confirmDialog.confirmText}
      />

      <StrategyBindDialog
        visible={showStrategyDialog}
        onClose={() => setShowStrategyDialog(false)}
        onConfirm={handleStrategyConfirm}
        currentStrategyIds={currentStrategyIds}
        bindingType="advertiser"
        bindingId={selectedAdvertiserIds[0]}
        bindingName={currentAdvertiserName}
        advertiserId="" // 广告主不继承其他广告主的策略
        projectId="" // 广告主不继承项目的策略
      />

      <AvatarUploadDialog
        visible={showAvatarDialog}
        onClose={handleAvatarUploadCancel}
        onConfirm={handleAvatarUploadConfirm}
        advertiserId={currentAdvertiserForAvatar?.advertiser_id}
        advertiserName={currentAdvertiserForAvatar?.advertiser_name}
        title="更改广告主头像"
      />

      <BatchAvatarUploadDialog
        visible={showBatchAvatarDialog}
        onClose={handleBatchAvatarUploadCancel}
        onConfirm={handleBatchAvatarUploadConfirm}
        selectedAdvertisers={selectedAdvertisersForBatchAvatar}
        title="批量更改广告主头像"
      />

      <CopyPageDialog
        isOpen={showCopyPageDialog}
        onClose={handleCloseCopyPageDialog}
        sourceAccountId={copyPageParams.sourceAccountId}
        sourceAdvertiserId={copyPageParams.sourceAdvertiserId}
        targetAccountId={copyPageParams.targetAccountId}
        targetAdvertiserId={copyPageParams.targetAdvertiserId}
        onCopySuccess={handleCopyPageSuccess}
        mode={copyPageParams.targetAccountId ? "paste" : "copy"}
      />
    </div>
  );
}

export default AdvertiserManager;
