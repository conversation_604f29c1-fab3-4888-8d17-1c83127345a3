import React, { useState, useEffect, useRef } from 'react';
import { Save, Trash2, CheckCircle, AlertCircle } from 'lucide-react';
import ConfirmDialog from '../components/ui/ConfirmDialog';

const TextPresetPage = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [textPresets, setTextPresets] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState(null);
  const previewRef = useRef(null);
  const textRef = useRef(null);
  
  // Toast提示状态
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });
  
  // 删除确认对话框状态
  const [deleteConfirm, setDeleteConfirm] = useState({
    show: false,
    presetId: null,
    presetName: ''
  });
  
  // 显示Toast函数
  const showToast = (message, type = 'success') => {
    setToast({ show: true, message, type });
    // 3秒后自动关闭
    setTimeout(() => {
      setToast({ show: false, message: '', type: 'success' });
    }, 3000);
  };
  
  // 初始预设设置 - 使用后端字段名
  const initialSettings = {
    text: '示例文字',
    font_file: 'microsoftyahei.ttf',
    font_size: 36,
    font_color: '#FFFFFF',
    position_x: 50,
    position_y: 50,
    box: 2, // 1:禁用, 2:启用
    box_color: '#000000',
    box_border_rw: 0,
    shadow_wx: 2,
    shadow_wy: 2,
    shadow_color: '#000000',
    border_rw: 0,
    border_color: '#000000'
  };

  const [settings, setSettings] = useState({ ...initialSettings });

  // 可用字体
  const fonts = [
    { value: 'arial.ttf', label: 'Arial' },
    { value: 'simhei.ttf', label: '黑体' },
    { value: 'simsun.ttf', label: '宋体' },
    { value: 'simkai.ttf', label: '楷体' },
    { value: 'simfang.ttf', label: '仿宋' },
    { value: 'microsoftyahei.ttf', label: '微软雅黑' }
  ];

  // 加载文字预设
  useEffect(() => {
    loadTextPresets();
  }, []);

  const loadTextPresets = async () => {
    try {
      const { TextInfoService, handleResult } = await import('../services/api.js');
      const result = await TextInfoService.GetTextInfoList({});
      const data = handleResult(result);
      
      if (data && data.data && data.data.list) {
        const presets = data.data.list.map(preset => ({
          ...preset,  // 直接展开后端字段
          name: preset.remark || `预设${preset.id}`,
          description: preset.remark || '自定义预设'
        }));
        setTextPresets(presets);
      }
    } catch (error) {
      console.error('加载文字预设失败:', error);
    }
  };

  // 处理设置变化
  const handleSettingChange = (e) => {
    const { name, value, type } = e.target;
    let parsedValue = value;
    
    if (type === 'number' || type === 'range') {
      // 检查是否需要保留小数
      if (name === 'position_x' || name === 'position_y') {
        parsedValue = parseFloat(parseFloat(value).toFixed(2));
      } else {
        parsedValue = parseInt(value);
      }
    } else if (name === 'box') {
      // 特别处理box字段，确保它是数字类型
      parsedValue = parseInt(value);
    }
    
    setSettings({
      ...settings,
      [name]: parsedValue
    });
  };

  // 开始拖拽文字
  const handleMouseDown = (e) => {
    if (!previewRef.current || !textRef.current) return;
    
    // 只有点击文字元素时才开始拖拽
    if (e.target === textRef.current || textRef.current.contains(e.target)) {
      setIsDragging(true);
      e.preventDefault(); // 防止选中文本
    }
  };

  // 拖拽移动文字
  const handleMouseMove = (e) => {
    if (!isDragging || !previewRef.current) return;
    
    const previewRect = previewRef.current.getBoundingClientRect();
    
    // 计算鼠标在预览区域内的相对位置（百分比）
    const relativeX = ((e.clientX - previewRect.left) / previewRect.width) * 100;
    const relativeY = ((e.clientY - previewRect.top) / previewRect.height) * 100;
    
    // 限制在预览区域内
    const boundedX = Math.max(0, Math.min(100, relativeX));
    const boundedY = Math.max(0, Math.min(100, relativeY));
    
    // 保留两位小数
    const positionX = parseFloat(boundedX.toFixed(2));
    const positionY = parseFloat(boundedY.toFixed(2));
    
    setSettings(prev => ({
      ...prev,
      position_x: positionX,
      position_y: positionY
    }));
  };

  // 结束拖拽
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加和移除全局鼠标事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // 应用预设
  const handleApplyPreset = (preset) => {
    setSettings({
      ...preset,  // 直接应用所有预设字段，因为字段名已一致
      // 确保必要字段有默认值
      box: preset.box || 2,
      box_color: preset.box_color || '#000000',
      box_border_rw: preset.box_border_rw || 0,
      shadow_wx: preset.shadow_wx || 2,
      shadow_wy: preset.shadow_wy || 2,
      shadow_color: preset.shadow_color || '#000000',
      border_rw: preset.border_rw || 0,
      border_color: preset.border_color || '#000000'
    });
    setSelectedPreset(preset);
  };

  // 删除文字预设
  const handleDeletePreset = async (presetId, presetName, event) => {
    // 阻止事件冒泡，避免触发预设选择
    event.stopPropagation();
    
    // 显示确认对话框
    setDeleteConfirm({
      show: true,
      presetId,
      presetName
    });
  };

  // 确认删除预设
  const confirmDeletePreset = async () => {
    try {
      const { TextInfoService, handleResult } = await import('../services/api.js');
      const result = await TextInfoService.DeleteTextInfo(deleteConfirm.presetId);
      handleResult(result);
      
      showToast('预设删除成功');
      
      // 如果删除的是当前选中的预设，清空选择
      if (selectedPreset && selectedPreset.id === deleteConfirm.presetId) {
        setSelectedPreset(null);
        setSettings({ ...initialSettings });
      }
      
      // 重新加载预设列表
      await loadTextPresets();
      
      // 关闭确认对话框
      setDeleteConfirm({ show: false, presetId: null, presetName: '' });
    } catch (error) {
      console.error('删除预设失败:', error);
      showToast('删除失败，请稍后重试', 'error');
      // 关闭确认对话框
      setDeleteConfirm({ show: false, presetId: null, presetName: '' });
    }
  };

  // 保存或更新文字预设
  const handleSavePreset = async () => {
    if (!settings.text.trim()) {
      showToast('请输入文字内容作为预设名称', 'error');
      return;
    }
    
    try {
      setIsSaving(true);
      const { TextInfoService, handleResult } = await import('../services/api.js');
      
      let result;
      
      // 构建要保存的数据，字段名已与后端一致
      const textInfoData = {
        ...settings,  // 直接展开所有设置，因为字段名已一致
        remark: settings.text
      };
      
      // 判断是新建还是更新预设 - 通过selectedPreset是否存在且有id来判断
      if (selectedPreset && selectedPreset.id) {
        // 更新现有预设
        textInfoData.id = selectedPreset.id;
        result = await TextInfoService.UpdateTextInfo(textInfoData);
        
        handleResult(result);
        showToast('预设更新成功');
      } else {
        // 创建新预设
        result = await TextInfoService.CreateTextInfo(textInfoData);
        
        handleResult(result);
        showToast('新预设保存成功');
      }
      
      // 重新加载预设列表
      await loadTextPresets();
      
      // 清空选中的预设
      setSelectedPreset(null);
      // 重置设置为初始值
      setSettings({ ...initialSettings });
    } catch (error) {
      console.error('保存预设失败:', error);
      showToast('保存失败，请稍后重试', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // 计算预览文本样式
  const getPreviewTextStyle = () => {
    // 背景框样式（box属性：1=禁用，2=启用）
    const useBox = settings.box === 2;
    
    // 计算缩放比例
    // 实际手机屏幕尺寸：1080*1920
    // 预览区域尺寸：获取实际渲染尺寸
    const PHONE_WIDTH = 1080;
    const PHONE_HEIGHT = 1920;
    
    // 预览区域的实际尺寸 - 使用420px宽度，按9:16比例计算高度
    const previewWidth = 420;
    const previewHeight = previewWidth * 16 / 9; // 约746.67px
    
    // 计算缩放比例（使用较小的比例以确保内容完全可见）
    const scaleX = previewWidth / PHONE_WIDTH;
    const scaleY = previewHeight / PHONE_HEIGHT;
    const scale = Math.min(scaleX, scaleY);
    
    // 缩放后的字体大小
    const scaledFontSize = Math.round(settings.font_size * scale);
    
    return {
      color: settings.font_color,
      fontFamily: getFontFamilyName(settings.font_file),
      fontSize: `${scaledFontSize}px`,
      backgroundColor: useBox 
        ? `rgba(${hexToRgb(settings.box_color)}, 0.5)`
        : 'transparent',
      padding: useBox ? `${Math.round(10 * scale)}px ${Math.round(15 * scale)}px` : `${Math.round(5 * scale)}px ${Math.round(10 * scale)}px`,
      borderRadius: `${Math.round(2 * scale)}px`,
      textAlign: 'center',
      position: 'absolute',
      transform: 'translate(-50%, -50%)',
      left: `${settings.position_x}%`,
      top: `${settings.position_y}%`,
      // 文本阴影 - 也需要缩放
      textShadow: settings.shadow_wx > 0 
        ? `${Math.round(settings.shadow_wx * scale)}px ${Math.round(settings.shadow_wy * scale)}px ${Math.max(1, Math.round(settings.shadow_wx * scale / 2))}px rgba(${hexToRgb(settings.shadow_color)}, 0.5)`
        : 'none',
      // 边框样式 - 也需要缩放
      border: settings.border_rw > 0
        ? `${Math.round(settings.border_rw * scale)}px solid ${settings.border_color}`
        : 'none',
      // 背景框边框 - 也需要缩放
      boxShadow: useBox && settings.box_border_rw > 0
        ? `0 0 0 ${Math.round(settings.box_border_rw * scale)}px ${settings.border_color}`
        : 'none',
      maxWidth: '80%',
      wordBreak: 'break-word'
    };
  };

  // 获取字体族名称
  const getFontFamilyName = (fontFile) => {
    const font = fonts.find(f => f.value === fontFile);
    if (font) {
      if (font.label === 'Arial') return 'Arial, sans-serif';
      if (font.label.includes('黑体')) return 'SimHei, sans-serif';
      if (font.label.includes('宋体')) return 'SimSun, serif';
      if (font.label.includes('楷体')) return 'KaiTi, serif';
      if (font.label.includes('仿宋')) return 'FangSong, serif';
      if (font.label.includes('微软雅黑')) return '"Microsoft YaHei", sans-serif';
    }
    return 'sans-serif';
  };

  // 将HEX颜色转换为RGB
  const hexToRgb = (hex) => {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
      : '0, 0, 0';
  };

  return (
    <div className="h-screen">
      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={deleteConfirm.show}
        onClose={() => setDeleteConfirm({ show: false, presetId: null, presetName: '' })}
        onConfirm={confirmDeletePreset}
        title="删除预设"
        message={`确定要删除预设"${deleteConfirm.presetName}"吗？删除后无法恢复。`}
        confirmText="删除"
        cancelText="取消"
        type="danger"
      />
      
      {/* Toast提示 */}
      {toast.show && (
        <div className="fixed top-4 right-4 z-50">
          <div 
            className={`px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 min-w-[300px] transform transition-all duration-300 ease-in-out ${
              toast.type === 'success' 
                ? 'bg-white border border-green-200 text-green-800' 
                : 'bg-white border border-red-200 text-red-800'
            }`}
            style={{
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
            }}
          >
            <div className={`flex-shrink-0 ${
              toast.type === 'success' ? 'text-green-500' : 'text-red-500'
            }`}>
              {toast.type === 'success' ? <CheckCircle size={20} /> : <AlertCircle size={20} />}
            </div>
            <div className="flex-1">
              <p className="font-medium">{toast.message}</p>
            </div>
            <button 
              className={`flex-shrink-0 rounded-full p-1 hover:bg-gray-100 transition-colors ${
                toast.type === 'success' ? 'text-green-600' : 'text-red-600'
              }`}
              onClick={() => setToast({ ...toast, show: false })}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
        {/* 左侧预览区域 */}
        <div className="relative h-full">
          {/* 预览区域 */}
          <div className="p-4 flex justify-center">
            <div 
              ref={previewRef}
              className="bg-slate-800 rounded-lg overflow-hidden border border-slate-700 relative w-full max-w-[420px]" 
              style={{ aspectRatio: '9/16' }}
              onMouseDown={handleMouseDown}
            >
              <div className="w-full h-full relative flex items-center justify-center">
                {/* 模拟视频背景 */}
                <div className="absolute inset-0 bg-gradient-to-r from-slate-900 to-slate-800"></div>
                
                {/* 拖拽指示 */}
                {isDragging && (
                  <>
                    <div className="absolute inset-0 pointer-events-none">
                      {/* 水平辅助线 */}
                      <div 
                        className="absolute left-0 right-0 border-t border-blue-400 border-dashed" 
                        style={{ top: `${settings.position_y}%` }}
                      />
                      {/* 垂直辅助线 */}
                      <div 
                        className="absolute top-0 bottom-0 border-l border-blue-400 border-dashed" 
                        style={{ left: `${settings.position_x}%` }}
                      />
                    </div>
                  </>
                )}
                
                {/* 文字预览 */}
                <div 
                  ref={textRef}
                  style={{
                    ...getPreviewTextStyle(),
                    cursor: 'move',
                    userSelect: 'none'
                  }}
                  className={isDragging ? 'ring-2 ring-blue-500' : 'hover:ring-2 hover:ring-blue-300'}
                >
                  {settings.text || '示例文字'}
                </div>
              </div>
              
              {/* 拖拽提示 */}
                              <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                拖动文字调整位置
              </div>
              
              {/* 缩放信息提示 */}
                              <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                <div>实际字体: {settings.font_size}px</div>
                <div>预览字体: {Math.round(settings.font_size * (420 / 1080))}px</div>
                <div>缩放比例: {Math.round((420 / 1080) * 100)}%</div>
              </div>
            </div>
          </div>
          
          {/* 预设选择 - 紧贴窗体底部 */}
          <div className="absolute bottom-0 left-0 right-0 bg-white shadow border-t border-gray-200" style={{ height: '260px' }}>
            <div className="flex justify-between items-center px-3 py-2 border-b border-gray-200 bg-gray-50 flex-shrink-0">
              <h3 className="text-sm font-medium text-slate-800">文字预设</h3>
              <button
                onClick={() => {
                  setSelectedPreset(null);
                  setSettings({ ...initialSettings });
                }}
                className="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                新建
              </button>
            </div>
            <div className="p-2" style={{ height: 'calc(100% - 56px)', overflow: 'hidden' }}>
              <div className="space-y-1 h-full overflow-y-auto">
                {textPresets.map((preset) => (
                  <div
                    key={preset.id}
                    className={`flex items-center justify-between px-3 py-2 rounded transition-colors cursor-pointer ${
                      selectedPreset && selectedPreset.id === preset.id
                        ? 'bg-blue-100 border border-blue-300'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                    onClick={() => handleApplyPreset(preset)}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-slate-900 truncate">{preset.name}</div>
                      <div className="text-xs text-slate-500 truncate">{preset.text}</div>
                    </div>
                    <button
                      onClick={(e) => handleDeletePreset(preset.id, preset.name, e)}
                      className="ml-2 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors flex-shrink-0"
                      title="删除预设"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
                
                {textPresets.length === 0 && (
                  <div className="text-center py-6">
                    <p className="text-sm text-slate-400">暂无预设</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* 右侧设置区域 */}
        <div className="bg-white flex-1 overflow-y-auto p-6 border-l border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-slate-800">文字设置</h3>
            <button
              onClick={handleSavePreset}
              disabled={isSaving || !settings.text.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
            >
              <Save size={16} />
              <span>
                {isSaving ? '保存中...' : '保存'}
              </span>
            </button>
          </div>
          
          <div className="space-y-5">
            {/* 基础设置 */}
            <div className="border-b border-slate-200 pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-blue-600">基础设置</h4>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">文字内容</label>
                  <textarea
                    name="text"
                    value={settings.text}
                    onChange={handleSettingChange}
                    className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                    rows="2"
                    placeholder="输入要添加的文字"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">字体</label>
                    <select
                      name="font_file"
                      value={settings.font_file}
                      onChange={handleSettingChange}
                      className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                    >
                      {fonts.map(font => (
                        <option key={font.value} value={font.value}>{font.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">字体大小</label>
                    <input
                      type="number"
                      name="font_size"
                      value={settings.font_size}
                      onChange={handleSettingChange}
                      className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                      min="12"
                      max="100"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* 颜色设置 */}
            <div className="border-b border-slate-200 pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-green-600">颜色设置</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">文字颜色</label>
                  <input
                    type="color"
                    name="font_color"
                    value={settings.font_color}
                    onChange={handleSettingChange}
                    className="w-full h-10 border border-slate-300 rounded cursor-pointer"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">背景框颜色</label>
                  <input
                    type="color"
                    name="box_color"
                    value={settings.box_color}
                    onChange={handleSettingChange}
                    className="w-full h-10 border border-slate-300 rounded cursor-pointer"
                  />
                </div>
              </div>
            </div>
            
            {/* 位置设置 */}
            <div className="border-b border-slate-200 pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-purple-600">位置设置</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">水平位置 (%)</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      name="position_x"
                      min="0"
                      max="100"
                      step="0.01"
                      value={settings.position_x}
                      onChange={handleSettingChange}
                      className="flex-grow"
                    />
                    <input
                      type="number"
                      name="position_x"
                      value={settings.position_x}
                      onChange={handleSettingChange}
                      className="w-14 px-2 py-1 border border-slate-300 rounded text-xs"
                      min="0"
                      max="100"
                      step="0.01"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">垂直位置 (%)</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      name="position_y"
                      min="0"
                      max="100"
                      step="0.01"
                      value={settings.position_y}
                      onChange={handleSettingChange}
                      className="flex-grow"
                    />
                    <input
                      type="number"
                      name="position_y"
                      value={settings.position_y}
                      onChange={handleSettingChange}
                      className="w-14 px-2 py-1 border border-slate-300 rounded text-xs"
                      min="0"
                      max="100"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
              <div className="text-xs text-slate-500 mt-2 text-center">
                <span className="text-blue-600 font-medium">提示：</span> 可直接拖动文字调整位置
              </div>
            </div>
            
            {/* 背景框设置 */}
            <div className="border-b border-slate-200 pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-orange-600">背景框设置</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <label className="text-sm font-medium text-slate-700">启用背景框</label>
                  <select
                    name="box"
                    value={settings.box}
                    onChange={handleSettingChange}
                    className="px-3 py-1 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                  >
                    <option value={1}>禁用</option>
                    <option value={2}>启用</option>
                  </select>
                </div>
                
                {settings.box === 2 && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">框边框宽度</label>
                    <input
                      type="number"
                      name="box_border_rw"
                      value={settings.box_border_rw}
                      onChange={handleSettingChange}
                      className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                      min="0"
                      max="10"
                    />
                  </div>
                )}
              </div>
            </div>
            
            {/* 边框设置 */}
            <div className="border-b border-slate-200 pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-red-600">文字边框</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">边框宽度</label>
                  <input
                    type="number"
                    name="border_rw"
                    value={settings.border_rw}
                    onChange={handleSettingChange}
                    className="w-full px-3 py-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
                    min="0"
                    max="10"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">边框颜色</label>
                  <input
                    type="color"
                    name="border_color"
                    value={settings.border_color}
                    onChange={handleSettingChange}
                    className="w-full h-10 border border-slate-300 rounded cursor-pointer"
                  />
                </div>
              </div>
            </div>
            
            {/* 阴影设置 */}
            <div className="pb-4">
              <h4 className="text-sm font-semibold text-slate-700 mb-3 uppercase tracking-wide text-indigo-600">阴影设置</h4>
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">X偏移</label>
                  <input
                    type="number"
                    name="shadow_wx"
                    value={settings.shadow_wx}
                    onChange={handleSettingChange}
                    className="w-full px-2 py-1 border border-slate-300 rounded text-sm"
                    min="-10"
                    max="10"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Y偏移</label>
                  <input
                    type="number"
                    name="shadow_wy"
                    value={settings.shadow_wy}
                    onChange={handleSettingChange}
                    className="w-full px-2 py-1 border border-slate-300 rounded text-sm"
                    min="-10"
                    max="10"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">阴影颜色</label>
                  <input
                    type="color"
                    name="shadow_color"
                    value={settings.shadow_color}
                    onChange={handleSettingChange}
                    className="w-full h-8 border border-slate-300 rounded cursor-pointer"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextPresetPage; 