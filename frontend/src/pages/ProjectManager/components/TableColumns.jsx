import React from "react";
import { Edit, Check, X, Trash2, ChevronUp, ChevronDown } from "lucide-react";
import StatusBadge from "../../../components/ui/StatusBadge";
import ActionButtons from "../../../components/ui/ActionButtons";
import HighlightText from "../../../components/ui/HighlightText";
import StrategyNameDisplay from "../../../components/strategy/StrategyNameDisplay";
import {
  STATUS_COLORS,
  STATUS_VARIANTS,
  ENABLE_STATUS_COLORS,
  ENABLE_STATUS_VARIANTS,
  TABLE_COLUMN_WIDTHS,
  PROJECT_STATUS,
  PROJECT_ENABLE_STATUS,
  DELIVERY_MODE,
  DELIVERY_MODE_TEXT,
  DELIVERY_MODE_COLORS,
} from "../constants";
import {
  formatAmount,
  formatPercentage,
  formatNumber,
  getRowIndex,
  getProjectStatusText,
  getProjectEnableStatusText,
  getDeliveryModeText,
  truncateText,
  formatCreateTime,
  formatUpdateTime,
} from "../utils";

/**
 * 排序标题组件
 */
const SortableTitle = ({ title, field, sortConfig, handleSort }) => {
  const isActive = sortConfig.field === field;
  const isAsc = isActive && sortConfig.order === "asc";
  const isDesc = isActive && sortConfig.order === "desc";

  return (
    <div
      className="flex items-center gap-1 cursor-pointer select-none hover:text-blue-600 transition-colors"
      onClick={() => handleSort(field)}
    >
      <span>{title}</span>
      <div className="flex flex-col -space-y-1.5">
        <ChevronUp
          size={12}
          className={`transition-colors ${
            isDesc ? "text-blue-600" : "text-slate-300"
          }`}
        />
        <ChevronDown
          size={12}
          className={`transition-colors ${
            isAsc ? "text-blue-600" : "text-slate-300"
          }`}
        />
      </div>
    </div>
  );
};

/**
 * 表格列配置
 * 根据传入的状态和处理函数动态生成表格列
 */
export const createTableColumns = ({
  selectedItems,
  editingProjectId,
  editingProjectName,
  setEditingProjectName,
  pagination,
  onSelectItem,
  onSelectAll,
  onUpdateStatus,
  onDeleteProject,
  onProjectSelect,
  onEditProjectName,
  onCancelEditProjectName,
  onSaveProjectName,
  onEditInputKeyDown,
  data,
  showDeleted,
  sortConfig,
  handleSort,
  searchTerm,
}) => {
  // 序号列
  const indexColumn = {
    key: "index",
    title: "序号",
    width: 60,
    render: (item, index) => (
      <span className="text-xs text-slate-900">
        {getRowIndex(index, pagination.page, pagination.pageSize)}
      </span>
    ),
  };

  // 启用状态列（仅展示）
  const enableStatusColumn = {
    key: "enable_status",
    title: "启用状态",
    width: TABLE_COLUMN_WIDTHS.ENABLE_STATUS,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      // 根据项目状态二级判断启用状态
      const isEnabled = !item.project_status_second?.includes(1); // 1表示禁用
      const enableStatus = isEnabled
        ? PROJECT_ENABLE_STATUS.ENABLED
        : PROJECT_ENABLE_STATUS.DISABLED;
      const statusText = getProjectEnableStatusText({
        enable_status: enableStatus,
      });

      return (
        <span
          className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${
            ENABLE_STATUS_COLORS[enableStatus] ||
            "text-gray-600 bg-gray-50 border-gray-200"
          }`}
          style={{ fontSize: "10px" }}
        >
          {statusText}
        </span>
      );
    },
  };

  // 项目名称列（支持编辑）
  const nameColumn = {
    key: "project_name",
    title: "项目名称",
    width: TABLE_COLUMN_WIDTHS.NAME,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      if (editingProjectId === item.project_id) {
        return (
          <div className="flex items-center space-x-1 max-w-[250px]">
            <input
              type="text"
              value={editingProjectName}
              onChange={(e) => setEditingProjectName(e.target.value)}
              onKeyDown={(e) => onEditInputKeyDown(e, item)}
              className="flex-1 px-1 py-0.5 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 min-w-0"
              placeholder="输入项目名称..."
              autoFocus
            />
            <button
              onClick={() => onSaveProjectName(item)}
              className="text-green-600 hover:text-green-800 transition-colors flex-shrink-0"
              title="保存"
            >
              <Check size={10} />
            </button>
            <button
              onClick={onCancelEditProjectName}
              className="text-gray-600 hover:text-gray-800 transition-colors flex-shrink-0"
              title="取消"
            >
              <X size={10} />
            </button>
          </div>
        );
      }

      return (
        <div
          className="text-xs font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate max-w-[250px]"
          title={item?.project_name}
          onClick={() =>
            onProjectSelect &&
            onProjectSelect({
              project_id: item?.project_id,
              project_name: item?.project_name,
              advertiser_id: item?.advertiser_id,
              advertiser_name:
                item?.advertiser_name || `广告主(${item?.advertiser_id})`,
            })
          }
        >
          <HighlightText
            text={item?.project_name || "未命名项目"}
            searchTerm={searchTerm}
          />
        </div>
      );
    },
  };

  // 项目ID列
  const projectIdColumn = {
    key: "project_id",
    title: "项目ID",
    width: TABLE_COLUMN_WIDTHS.ID,
    render: (item) => (
      <div className="text-xs text-slate-500" title={item?.project_id}>
        <HighlightText text={item?.project_id || "-"} searchTerm={searchTerm} />
      </div>
    ),
  };

  // 广告主ID列
  const advertiserIdColumn = {
    key: "advertiser_id",
    title: "广告主ID",
    width: TABLE_COLUMN_WIDTHS.ADVERTISER_ID,
    render: (item) => (
      <div className="text-xs text-slate-500" title={item?.advertiser_id}>
        <HighlightText
          text={item?.advertiser_id || "-"}
          searchTerm={searchTerm}
        />
      </div>
    ),
  };

  // 项目状态列
  const statusColumn = {
    key: "project_status",
    title: "项目状态",
    width: TABLE_COLUMN_WIDTHS.STATUS,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      const status = item.project_status_first;
      const statusText = getProjectStatusText(item);

      return (
        <StatusBadge
          variant={STATUS_VARIANTS[status] || "default"}
          className="text-xs"
          style={{ fontSize: "10px" }}
        >
          {statusText}
        </StatusBadge>
      );
    },
  };

  // 投放模式列
  const deliveryModeColumn = {
    key: "delivery_mode",
    title: "投放模式",
    width: TABLE_COLUMN_WIDTHS.DELIVERY_MODE,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      const deliveryMode = item.delivery_mode;
      const modeText = getDeliveryModeText(item);

      return (
        <span
          className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${
            DELIVERY_MODE_COLORS[deliveryMode] ||
            "text-gray-600 bg-gray-50 border-gray-200"
          }`}
          style={{ fontSize: "10px" }}
        >
          {modeText}
        </span>
      );
    },
  };

  // 项目出价列
  const projectBidColumn = {
    key: "project_bid",
    title: (
      <SortableTitle
        title="项目出价"
        field="project_bid"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.PROJECT_BID,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatAmount(item?.project_bid)}
      </span>
    ),
  };

  // 平均转化成本列
  const conversionCostColumn = {
    key: "conversion_cost",
    title: (
      <SortableTitle
        title="平均转化成本"
        field="conversion_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERSION_COST,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatAmount(item?.conversion_cost)}
      </span>
    ),
  };

  // 消耗数列
  const statCostColumn = {
    key: "stat_cost",
    title: (
      <SortableTitle
        title="消耗数"
        field="stat_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.STAT_COST,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatAmount(item?.stat_cost)}
      </span>
    ),
  };

  // 展示数列
  const showCountColumn = {
    key: "show_cnt",
    title: (
      <SortableTitle
        title="展示数"
        field="show_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.SHOW_COUNT,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatNumber(item?.show_cnt)}
      </span>
    ),
  };

  // 转化数列
  const convertCountColumn = {
    key: "convert_cnt",
    title: (
      <SortableTitle
        title="转化数"
        field="convert_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERT_COUNT,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatNumber(item?.convert_cnt)}
      </span>
    ),
  };

  // 策略列
  const strategyColumn = {
    key: "strategies",
    title: "已绑定策略",
    width: 120,
    render: (item) => (
      <StrategyNameDisplay
        strategyIds={item.strategy_ids || []}
        showAsTags={true}
        maxDisplay={2}
      />
    ),
  };

  // 转化率列
  const conversionRateColumn = {
    key: "conversion_rate",
    title: (
      <SortableTitle
        title="转化率"
        field="conversion_rate"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERSION_RATE,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatPercentage(item?.conversion_rate)}
      </span>
    ),
  };

  // 创建时间列
  const createTimeColumn = {
    key: "create_time",
    title: (
      <SortableTitle
        title="创建时间"
        field="create_time"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (item) => {
      if (!item?.create_time)
        return <span className="text-xs text-slate-500">-</span>;

      // 解析时间并减去8小时用于显示
      const createTime = new Date(item.create_time);
      createTime.setHours(createTime.getHours() - 8);

      const fullTime = createTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      return (
        <span
          className="text-xs text-slate-600 whitespace-nowrap"
          title={`创建于: ${fullTime}`}
        >
          {formatCreateTime(item.create_time)}
        </span>
      );
    },
  };

  // 更新时间列
  const updateTimeColumn = {
    key: "update_time",
    title: (
      <SortableTitle
        title="更新时间"
        field="update_time"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (item) => {
      if (!item?.update_time)
        return <span className="text-xs text-slate-500">-</span>;

      const updateTime = new Date(item.update_time);

      const fullTime = updateTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      return (
        <span
          className="text-xs text-slate-600 whitespace-nowrap"
          title={`更新于: ${fullTime}`}
        >
          {formatUpdateTime(item.update_time)}
        </span>
      );
    },
  };

  // 操作列
  const actionsColumn = {
    key: "actions",
    title: "操作",
    width: TABLE_COLUMN_WIDTHS.ACTIONS,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      return (
        <div className="flex items-center space-x-2">
          <ActionButtons
            actions={[
              {
                key: "delete",
                type: "delete",
                onClick: () => onDeleteProject(item),
                title: "删除",
              },
            ]}
            size="sm"
          />
        </div>
      );
    },
  };

  // 删除时间列（仅在显示已删除时显示）
  const deleteTimeColumn = {
    key: "delete_time",
    title: "删除时间",
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (item) => {
      if (!item?.delete_time)
        return <span className="text-xs text-slate-500">-</span>;

      const deleteTime = new Date(item.delete_time * 1000);
      const now = new Date();
      const diffDays = Math.floor((now - deleteTime) / (1000 * 60 * 60 * 24));

      let displayText;

      if (diffDays === 0) {
        // 今天：今天 14:30
        displayText = `今天 ${deleteTime.toLocaleString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })}`;
      } else if (diffDays === 1) {
        // 昨天：昨天 14:30
        displayText = `昨天 ${deleteTime.toLocaleString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })}`;
      } else if (diffDays < 30) {
        // 30天内：01/15 14:30
        displayText = deleteTime.toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      } else {
        // 更久前：2024/01/15 14:30
        displayText = deleteTime.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      const fullTime = deleteTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      return (
        <span
          className="text-xs text-red-600 font-bold whitespace-nowrap delete-time-cell"
          title={`删除于: ${fullTime}`}
        >
          {displayText}
        </span>
      );
    },
  };

  const baseColumns = [
    indexColumn,
    enableStatusColumn,
    nameColumn,
    projectIdColumn,
    advertiserIdColumn,
    statusColumn,
    deliveryModeColumn,
    projectBidColumn,
    conversionCostColumn,
    statCostColumn,
    showCountColumn,
    convertCountColumn,
    strategyColumn,
    conversionRateColumn,
    createTimeColumn,
    updateTimeColumn,
  ];

  // 如果显示已删除项，添加删除时间列
  if (showDeleted) {
    baseColumns.push(deleteTimeColumn);
  }

  return baseColumns;
};
