import React, { useState, useEffect } from "react";
import { Button, Input } from "../../../components/ui";
import { X, DollarSign, AlertTriangle } from "lucide-react";

/**
 * UpdateBidDialog - 修改出价对话框
 * 支持单个和批量项目的出价修改
 */
const UpdateBidDialog = ({
  isOpen,
  onClose,
  onConfirm,
  projects = [],
  loading = false,
}) => {
  const [bidValue, setBidValue] = useState("");
  const [errors, setErrors] = useState({});

  // 重置状态
  useEffect(() => {
    if (isOpen) {
      setBidValue("");
      setErrors({});
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // 验证出价
  const validateBid = (value) => {
    if (!value || value.trim() === "") {
      return "请输入出价金额";
    }
    
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return "请输入有效的数字";
    }
    
    if (numValue <= 0) {
      return "出价金额必须大于0";
    }
    
    if (numValue > 999999) {
      return "出价金额不能超过999,999";
    }
    
    // 检查小数位数（最多2位小数）
    if (value.includes('.') && value.split('.')[1].length > 2) {
      return "出价金额最多支持2位小数";
    }
    
    return null;
  };

  // 处理确认
  const handleConfirm = () => {
    const error = validateBid(bidValue);
    if (error) {
      setErrors({ bid: error });
      return;
    }

    // 构建出价映射
    const promotionBidMap = {};
    projects.forEach(project => {
      promotionBidMap[project.project_id] = bidValue.trim();
    });

    onConfirm({
      bidValue: bidValue.trim(),
      promotionBidMap,
    });
  };

  // 处理输入变化
  const handleBidChange = (e) => {
    const value = e.target.value;
    setBidValue(value);
    
    // 实时验证
    const error = validateBid(value);
    setErrors(prev => ({
      ...prev,
      bid: error
    }));
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !errors.bid) {
      handleConfirm();
    } else if (e.key === "Escape") {
      onClose();
    }
  };

  const hasManualDelivery = projects.some(project => project.delivery_mode === 1);

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center z-50" 
      style={{ backgroundColor: 'rgba(107, 114, 128, 0.3)' }}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              修改出价
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={loading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6">
          {/* 项目信息 */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">
              将为以下 {projects.length} 个项目修改出价：
            </div>
            <div className="max-h-32 overflow-y-auto bg-gray-50 rounded p-3">
              {projects.map((project, index) => (
                <div key={project.project_id} className="text-xs text-gray-700 mb-1">
                  {index + 1}. {project.project_name || project.project_id}
                  {project.delivery_mode === 1 && (
                    <span className="ml-2 text-orange-600 font-medium">
                      (手动投放)
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 手动投放警告 */}
          {hasManualDelivery && (
            <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-orange-800">
                  <div className="font-medium mb-1">注意事项</div>
                  <div>
                    选中的项目中包含手动投放模式的项目，修改出价可能对这些项目的投放效果产生不同影响。
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 出价输入 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              新出价金额 (元)
            </label>
            <Input
              type="text"
              value={bidValue}
              onChange={handleBidChange}
              onKeyDown={handleKeyDown}
              placeholder="请输入出价金额，如：10.50"
              className={`w-full ${errors.bid ? 'border-red-300 focus:border-red-500' : ''}`}
              disabled={loading}
              autoFocus
            />
            {errors.bid && (
              <div className="mt-1 text-sm text-red-600">
                {errors.bid}
              </div>
            )}
            <div className="mt-1 text-xs text-gray-500">
              支持最多2位小数，范围：0.01 - 999,999.99
            </div>
          </div>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 mb-4">
            <div>• 出价修改将立即生效</div>
            <div>• 建议根据项目的投放效果合理设置出价</div>
            <div>• 手动投放项目的出价修改可能需要手动调整投放策略</div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleConfirm}
            disabled={loading || !!errors.bid || !bidValue.trim()}
            loading={loading}
          >
            确认修改
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UpdateBidDialog; 