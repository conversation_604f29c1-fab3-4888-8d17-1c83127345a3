import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  X,
  <PERSON>,
  <PERSON>,
  EyeOff,
  CheckCircle,
  XCircle,
  Plus,
  Minus,
  PlayCircle,
} from "lucide-react";
import Button from "../../../components/ui/Button";
import { toast } from "../../../components/ui/Toast";
import { retargetingTagService } from "../../../services/retargetingTagService";
import { projectService } from "../../../services/projectservice";
import promotionService from "../../../services/promotionService";

/**
 * CopyProjectDialog 复制项目确认对话框
 */
const CopyProjectDialog = ({
  isOpen,
  onClose,
  project,
  onCopyConfirm,
  projectDetail: preloadedProjectDetail,
}) => {
  const [newProjectName, setNewProjectName] = useState("");
  const [copyCount, setCopyCount] = useState(1);
  const [copying, setCopying] = useState(false);
  const [customAudiences, setCustomAudiences] = useState([]);
  const [loadingAudiences, setLoadingAudiences] = useState(false);
  const [showAudiences, setShowAudiences] = useState(true);
  const [projectDetail, setProjectDetail] = useState(null);
  const [loadingProjectDetail, setLoadingProjectDetail] = useState(false);
  const [retargetingTags, setRetargetingTags] = useState([]);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [dataLoadFailed, setDataLoadFailed] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // 人群包选择相关状态
  const [selectedAudiences, setSelectedAudiences] = useState(new Set());
  const [selectedExcludeAudiences, setSelectedExcludeAudiences] = useState(
    new Set()
  );
  const [audienceSelectionMode, setAudienceSelectionMode] = useState("inherit"); // 'inherit', 'custom', 'none'
  const [retargetingTagsExclude, setRetargetingTagsExclude] = useState([]);
  const [activeTab, setActiveTab] = useState("target"); // 'target', 'exclude'

  // 复制广告相关状态
  const [copyPromotions, setCopyPromotions] = useState(false);
  const [promotions, setPromotions] = useState([]);
  const [loadingPromotions, setLoadingPromotions] = useState(false);
  const [promotionStep, setPromotionStep] = useState("idle"); // 'idle', 'copying'
  const [copiedProjects, setCopiedProjects] = useState([]); // 已复制成功的项目
  const [promotionProgress, setPromotionProgress] = useState({
    current: 0,
    total: 0,
    projectIndex: 0,
    projectName: "",
  });

  // 重置表单
  const resetForm = () => {
    setNewProjectName("");
    setCopyCount(1);
    setCopying(false);
    setCustomAudiences([]);
    setLoadingAudiences(false);
    setShowAudiences(true);
    setProjectDetail(null);
    setLoadingProjectDetail(false);
    setRetargetingTags([]);
    setDataLoaded(false);
    setDataLoadFailed(false);
    setRetryCount(0);
    setSelectedAudiences(new Set());
    setSelectedExcludeAudiences(new Set());
    setAudienceSelectionMode("inherit");
    setRetargetingTagsExclude([]);
    setActiveTab("target");

    // 重置广告复制相关状态
    setCopyPromotions(false);
    setPromotions([]);
    setLoadingPromotions(false);
    setPromotionStep("idle");
    setCopiedProjects([]);
    setPromotionProgress({
      current: 0,
      total: 0,
      projectIndex: 0,
      projectName: "",
    });
  };

  // 带重试机制的异步函数执行器
  const executeWithRetry = async (asyncFunction, maxRetries = 2) => {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await asyncFunction();
        return result;
      } catch (error) {
        lastError = error;
        console.warn(`第 ${attempt + 1} 次尝试失败:`, error.message);

        if (attempt < maxRetries) {
          console.log(`等待 1 秒后进行第 ${attempt + 2} 次重试...`);
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    }

    throw lastError;
  };

  // 获取项目下的广告列表
  const loadPromotions = async () => {
    if (!project?.account_id || !project?.project_id) {
      console.log("项目数据不完整，无法获取广告列表:", project);
      return;
    }

    setLoadingPromotions(true);
    try {
      const params = {
        account_id: parseInt(project.account_id),
        project_id: String(project.project_id),
        page: 0,
        page_size: 0, // 获取所有数据
      };

      console.log("获取项目广告列表，参数:", params);
      const response = await promotionService.GetPromotionList(params);

      if (
        response &&
        response.code === 0 &&
        response.data &&
        response.data.list
      ) {
        setPromotions(response.data.list);
        console.log(
          `项目 ${project.project_name} 下有 ${response.data.list.length} 个广告`
        );
      } else {
        console.log("获取广告列表返回空数据或失败:", response);
        setPromotions([]);
      }
    } catch (error) {
      console.error("获取项目广告列表失败:", error);
      setPromotions([]);
    } finally {
      setLoadingPromotions(false);
    }
  };

  // 复制单个广告到指定项目（使用完整的复制逻辑，但采用默认配置）
  const copyPromotionToProjectAdvanced = async (
    promotion,
    targetProjectId,
    projectName
  ) => {
    try {
      const timestamp = new Date()
        .toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/[/:]/g, "_")
        .replace(/\s/g, "_");

      const newPromotionName = `${promotion.promotion_name}_复制_${timestamp}`;

      // 构建与直接粘贴广告相同的复制任务结构
      const copyTask = {
        promotion: promotion,
        newPromotionName: newPromotionName,
        targetProjectId: String(targetProjectId),
        videoFiles: [], // 默认不上传新视频
        existingVideoMaterials: [], // 使用原有素材（会在处理时自动获取）
        isVariationCopy: false, // 默认使用原样复制
        // 添加项目复制的特殊标识
        isFromProjectCopy: true,
        sourceAccountId: parseInt(project.account_id),
      };

      console.log(
        `构建广告复制任务 ${promotion.promotion_name} 到项目 ${projectName}:`,
        copyTask
      );

      return copyTask;
    } catch (error) {
      console.error(`构建广告复制任务失败:`, error);
      throw error;
    }
  };

  // 批量复制广告到所有新项目（使用完整的复制逻辑）
  const copyPromotionsToAllProjects = async (targetProjects = null) => {
    console.log("copyPromotionsToAllProjects 开始执行（使用完整复制逻辑）");
    console.log("传入的 targetProjects:", targetProjects);
    console.log("当前状态 promotions:", promotions);
    console.log("promotions.length:", promotions.length);

    // 使用传入的参数或者状态中的项目列表
    const projectsToUse = targetProjects || copiedProjects;
    console.log("最终使用的项目列表 projectsToUse:", projectsToUse);
    console.log("projectsToUse.length:", projectsToUse.length);

    if (promotions.length === 0 || projectsToUse.length === 0) {
      console.log("没有广告需要复制或没有目标项目", {
        promotionsLength: promotions.length,
        projectsLength: projectsToUse.length,
        targetProjects,
        copiedProjects,
      });
      return;
    }

    setPromotionStep("copying");
    const totalPromotions = promotions.length * projectsToUse.length;

    console.log(
      `开始批量复制广告（完整逻辑）: ${promotions.length} 个广告 × ${projectsToUse.length} 个项目 = ${totalPromotions} 个任务`
    );

    try {
      // 构建所有复制任务
      const allCopyTasks = [];

      for (const targetProject of projectsToUse) {
        for (const promotion of promotions) {
          const copyTask = await copyPromotionToProjectAdvanced(
            promotion,
            targetProject.projectId,
            targetProject.projectName
          );
          allCopyTasks.push(copyTask);
        }
      }

      console.log(`已构建 ${allCopyTasks.length} 个复制任务，开始执行`);

      // 设置初始进度
      setPromotionProgress({
        current: 0,
        total: totalPromotions,
        projectIndex: 1,
        projectName: "准备中...",
      });

      // 调用与直接粘贴广告相同的处理逻辑
      const results = await processAdvancedCopyTasks(allCopyTasks);

      // 显示结果
      const { successCount, failedTasks } = results;
      if (failedTasks.length === 0) {
        toast.success(
          `✅ 广告粘贴完成！成功将 ${successCount} 个广告粘贴到新项目`
        );
      } else {
        toast.warning(
          `⚠️ 广告粘贴完成！成功 ${successCount} 个，失败 ${failedTasks.length} 个`
        );
        console.error("广告粘贴失败:", failedTasks);
      }
    } catch (error) {
      console.error("批量粘贴广告异常:", error);
      toast.error(`❌ 广告粘贴失败: ${error.message}`);
    } finally {
      setPromotionStep("idle");
    }
  };

  // 高级复制任务处理器（使用与 PromotionManager 相同的逻辑）
  const processAdvancedCopyTasks = async (copyTasks) => {
    let successCount = 0;
    let failedTasks = [];

    console.log(`开始处理 ${copyTasks.length} 个高级复制任务`);

    // 由于项目复制中的广告都使用默认配置（原样复制，无新视频），
    // 可以直接逐个处理，不需要复杂的上传优化
    for (let i = 0; i < copyTasks.length; i++) {
      const task = copyTasks[i];

      // 更新进度
      setPromotionProgress({
        current: i + 1,
        total: copyTasks.length,
        projectIndex: Math.floor(i / promotions.length) + 1,
        projectName: `处理广告: ${task.promotion.promotion_name}`,
      });

      try {
        const result = await processAdvancedSingleCopyTask(task, i + 1);

        if (result.success) {
          successCount++;
          console.log(
            `✅ 任务 ${i + 1}/${copyTasks.length} 成功: ${
              task.newPromotionName
            }`
          );
        } else {
          failedTasks.push(result.error);
          console.error(
            `❌ 任务 ${i + 1}/${copyTasks.length} 失败: ${
              task.newPromotionName
            }`,
            result.error
          );
        }

        // 添加小延迟避免请求过于频繁
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        const errorInfo = {
          index: i + 1,
          name: task.newPromotionName,
          error: error.message,
        };
        failedTasks.push(errorInfo);
        console.error(`❌ 任务 ${i + 1}/${copyTasks.length} 异常:`, error);
      }
    }

    return { successCount, failedTasks };
  };

  // 处理单个高级复制任务
  const processAdvancedSingleCopyTask = async (copyTask, taskIndex) => {
    const { promotion, newPromotionName, targetProjectId, sourceAccountId } =
      copyTask;

    console.log(`处理第${taskIndex}个高级任务: ${newPromotionName}`);

    try {
      // 由于是项目复制，使用原有素材，不需要上传新视频
      // 直接调用复制接口
      const copyParams = {
        account_id: sourceAccountId,
        advertiser_id: parseInt(promotion.advertiser_id),
        source_promotion_id: String(promotion.promotion_id),
        new_promotion_name: newPromotionName,
        new_project_id: targetProjectId,
        video_material_info: [], // 使用原有素材
      };

      console.log(`调用高级复制接口，参数:`, copyParams);
      const result = await promotionService.CopyPromotion(copyParams);

      // 使用正确的双层判断逻辑（与右键粘贴广告一致）
      if (result && result.code === 0) {
        // 检查内层业务逻辑是否成功
        if (result.data && result.data.code === 0) {
          // 真正成功：外层和内层code都为0
          console.log(`高级复制成功: ${newPromotionName}`, {
            promotionId: result.data?.data?.promotion_id,
            requestId: result.data?.request_id,
          });
          return { success: true };
        } else {
          // 外层成功但内层失败：业务逻辑处理失败
          const innerErrorMsg = result.data?.msg || "业务逻辑处理失败";
          console.error(`高级复制业务逻辑失败: ${newPromotionName}`, {
            innerCode: result.data?.code,
            innerMsg: innerErrorMsg,
            requestId: result.data?.request_id,
          });
          return {
            success: false,
            error: {
              index: taskIndex,
              name: newPromotionName,
              error: innerErrorMsg,
            },
          };
        }
      } else {
        // 外层接口调用失败
        const outerErrorMsg = result?.msg || "接口调用失败";
        console.error(`高级复制接口调用失败: ${newPromotionName}`, {
          outerCode: result?.code,
          outerMsg: outerErrorMsg,
        });
        return {
          success: false,
          error: {
            index: taskIndex,
            name: newPromotionName,
            error: outerErrorMsg,
          },
        };
      }
    } catch (error) {
      console.error(`高级复制异常:`, error);
      return {
        success: false,
        error: {
          index: taskIndex,
          name: newPromotionName,
          error: error.message,
        },
      };
    }
  };

  // 获取项目详情
  const loadProjectDetail = async () => {
    if (
      !project?.account_id ||
      !project?.advertiser_id ||
      !project?.project_id
    ) {
      console.log("项目数据不完整，无法获取详情:", project);
      throw new Error("项目数据不完整");
    }

    setLoadingProjectDetail(true);
    try {
      const accountId = parseInt(project.account_id);
      const advertiserId = parseInt(project.advertiser_id);
      const projectId = project.project_id;

      console.log("获取项目详情，参数:", {
        accountId,
        advertiserId,
        projectId,
      });

      const response = await executeWithRetry(async () => {
        console.log("开始获取项目详情");

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("获取项目详情超时")), 60000); // 60秒超时
        });

        const requestPromise = projectService.getProjectDetail(
          accountId,
          advertiserId,
          projectId
        );

        return Promise.race([requestPromise, timeoutPromise]);
      });

      console.log("项目详情返回数据:", response);

      // 更灵活的数据检查
      let projectData = null;

      // 1. 检查标准格式：response.data
      if (
        response &&
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        projectData = response.data[0];
      }
      // 2. 检查直接返回数组格式：response本身就是数组
      else if (response && Array.isArray(response) && response.length > 0) {
        projectData = response[0];
      }
      // 3. 检查其他可能的格式
      else if (response && response.data && !Array.isArray(response.data)) {
        projectData = response.data;
      }

      if (projectData) {
        // console.log("解析后的项目数据:", projectData);
        // console.log("- 是否有Audience字段:", projectData.data[0].Data.Audience.RetargetingTags);

        setProjectDetail(projectData);

        // 提取定向人群包信息
        if (projectData.data[0].Data.Audience.RetargetingTags) {
          setRetargetingTags(projectData.data[0].Data.Audience.RetargetingTags);
          console.log(
            "提取到定向人群包:",
            projectData.data[0].Data.Audience.RetargetingTags
          );
        } else {
          setRetargetingTags([]);
          console.log("未找到定向人群包信息");
        }

        // 提取排除人群包信息
        if (projectData.data[0].Data.Audience.RetargetingTagsExclude) {
          setRetargetingTagsExclude(
            projectData.data[0].Data.Audience.RetargetingTagsExclude
          );
          console.log(
            "提取到排除人群包:",
            projectData.data[0].Data.Audience.RetargetingTagsExclude
          );
        } else {
          setRetargetingTagsExclude([]);
          console.log("未找到排除人群包信息");
        }
      } else {
        console.error("无法解析项目详情数据:", response);
        throw new Error("项目详情数据格式不正确");
      }
    } catch (error) {
      console.error("获取项目详情失败:", error);
      throw error; // 重新抛出错误，让上层处理
    } finally {
      setLoadingProjectDetail(false);
    }
  };

  // 获取人群包数据
  const loadCustomAudiences = async () => {
    if (!project?.advertiser_id) {
      console.log("项目数据不完整，缺少advertiser_id:", project);
      throw new Error("项目数据不完整，缺少advertiser_id");
    }

    setLoadingAudiences(true);
    try {
      // 确保accountId是数字类型，advertiserId是字符串类型
      const accountId = parseInt(project.account_id) || 0;
      const advertiserId = String(project.advertiser_id);

      console.log("调用人群包服务，参数:", {
        accountId,
        advertiserId,
        type: typeof accountId,
      });
      console.log("项目数据:", project);

      const response = await executeWithRetry(async () => {
        return await retargetingTagService.getRetargetingTagsByAccount(
          accountId,
          advertiserId
        );
      });

      // 检查response.data中的custom_audiences字段
      if (response && response.data && response.data.custom_audiences) {
        setCustomAudiences(response.data.custom_audiences);
      } else {
        console.log("人群包数据为空或格式不正确:", response);
        setCustomAudiences([]);
      }
    } catch (error) {
      console.error("加载人群包数据失败:", error);
      setCustomAudiences([]);
    } finally {
      setLoadingAudiences(false);
    }
  };

  // 检查人群包是否已绑定
  const isAudienceBound = (audience) => {
    // 确保数据已加载完成且retargetingTags存在
    if (!dataLoaded || !retargetingTags || retargetingTags.length === 0) {
      console.log(
        `人群包 ${audience.id} (${audience.name}) 匹配失败: 数据未加载完成或retargetingTags为空`
      );
      console.log("当前状态:", {
        dataLoaded,
        retargetingTagsLength: retargetingTags?.length,
      });
      return false;
    }

    // 确保类型匹配，将audience.id转换为字符串进行比较
    const audienceId = String(audience.id);
    const isBound = retargetingTags.some(
      (tagId) => String(tagId) === audienceId
    );

    // console.log(`人群包 ${audienceId} (${audience.name}) 匹配结果:`, {
    //   audienceId,
    //   audienceIdType: typeof audienceId,
    //   retargetingTags,
    //   retargetingTagsTypes: retargetingTags.map((tag) => ({
    //     value: tag,
    //     type: typeof tag,
    //   })),
    //   isBound,
    // });

    return isBound;
  };

  // 检查人群包是否被排除
  const isAudienceExcluded = (audience) => {
    // 确保数据已加载完成且retargetingTagsExclude存在
    if (
      !dataLoaded ||
      !retargetingTagsExclude ||
      retargetingTagsExclude.length === 0
    ) {
      return false;
    }

    // 确保类型匹配，将audience.id转换为字符串进行比较
    const audienceId = String(audience.id);
    const isExcluded = retargetingTagsExclude.some(
      (tagId) => String(tagId) === audienceId
    );

    return isExcluded;
  };

  // 人群包选择相关函数
  const handleAudienceSelect = (audienceId) => {
    const newSelected = new Set(selectedAudiences);
    if (newSelected.has(audienceId)) {
      newSelected.delete(audienceId);
    } else {
      newSelected.add(audienceId);
    }
    setSelectedAudiences(newSelected);
  };

  // 排除人群包选择相关函数
  const handleExcludeAudienceSelect = (audienceId) => {
    const newSelected = new Set(selectedExcludeAudiences);
    if (newSelected.has(audienceId)) {
      newSelected.delete(audienceId);
    } else {
      newSelected.add(audienceId);
    }
    setSelectedExcludeAudiences(newSelected);
  };

  const handleSelectAll = () => {
    const allAudienceIds = customAudiences.map((audience) => audience.id);
    setSelectedAudiences(new Set(allAudienceIds));
  };

  const handleDeselectAll = () => {
    setSelectedAudiences(new Set());
  };

  const handleSelectBound = () => {
    const boundAudienceIds = customAudiences
      .filter((audience) => isAudienceBound(audience))
      .map((audience) => audience.id);
    setSelectedAudiences(new Set(boundAudienceIds));
  };

  // 排除人群包批量操作
  const handleSelectAllExclude = () => {
    const allAudienceIds = customAudiences.map((audience) => audience.id);
    setSelectedExcludeAudiences(new Set(allAudienceIds));
    console.log(`全选排除人群包:`, allAudienceIds);
  };

  const handleDeselectAllExclude = () => {
    setSelectedExcludeAudiences(new Set());
    console.log(`取消全选排除人群包`);
  };

  const handleSelectExcluded = () => {
    // 过滤出已排除的人群包
    const excludedAudiences = customAudiences.filter((audience) =>
      isAudienceExcluded(audience)
    );

    // 提取ID列表
    const excludedAudienceIds = excludedAudiences.map(
      (audience) => audience.id
    );

    // 设置选择状态
    const newSelected = new Set(excludedAudienceIds);
    setSelectedExcludeAudiences(newSelected);
  };

  // 获取当前选中的人群包ID列表
  const getSelectedAudienceIds = () => {
    let result;
    if (audienceSelectionMode === "inherit") {
      // 继承原项目的人群包
      result = retargetingTags;
      console.log("继承模式 - 定向人群包:", result);
    } else if (audienceSelectionMode === "custom") {
      // 使用自定义选择的人群包
      result = Array.from(selectedAudiences);
      console.log("自定义模式 - 定向人群包:", result);
    } else {
      // 不包含人群包
      result = [];
      console.log("无人群包模式 - 定向人群包:", result);
    }
    return result;
  };

  // 获取当前选中的排除人群包ID列表
  const getSelectedExcludeAudienceIds = () => {
    let result;
    if (audienceSelectionMode === "inherit") {
      // 继承原项目的排除人群包
      result = retargetingTagsExclude;
    } else if (audienceSelectionMode === "custom") {
      // 使用自定义选择的排除人群包
      result = Array.from(selectedExcludeAudiences);
    } else {
      // 不包含排除人群包
      result = [];
    }
    return result;
  };

  // 处理确认复制
  const handleConfirm = async () => {
    if (!newProjectName.trim()) {
      toast.warning("请输入新项目名称");
      return;
    }

    if (copyCount < 1 || copyCount > 10) {
      toast.warning("复制数量必须在1-10之间");
      return;
    }

    setCopying(true);
    try {
      // 获取选中的人群包ID列表
      const selectedAudienceIds = getSelectedAudienceIds();
      const selectedExcludeAudienceIds = getSelectedExcludeAudienceIds();

      // 确保项目详情数据格式正确
      let projectDetailData = null;
      if (projectDetail) {
        // 如果projectDetail是数组，取第一个元素
        if (Array.isArray(projectDetail)) {
          projectDetailData = projectDetail[0];
        } else {
          projectDetailData = projectDetail;
        }

        // 验证数据结构
        if (
          !projectDetailData.data[0] ||
          !projectDetailData.data[0].Data.Audience
        ) {
          console.warn("项目详情数据结构不完整，将使用后端获取");
          projectDetailData = null;
        } else {
          // 根据人群包选择模式修改项目详情数据
          if (audienceSelectionMode === "custom") {
            // 自定义选择模式：使用用户选择的人群包配置
            projectDetailData.data[0].Data.Audience.RetargetingTags =
              selectedAudienceIds;
            projectDetailData.data[0].Data.Audience.RetargetingTagsExclude =
              selectedExcludeAudienceIds;
          } else if (audienceSelectionMode === "none") {
            // 不包含人群包模式：清空所有人群包配置
            projectDetailData.data[0].Data.Audience.RetargetingTags = [];
            projectDetailData.data[0].Data.Audience.RetargetingTagsExclude = [];
          }
          // 如果是inherit模式，保持原项目的人群包配置不变

          // 提取正确的ProjectInfoData结构传递给后端
          projectDetailData = projectDetailData.data[0];
        }
      }

      // 构建复制参数
      const copyParams = {
        project,
        newProjectName: newProjectName.trim(),
        copyCount,
        audienceSelectionMode,
        selectedAudienceIds,
        selectedExcludeAudienceIds,
        inheritOriginalAudiences: audienceSelectionMode === "inherit",
        projectDetail: projectDetailData,
      };

      // 执行项目复制
      const projectResults = await onCopyConfirm(copyParams);

      // 检查是否需要复制广告
      if (copyPromotions && promotions.length > 0 && projectResults) {
        console.log("项目复制成功，开始复制广告...");

        // 解析项目复制结果，提取新项目的ID和名称
        const copiedProjectsList = [];

        console.log("解析项目复制结果，原始数据:", projectResults);

        if (Array.isArray(projectResults)) {
          // 如果返回的是数组（批量复制）
          projectResults.forEach((result, index) => {
            console.log(`解析数组项目结果 ${index}:`, result);

            // 尝试不同的数据结构路径
            let projectId = null;

            if (
              result &&
              result.data &&
              result.data.created_project &&
              result.data.created_project.data &&
              result.data.created_project.data.id
            ) {
              projectId = result.data.created_project.data.id;
              console.log(
                "从 projectResults.data.created_project.data.id 提取到:",
                projectId
              );
            } else if (
              result &&
              result.data &&
              result.data.created_project &&
              result.data.created_project.data &&
              result.data.created_project.data.project_id
            ) {
              projectId = result.data.created_project.data.project_id;
              console.log(
                "从 projectResults.data.created_project.data.project_id 提取到:",
                projectId
              );
            } else if (result && result.data && result.data.project_id) {
              projectId = result.data.project_id;
              console.log(
                "从 projectResults.data.project_id 提取到:",
                projectId
              );
            } else if (
              result &&
              result.created_project &&
              result.created_project.data &&
              result.created_project.data.project_id
            ) {
              projectId = result.created_project.data.project_id;
              console.log(
                "从 projectResults.created_project.data.project_id 提取到:",
                projectId
              );
            } else if (
              result &&
              result.created_project &&
              result.created_project.data &&
              result.created_project.data.id
            ) {
              projectId = result.created_project.data.id;
              console.log(
                "从 projectResults.created_project.data.id 提取到:",
                projectId
              );
            } else {
              console.log("尝试所有可能的路径都失败了，打印完整数据结构:");
              console.log(
                "projectResults:",
                JSON.stringify(projectResults, null, 2)
              );
            }

            if (projectId) {
              copiedProjectsList.push({
                projectId: projectId,
                projectName:
                  copyCount === 1
                    ? newProjectName.trim()
                    : `${newProjectName.trim()}_${index + 1}`,
              });
              console.log(
                `提取到项目ID: ${projectId}, 名称: ${
                  copyCount === 1
                    ? newProjectName.trim()
                    : `${newProjectName.trim()}_${index + 1}`
                }`
              );
            } else {
              console.log(`无法从结果 ${index} 中提取项目ID`);
            }
          });
        } else if (projectResults) {
          // 如果返回的是单个结果
          console.log("解析单个项目结果:", projectResults);

          let projectId = null;

          // 尝试不同的数据结构路径
          if (
            projectResults.data &&
            projectResults.data.created_project &&
            projectResults.data.created_project.data &&
            projectResults.data.created_project.data.id
          ) {
            projectId = projectResults.data.created_project.data.id;
            console.log(
              "从 projectResults.data.created_project.data.id 提取到:",
              projectId
            );
          } else if (
            projectResults.data &&
            projectResults.data.created_project &&
            projectResults.data.created_project.data &&
            projectResults.data.created_project.data.project_id
          ) {
            projectId = projectResults.data.created_project.data.project_id;
            console.log(
              "从 projectResults.data.created_project.data.project_id 提取到:",
              projectId
            );
          } else if (projectResults.data && projectResults.data.project_id) {
            projectId = projectResults.data.project_id;
            console.log("从 projectResults.data.project_id 提取到:", projectId);
          } else if (
            projectResults.created_project &&
            projectResults.created_project.data &&
            projectResults.created_project.data.project_id
          ) {
            projectId = projectResults.created_project.data.project_id;
            console.log(
              "从 projectResults.created_project.data.project_id 提取到:",
              projectId
            );
          } else if (
            projectResults.created_project &&
            projectResults.created_project.data &&
            projectResults.created_project.data.id
          ) {
            projectId = projectResults.created_project.data.id;
            console.log(
              "从 projectResults.created_project.data.id 提取到:",
              projectId
            );
          } else {
            console.log("尝试所有可能的路径都失败了，打印完整数据结构:");
            console.log(
              "projectResults:",
              JSON.stringify(projectResults, null, 2)
            );
          }

          if (projectId) {
            copiedProjectsList.push({
              projectId: projectId,
              projectName: newProjectName.trim(),
            });
            console.log(
              `提取到项目ID: ${projectId}, 名称: ${newProjectName.trim()}`
            );
          } else {
            console.log("无法从单个结果中提取项目ID");
          }
        }

        if (copiedProjectsList.length > 0) {
          setCopiedProjects(copiedProjectsList);

          console.log("准备开始复制广告，检查数据:");
          console.log("- copiedProjectsList:", copiedProjectsList);
          console.log("- promotions:", promotions);
          console.log("- promotions.length:", promotions.length);
          console.log("- copyPromotions 选项:", copyPromotions);

          // 显示项目复制成功，开始粘贴广告的提示
          toast.info(
            `🚀 项目复制成功！开始粘贴 ${promotions.length} 个广告到 ${copiedProjectsList.length} 个新项目...`
          );

          // 开始复制广告，直接传入项目列表而不等待状态更新
          await copyPromotionsToAllProjects(copiedProjectsList);

          // 广告复制完成后才关闭弹窗
          resetForm();
          onClose();
        } else {
          console.error("无法解析项目复制结果:", projectResults);
          toast.warning("项目复制成功，但无法获取新项目信息，跳过广告粘贴");
          resetForm();
          onClose();
        }
      } else {
        // 如果不需要复制广告，直接关闭弹窗
        resetForm();
        onClose();
      }
    } catch (error) {
      console.error("复制项目失败:", error);
      toast.error(`复制项目失败: ${error.message}`);
    } finally {
      if (promotionStep === "idle") {
        setCopying(false);
      }
    }
  };

  // 处理取消
  const handleCancel = () => {
    // 如果正在粘贴广告，给出提示
    if (promotionStep === "copying") {
      toast.warning("正在粘贴广告，请稍候完成后再关闭");
      return;
    }

    resetForm();
    onClose();
  };

  // 当对话框打开时，自动生成新项目名称并加载数据
  useEffect(() => {
    if (isOpen && project) {
      // 生成新项目名称
      const timestamp = new Date()
        .toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/[/:]/g, "_")
        .replace(/\s/g, "_");

      let baseName = project.project_name || "项目";
      // 如果原名称已包含复制标识，先移除
      if (baseName.includes("_复制_")) {
        baseName = baseName.split("_复制_")[0];
      }
      const newName = `${baseName}_复制_${timestamp}`;
      setNewProjectName(newName);

      // 加载数据
      const loadData = async () => {
        try {
          setDataLoaded(false);
          setDataLoadFailed(false);
          setRetryCount(0);

          // 如果有预加载的项目详情，直接使用
          if (preloadedProjectDetail) {
            console.log("使用预加载的项目详情数据");
            setProjectDetail(preloadedProjectDetail);
            console.log("preloadedProjectDetail", preloadedProjectDetail);
            // 提取定向人群包信息
            if (preloadedProjectDetail.data[0].Data.Audience.RetargetingTags) {
              setRetargetingTags(
                preloadedProjectDetail.data[0].Data.Audience.RetargetingTags
              );
              console.log(
                "提取到定向人群包:",
                preloadedProjectDetail.data[0].Data.Audience.RetargetingTags
              );
            } else {
              setRetargetingTags([]);
              console.log("未找到定向人群包信息");
            }

            // 提取排除人群包信息
            if (
              preloadedProjectDetail.data[0].Data.Audience
                .RetargetingTagsExclude
            ) {
              setRetargetingTagsExclude(
                preloadedProjectDetail.data[0].Data.Audience
                  .RetargetingTagsExclude
              );
              console.log(
                "提取到排除人群包:",
                preloadedProjectDetail.data[0].Data.Audience
                  .RetargetingTagsExclude
              );
            } else {
              setRetargetingTagsExclude([]);
              console.log("未找到排除人群包信息");
            }

            // 即使使用预加载数据，也需要获取可用的人群包列表
            try {
              await loadCustomAudiences();
            } catch (error) {
              console.error("加载人群包数据失败:", error);
              // 不抛出错误，继续执行
            }

            setDataLoaded(true);
          } else {
            // 否则重新获取项目详情
            await loadProjectDetail();
            await loadCustomAudiences();
            setDataLoaded(true);
          }

          // 加载项目下的广告列表
          await loadPromotions();
        } catch (error) {
          console.error("加载数据失败:", error);
          setDataLoadFailed(true);
          setRetryCount((prev) => prev + 1);
        }
      };

      loadData();
    } else if (!isOpen) {
      // 对话框关闭时重置状态
      resetForm();
    }
  }, [isOpen, project, preloadedProjectDetail]);

  // 监听retargetingTags变化，用于调试
  useEffect(() => {
    if (retargetingTags.length > 0) {
      console.log("RetargetingTags已更新:", retargetingTags);
    }
  }, [retargetingTags]);

  // 渲染人群包状态
  const renderAudienceStatus = (audience) => {
    const statusMap = {
      2: "已生效",
      1: "计算中",
      0: "未生效",
    };

    return (
      <span className="text-xs text-gray-600">
        {statusMap[audience.status] || "未知"}
      </span>
    );
  };

  // 渲染人群包标签
  const renderAudienceTag = (audience) => {
    if (!audience.tag) return null;

    return (
      <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
        {audience.tag}
      </span>
    );
  };

  // 渲染绑定状态图标
  const renderBindingStatus = (audience) => {
    // 确保数据已加载完成且retargetingTags或retargetingTagsExclude存在
    if (
      !dataLoaded ||
      ((!retargetingTags || retargetingTags.length === 0) &&
        (!retargetingTagsExclude || retargetingTagsExclude.length === 0))
    ) {
      return (
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-500">加载中...</span>
        </div>
      );
    }

    const isBound = isAudienceBound(audience);
    const isExcluded = isAudienceExcluded(audience);

    // console.log(`渲染绑定状态 - 人群包 ${audience.id} (${audience.name}):`, {
    //   isBound,
    //   isExcluded,
    //   dataLoaded,
    //   retargetingTagsLength: retargetingTags?.length,
    //   retargetingTagsExcludeLength: retargetingTagsExclude?.length,
    // });

    if (isBound) {
      // 已绑定的简洁显示
      return (
        <div className="flex items-center space-x-1">
          <CheckCircle size={16} className="text-green-500" />
          <span className="text-xs font-medium text-green-600">已绑定</span>
        </div>
      );
    } else if (isExcluded) {
      // 已排除的红色显示
      return (
        <div className="flex items-center space-x-1">
          <XCircle size={16} className="text-red-500" />
          <span className="text-xs font-medium text-red-600">已排除</span>
        </div>
      );
    } else {
      // 未绑定的明显提示
      return (
        <div className="flex items-center space-x-1">
          <XCircle size={16} className="text-gray-400" />
          <span className="text-xs text-gray-500">未绑定</span>
        </div>
      );
    }
  };

  // 处理复制数量变化
  const handleCopyCountChange = (newCount) => {
    if (newCount >= 1 && newCount <= 10) {
      setCopyCount(newCount);
    }
  };

  // 增加复制数量
  const increaseCopyCount = () => {
    if (copyCount < 10) {
      setCopyCount(copyCount + 1);
    }
  };

  // 减少复制数量
  const decreaseCopyCount = () => {
    if (copyCount > 1) {
      setCopyCount(copyCount - 1);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50"
      style={{ backgroundColor: "rgba(107, 114, 128, 0.3)" }}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-full bg-purple-100 text-purple-600">
              <Copy size={20} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">复制项目</h3>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={copying}
          >
            <X size={20} />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-4 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* 源项目信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-700 mb-3">
              源项目信息
            </div>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-500">项目名称</span>
                <div className="font-medium text-gray-900 truncate">
                  {project?.project_name || "未知"}
                </div>
              </div>
              <div>
                <span className="text-gray-500">项目ID</span>
                <div className="font-mono text-gray-900 text-xs">
                  {project?.project_id || "未知"}
                </div>
              </div>
              <div>
                <span className="text-gray-500">广告主ID</span>
                <div className="font-mono text-gray-900 text-xs">
                  {project?.advertiser_id || "未知"}
                </div>
              </div>
              <div>
                <span className="text-gray-500">状态</span>
                <div className="text-gray-900">
                  {project?.project_status_second?.includes(1)
                    ? "已禁用"
                    : "正常"}
                </div>
              </div>
            </div>

            {/* 项目详情加载状态 */}
            {loadingProjectDetail && (
              <div className="mt-3 flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-blue-600">
                  正在获取项目详情...
                </span>
              </div>
            )}

            {/* 重定向标签信息 */}
            {projectDetail && retargetingTags.length > 0 && (
              <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                <div className="text-xs font-medium text-blue-800 mb-1">
                  项目已绑定人群包 ({retargetingTags.length} 个)
                </div>
                <div className="flex flex-wrap gap-1">
                  {retargetingTags.map((tagId, index) => {
                    // 根据ID查找人群包名称
                    const audience = customAudiences.find(
                      (aud) => String(aud.id) === String(tagId)
                    );
                    const displayName = audience
                      ? audience.name
                      : `ID: ${tagId}`;

                    return (
                      <span
                        key={index}
                        className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800 truncate max-w-32"
                        title={displayName}
                      >
                        {displayName}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}

            {projectDetail && retargetingTagsExclude.length > 0 && (
              <div className="mt-2 p-2 bg-red-50 rounded border border-red-200">
                <div className="text-xs font-medium text-red-800 mb-1">
                  项目已排除人群包 ({retargetingTagsExclude.length} 个)
                </div>
                <div className="flex flex-wrap gap-1">
                  {retargetingTagsExclude.map((tagId, index) => {
                    // 根据ID查找人群包名称
                    const audience = customAudiences.find(
                      (aud) => String(aud.id) === String(tagId)
                    );
                    const displayName = audience
                      ? audience.name
                      : `ID: ${tagId}`;

                    return (
                      <span
                        key={index}
                        className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800 truncate max-w-32"
                        title={displayName}
                      >
                        {displayName}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* 人群包信息 */}
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Users size={16} className="text-blue-600" />
                <span className="text-sm font-medium text-blue-800">
                  可用人群包
                </span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                  {customAudiences.length} 个
                </span>
                {dataLoaded &&
                  !dataLoadFailed &&
                  retargetingTags.length > 0 && (
                    <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                      已绑定 {retargetingTags.length} 个
                    </span>
                  )}
                {dataLoaded &&
                  !dataLoadFailed &&
                  retargetingTagsExclude.length > 0 && (
                    <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">
                      已排除 {retargetingTagsExclude.length} 个
                    </span>
                  )}
                {!dataLoaded && !dataLoadFailed && (
                  <span className="text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full">
                    匹配中...
                  </span>
                )}
                {dataLoadFailed && (
                  <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">
                    加载失败
                  </span>
                )}
              </div>
              <button
                onClick={() => setShowAudiences(!showAudiences)}
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
                disabled={loadingAudiences || !dataLoaded}
              >
                {showAudiences ? <EyeOff size={16} /> : <Eye size={16} />}
                <span className="text-sm">
                  {showAudiences ? "隐藏" : "查看"}人群包
                </span>
              </button>
            </div>

            {/* 人群包选择模式 */}
            {dataLoaded && !dataLoadFailed && (
              <div className="mb-2 p-2 bg-white rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    人群包配置方式
                  </span>
                  <span className="text-xs text-gray-500">
                    当前选择: 定向 {selectedAudiences.size} 个, 排除{" "}
                    {selectedExcludeAudiences.size} 个
                  </span>
                </div>

                <div className="flex items-center space-x-4 mb-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="audienceMode"
                      value="inherit"
                      checked={audienceSelectionMode === "inherit"}
                      onChange={(e) => {
                        console.log("选择继承模式:", e.target.value);
                        setAudienceSelectionMode(e.target.value);
                      }}
                      className="text-blue-600"
                    />
                    <span className="text-sm text-gray-700">
                      继承原项目人群包
                    </span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="audienceMode"
                      value="custom"
                      checked={audienceSelectionMode === "custom"}
                      onChange={(e) => {
                        console.log("选择自定义模式:", e.target.value);
                        setAudienceSelectionMode(e.target.value);
                      }}
                      className="text-blue-600"
                    />
                    <span className="text-sm text-gray-700">自定义选择</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="audienceMode"
                      value="none"
                      checked={audienceSelectionMode === "none"}
                      onChange={(e) => {
                        console.log("选择无人群包模式:", e.target.value);
                        setAudienceSelectionMode(e.target.value);
                      }}
                      className="text-blue-600"
                    />
                    <span className="text-sm text-gray-700">不包含人群包</span>
                  </label>
                </div>

                {/* 人群包类型选择标签页 */}
                {audienceSelectionMode === "custom" && (
                  <div className="border-t border-gray-200 pt-2">
                    <div className="flex space-x-1 mb-2">
                      <button
                        className={`px-3 py-1 text-xs rounded-t-lg font-medium transition-colors ${
                          activeTab === "target"
                            ? "bg-blue-100 text-blue-700 border-b-2 border-blue-500"
                            : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                        }`}
                        onClick={() => setActiveTab("target")}
                      >
                        定向人群包 ({selectedAudiences.size})
                      </button>
                      <button
                        className={`px-3 py-1 text-xs rounded-t-lg font-medium transition-colors ${
                          activeTab === "exclude"
                            ? "bg-red-100 text-red-700 border-b-2 border-red-500"
                            : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                        }`}
                        onClick={() => setActiveTab("exclude")}
                      >
                        排除人群包 ({selectedExcludeAudiences.size})
                      </button>
                    </div>

                    {/* 定向人群包操作按钮 */}
                    {activeTab === "target" && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleSelectAll}
                          className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        >
                          全选
                        </button>
                        <button
                          onClick={handleDeselectAll}
                          className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                        >
                          取消全选
                        </button>
                        <button
                          onClick={handleSelectBound}
                          className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                        >
                          选择已绑定
                        </button>
                        <button
                          onClick={() => {
                            console.log("当前定向人群包选择状态:", {
                              selectedAudiences: Array.from(selectedAudiences),
                              selectedAudiencesSize: selectedAudiences.size,
                              audienceSelectionMode,
                              activeTab,
                            });
                            toast.info(
                              `已选择 ${selectedAudiences.size} 个定向人群包`,
                              2000
                            );
                          }}
                          className="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
                        >
                          查看选择状态
                        </button>
                      </div>
                    )}

                    {/* 排除人群包操作按钮 */}
                    {activeTab === "exclude" && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleSelectAllExclude}
                          className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                        >
                          全选
                        </button>
                        <button
                          onClick={handleDeselectAllExclude}
                          className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                        >
                          取消全选
                        </button>
                        <button
                          onClick={handleSelectExcluded}
                          className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors"
                        >
                          选择已排除
                        </button>
                        <button
                          onClick={() => {
                            console.log("当前排除人群包选择状态:", {
                              selectedExcludeAudiences: Array.from(
                                selectedExcludeAudiences
                              ),
                              selectedExcludeAudiencesSize:
                                selectedExcludeAudiences.size,
                              audienceSelectionMode,
                              activeTab,
                            });
                            toast.info(
                              `已选择 ${selectedExcludeAudiences.size} 个排除人群包`,
                              2000
                            );
                          }}
                          className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        >
                          查看选择状态
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {loadingAudiences && (
              <div className="flex items-center justify-center py-4">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-blue-600">
                  加载人群中...
                </span>
              </div>
            )}

            {!dataLoaded && !loadingAudiences && !dataLoadFailed && (
              <div className="flex items-center justify-center py-4">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-blue-600">
                  正在匹配人群包绑定状态...
                </span>
              </div>
            )}

            {dataLoadFailed && (
              <div className="flex items-center justify-center py-4">
                <div className="text-center">
                  <div className="text-red-600 text-sm font-medium mb-2">
                    数据加载失败
                  </div>
                  <div className="text-xs text-gray-600">
                    请检查网络连接后重新打开弹窗
                  </div>
                </div>
              </div>
            )}

            {showAudiences &&
              dataLoaded &&
              !loadingAudiences &&
              !dataLoadFailed && (
                <div>
                  {/* 绑定统计信息 */}
                  {retargetingTags.length > 0 && (
                    <div className="mb-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1">
                            <div className="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                            <span className="text-green-700 font-medium text-xs">
                              已绑定:{" "}
                              {
                                customAudiences.filter((audience) =>
                                  isAudienceBound(audience)
                                ).length
                              }{" "}
                              个
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <div className="w-2.5 h-2.5 bg-red-500 rounded-full"></div>
                            <span className="text-red-700 font-medium text-xs">
                              已排除:{" "}
                              {
                                customAudiences.filter((audience) =>
                                  isAudienceExcluded(audience)
                                ).length
                              }{" "}
                              个
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <div className="w-2.5 h-2.5 bg-gray-400 rounded-full"></div>
                            <span className="text-gray-600 font-medium text-xs">
                              未设置:{" "}
                              {
                                customAudiences.filter(
                                  (audience) =>
                                    !isAudienceBound(audience) &&
                                    !isAudienceExcluded(audience)
                                ).length
                              }{" "}
                              个
                            </span>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          总计: {customAudiences.length} 个
                        </div>
                      </div>
                      <div className="mt-1 text-xs text-gray-500">
                        💡
                        人群包只能被绑定或排除，两者互斥。已绑定的人群包用绿色标识，已排除的人群包用红色标识
                      </div>
                      {activeTab === "target" && (
                        <div className="mt-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          📋 当前标签页：定向人群包 -
                          显示所有人群包，可选择要包含的人群包
                        </div>
                      )}
                      {activeTab === "exclude" && (
                        <div className="mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                          📋 当前标签页：排除人群包 -{" "}
                          {audienceSelectionMode === "custom"
                            ? "显示所有人群包，可选择要排除的人群包（红色标记为已排除）"
                            : audienceSelectionMode === "inherit"
                            ? "显示已被排除的人群包，将继承原项目的排除设置"
                            : "已选择不包含人群包模式，不显示任何人群包"}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 人群包列表 */}
                  <div className="space-y-2 max-h-80 overflow-y-auto">
                    {customAudiences.length === 0 ? (
                      <div className="text-center py-4 text-gray-500 text-sm">
                        暂无可用人群包
                      </div>
                    ) : (
                      customAudiences
                        .map((audience, index) => {
                          const isBound =
                            dataLoaded &&
                            retargetingTags.length > 0 &&
                            isAudienceBound(audience);
                          const isExcluded =
                            dataLoaded &&
                            retargetingTagsExclude.length > 0 &&
                            isAudienceExcluded(audience);
                          const isSelected = selectedAudiences.has(audience.id);
                          const isSelectedExclude =
                            selectedExcludeAudiences.has(audience.id);
                          const isCustomMode =
                            audienceSelectionMode === "custom";
                          const isInheritMode =
                            audienceSelectionMode === "inherit";

                          return {
                            ...audience,
                            isBound,
                            isExcluded,
                            isSelected,
                            isSelectedExclude,
                            isCustomMode,
                            isInheritMode,
                            originalIndex: index,
                          };
                        })
                        .sort((a, b) => {
                          // 已绑定的排在前面，已排除的排在中间，未设置的排在后面
                          if (a.isBound && !b.isBound) return -1;
                          if (!a.isBound && b.isBound) return 1;
                          if (a.isExcluded && !b.isExcluded) return -1;
                          if (!a.isExcluded && b.isExcluded) return 1;
                          // 如果状态相同，按原始顺序排列
                          return a.originalIndex - b.originalIndex;
                        })
                        .filter((audience) => {
                          // 根据当前标签页和选择模式过滤人群包
                          if (activeTab === "target") {
                            // 定向人群包标签页：显示所有人群包
                            return true;
                          } else if (activeTab === "exclude") {
                            // 排除人群包标签页
                            if (audienceSelectionMode === "custom") {
                              // 自定义选择模式：显示所有人群包，让用户可以选择要排除的人群包
                              return true;
                            } else if (audienceSelectionMode === "inherit") {
                              // 继承模式：只显示已经被排除的人群包
                              return audience.isExcluded;
                            } else {
                              // 不包含人群包模式：不显示任何人群包
                              return false;
                            }
                          }
                          return true;
                        })
                        .map((audience, index) => {
                          return (
                            <div
                              key={audience.id || audience.originalIndex}
                              className={`relative rounded-md p-3 border transition-all duration-200 ${
                                audience.isBound
                                  ? "border-green-300 bg-gradient-to-r from-green-50 to-emerald-50 shadow-sm"
                                  : audience.isExcluded
                                  ? "border-red-300 bg-gradient-to-r from-red-50 to-pink-50 shadow-sm"
                                  : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
                              } ${
                                (audience.isSelected &&
                                  audience.isCustomMode &&
                                  activeTab === "target") ||
                                (audience.isSelectedExclude &&
                                  audience.isCustomMode &&
                                  activeTab === "exclude")
                                  ? "ring-2 ring-blue-400"
                                  : ""
                              }`}
                            >
                              {/* 选择复选框 */}
                              {audience.isCustomMode && (
                                <div className="absolute top-1.5 left-1.5">
                                  <input
                                    type="checkbox"
                                    checked={
                                      activeTab === "target"
                                        ? audience.isSelected
                                        : audience.isSelectedExclude
                                    }
                                    onChange={() => {
                                      console.log("复选框点击事件:", {
                                        audienceId: audience.id,
                                        activeTab,
                                        audienceSelectionMode,
                                        isCustomMode: audience.isCustomMode,
                                        isSelected: audience.isSelected,
                                        isSelectedExclude:
                                          audience.isSelectedExclude,
                                      });

                                      if (activeTab === "target") {
                                        console.log(
                                          "调用 handleAudienceSelect"
                                        );
                                        handleAudienceSelect(audience.id);
                                      } else if (activeTab === "exclude") {
                                        console.log(
                                          "调用 handleExcludeAudienceSelect"
                                        );
                                        handleExcludeAudienceSelect(
                                          audience.id
                                        );
                                      } else {
                                        console.log("未知的标签页:", activeTab);
                                      }
                                    }}
                                    className="w-3.5 h-3.5 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-1"
                                  />
                                </div>
                              )}

                              {/* 绑定状态指示器 */}
                              {audience.isBound && (
                                <div className="absolute top-1.5 right-1.5">
                                  <div className="flex items-center space-x-1 px-1.5 py-0.5 bg-green-100 rounded-full">
                                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                    <span className="text-xs font-medium text-green-700">
                                      已绑定
                                    </span>
                                  </div>
                                </div>
                              )}

                              {/* 排除状态指示器 */}
                              {audience.isExcluded && (
                                <div className="absolute top-1.5 right-1.5">
                                  <div className="flex items-center space-x-1 px-1.5 py-0.5 bg-red-100 rounded-full">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                    <span className="text-xs font-medium text-red-700">
                                      已排除
                                    </span>
                                  </div>
                                </div>
                              )}

                              {/* 人群包内容 */}
                              <div
                                className={`${
                                  audience.isBound || audience.isExcluded
                                    ? "pr-16"
                                    : ""
                                } ${audience.isCustomMode ? "pl-6" : ""}`}
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                                    <h4
                                      className={`text-sm font-medium truncate ${
                                        audience.isBound
                                          ? "text-green-800"
                                          : audience.isExcluded
                                          ? "text-red-800"
                                          : "text-gray-900"
                                      }`}
                                    >
                                      {audience.name || "未命名人群包"}
                                    </h4>
                                    {renderAudienceTag(audience)}
                                    {/* 选择模式指示 */}
                                    {audience.isCustomMode &&
                                      audience.isSelected &&
                                      activeTab === "target" && (
                                        <span className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800 flex-shrink-0">
                                          已选择
                                        </span>
                                      )}
                                    {audience.isCustomMode &&
                                      audience.isSelectedExclude &&
                                      activeTab === "exclude" && (
                                        <span className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800 flex-shrink-0">
                                          已选择排除
                                        </span>
                                      )}
                                    {audience.isInheritMode &&
                                      audience.isBound && (
                                        <span className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-purple-100 text-purple-800 flex-shrink-0">
                                          将继承
                                        </span>
                                      )}
                                    {audience.isInheritMode &&
                                      audience.isExcluded && (
                                        <span className="inline-block px-1.5 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800 flex-shrink-0">
                                          将排除
                                        </span>
                                      )}
                                  </div>
                                  <div className="flex items-center space-x-2 flex-shrink-0">
                                    {dataLoaded &&
                                      (retargetingTags.length > 0 ||
                                        retargetingTagsExclude.length > 0) &&
                                      renderBindingStatus(audience)}
                                  </div>
                                </div>

                                <div className="grid grid-cols-4 gap-2 text-xs">
                                  <div
                                    className={`p-1.5 rounded ${
                                      audience.isBound
                                        ? "bg-green-100 text-green-700"
                                        : audience.isExcluded
                                        ? "bg-red-100 text-red-700"
                                        : "bg-gray-50 text-gray-600"
                                    }`}
                                  >
                                    <div className="font-medium text-xs">
                                      ID
                                    </div>
                                    <div className="font-mono text-xs truncate">
                                      {audience.id || "未知"}
                                    </div>
                                  </div>
                                  <div
                                    className={`p-1.5 rounded ${
                                      audience.isBound
                                        ? "bg-green-100 text-green-700"
                                        : audience.isExcluded
                                        ? "bg-red-100 text-red-700"
                                        : "bg-gray-50 text-gray-600"
                                    }`}
                                  >
                                    <div className="font-medium text-xs">
                                      覆盖人数
                                    </div>
                                    <div className="text-xs">
                                      {audience.cover_num || "0"}
                                    </div>
                                  </div>
                                  <div
                                    className={`p-1.5 rounded ${
                                      audience.isBound
                                        ? "bg-green-100 text-green-700"
                                        : audience.isExcluded
                                        ? "bg-red-100 text-red-700"
                                        : "bg-gray-50 text-gray-600"
                                    }`}
                                  >
                                    <div className="font-medium text-xs">
                                      状态
                                    </div>
                                    <div className="text-xs">
                                      {renderAudienceStatus(audience)}
                                    </div>
                                  </div>
                                  <div
                                    className={`p-1.5 rounded ${
                                      audience.isBound
                                        ? "bg-green-100 text-green-700"
                                        : audience.isExcluded
                                        ? "bg-red-100 text-red-700"
                                        : "bg-gray-50 text-gray-600"
                                    }`}
                                  >
                                    <div className="font-medium text-xs">
                                      创建时间
                                    </div>
                                    <div className="text-xs truncate">
                                      {audience.create_time || "未知"}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* 绑定状态装饰 */}
                              {audience.isBound && (
                                <div className="absolute inset-0 pointer-events-none">
                                  <div className="absolute top-0 left-0 w-0 h-0 border-l-6 border-t-6 border-transparent border-l-green-400 border-t-green-400"></div>
                                  <div className="absolute bottom-0 right-0 w-0 h-0 border-r-6 border-b-6 border-transparent border-r-green-400 border-b-green-400"></div>
                                </div>
                              )}

                              {/* 排除状态装饰 */}
                              {audience.isExcluded && (
                                <div className="absolute inset-0 pointer-events-none">
                                  <div className="absolute top-0 left-0 w-0 h-0 border-l-6 border-t-6 border-transparent border-l-red-400 border-t-red-400"></div>
                                  <div className="absolute bottom-0 right-0 w-0 h-0 border-r-6 border-b-6 border-transparent border-r-red-400 border-b-red-400"></div>
                                </div>
                              )}
                            </div>
                          );
                        })
                    )}

                    {/* 排除人群包标签页为空时的提示 */}
                    {activeTab === "exclude" &&
                      audienceSelectionMode !== "custom" &&
                      customAudiences.filter(
                        (audience) =>
                          dataLoaded &&
                          retargetingTagsExclude.length > 0 &&
                          isAudienceExcluded(audience)
                      ).length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <div className="text-sm font-medium mb-2">
                            {audienceSelectionMode === "none"
                              ? "已选择不包含人群包模式"
                              : "暂无被排除的人群包"}
                          </div>
                          <div className="text-xs">
                            {audienceSelectionMode === "none"
                              ? "当前选择不包含任何人群包，新项目将不会设置排除人群包"
                              : "当前项目没有设置排除人群包，或所有排除的人群包都不在可用列表中"}
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              )}
          </div>

          {/* 新项目名称输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              新项目名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              placeholder="请输入新项目名称"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              maxLength={100}
              disabled={copying}
            />
            <div className="text-xs text-gray-500 mt-1">最多100个字符</div>
          </div>

          {/* 复制数量 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              复制数量
            </label>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={decreaseCopyCount}
                disabled={copyCount <= 1 || copying}
                className="p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Minus size={16} />
              </button>
              <input
                type="number"
                min="1"
                max="10"
                value={copyCount}
                onChange={(e) =>
                  handleCopyCountChange(parseInt(e.target.value) || 1)
                }
                className="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={copying}
              />
              <button
                type="button"
                onClick={increaseCopyCount}
                disabled={copyCount >= 10 || copying}
                className="p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus size={16} />
              </button>
              <span className="text-sm text-gray-600">个（最多10个）</span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              将创建 {copyCount} 个相同的项目，每个项目名称会自动添加序号
            </div>
          </div>

          {/* 复制广告选项 */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <PlayCircle size={18} className="text-orange-600" />
                <span className="text-sm font-medium text-orange-800">
                  粘贴广告
                </span>
                {loadingPromotions && (
                  <div className="w-4 h-4 border-2 border-orange-600 border-t-transparent rounded-full animate-spin"></div>
                )}
              </div>
              <div className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                {loadingPromotions
                  ? "加载中..."
                  : `${promotions.length} 个广告`}
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={copyPromotions}
                  onChange={(e) => setCopyPromotions(e.target.checked)}
                  disabled={copying || loadingPromotions}
                  className="mt-1 w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    同时粘贴当前项目下的广告
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {promotions.length === 0
                      ? "当前项目下暂无广告"
                      : loadingPromotions
                      ? "正在加载广告列表..."
                      : `将把当前项目下的 ${promotions.length} 个广告粘贴到所有新项目中`}
                  </div>
                </div>
              </label>

              {copyPromotions && promotions.length > 0 && (
                <div className="bg-white border border-orange-200 rounded p-3 space-y-2">
                  <div className="text-xs font-medium text-gray-700 mb-2">
                    将要粘贴的广告列表：
                  </div>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {promotions.slice(0, 5).map((promotion, index) => (
                      <div
                        key={promotion.promotion_id || index}
                        className="flex items-center justify-between text-xs"
                      >
                        <span className="text-gray-700 truncate flex-1">
                          {promotion.promotion_name || "未命名广告"}
                        </span>
                        <span className="text-gray-500 font-mono ml-2">
                          ID: {promotion.promotion_id}
                        </span>
                      </div>
                    ))}
                    {promotions.length > 5 && (
                      <div className="text-xs text-gray-500 text-center py-1">
                        还有 {promotions.length - 5} 个广告...
                      </div>
                    )}
                  </div>

                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <div className="text-xs text-orange-700 bg-orange-50 px-2 py-1 rounded">
                      📊 粘贴统计：{promotions.length} 个广告 × {copyCount}{" "}
                      个项目 = {promotions.length * copyCount} 个粘贴任务
                    </div>
                  </div>
                </div>
              )}

              {/* 广告复制进度显示 */}
              {promotionStep === "copying" && (
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm font-medium text-blue-800">
                      正在粘贴广告...
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="text-xs text-blue-700">
                      当前项目：{promotionProgress.projectName} (
                      {promotionProgress.projectIndex}/{copiedProjects.length})
                    </div>

                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${
                            promotionProgress.total > 0
                              ? (promotionProgress.current /
                                  promotionProgress.total) *
                                100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>

                    <div className="text-xs text-blue-600 text-center">
                      {promotionProgress.current} / {promotionProgress.total}{" "}
                      个广告已粘贴
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 提示信息 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-1">复制说明：</div>
              <ul className="text-xs space-y-1">
                <li>• 将复制项目的所有配置信息，包括人群包绑定设置</li>
                <li>• 新项目将保持原有的广告策略和受众定向配置</li>
                <li>• 复制后的项目需要重新启用才能开始投放</li>
                <li>
                  •
                  批量复制时会自动为每个项目添加序号（如：项目名_复制_01、项目名_复制_02）
                </li>
                <li>
                  • <strong>人群包配置方式：</strong>
                </li>
                <li className="ml-2">
                  - 继承原项目人群包：保持原项目的人群包配置
                </li>
                <li className="ml-2">- 自定义选择：手动选择要包含的人群包</li>
                <li className="ml-2">- 不包含人群包：新项目不包含任何人群包</li>
                <li>
                  • <strong>人群包状态说明：</strong>
                </li>
                <li className="ml-2">
                  - 🟢 绿色标记：已绑定的人群包（定向包含）
                </li>
                <li className="ml-2">
                  - 🔴 红色标记：已排除的人群包（定向排除）
                </li>
                <li className="ml-2">- ⚪ 灰色标记：未设置的人群包</li>
                <li>
                  • <strong>重要：</strong>人群包只能被绑定或排除，两者互斥关系
                </li>
                <li>• 复制后可根据需要调整人群包配置</li>
                {copyPromotions && promotions.length > 0 && (
                  <>
                    <li className="mt-2">
                      •{" "}
                      <strong className="text-orange-600">
                        广告粘贴说明：
                      </strong>
                    </li>
                    <li className="ml-2">
                      - 已选择粘贴当前项目下的 {promotions.length} 个广告
                    </li>
                    <li className="ml-2">
                      - 每个广告都会粘贴到所有新创建的项目中
                    </li>
                    <li className="ml-2">
                      - 粘贴的广告名称会自动添加时间戳标识
                    </li>
                    <li className="ml-2">
                      - 项目复制完成后会继续执行广告粘贴，请耐心等待
                    </li>
                    <li className="ml-2 text-orange-700">
                      - 总计将创建 {promotions.length * copyCount} 个广告副本
                    </li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* 按钮 */}
        <div className="flex justify-end space-x-3 p-6 pt-0 border-t border-gray-200">
          <button
            onClick={handleCancel}
            disabled={copying || promotionStep === "copying"}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            disabled={copying || !newProjectName.trim() || dataLoadFailed}
            className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {copying && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            <span>
              {copying
                ? promotionStep === "copying"
                  ? "正在粘贴广告..."
                  : `复制项目中... (${copyCount}个)`
                : dataLoadFailed
                ? "数据加载失败，无法复制"
                : copyPromotions && promotions.length > 0
                ? `确认复制 (${copyCount}个项目 + ${
                    promotions.length * copyCount
                  }个广告)`
                : `确认复制 (${copyCount}个项目)`}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CopyProjectDialog;
