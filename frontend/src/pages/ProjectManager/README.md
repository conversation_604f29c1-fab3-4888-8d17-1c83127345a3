# ProjectManager 项目管理页面

## 概述

ProjectManager 是一个现代化的项目管理页面，提供项目列表查看、搜索、编辑、删除、复制等功能。采用模块化架构设计，具有良好的可维护性和扩展性。

## 功能特性

- ✅ 项目列表展示（支持分页）
- ✅ 实时搜索项目名称
- ✅ 项目状态管理（启用/禁用切换）
- ✅ 在线编辑项目名称
- ✅ 单个/批量删除项目
- ✅ 账户选择器集成
- ✅ 子账户筛选支持
- ✅ 现代化UI设计（渐变背景、毛玻璃效果）
- ✅ 响应式布局
- ✅ 复制项目功能

## 目录结构

```
ProjectManager/
├── components/           # 组件模块
│   ├── TableColumns.jsx  # 表格列配置
│   ├── CopyProjectDialog.jsx  # 复制项目确认对话框
│   └── index.js          # 组件导出
├── constants/            # 常量定义
│   └── index.js          # 状态常量、UI配置等
├── hooks/                # 自定义hooks
│   ├── useProjectManager.js  # 主业务逻辑hook
│   └── index.js          # hooks导出
├── utils/                # 工具函数
│   └── index.js          # 格式化、计算等工具函数
├── ProjectManager.jsx    # 主组件
├── index.js             # 入口文件
└── README.md            # 文档
```

## 组件架构

### 1. 主组件 (ProjectManager.jsx)
- 负责整体布局和UI渲染
- 集成各个子模块
- 处理用户交互和事件分发

### 2. 业务逻辑 (hooks/useProjectManager.js)
- 状态管理（数据、分页、选择等）
- API调用（直接使用bindings）
- 业务逻辑处理（搜索、编辑、删除等）

### 3. 表格配置 (components/TableColumns.jsx)
- 动态表格列定义
- 单元格渲染逻辑
- 交互处理（编辑、删除等）

### 4. 常量定义 (constants/index.js)
- 项目状态枚举
- UI颜色配置
- 表格列宽度设置

### 5. 工具函数 (utils/index.js)
- 数据格式化（时间、金额、百分比）
- 状态判断和转换
- 通用计算函数

## 使用方法

### 基础使用

```jsx
import ProjectManager from './pages/ProjectManager';

function App() {
  return (
    <ProjectManager
      onProjectSelect={(project) => {
        // 处理项目选择
        console.log('选中项目:', project);
      }}
    />
  );
}
```

### 带子账户筛选

```jsx
import ProjectManager from './pages/ProjectManager';

function App() {
  const preSelectedSubAccount = {
    advertiser_id: '123456',
    advertiser_name: '测试广告主',
    account_id: '789'
  };

  return (
    <ProjectManager
      preSelectedSubAccount={preSelectedSubAccount}
      onSubAccountSelect={(subAccount) => {
        // 处理子账户选择变化
      }}
      onProjectSelect={(project) => {
        // 处理项目选择
      }}
    />
  );
}
```

## API集成

### 直接使用Bindings

组件直接使用 `@services/projectservice.js` bindings，避免中间层封装：

```javascript
import * as ProjectService from '@services/projectservice.js';

// 获取项目列表
const result = await ProjectService.GetProjectListAsync(params);

// 删除项目
const result = await ProjectService.DeleteProjectAsync(req);

// 更新项目状态
const result = await ProjectService.UpdateProjectStatus(req);

// 修改项目名称
const result = await ProjectService.UpdateProjectName(req);
```

### 请求参数格式

```javascript
// 获取项目列表
const listParams = {
  account_id: 123,           // 主账户ID
  keyword: '456',            // 关键词（通常是子账户ID）
  project_name: '项目名',     // 项目名称搜索
  project_id: '',            // 项目ID筛选
  promotion_id: '',          // 广告ID筛选
  page: 1,                   // 页码
  page_size: 10             // 每页大小
};

// 删除项目
const deleteParams = {
  account_id: 123,                    // 主账户ID
  advertiser_id: "456",              // 广告主ID
  project_ids: ["789", "101112"]     // 项目ID数组
};
```

## 状态管理

### 项目状态类型

```javascript
// 项目基础状态
PROJECT_STATUS = {
  ACTIVE: 1,      // 投放中
  INACTIVE: 0,    // 未投放
  SUSPENDED: 2,   // 已暂停
  DELETED: 3      // 已删除
}

// 启用状态
PROJECT_ENABLE_STATUS = {
  ENABLED: 1,     // 已启用
  DISABLED: 0     // 已禁用
}
```

### 状态颜色配置

```javascript
STATUS_COLORS = {
  1: 'text-emerald-600 bg-emerald-50 border-emerald-200',  // 投放中
  0: 'text-gray-600 bg-gray-50 border-gray-200',          // 未投放
  2: 'text-amber-600 bg-amber-50 border-amber-200',       // 已暂停
  3: 'text-red-600 bg-red-50 border-red-200'             // 已删除
}
```

## 样式设计

### 现代化UI特性

- **渐变背景**: `bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20`
- **毛玻璃效果**: `backdrop-blur-sm bg-white/80`
- **圆角设计**: `rounded-xl` 大圆角设计
- **阴影层次**: `shadow-sm` 轻微阴影
- **交互反馈**: hover状态和过渡动画

### 响应式设计

- 自适应表格布局
- 灵活的工具栏布局
- 移动端友好的交互设计

## 性能优化

1. **防抖搜索**: 搜索输入100ms防抖
2. **参数缓存**: 避免重复相同参数的API调用
3. **状态优化**: 合理的state更新策略
4. **组件记忆**: 使用useCallback优化函数引用

## 开发规范

### 代码组织
- 单一职责原则：每个文件专注特定功能
- 模块化设计：便于测试和维护
- 类型安全：充分的参数验证

### 命名规范
- 组件：PascalCase (ProjectManager)
- 函数：camelCase (handleSearch)
- 常量：UPPER_SNAKE_CASE (PROJECT_STATUS)
- 文件：kebab-case (table-columns.jsx)

### 注释规范
- JSDoc格式的函数注释
- 重要业务逻辑的行内注释
- 组件和模块的顶部说明注释

## 扩展指南

### 添加新的表格列

1. 在 `constants/index.js` 中添加列宽配置
2. 在 `utils/index.js` 中添加格式化函数（如需要）
3. 在 `components/TableColumns.jsx` 中添加列定义

### 添加新的操作功能

1. 在 `hooks/useProjectManager.js` 中添加业务逻辑
2. 在 `components/TableColumns.jsx` 中添加操作按钮
3. 在主组件中集成新功能

### 自定义样式主题

修改 `constants/index.js` 中的颜色配置：

```javascript
STATUS_COLORS = {
  1: 'text-green-600 bg-green-50 border-green-200',  // 自定义成功色
  // ... 其他状态
}
```

## 维护指南

- 定期更新依赖版本
- 关注性能监控指标
- 及时修复用户反馈的问题
- 保持代码风格一致性

## 复制项目功能

### 功能特点
- **确认对话框**：复制前弹出确认对话框，避免误操作
- **项目信息预览**：显示源项目的详细信息（名称、ID、广告主ID、状态）
- **自定义名称**：支持输入新项目名称，自动生成带时间戳的默认名称
- **复制说明**：提供复制操作的详细说明和注意事项
- **加载状态**：复制过程中显示加载动画，防止重复操作

### 使用方式
1. 在项目列表中右键点击任意项目
2. 选择"复制项目"菜单项
3. 在弹出的对话框中查看源项目信息
4. 输入新项目名称（可选，系统会自动生成）
5. 点击"确认复制"按钮执行复制操作

### 复制说明
- 将复制项目的所有配置信息
- 新项目将保持原有的广告策略设置
- 复制后的项目需要重新启用

## 更新日志

### v1.1.0 (2024-01-XX)
- ✨ 新增复制项目确认对话框功能
- 🎨 优化复制项目用户体验
- 📝 添加复制操作说明和提示
- 🔧 改进错误处理和加载状态

### v1.0.0
- 🎉 初始版本发布
- 📋 基础项目管理功能
- 🔍 搜索和筛选功能
- 📊 数据表格和虚拟滚动 