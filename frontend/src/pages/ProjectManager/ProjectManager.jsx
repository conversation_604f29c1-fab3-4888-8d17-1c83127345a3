import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import {
  RefreshCw,
  Trash2,
  Download,
  ToggleLeft,
  ToggleRight,
  Edit,
  Copy,
  Search,
  Filter,
  X,
  CheckSquare,
  Square,
  Eye,
  EyeOff,
  Target,
  DollarSign,
} from "lucide-react";
import {
  Button,
  Card,
  Input,
  SearchInput,
  Toast,
  Pagination,
  AccountDialog,
  LoginDialog,
  ConfirmDialog,
  SearchDialog,
  VirtualDataTable,
  EmptyState,
  FilterDialog,
  StatusBar,
  MobileStatsBar,
  HighlightText,
  FloatingSearchBox,
  StrategyBindDialog,
} from "../../components/ui";
import { toast } from "../../components/ui/Toast";
import { useProjectManager } from "./hooks";
import {
  createTableColumns,
  CopyProjectDialog,
  UpdateBidDialog,
} from "./components";
import { INITIAL_CONFIRM_DIALOG, DELIVERY_MODE } from "./constants";
import { useManagerPage } from "../../hooks/useManagerPage";
import ProjectService from "../../services/projectservice";
import { useCopyProjectDetail } from "../../contexts/CopyProjectContext";
import strategyBindingService from "../../services/strategyBindingService";

/**
 * ProjectManager 项目管理页面
 * 支持项目列表查看、搜索、编辑、删除等功能
 */

/**
 * 状态筛选选项
 */
const STATUS_FILTER_OPTIONS = [
  { value: "all", label: "全部状态" },
  { value: "投放中", label: "投放中" },
  { value: "已暂停", label: "已暂停" },
  { value: "待审核", label: "待审核" },
  { value: "审核通过", label: "审核通过" },
  { value: "审核不通过", label: "审核不通过" },
  { value: "已完成", label: "已完成" },
  { value: "待激活", label: "待激活" },
];

// 移除重复的对话框组件定义，使用通用组件

function ProjectManager({
  preSelectedAccount,
  preSelectedSubAccount,
  onSubAccountSelect,
  onProjectSelect,
}) {
  // 添加缺失的状态变量
  const [selectedProjectForStrategy, setSelectedProjectForStrategy] =
    useState(null);
  const [showStrategyDialog, setShowStrategyDialog] = useState(false);
  const [selectedProjectIds, setSelectedProjectIds] = useState([]);
  const [currentStrategyIds, setCurrentStrategyIds] = useState("");
  const [currentProjectName, setCurrentProjectName] = useState("");
  const hasRestoredScroll = useRef(false);

  // 复制项目对话框状态
  const [copyProjectDialog, setCopyProjectDialog] = useState({
    isOpen: false,
    project: null,
    projectDetail: null,
  });

  // 修改出价对话框状态
  const [updateBidDialog, setUpdateBidDialog] = useState({
    isOpen: false,
    projects: [],
  });
  const [bidUpdateLoading, setBidUpdateLoading] = useState(false);

  const {
    // 状态
    loading,
    syncLoading,
    data,
    pagination,
    selectedItems,
    selectedAccount,
    selectedSubAccount,
    editingProjectId,
    editingProjectName,
    setEditingProjectName,
    confirmDialog,
    sortConfig,

    // 操作函数
    loadData,
    deleteProject,
    batchDelete,
    updateStatus,
    refreshProjects,
    copyProject,
    toggleSelectItem,
    toggleSelectAll,
    handleAccountChange,
    handleEditProjectName,
    handleCancelEditProjectName,
    handleSaveProjectName,
    handleEditInputKeyDown,
    handleSort,
    getSortedData,
    toggleShowDeleted,

    // 对话框控制
    setConfirmDialog,
  } = useProjectManager(preSelectedSubAccount, onSubAccountSelect);

  // 使用通用管理页面Hook - 纯前端筛选
  const {
    searchTerm,
    statusFilter,
    showDeleted,
    filteredData,
    showSearchDialog,
    showFilterDialog,
    scrollContainerRef,
    statusBarFilters,
    handleSearch,
    handleStatusFilterChange,
    handleClearAllFilters: originalHandleClearAllFilters,
    handleShowDeleted,
    handleShowSearch,
    handleCloseSearch,
    handleShowFilter,
    handleCloseFilter,
    handleScroll,
    saveScrollPosition,
    restoreScrollPosition,
    generateContextMenuItems,
  } = useManagerPage(
    {
      data,
      onSearch: null, // 不触发服务器端搜索，纯前端筛选
      searchFields: ["project_name", "project_id", "advertiser_id"],
      statusField: "project_status_name",
      filterOptions: STATUS_FILTER_OPTIONS,
      itemLabel: "个项目",
      pageKey: "project",
      onClearSelection: () => {
        // 清空选择逻辑
        if (selectedItems.length > 0) {
          toggleSelectAll();
        }
      },
      onToggleSelectAll: toggleSelectAll,
      onBatchDeleteConfirm: () => {
        if (selectedItems.length === 0) return;
        setConfirmDialog({
          isOpen: true,
          title: "批量删除",
          message: `确定要删除选中的 ${selectedItems.length} 个项目吗？此操作无法撤销。`,
          onConfirm: () => {
            batchDelete();
            setConfirmDialog(INITIAL_CONFIRM_DIALOG);
          },
        });
      },
    },
    [selectedItems, toggleSelectAll]
  );

  // 重写清除所有筛选函数，包含子账户筛选清除
  const handleClearAllFilters = useCallback(() => {
    // 调用原始的清除函数（清除搜索和状态筛选）
    originalHandleClearAllFilters();
    // 清除子账户筛选
    if (onSubAccountSelect) {
      onSubAccountSelect(null);
    }
  }, [originalHandleClearAllFilters, onSubAccountSelect]);

  // 获取排序后的数据
  const sortedData = useMemo(() => {
    // 确保 filteredData 不为 undefined
    const safeFilteredData = filteredData || [];
    return getSortedData(safeFilteredData);
  }, [filteredData, getSortedData]);

  // 计算搜索匹配数量
  const searchMatchCount = useMemo(() => {
    if (!searchTerm || !searchTerm.trim()) {
      return data.length;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    const searchFields = ["project_name", "project_id", "advertiser_id"];

    return data.filter((item) => {
      return searchFields.some((field) => {
        const value = item[field];
        if (!value) return false;
        return value.toString().toLowerCase().includes(searchLower);
      });
    }).length;
  }, [data, searchTerm]);

  // 策略选择处理
  const handleChangeStrategy = useCallback((project) => {
    setSelectedProjectForStrategy(project);
    setShowStrategyDialog(true);
  }, []);

  const handleStrategyDialogClose = useCallback(() => {
    setShowStrategyDialog(false);
    setSelectedProjectForStrategy(null);
  }, []);

  // 获取复制项目Context的方法
  const {
    copyProject: copyProjectToGlobal,
    pasteProject: pasteProjectFromGlobal,
    hasCopiedProject,
    clearCopiedProject,
  } = useCopyProjectDetail();

  // 处理复制按钮点击（存储项目信息到全局key）
  const handleCopyProject = async (project) => {
    try {
      console.log("开始复制项目:", project);

      // 获取项目详情
      const accountId = parseInt(project.account_id);
      const advertiserId = parseInt(project.advertiser_id);
      const projectId = project.project_id;

      console.log("获取项目详情用于复制，参数:", {
        accountId,
        advertiserId,
        projectId,
      });

      console.log("开始调用 ProjectService.getProjectDetail...");
      const response = await ProjectService.getProjectDetail(
        accountId,
        advertiserId,
        projectId
      );
      console.log("ProjectService.getProjectDetail 调用完成");
      console.log("项目详情返回数据:", response);

      // 解析项目详情数据
      let projectData = null;
      console.log("开始解析项目详情数据...");

      if (
        response &&
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        console.log("使用 response.data[0] 格式");
        projectData = response.data[0];
      } else if (response && Array.isArray(response) && response.length > 0) {
        console.log("使用 response[0] 格式");
        projectData = response[0];
      } else if (response && response.data && !Array.isArray(response.data)) {
        console.log("使用 response.data 格式");
        projectData = response.data;
      } else {
        console.log("无法解析数据格式，response:", response);
      }

      console.log("解析后的 projectData:", projectData);

      if (projectData) {
        // 存储项目信息到全局key
        console.log("准备存储项目到全局key...");
        copyProjectToGlobal(projectData, project);
        console.log("已存储项目到全局key:", { projectData, project });
        toast.success(
          `已复制项目"${project.project_name}"，可在右键菜单中粘贴`
        );
      } else {
        console.error("projectData 为空，无法复制");
        toast.error("获取项目详情失败，无法复制");
      }
    } catch (error) {
      console.error("获取项目详情失败:", error);
      console.error("错误详情:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      toast.error(`获取项目详情失败: ${error.message}`);
    }
  };

  // 处理复制项目确认
  const handleCopyProjectConfirm = async (copyParams) => {
    console.log("copy", copyParams);
    const project = copyProjectDialog.project;
    if (!project) {
      throw new Error("项目信息不存在");
    }

    try {
      // 构建复制参数，包含人群包配置和项目详情数据
      const copyRequest = {
        project,
        newProjectName: copyParams.newProjectName,
        copyCount: copyParams.copyCount || 1, // 新增：复制数量
        audienceSelectionMode: copyParams.audienceSelectionMode,
        selectedAudienceIds: copyParams.selectedAudienceIds,
        selectedExcludeAudienceIds: copyParams.selectedExcludeAudienceIds,
        inheritOriginalAudiences: copyParams.inheritOriginalAudiences,
        projectDetail: copyParams.projectDetail, // 传递项目详情数据
      };

      // 调用复制项目方法并获取结果
      const result = await copyProject(copyRequest);

      // 不再清空全局key中的项目信息，保持项目信息以便继续复制
      // clearCopiedProject();
      // toast.success("复制完成，已清空复制的项目信息");

      toast.success("复制完成！项目信息已保留，可继续粘贴到其他位置");

      // 返回复制结果，以便 CopyProjectDialog 能够获取新项目信息
      return result;
    } catch (error) {
      console.error("复制项目失败:", error);
      throw error;
    }
  };

  // 处理粘贴项目（从全局key获取项目信息并打开复制对话框）
  const handlePasteProject = () => {
    const { projectDetail, projectInfo } = pasteProjectFromGlobal();
    if (projectDetail && projectInfo) {
      setCopyProjectDialog({
        isOpen: true,
        project: projectInfo,
        projectDetail: projectDetail,
      });
    } else {
      toast.error("没有可粘贴的项目信息");
    }
  };

  // 处理修改出价确认
  const handleUpdateBidConfirm = async (bidData) => {
    try {
      setBidUpdateLoading(true);

      // 获取项目信息以构建请求参数
      const firstProject = updateBidDialog.projects[0];
      if (!firstProject) {
        throw new Error("没有选中的项目");
      }

      // 调用批量修改出价接口
      const result = await ProjectService.BatchUpdateProjectBid({
        account_id: parseInt(firstProject.account_id),
        advertiser_id: parseInt(firstProject.advertiser_id),
        promotion_bid_map: bidData.promotionBidMap,
        is_async: false, // 同步执行
      });

      if (result.code === 0) {
        toast.success(
          `成功修改 ${updateBidDialog.projects.length} 个项目的出价`
        );
        setUpdateBidDialog({ isOpen: false, projects: [] });
        // 刷新数据
        loadData();
      } else {
        throw new Error(result.msg || "修改出价失败");
      }
    } catch (error) {
      console.error("修改出价失败:", error);
      toast.error(`修改出价失败: ${error.message}`);
    } finally {
      setBidUpdateLoading(false);
    }
  };

  // 打开修改出价对话框
  const handleShowUpdateBid = () => {
    const selectedProjects = data.filter((item) =>
      selectedItems.includes(item.project_id)
    );

    if (selectedProjects.length === 0) {
      toast.warning("请先选择要修改出价的项目");
      return;
    }

    setUpdateBidDialog({
      isOpen: true,
      projects: selectedProjects,
    });
  };

  // 绑定策略回调
  const handleStrategyConfirm = async (strategyIdsStr) => {
    try {
      // 如果策略ID为空，说明用户要清空所有策略绑定
      if (!strategyIdsStr || strategyIdsStr.trim() === "") {
        // 如果选择了多个项目，批量解绑
        if (selectedProjectIds.length > 1) {
          // 为每个项目单独调用解绑API
          let successCount = 0;
          let errorCount = 0;

          for (const projectId of selectedProjectIds) {
            try {
              const result = await ProjectService.unbindStrategyFromEntity({
                strategy_ids: [], // 空数组表示解绑所有策略
                binding_type: "project",
                binding_id: projectId.toString(),
              });

              if (result.code === 0) {
                successCount++;
              } else {
                errorCount++;
                console.error(
                  `项目 ${projectId} 清空策略绑定失败:`,
                  result.msg
                );
              }
            } catch (error) {
              errorCount++;
              console.error(`项目 ${projectId} 清空策略绑定异常:`, error);
            }
          }

          setShowStrategyDialog(false);
          loadData(); // 刷新数据

          if (errorCount === 0) {
            toast.success(`已清空 ${successCount} 个项目的策略绑定`);
          } else if (successCount > 0) {
            toast.warning(
              `成功清空 ${successCount} 个项目的策略绑定，${errorCount} 个失败`
            );
          } else {
            toast.error(`清空策略绑定失败，共 ${errorCount} 个错误`);
          }
        } else {
          // 单个项目解绑
          const result = await ProjectService.unbindStrategyFromEntity({
            strategy_ids: [], // 空数组表示解绑所有策略
            binding_type: "project",
            binding_id: selectedProjectIds[0].toString(),
          });

          if (result.code === 0) {
            setShowStrategyDialog(false);
            loadData(); // 刷新数据
            toast.success("已清空策略绑定");
          } else {
            console.error("清空策略绑定失败:", result.msg);
            toast.error("清空策略绑定失败: " + result.msg);
          }
        }
        return;
      }

      // 处理单个项目的策略更新（包括部分删除和添加）
      if (selectedProjectIds.length === 1) {
        const projectId = selectedProjectIds[0].toString();
        const newStrategyIds = strategyIdsStr
          .split(",")
          .map((id) => Number(id))
          .filter((id) => id > 0);

        // 获取当前绑定的策略
        const currentResult =
          await strategyBindingService.getStrategiesByBinding(
            "project",
            projectId
          );
        const currentStrategyIds = [];

        if (
          currentResult.code === 0 &&
          currentResult.data &&
          currentResult.data.strategies
        ) {
          currentStrategyIds.push(
            ...currentResult.data.strategies.map((s) => s.id)
          );
        }

        console.log("策略更新对比:", {
          current: currentStrategyIds,
          new: newStrategyIds,
        });

        // 计算需要删除的策略
        const strategiesToRemove = currentStrategyIds.filter(
          (id) => !newStrategyIds.includes(id)
        );
        // 计算需要添加的策略
        const strategiesToAdd = newStrategyIds.filter(
          (id) => !currentStrategyIds.includes(id)
        );

        console.log("策略变更:", {
          toRemove: strategiesToRemove,
          toAdd: strategiesToAdd,
        });

        // 先删除需要移除的策略
        if (strategiesToRemove.length > 0) {
          const removeResult = await ProjectService.unbindStrategyFromEntity({
            strategy_ids: strategiesToRemove,
            binding_type: "project",
            binding_id: projectId,
          });

          if (removeResult.code !== 0) {
            console.error("删除策略失败:", removeResult.msg);
            toast.error("删除策略失败: " + removeResult.msg);
            return;
          }
        }

        // 再添加新的策略
        if (strategiesToAdd.length > 0) {
          const addResult = await ProjectService.bindStrategyToEntity({
            strategy_ids: strategiesToAdd,
            binding_type: "project",
            binding_id: projectId,
            binding_name: currentProjectName,
          });

          if (addResult.code !== 0) {
            console.error("添加策略失败:", addResult.msg);
            toast.error("添加策略失败: " + addResult.msg);
            return;
          }
        }

        setShowStrategyDialog(false);
        loadData(); // 刷新数据

        // 显示操作结果
        let message = "";
        if (strategiesToRemove.length > 0 && strategiesToAdd.length > 0) {
          message = `已删除 ${strategiesToRemove.length} 个策略，添加 ${strategiesToAdd.length} 个策略`;
        } else if (strategiesToRemove.length > 0) {
          message = `已删除 ${strategiesToRemove.length} 个策略`;
        } else if (strategiesToAdd.length > 0) {
          message = `已添加 ${strategiesToAdd.length} 个策略`;
        } else {
          message = "策略绑定无变化";
        }

        toast.success(message);
        return;
      }

      // 如果选择了多个项目，使用批量绑定（完全重新绑定）
      if (selectedProjectIds.length > 1) {
        const entities = selectedProjectIds.map((id) => {
          const project = data.find((item) => item.project_id === id);
          return {
            binding_id: id.toString(),
            binding_name: project ? project.project_name : `项目#${id}`,
          };
        });

        const result = await ProjectService.batchBindStrategyToEntities({
          strategy_ids: strategyIdsStr.split(",").map((id) => Number(id)),
          binding_type: "project",
          entities: entities,
          priority: 0,
          description: "",
        });

        if (result.code === 0) {
          setShowStrategyDialog(false);
          loadData(); // 刷新数据
          toast.success(`成功为 ${selectedProjectIds.length} 个项目绑定策略`);
        } else {
          console.error("批量绑定策略失败:", result.msg);
          toast.error("批量绑定策略失败: " + result.msg);
        }
      }
    } catch (error) {
      console.error("绑定策略异常:", error);
      toast.error("绑定策略失败: " + error.message);
    }
  };

  // 恢复滚动位置
  useEffect(() => {
    if (
      !hasRestoredScroll.current &&
      filteredData.length > 0 &&
      scrollContainerRef.current
    ) {
      const scrollTop = restoreScrollPosition();
      if (scrollTop > 0) {
        setTimeout(() => {
          scrollContainerRef.current.scrollTop = scrollTop;
        }, 100);
      }
      hasRestoredScroll.current = true;
    }
  }, [filteredData, restoreScrollPosition]);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+A 全选功能
      if (event.ctrlKey && event.key === "a") {
        event.preventDefault();
        // 确保当前焦点在表格区域内或没有其他输入框聚焦
        const activeElement = document.activeElement;
        const isInputElement =
          activeElement &&
          (activeElement.tagName === "INPUT" ||
            activeElement.tagName === "TEXTAREA" ||
            activeElement.isContentEditable);

        // 如果当前没有输入框聚焦，且有数据可选择，则执行全选
        if (!isInputElement && sortedData.length > 0) {
          toggleSelectAll();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [toggleSelectAll, sortedData]);

  // 删除确认
  const handleDeleteConfirm = (project) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认删除",
      message: `确定要删除项目"${project.project_name}"吗？此操作无法撤销。`,
      onConfirm: () => {
        deleteProject(project);
        setConfirmDialog(INITIAL_CONFIRM_DIALOG);
      },
    });
  };

  // 批量删除确认
  const handleBatchDeleteConfirm = () => {
    if (selectedItems.length === 0) return;

    setConfirmDialog({
      isOpen: true,
      title: "批量删除",
      message: `确定要删除选中的 ${selectedItems.length} 个项目吗？此操作无法撤销。`,
      onConfirm: () => {
        batchDelete();
        setConfirmDialog(INITIAL_CONFIRM_DIALOG);
      },
    });
  };

  // 创建表格列配置
  const columns = createTableColumns({
    selectedItems,
    editingProjectId,
    editingProjectName,
    setEditingProjectName,
    pagination,
    onSelectItem: toggleSelectItem,
    onSelectAll: toggleSelectAll,
    onUpdateStatus: updateStatus,
    onDeleteProject: handleDeleteConfirm,
    onProjectSelect,
    onEditProjectName: handleEditProjectName,
    onCancelEditProjectName: handleCancelEditProjectName,
    onSaveProjectName: handleSaveProjectName,
    onEditInputKeyDown: handleEditInputKeyDown,
    data: sortedData,
    showDeleted,
    sortConfig,
    handleSort,
    searchTerm,
  });

  /**
   * 复制到剪贴板的工具函数
   */
  const copyToClipboard = async (text, fieldName = "值") => {
    try {
      await navigator.clipboard.writeText(text);
      console.log(`已复制${fieldName}: ${text}`);
    } catch (err) {
      console.error("复制失败:", err);
      // 降级方案
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand("copy");
        console.log(`已复制${fieldName}(降级): ${text}`);
      } catch (fallbackErr) {
        console.error("降级复制也失败:", fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  // 统一的右键菜单配置（动态生成）
  const unifiedContextMenuItems = (item, cellData) => {
    // 如果是行右键，获取项目状态信息
    let isEnabled = false;
    let toggleLabel = "启用";
    let toggleIcon = <ToggleRight size={14} />;

    if (item) {
      // 根据项目状态二级判断启用状态
      isEnabled = !item.project_status_second?.includes(1); // 1表示禁用
      toggleLabel = isEnabled ? "禁用" : "启用";
      toggleIcon = isEnabled ? (
        <ToggleLeft size={14} />
      ) : (
        <ToggleRight size={14} />
      );
    }

    // 调试信息：检查复制状态
    const hasCopied = hasCopiedProject();
    console.log("右键菜单调试 - 是否有复制的项目:", hasCopied);

    // 准备复制值菜单项
    const copyMenuItems = [];
    if (
      cellData &&
      cellData.fieldValue !== undefined &&
      cellData.fieldValue !== null &&
      cellData.fieldValue !== ""
    ) {
      const copyValue = cellData.fieldValue.toString();
      copyMenuItems.push({
        key: "copyValue",
        label: "复制值",
        icon: <Copy size={14} />,
        color: "#3b82f6",
        onClick: () => copyToClipboard(copyValue, cellData.fieldName),
      });
      copyMenuItems.push({ type: "divider" });
    }

    // 全局复制/粘贴项目相关菜单项
    const globalCopyMenuItems = [];

    // 检查是否有复制的项目，如果有则显示粘贴按钮
    if (hasCopied) {
      const { projectInfo } = pasteProjectFromGlobal();
      console.log("右键菜单调试 - 复制的项目信息:", projectInfo);
      globalCopyMenuItems.push({
        key: "pasteProject",
        label: `粘贴项目 (${projectInfo?.project_name || "未知项目"})`,
        icon: <Copy size={14} />,
        color: "#8b5cf6",
        onClick: () => handlePasteProject(),
      });
      globalCopyMenuItems.push({ type: "divider" });
    }

    const baseMenuItems = [
      // 项目特定操作（仅在右键行时可用）
      ...(item
        ? [
            {
              key: "edit",
              label: "编辑名称",
              icon: <Edit size={14} />,
              color: "#3b82f6",
              onClick: () => handleEditProjectName(item),
            },
            // 复制按钮始终显示（当选中项目时）
            {
              key: "copy",
              label: "复制项目",
              icon: <Copy size={14} />,
              color: "#8b5cf6",
              onClick: () => handleCopyProject(item),
            },
            {
              type: "divider",
            },
            {
              key: "toggleStatus",
              label: toggleLabel,
              icon: toggleIcon,
              color: "#10b981",
              onClick: () => {
                const newStatus = isEnabled ? 1 : 0; // 切换状态
                updateStatus(item, newStatus);
              },
            },
            {
              type: "divider",
            },
            {
              key: "delete",
              label: "删除项目",
              icon: <Trash2 size={14} />,
              color: "#ef4444",
              onClick: () => handleDeleteConfirm(item),
            },
            {
              type: "divider",
            },
          ]
        : []),

      // 全局操作（总是可用）
      {
        key: "search",
        label: "搜索 (Ctrl+F)",
        icon: <Search size={14} />,
        color: "#3b82f6",
        disabled: loading,
        onClick: () => handleShowSearch(),
      },
      {
        key: "filter",
        label: "状态筛选",
        icon: <Filter size={14} />,
        color: "#8b5cf6",
        disabled: loading,
        onClick: () => handleShowFilter(),
      },
      {
        type: "divider",
      },
      {
        key: "refresh",
        label: "刷新数据",
        icon: <RefreshCw size={14} className={loading ? "animate-spin" : ""} />,
        color: "#3b82f6",
        disabled: loading,
        onClick: () => loadData(true),
      },
      {
        key: "sync",
        label: "同步数据",
        icon: (
          <Download size={14} className={syncLoading ? "animate-spin" : ""} />
        ),
        color: "#10b981",
        disabled: syncLoading || !selectedAccount,
        onClick: () => refreshProjects(),
      },
      {
        type: "divider",
      },
      {
        key: "toggleSelectAll",
        label:
          selectedItems.length === sortedData.length
            ? "取消全选 (Ctrl+A)"
            : "全选 (Ctrl+A)",
        icon:
          selectedItems.length === sortedData.length ? (
            <Square size={14} />
          ) : (
            <CheckSquare size={14} />
          ),
        color: "#8b5cf6",
        disabled: loading || sortedData.length === 0,
        onClick: () => toggleSelectAll(),
      },
      {
        key: "clearSelection",
        label: `清空选择 (${selectedItems.length})`,
        icon: <Trash2 size={14} />,
        color: "#ef4444",
        disabled: selectedItems.length === 0,
        onClick: () => handleClearSelection(),
      },
      {
        key: "batchDelete",
        label: `批量删除 (${selectedItems.length})`,
        icon: <Trash2 size={14} />,
        color: "#ef4444",
        disabled: selectedItems.length === 0,
        onClick: () => handleBatchDeleteConfirm(),
      },
      {
        type: "divider",
      },
      {
        key: "toggleDeleted",
        label: showDeleted ? "隐藏已删除" : "显示已删除",
        icon: showDeleted ? <EyeOff size={14} /> : <Eye size={14} />,
        color: "#8b5cf6",
        onClick: () => toggleShowDeleted(),
      },
      {
        key: "bindStrategy",
        label: "绑定策略",
        icon: <Target size={14} />,
        color: "#3b82f6",
        disabled: selectedItems.length === 0,
        onClick: async () => {
          if (selectedItems.length === 1) {
            const project = data.find(
              (item) => item.project_id === selectedItems[0]
            );

            // 获取当前项目的策略绑定信息
            try {
              console.log("获取项目策略绑定信息，项目ID:", selectedItems[0]);
              console.log("项目信息:", project);
              const result =
                await strategyBindingService.getStrategiesByBinding(
                  "project",
                  selectedItems[0].toString()
                );

              console.log("获取策略绑定信息的结果:", result);
              console.log("返回数据的详细信息:", {
                code: result.code,
                data: result.data,
                dataType: typeof result.data,
                dataKeys: result.data ? Object.keys(result.data) : null,
                strategies: result.data?.strategies,
                strategiesLength: result.data?.strategies?.length,
              });

              let existingStrategyIds = "";
              if (
                result.code === 0 &&
                result.data &&
                result.data.strategies &&
                result.data.strategies.length > 0
              ) {
                existingStrategyIds = result.data.strategies
                  .map((s) => s.id)
                  .join(",");
                console.log("解析到的已绑定策略ID:", existingStrategyIds);
              } else {
                console.log("没有找到已绑定的策略:", {
                  code: result.code,
                  dataLength: result.data?.strategies?.length,
                  data: result.data,
                });
              }

              console.log("设置对话框状态，策略ID:", existingStrategyIds);
              setSelectedProjectForStrategy(project);
              setCurrentProjectName(project?.project_name || "");
              setSelectedProjectIds(selectedItems);
              setCurrentStrategyIds(existingStrategyIds);
              setShowStrategyDialog(true);
            } catch (error) {
              console.error("获取策略绑定信息失败:", error);
              toast.error("获取策略绑定信息失败: " + error.message);
              // 即使获取失败，也允许用户打开对话框
              setSelectedProjectForStrategy(project);
              setCurrentProjectName(project?.project_name || "");
              setSelectedProjectIds(selectedItems);
              setCurrentStrategyIds("");
              setShowStrategyDialog(true);
            }
          } else if (selectedItems.length > 1) {
            // 多选情况下，不预加载策略信息
            setSelectedProjectForStrategy(null);
            setCurrentProjectName("");
            setSelectedProjectIds(selectedItems);
            setCurrentStrategyIds("");
            setShowStrategyDialog(true);
          }
        },
      },
      {
        key: "updateBid",
        label: `修改出价 (${selectedItems.length})`,
        icon: <DollarSign size={14} />,
        color: "#10b981",
        disabled: (() => {
          if (selectedItems.length === 0) return true;

          // 检查选中的项目中是否包含手动投放的项目
          const selectedProjects = data.filter((item) =>
            selectedItems.includes(item.project_id)
          );

          const hasManualDelivery = selectedProjects.some(
            (project) => project.delivery_mode === DELIVERY_MODE.MANUAL
          );

          return hasManualDelivery;
        })(),
        onClick: () => handleShowUpdateBid(),
      },
    ];

    // 合并所有菜单项
    return [...copyMenuItems, ...globalCopyMenuItems, ...baseMenuItems];
  };

  // 清空选择函数
  const handleClearSelection = () => {
    if (selectedItems.length > 0) {
      toggleSelectAll();
    }
  };

  return (
    <div
      className="flex flex-col p-4 h-full min-h-0"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      {/* 统一的卡片容器 */}
      <Card className="overflow-hidden h-full flex flex-col">
        {/* 状态提示栏 */}
        {(statusBarFilters.length > 0 || selectedSubAccount) && (
          <StatusBar
            filters={[
              ...(selectedSubAccount
                ? [
                    {
                      icon: null,
                      label: "筛选子账户",
                      value: selectedSubAccount.advertiser_name,
                      textColor: "#1e40af",
                      clearColor: "#60a5fa",
                      clearTitle: "清除子账户筛选",
                      onClear: () =>
                        onSubAccountSelect && onSubAccountSelect(null),
                    },
                  ]
                : []),
              ...statusBarFilters,
            ]}
            totalCount={data.length}
            filteredCount={sortedData.length}
            selectedCount={selectedItems.length}
            itemLabel="个项目"
            showDeleted={showDeleted}
            onClearAll={handleClearAllFilters}
          />
        )}

        {/* 数据表格 - 使用虚拟滚动优化性能 */}
        <div
          ref={scrollContainerRef}
          className="h-full overflow-auto"
          onScroll={handleScroll}
        >
          <VirtualDataTable
            columns={columns}
            data={sortedData}
            loading={loading}
            selectable={true}
            selectedItems={selectedItems}
            onSelectItem={toggleSelectItem}
            onSelectAll={toggleSelectAll}
            rowKey="project_id"
            rowClassName={(item) => {
              // 为已删除的项添加特殊样式
              if (item && item.delete_time) {
                return "deleted-row";
              }
              return "";
            }}
            contextMenuItems={unifiedContextMenuItems}
            onRowClick={onProjectSelect}
            emptyState={
              <EmptyState
                type="empty"
                title="暂无数据"
                description="💡 右键表格区域进行搜索、筛选和数据操作 | Ctrl+F 快速搜索"
              />
            }
            loadingState={
              <EmptyState
                type="loading"
                title="加载中..."
                description="正在获取项目数据，请稍候"
              />
            }
            itemHeight={28} // 舒适行高
            className="project-table-virtual"
          />
        </div>

        {/* 移动端统计信息栏 */}
        <MobileStatsBar
          totalCount={data.length}
          filteredCount={sortedData.length}
          selectedCount={selectedItems.length}
          itemLabel="个项目"
          showDeleted={showDeleted}
        />
      </Card>

      {/* 浮动搜索框 */}
      <FloatingSearchBox
        isOpen={showSearchDialog}
        onClose={handleCloseSearch}
        searchTerm={searchTerm}
        onSearch={handleSearch}
        placeholder="搜索项目名称、项目ID或广告主ID"
        matchCount={searchMatchCount}
        totalCount={data.length}
      />

      {/* 筛选对话框 */}
      <FilterDialog
        isOpen={showFilterDialog}
        onClose={handleCloseFilter}
        filterValue={statusFilter}
        onFilterChange={handleStatusFilterChange}
        filterOptions={STATUS_FILTER_OPTIONS}
        title="状态筛选"
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(INITIAL_CONFIRM_DIALOG)}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
      />

      {/* 策略选择对话框 */}
      <StrategyBindDialog
        visible={showStrategyDialog}
        onClose={handleStrategyDialogClose}
        onConfirm={handleStrategyConfirm}
        currentStrategyIds={currentStrategyIds}
        bindingType="project"
        bindingId={selectedProjectIds[0]?.toString() || ""}
        bindingName={currentProjectName}
        advertiserId={(() => {
          // 获取当前选中项目的广告主ID
          const project = data.find(
            (item) => item.project_id === selectedProjectIds[0]
          );
          return project?.advertiser_id?.toString() || "";
        })()}
        projectId="" // 项目不从其他项目继承策略
      />

      {/* 复制项目对话框 */}
      <CopyProjectDialog
        isOpen={copyProjectDialog.isOpen}
        onClose={() => setCopyProjectDialog({ isOpen: false, project: null })}
        project={copyProjectDialog.project}
        projectDetail={copyProjectDialog.projectDetail}
        onCopyConfirm={handleCopyProjectConfirm}
      />

      {/* 修改出价对话框 */}
      <UpdateBidDialog
        isOpen={updateBidDialog.isOpen}
        onClose={() => setUpdateBidDialog({ isOpen: false, projects: [] })}
        projects={updateBidDialog.projects}
        onConfirm={handleUpdateBidConfirm}
        loading={bidUpdateLoading}
      />
    </div>
  );
}

export default ProjectManager;
