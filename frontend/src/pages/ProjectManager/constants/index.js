// 项目状态常量
export const PROJECT_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
  SUSPENDED: 2,
  DELETED: 3,
};

// 项目启用状态
export const PROJECT_ENABLE_STATUS = {
  ENABLED: 0,
  DISABLED: 1,
};

// 状态对应的颜色配置
export const STATUS_COLORS = {
  [PROJECT_STATUS.ACTIVE]: "text-emerald-600 bg-emerald-50 border-emerald-200",
  [PROJECT_STATUS.INACTIVE]: "text-gray-600 bg-gray-50 border-gray-200",
  [PROJECT_STATUS.SUSPENDED]: "text-amber-600 bg-amber-50 border-amber-200",
  [PROJECT_STATUS.DELETED]: "text-red-600 bg-red-50 border-red-200",
};

// 启用状态颜色配置
export const ENABLE_STATUS_COLORS = {
  [PROJECT_ENABLE_STATUS.ENABLED]:
    "text-emerald-600 bg-emerald-50 border-emerald-200",
  [PROJECT_ENABLE_STATUS.DISABLED]: "text-gray-600 bg-gray-50 border-gray-200",
};

// 状态对应的UI变体（用于StatusBadge组件）
export const STATUS_VARIANTS = {
  [PROJECT_STATUS.ACTIVE]: "success",
  [PROJECT_STATUS.INACTIVE]: "default",
  [PROJECT_STATUS.SUSPENDED]: "warning",
  [PROJECT_STATUS.DELETED]: "error",
};

// 启用状态变体
export const ENABLE_STATUS_VARIANTS = {
  [PROJECT_ENABLE_STATUS.ENABLED]: "success",
  [PROJECT_ENABLE_STATUS.DISABLED]: "default",
};

// 状态文本
export const STATUS_TEXT = {
  [PROJECT_STATUS.ACTIVE]: "投放中",
  [PROJECT_STATUS.INACTIVE]: "未投放",
  [PROJECT_STATUS.SUSPENDED]: "已暂停",
  [PROJECT_STATUS.DELETED]: "已删除",
};

// 启用状态文本
export const ENABLE_STATUS_TEXT = {
  [PROJECT_ENABLE_STATUS.ENABLED]: "已启用",
  [PROJECT_ENABLE_STATUS.DISABLED]: "已禁用",
};

// 投放模式常量
export const DELIVERY_MODE = {
  MANUAL: 1,      // 手动投放
  AUTOMATIC: 3,   // 自动投放
};

// 投放模式文本
export const DELIVERY_MODE_TEXT = {
  [DELIVERY_MODE.MANUAL]: "手动投放",
  [DELIVERY_MODE.AUTOMATIC]: "自动投放",
};

// 投放模式颜色配置
export const DELIVERY_MODE_COLORS = {
  [DELIVERY_MODE.MANUAL]: "text-blue-600 bg-blue-50 border-blue-200",        // 手动投放：蓝色
  [DELIVERY_MODE.AUTOMATIC]: "text-emerald-600 bg-emerald-50 border-emerald-200", // 自动投放：绿色
};

// 初始分页状态（取消分页，查询全部数据）
export const INITIAL_PAGINATION = {
  page: 0,
  pageSize: 0,
  total: 0,
};

// 初始确认对话框状态
export const INITIAL_CONFIRM_DIALOG = {
  isOpen: false,
  title: "",
  message: "",
  onConfirm: null,
};

// 表格列宽度配置（像素值）
export const TABLE_COLUMN_WIDTHS = {
  INDEX: 60,
  ENABLE_STATUS: 90,
  NAME: 200,
  ID: 160, // 增加项目ID列宽度以完整显示长ID
  ADVERTISER_ID: 160, // 增加广告主ID列宽度以完整显示长ID
  STATUS: 90,
  DELIVERY_MODE: 100, // 投放模式列宽度
  PROJECT_BID: 100, // 项目出价列宽度
  CONVERSION_COST: 120,
  STAT_COST: 100,
  SHOW_COUNT: 100,
  CONVERT_COUNT: 100,
  ADVERTISER: 100,
  STRATEGIES: 110, // 减小策略列宽度
  CONVERSION_RATE: 100,
  CREATE_TIME: 140, // 创建时间列宽度
  ACTIONS: 80,
};
