import { useState, useEffect, useCallback, useRef } from "react";
import { useAccountContext } from "../../../contexts/AccountContext";
import { useStrategyMap } from "../../../contexts/StrategyMapContext";
import { toast } from "../../../components/ui/Toast";
import * as ProjectService from "../../../services/projectservice.js";
import strategyService from "../../../services/strategyService.js";
import dataSyncService from "../../../services/dataSyncService.js";
import { INITIAL_PAGINATION, INITIAL_CONFIRM_DIALOG } from "../constants";

/**
 * ProjectManager 自定义Hook
 * 管理项目列表的状态和业务逻辑
 */
export const useProjectManager = (
  preSelectedSubAccount,
  onSubAccountSelect
) => {
  // 基础状态 - 搜索功能由useManagerPage处理
  const [loading, setLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showDeleted, setShowDeleted] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(INITIAL_CONFIRM_DIALOG);

  // 排序状态
  const SORT_KEY = "projectManagerSort";

  const getDefaultSort = () => {
    try {
      const saved = localStorage.getItem(SORT_KEY);
      if (saved) return JSON.parse(saved);
    } catch {}
    return { field: "stat_cost", order: "desc" }; // 默认值
  };

  const [sortConfig, setSortConfig] = useState(getDefaultSort());

  // 编辑状态管理
  const [editingProjectId, setEditingProjectId] = useState(null);
  const [editingProjectName, setEditingProjectName] = useState("");

  // 使用全局账户状态
  const { sharedAccount, sharedSubAccount, setAccount, setSubAccount } =
    useAccountContext();

  // 当前使用的账户和子账户：优先使用共享状态，其次使用传入的预选参数
  const selectedAccount = sharedAccount;
  const selectedSubAccount = sharedSubAccount || preSelectedSubAccount;

  // 使用ref来追踪查询参数的变化
  const prevParamsRef = useRef(null);

  // 使用策略映射
  const { updateStrategyMap } = useStrategyMap();

  // 预加载策略映射
  const preloadStrategyMap = useCallback(async () => {
    try {
      const response = await strategyService.getStrategyList({
        page: 1,
        pageSize: 1000,
        enabled: undefined,
      });

      if (response.code === 0 && response.data && response.data.list) {
        updateStrategyMap(response.data.list);
        console.log(
          `项目管理页面预加载了 ${response.data.list.length} 个策略的映射`
        );
      }
    } catch (error) {
      console.warn("项目管理页面预加载策略映射失败:", error);
    }
  }, [updateStrategyMap]);

  // 保存排序配置到本地存储
  useEffect(() => {
    localStorage.setItem(SORT_KEY, JSON.stringify(sortConfig));
  }, [sortConfig]); // 当 sortConfig 变化时执行

  // 当传入新的预选子账户时，更新到共享状态
  useEffect(() => {
    if (
      preSelectedSubAccount &&
      (!sharedSubAccount ||
        sharedSubAccount.advertiser_id !== preSelectedSubAccount.advertiser_id)
    ) {
      setSubAccount(preSelectedSubAccount);

      // 如果有预选子账户但没有主账户，从子账户信息中构建主账户
      if (!sharedAccount && preSelectedSubAccount.account_id) {
        const mainAccount = {
          account_id: preSelectedSubAccount.account_id,
          account_name:
            preSelectedSubAccount.main_account_name ||
            `主账户(${preSelectedSubAccount.account_id})`,
          id: `main_${preSelectedSubAccount.account_id}`,
        };
        setAccount(mainAccount);
      }
    }
  }, [
    preSelectedSubAccount,
    sharedSubAccount,
    sharedAccount,
    setAccount,
    setSubAccount,
  ]);

  // 构建查询参数（用于本地数据库查询，不分页）
  const buildQueryParams = useCallback(() => {
    const extraFilters = {};

    // 如果有选中的子账户，在extra_filters中添加广告主ID筛选
    // 注意：后端期望 map[string]string，尝试直接传递对象
    if (selectedSubAccount?.advertiser_id) {
      extraFilters.advertiser_id = String(selectedSubAccount.advertiser_id);
    }

    const params = {
      page: 0,
      page_size: 0,
      account_id: selectedAccount?.account_id || 0,
      project_name: "", // 移除搜索条件，获取所有数据，由前端筛选
      status: "",
      remark: "",
      order_by: "",
      order_desc: false,
      extra_filters: extraFilters, // 直接传递对象，让框架处理序列化
    };

    return params;
  }, [selectedAccount, selectedSubAccount]);

  // 加载数据
  const loadData = useCallback(
    async (force = false) => {
      if (!selectedAccount) {
        setData([]);
        setPagination((prev) => ({ ...prev, total: 0 }));
        return;
      }

      const currentParams = buildQueryParams();
      const paramsChanged =
        JSON.stringify(currentParams) !== JSON.stringify(prevParamsRef.current);

      // 如果参数没有变化且不是强制刷新，则不重新加载
      if (!force && !paramsChanged && prevParamsRef.current) {
        return;
      }

      prevParamsRef.current = currentParams;
      setLoading(true);

      try {
        // 先预加载策略映射，确保策略名称能正确显示
        await preloadStrategyMap();

        const result = await ProjectService.GetProjectList(currentParams);

        // 打印项目查询请求参数和响应数据
        console.log("项目列表查询请求参数:", currentParams);
        console.log("项目列表查询响应数据:", result);

        if (result && result.code === 0 && result.data) {
          // 直接使用数据库返回的数据结构
          const dataList = result.data.list || [];
          const total = result.data.total || 0;

          console.log("解析后的项目数据列表:", dataList);
          console.log("项目总数:", total);
          console.log("即将设置数据到state, dataList长度:", dataList.length);

          setData(dataList);
          setPagination((prev) => ({
            ...prev,
            total: total,
          }));

          // 验证数据是否成功设置
          setTimeout(() => {
            console.log("设置数据后，检查当前data状态（延迟100ms）");
          }, 100);
        } else {
          console.error("加载项目数据失败:", result?.msg);
          toast.error("加载项目数据失败: " + (result?.msg || "未知错误"));
        }
      } catch (error) {
        console.error("加载项目数据失败:", error);
        toast.error("加载项目数据失败: " + error.message);
      } finally {
        setLoading(false);
      }
    },
    [buildQueryParams, selectedAccount, preloadStrategyMap]
  );

  // 删除项目
  const deleteProject = async (projectData) => {
    try {
      // 构建DeleteProjectAsyncReq需要的请求结构
      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        advertiser_id: String(projectData.advertiser_id || ""), // string
        project_ids: [String(projectData.project_id || "")], // []string，即使是单个删除也是数组
      };

      console.log("调用DeleteProjectAsync，参数:", req);

      // 添加加载提示
      toast.info("正在删除项目，请稍候...");

      const result = await ProjectService.DeleteProjectAsync(req);

      // 打印删除项目响应数据
      console.log("删除项目响应数据:", result);

      if (result && result.code === 0) {
        // 立即刷新列表
        await loadData(true);
        console.log("项目删除成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 删除成功！项目"${projectData.project_name}"已被删除`,
          4000
        );
      } else {
        toast.error(`❌ 删除项目失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("删除项目失败:", error);
      toast.error(`❌ 删除项目失败: ${error.message}`, 5000);
    }
  };

  // 批量删除
  const batchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.warning("请选择要删除的项目");
      return;
    }

    try {
      // 构建BatchDeleteProjectAsyncReq需要的请求结构
      const accountDetailList = selectedItems.map((projectId) => {
        const projectData = data.find((item) => item.project_id === projectId);
        return {
          advertiser_id: String(projectData?.advertiser_id || ""), // string
          id: String(projectData?.project_id || ""), // string
          name: String(projectData?.project_name || ""), // string
        };
      });

      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        account_detail_list: accountDetailList,
      };

      console.log("调用BatchDeleteProjectAsync，参数:", req);

      // 添加加载提示
      toast.info(`正在批量删除 ${selectedItems.length} 个项目，请稍候...`);

      const result = await ProjectService.BatchDeleteProjectAsync(req);

      // 打印批量删除项目响应数据
      console.log("批量删除项目响应数据:", result);

      if (result && result.code === 0) {
        // 立即清空选中项
        setSelectedItems([]);

        // 立即刷新列表
        await loadData(true);
        console.log(`批量删除 ${selectedItems.length} 个项目成功，列表已刷新`);

        // 成功提示
        toast.success(
          `🎉 批量删除成功！已删除 ${selectedItems.length} 个项目`,
          4000
        );
      } else {
        toast.error(`❌ 批量删除项目失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("批量删除项目失败:", error);
      toast.error(`❌ 批量删除项目失败: ${error.message}`, 5000);
    }
  };

  // 更新项目状态
  const updateStatus = async (project, status) => {
    try {
      // 构建状态映射：projectId -> status
      const statusMap = {};
      statusMap[String(project.project_id)] = status;

      const req = {
        account_id: selectedAccount?.account_id || 0,
        advertiser_id: parseInt(project.advertiser_id) || 0,
        status_map: statusMap,
      };

      console.log("调用UpdateProjectStatus，参数:", req);

      // 添加加载提示
      toast.info("正在更新项目状态，请稍候...");

      const result = await ProjectService.UpdateProjectStatus(req);

      // 打印更新项目状态响应数据
      console.log("更新项目状态响应数据:", result);

      if (result && result.code === 0) {
        // 立即刷新列表
        await loadData(true);
        console.log("项目状态更新成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 状态更新成功！项目"${project.project_name}"状态已更新`,
          4000
        );
      } else {
        toast.error(`❌ 状态更新失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("状态更新失败:", error);
      toast.error(`❌ 状态更新失败: ${error.message}`, 5000);
    }
  };

  // 开始编辑项目名称
  const handleEditProjectName = (project) => {
    setEditingProjectId(project.project_id);
    setEditingProjectName(project.project_name || "");
  };

  // 取消编辑项目名称
  const handleCancelEditProjectName = () => {
    setEditingProjectId(null);
    setEditingProjectName("");
  };

  // 保存项目名称
  const handleSaveProjectName = async (project) => {
    if (!editingProjectName.trim()) {
      toast.warning("项目名称不能为空");
      return;
    }

    if (editingProjectName === project.project_name) {
      // 名称没有变化，直接取消编辑
      handleCancelEditProjectName();
      return;
    }

    try {
      const req = {
        account_id: selectedAccount?.account_id || 0,
        advertiser_id: parseInt(project.advertiser_id) || 0,
        project_id: String(project.project_id || ""),
        name: editingProjectName.trim(),
      };

      console.log("调用UpdateProjectName，参数:", req);

      // 添加加载提示
      toast.info("正在修改项目名称，请稍候...");

      const result = await ProjectService.UpdateProjectName(req);

      // 打印更新项目名称响应数据
      console.log("更新项目名称响应数据:", result);

      if (result && result.code === 0) {
        // 取消编辑状态
        handleCancelEditProjectName();

        // 立即刷新列表
        await loadData(true);
        console.log("项目名称修改成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 修改成功！项目名称已更新为"${editingProjectName}"`,
          4000
        );
      } else {
        toast.error(`❌ 修改项目名称失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("修改项目名称失败:", error);
      toast.error(`❌ 修改项目名称失败: ${error.message}`, 5000);
    }
  };

  // 处理编辑输入框的键盘事件
  const handleEditInputKeyDown = (e, project) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSaveProjectName(project);
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancelEditProjectName();
    }
  };

  // 选择处理 - 参数调整为与广告管理页面保持一致
  const toggleSelectItem = (projectIdOrIds, isCtrlKey = false) => {
    // 如果传入的是数组（批量选择）
    if (Array.isArray(projectIdOrIds)) {
      const newItems = projectIdOrIds;
      if (isCtrlKey) {
        // Ctrl+拖拽：合并选择
        setSelectedItems((prev) => [...new Set([...prev, ...newItems])]);
      } else {
        // 普通拖拽：替换选择
        setSelectedItems(newItems);
      }
    } else {
      // 单项选择逻辑
      const projectId = projectIdOrIds;
      setSelectedItems((prev) => {
        if (isCtrlKey) {
          // Ctrl+单击：切换选择状态（多选模式）
          if (prev.includes(projectId)) {
            return prev.filter((item) => item !== projectId);
          } else {
            return [...prev, projectId];
          }
        } else {
          // 普通单击：单选模式，清空其他选择，只选中当前项
          if (prev.includes(projectId) && prev.length === 1) {
            // 如果当前项已选中且只选中了这一项，则取消选择
            return [];
          } else {
            // 否则清空其他选择，只选中当前项
            return [projectId];
          }
        }
      });
    }
  };

  // 为toggleSelectItem添加批量选择标识，供VirtualDataTable识别
  toggleSelectItem.isBatchSelect = true;

  const toggleSelectAll = () => {
    setSelectedItems((prev) =>
      prev.length === data.length && data.length > 0
        ? []
        : data.map((item) => item.project_id)
    );
  };

  // 账号切换处理
  const handleAccountChange = useCallback(
    (account) => {
      // 清空子账户选择
      if (onSubAccountSelect) {
        onSubAccountSelect(null);
      }

      if (selectedAccount?.account_id !== account?.account_id) {
        // 清空选中项（搜索条件由useManagerPage处理）
        setSelectedItems([]);

        // 更新主账户，清空子账户
        setAccount(account);
        setSubAccount(null);
      }
    },
    [selectedAccount, setAccount, setSubAccount, onSubAccountSelect]
  );

  // 注意：搜索处理现在由useManagerPage提供

  // 切换显示/隐藏已删除项
  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
  }, []);

  // 处理排序
  const handleSort = useCallback((field) => {
    setSortConfig((prev) => {
      // 如果点击的是当前排序字段
      if (prev.field === field) {
        // 循环切换：desc -> asc -> null
        if (prev.order === "desc") {
          return { field, order: "asc" };
        } else if (prev.order === "asc") {
          return { field: null, order: null };
        }
      }
      // 如果点击的是新字段，默认降序
      return { field, order: "desc" };
    });
  }, []);

  // 获取排序后的数据
  const getSortedData = useCallback(
    (dataToSort) => {
      if (!sortConfig.field || !sortConfig.order) {
        return dataToSort;
      }

      const sorted = [...dataToSort].sort((a, b) => {
        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];

        // 处理空值
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        // 特殊处理时间字段 - 使用字符串比较，因为ISO 8601格式可以直接字典序排序
        if (
          sortConfig.field === "create_time" ||
          sortConfig.field === "update_time"
        ) {
          const aTimeStr = String(aValue);
          const bTimeStr = String(bValue);

          return sortConfig.order === "asc"
            ? aTimeStr.localeCompare(bTimeStr)
            : bTimeStr.localeCompare(aTimeStr);
        }

        // 数值比较
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return sortConfig.order === "asc" ? aNum - bNum : bNum - aNum;
        }

        // 字符串比较（备用）
        return sortConfig.order === "asc"
          ? String(aValue).localeCompare(String(bValue))
          : String(bValue).localeCompare(String(aValue));
      });

      return sorted;
    },
    [sortConfig]
  );

  // 智能同步项目数据
  const refreshProjects = async () => {
    console.log(
      "refreshProjects 开始执行，selectedAccount:",
      selectedAccount,
      "selectedSubAccount:",
      selectedSubAccount
    );

    // 详细调试信息
    console.log(
      "调试信息 - selectedSubAccount?.advertiser_id:",
      selectedSubAccount?.advertiser_id
    );
    console.log(
      "调试信息 - selectedSubAccount类型:",
      typeof selectedSubAccount?.advertiser_id
    );
    console.log(
      "调试信息 - 条件判断结果:",
      !!selectedSubAccount?.advertiser_id
    );

    if (!selectedAccount?.account_id) {
      console.warn("refreshProjects: 没有选中的账户");
      toast.warning("请先选择账户");
      return;
    }

    // 设置同步loading状态
    setSyncLoading(true);

    try {
      let result;
      let syncMessage = "";
      let successMessage = "";

      if (selectedSubAccount?.advertiser_id) {
        // 如果选中了广告主，只同步该广告主的项目信息
        console.log(
          "refreshProjects: 同步选中广告主的项目数据，广告主ID:",
          selectedSubAccount.advertiser_id,
          "广告主名称:",
          selectedSubAccount.advertiser_name
        );

        syncMessage = `正在同步广告主 "${selectedSubAccount.advertiser_name}" 的项目和广告数据...`;
        toast.info(syncMessage, 3000);

        result = await dataSyncService.smartSyncProjectData({
          accountId: selectedAccount.account_id,
          advertiserId: selectedSubAccount.advertiser_id,
          syncType: "all", // 同时同步项目和广告
        });

        successMessage = `🎉 同步成功！广告主 "${selectedSubAccount.advertiser_name}" 的项目和广告数据已同步`;
      } else {
        // 如果没有选中广告主，同步账户下所有广告主的项目数据
        console.log(
          "refreshProjects: 同步账户下所有项目数据，账户ID:",
          selectedAccount.account_id,
          "账户名称:",
          selectedAccount.account_name
        );

        syncMessage = `正在同步账户 "${selectedAccount.account_name}" 下所有项目和广告数据...`;
        toast.info(syncMessage, 3000);

        result = await dataSyncService.smartSyncProjectData({
          accountId: selectedAccount.account_id,
          syncType: "all", // 同时同步项目和广告
        });

        successMessage = `🎉 同步成功！账户 "${selectedAccount.account_name}" 下所有项目和广告数据已同步`;
      }

      console.log("refreshProjects: 收到响应数据:", result);

      if (result && result.code === 0) {
        console.log("refreshProjects: 同步成功，准备刷新列表");

        // 立即刷新列表
        await loadData(true);
        console.log("refreshProjects: 项目数据同步成功，列表已刷新");

        // 成功提示
        toast.success(successMessage, 4000);
      } else {
        console.error(
          "refreshProjects: 同步失败，响应码:",
          result?.code,
          "错误信息:",
          result?.msg
        );
        toast.error(
          `❌ 同步项目和广告数据失败: ${result?.msg || "未知错误"}`,
          5000
        );
      }
    } catch (error) {
      console.error("refreshProjects: 异常捕获:", error);
      console.error("refreshProjects: 错误详情:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      let errorMessage = "❌ 同步项目和广告数据失败: ";
      if (selectedSubAccount?.advertiser_id) {
        errorMessage += `无法同步广告主 "${selectedSubAccount.advertiser_name}" 的项目和广告数据，${error.message}`;
      } else {
        errorMessage += `无法同步账户下的项目和广告数据，${error.message}`;
      }

      toast.error(errorMessage, 5000);
    } finally {
      // 清除同步loading状态
      setSyncLoading(false);
    }
  };

  // 复制项目
  const copyProject = async (copyRequest) => {
    if (!selectedAccount?.account_id) {
      toast.warning("请先选择账户");
      return;
    }

    const {
      project,
      newProjectName,
      copyCount = 1, // 新增：复制数量，默认为1
      audienceSelectionMode,
      selectedAudienceIds,
      inheritOriginalAudiences,
      projectDetail, // 新增：前端获取的项目详情数据
      selectedExcludeAudienceIds,
    } = copyRequest;

    try {
      // 构建覆盖参数，包含人群包配置
      const overrideParams = {};

      // 从项目详情中提取bid相关字段
      if (
        projectDetail &&
        projectDetail.Data &&
        projectDetail.Data.PromotionStrategy
      ) {
        const strategy = projectDetail.Data.PromotionStrategy;

        // 提取bid字段，确保是数字类型
        if (strategy.Bid !== undefined && strategy.Bid !== null) {
          const bidValue = parseFloat(strategy.Bid);
          if (!isNaN(bidValue)) {
            overrideParams.bid = bidValue;
            console.log("提取到bid字段:", bidValue);
          } else {
            console.warn("bid字段不是有效的数字:", strategy.Bid);
          }
        }
      }

      // 根据人群包选择模式设置RetargetingTags
      if (audienceSelectionMode === "custom") {
        // 自定义选择模式：使用用户选择的人群包，如果没有选择则为空数组
        overrideParams.RetargetingTags = selectedAudienceIds || [];
      } else if (audienceSelectionMode === "none") {
        // 不包含人群包
        overrideParams.RetargetingTags = [];
      }
      // 如果是inherit模式，不设置overrideParams.RetargetingTags，保持原项目的人群包配置

      // 根据人群包选择模式设置RetargetingTagsExclude
      if (audienceSelectionMode === "custom") {
        // 自定义选择模式：使用用户选择的排除人群包，如果没有选择则为空数组
        overrideParams.RetargetingTagsExclude =
          selectedExcludeAudienceIds || [];
      } else if (audienceSelectionMode === "none") {
        // 不包含排除人群包
        overrideParams.RetargetingTagsExclude = [];
      }
      // 如果是inherit模式，不设置overrideParams.RetargetingTagsExclude，保持原项目的排除人群包配置

      // 检查当前是否有选中的广告主，如果有，则将复制项目的广告主ID切换为当前选中的广告主ID
      let targetAdvertiserId = parseInt(project.advertiser_id) || 0;
      let advertiserSwitchMessage = "";

      if (
        selectedSubAccount?.advertiser_id &&
        selectedSubAccount.advertiser_id !== project.advertiser_id
      ) {
        targetAdvertiserId = selectedSubAccount.advertiser_id;
        advertiserSwitchMessage = `，已切换广告主为: ${
          selectedSubAccount.advertiser_name || selectedSubAccount.advertiser_id
        }`;
        console.log(
          `复制项目时切换广告主: 从 ${project.advertiser_id} 切换到 ${targetAdvertiserId}`
        );
      }

      const req = {
        account_id: selectedAccount.account_id,
        source_project_id: String(project.project_id || ""),
        source_advertiser_id: parseInt(project.advertiser_id) || 0, // 源项目广告主ID
        advertiser_id: targetAdvertiserId, // 目标广告主ID
        new_project_name: newProjectName.trim() || "", // 如果不传新名称，后端会自动生成
        copy_count: copyCount, // 新增：复制数量
        override_params: overrideParams, // 包含人群包配置的覆盖参数
        source_project_detail: projectDetail, // 传递前端获取的项目详情数据，避免后端重复获取
      };

      // 添加加载提示
      if (copyCount > 1) {
        toast.info(`正在批量复制项目，共${copyCount}个，请稍候...`, 3000);
      } else {
        toast.info("正在复制项目，请稍候...", 3000);
      }

      const result = await ProjectService.CopyProject(req);

      if (result && result.code === 0) {
        // 成功提示
        const originalName = project.project_name || "";
        const copiedName =
          result.data?.created_project?.data?.project_name || "新项目";

        // 根据复制数量和人群包配置显示不同的成功消息
        let successMessage = "";
        if (copyCount > 1) {
          successMessage = `🎉 批量复制成功！已从"${originalName}"复制${copyCount}个项目`;
        } else {
          successMessage = `🎉 复制成功！已从"${originalName}"复制为"${copiedName}"`;
        }

        // 添加广告主切换信息
        if (advertiserSwitchMessage) {
          successMessage += advertiserSwitchMessage;
        }

        if (audienceSelectionMode === "custom") {
          const targetCount = selectedAudienceIds?.length || 0;
          const excludeCount = selectedExcludeAudienceIds?.length || 0;
          successMessage += `，已配置 ${targetCount} 个定向人群包，${excludeCount} 个排除人群包`;
        } else if (audienceSelectionMode === "none") {
          successMessage += `，未包含人群包`;
        } else {
          successMessage += `，继承原项目人群包配置`;
        }

        toast.success(successMessage, 4000);

        // 智能同步数据，确保新项目完全创建完成
        const performSmartSync = async () => {
          const maxRetries = copyCount > 1 ? 3 : 2; // 批量复制最多重试3次，单个复制最多重试2次
          const baseDelay = copyCount > 1 ? 8000 : 3000; // 基础延迟时间

          for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
              // 显示同步提示
              if (copyCount > 1) {
                if (attempt === 1) {
                  toast.info("正在同步批量复制的项目数据，请稍候...", 3000);
                } else {
                  toast.info(
                    `正在重试同步数据 (第${attempt}次)，请稍候...`,
                    3000
                  );
                }
              } else {
                if (attempt === 1) {
                  toast.info("正在同步项目数据，请稍候...", 3000);
                } else {
                  toast.info(
                    `正在重试同步数据 (第${attempt}次)，请稍候...`,
                    3000
                  );
                }
              }

              // 调用数据同步
              const syncResult = await ProjectService.RefreshProjects(
                selectedAccount.account_id
              );

              if (syncResult && syncResult.code === 0) {
                // 同步成功，刷新列表
                await loadData(true);
                if (copyCount > 1) {
                  toast.success(
                    `🎉 批量复制项目数据同步完成！${
                      syncResult.data || "项目数据已同步"
                    }`,
                    4000
                  );
                } else {
                  toast.success(
                    `🎉 项目数据同步完成！${
                      syncResult.data || "项目数据已同步"
                    }`,
                    4000
                  );
                }
                return; // 同步成功，退出重试循环
              } else {
                // 同步失败，记录错误
                console.error(
                  `数据同步失败 (第${attempt}次):`,
                  syncResult?.msg
                );

                if (attempt === maxRetries) {
                  // 最后一次尝试失败，但仍然刷新列表（因为复制已经成功）
                  await loadData(true);
                  toast.warning(
                    `⚠️ 数据同步失败，但项目复制成功。${
                      syncResult?.msg || "未知错误"
                    }`,
                    5000
                  );
                } else {
                  // 等待一段时间后重试
                  const retryDelay = baseDelay * attempt; // 递增延迟时间
                  console.log(`等待 ${retryDelay}ms 后重试同步...`);
                  await new Promise((resolve) =>
                    setTimeout(resolve, retryDelay)
                  );
                }
              }
            } catch (syncError) {
              // 同步异常
              console.error(`数据同步异常 (第${attempt}次):`, syncError);

              if (attempt === maxRetries) {
                // 最后一次尝试异常，但仍然刷新列表
                await loadData(true);
                toast.warning(
                  `⚠️ 数据同步异常，但项目复制成功。${syncError.message}`,
                  5000
                );
              } else {
                // 等待一段时间后重试
                const retryDelay = baseDelay * attempt; // 递增延迟时间
                console.log(`等待 ${retryDelay}ms 后重试同步...`);
                await new Promise((resolve) => setTimeout(resolve, retryDelay));
              }
            }
          }
        };

        // 延迟执行智能同步
        setTimeout(performSmartSync, copyCount > 1 ? 5000 : 2000); // 初始延迟时间

        // 返回复制结果，以便调用方能够获取新项目信息
        return result;
      } else {
        // 错误处理
        const errorMsg = result?.msg || "复制项目失败";
        toast.error(`❌ ${errorMsg}`, 5000);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error("copyProject: 异常捕获:", error);
      console.error("copyProject: 错误详情:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      toast.error(`❌ 复制项目失败: ${error.message}`, 5000);
      throw error;
    }
  };

  // 监听查询条件变化，自动加载数据 - 搜索由前端处理
  useEffect(() => {
    if (selectedAccount?.account_id) {
      const timer = setTimeout(() => {
        loadData();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [
    selectedAccount?.account_id,
    selectedSubAccount?.advertiser_id,
    loadData,
  ]);

  return {
    // 状态
    loading,
    syncLoading,
    data,
    pagination,
    selectedItems,
    selectedAccount,
    selectedSubAccount,
    showDeleted,
    editingProjectId,
    editingProjectName,
    setEditingProjectName,
    confirmDialog,
    sortConfig,

    // 操作函数
    loadData,
    deleteProject,
    batchDelete,
    updateStatus,
    refreshProjects,
    copyProject,
    toggleSelectItem,
    toggleSelectAll,
    handleAccountChange,
    handleEditProjectName,
    handleCancelEditProjectName,
    handleSaveProjectName,
    handleEditInputKeyDown,
    toggleShowDeleted,
    handleSort,
    getSortedData,

    // 对话框控制
    setConfirmDialog,

    // 常量
    INITIAL_CONFIRM_DIALOG,
  };
};
