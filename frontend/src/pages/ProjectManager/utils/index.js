/**
 * 格式化时间字符串
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间或'-'
 */
export const formatTime = (timeStr) => {
  if (!timeStr) return "-";
  try {
    const date = new Date(timeStr);
    return date.toLocaleString("zh-CN");
  } catch {
    return timeStr;
  }
};

/**
 * 格式化创建时间
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间或'-'
 */
export const formatCreateTime = (timeStr) => {
  if (!timeStr) return "-";

  try {
    // 解析时间并减去8小时
    const createTime = new Date(timeStr);
    createTime.setHours(createTime.getHours() - 8);

    const now = new Date();
    const diffDays = Math.floor((now - createTime) / (1000 * 60 * 60 * 24));

    let displayText;

    if (diffDays === 0) {
      // 今天：今天 14:30
      displayText = `今天 ${createTime.toLocaleString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else if (diffDays === 1) {
      // 昨天：昨天 14:30
      displayText = `昨天 ${createTime.toLocaleString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else if (diffDays < 30) {
      // 30天内：01/15 14:30
      displayText = createTime.toLocaleString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      // 更久前：2024/01/15 14:30
      displayText = createTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    return displayText;
  } catch {
    return timeStr;
  }
};

/**
 * 格式化更新时间
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间或'-'
 */
export const formatUpdateTime = (timeStr) => {
  if (!timeStr) return "-";

  try {
    const updateTime = new Date(timeStr);
    const now = new Date();
    const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    let displayText;

    if (diffMinutes < 1) {
      displayText = "刚刚";
    } else if (diffMinutes < 60) {
      displayText = `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      displayText = `${diffHours}小时前`;
    } else if (diffDays === 1) {
      displayText = `昨天 ${updateTime.toLocaleString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else if (diffDays < 30) {
      displayText = updateTime.toLocaleString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      displayText = updateTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    return displayText;
  } catch {
    return timeStr;
  }
};

/**
 * 格式化金额
 * @param {number|string} amount - 金额
 * @returns {string} 格式化后的金额
 */
export const formatAmount = (amount) => {
  if (!amount || amount === 0) return "¥0.00";
  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return "¥0.00";

  // 对于大金额，添加千分位分隔符
  return `¥${numAmount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

/**
 * 计算表格序号
 * @param {number} index - 当前行索引
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页大小
 * @returns {number} 序号
 */
export const getRowIndex = (index, page, pageSize) => {
  return index + 1 + (page - 1) * pageSize;
};

/**
 * 格式化百分比
 * @param {number|string} rate - 比率值
 * @returns {string} 格式化后的百分比
 */
export const formatPercentage = (rate) => {
  if (!rate || rate === 0) return "0%";
  const numRate = typeof rate === "string" ? parseFloat(rate) : rate;
  if (isNaN(numRate)) return "0%";

  // 如果传入的是小数（如0.1234），转换为百分比
  if (numRate < 1) {
    return `${(numRate * 100).toFixed(2)}%`;
  }

  // 如果传入的已经是百分比数值（如12.34），直接格式化
  return `${numRate.toFixed(2)}%`;
};

/**
 * 格式化数字显示
 * @param {number|string} num - 数字
 * @returns {string} 格式化后的数字
 */
export const formatNumber = (num) => {
  if (!num || num === 0) return "0";
  const numValue = typeof num === "string" ? parseInt(num) : num;
  if (isNaN(numValue)) return "0";

  // 对于大数字，添加千分位分隔符
  return numValue.toLocaleString("zh-CN");
};

/**
 * 检查项目是否可点击（有项目ID）
 * @param {Object} project - 项目对象
 * @returns {boolean} 是否可点击
 */
export const isProjectClickable = (project) => {
  return project && project.project_id;
};

/**
 * 截断文本并显示省略号
 * @param {string} text - 文本
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的文本
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return "-";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

/**
 * 获取项目状态文本
 * @param {Object} project - 项目对象
 * @returns {string} 状态文本
 */
export const getProjectStatusText = (project) => {
  if (!project) return "未知状态";

  // 优先使用项目状态名称字段
  if (project.project_status_name) {
    return project.project_status_name;
  }

  // 如果没有名称字段，根据状态码映射
  const statusMap = {
    1: "投放中",
    0: "未投放",
    2: "已暂停",
    3: "已删除",
  };

  return statusMap[project.project_status] || "未知状态";
};

/**
 * 获取项目启用状态文本
 * @param {Object} project - 项目对象
 * @returns {string} 启用状态文本
 */
export const getProjectEnableStatusText = (project) => {
  if (!project) return "未知状态";

  return project.enable_status === 0 ? "已启用" : "已禁用";
};

/**
 * 获取投放模式文本
 * @param {Object} project - 项目对象
 * @returns {string} 投放模式文本
 */
export const getDeliveryModeText = (project) => {
  if (
    !project ||
    project.delivery_mode === undefined ||
    project.delivery_mode === null
  ) {
    return "未知模式";
  }

  const deliveryModeMap = {
    1: "手动投放",
    3: "自动投放",
  };

  return deliveryModeMap[project.delivery_mode] || "未知模式";
};
