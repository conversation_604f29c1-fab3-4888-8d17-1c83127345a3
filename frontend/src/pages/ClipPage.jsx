import React, { useState, useEffect } from 'react';
import { Upload, Music, Image, X, FileText } from 'lucide-react';
import { toast } from '../components/ui/Toast';

const ClipPage = () => {
  // 生成默认任务名称
  const generateDefaultTaskName = () => {
    const now = new Date();
    const dateStr = now.toLocaleDateString('zh-CN').replace(/\//g, '-');
    const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false }).replace(/:/g, '-');
    return `混剪任务_${dateStr}_${timeStr}`;
  };

  // 视频混剪设置
  const [clipSettings, setClipSettings] = useState({
    clipTaskName: generateDefaultTaskName(),
    aspectRatio: '9:16',
    scaleMode: 'WidthFix',
    musicMixMode: 1,
    musicUrl: [],
    headCover: [],
    tailCover: [],
    groupList: []
  });

  // 计算可生成视频总数
  const calculateGenTotal = () => {
    const musicCount = Math.max(clipSettings.musicUrl.length, 1); // 至少为1
    const headCoverCount = Math.max(clipSettings.headCover.length, 1); // 至少为1
    const tailCoverCount = Math.max(clipSettings.tailCover.length, 1); // 至少为1

    // 计算所有视频组中视频数量的乘积
    let groupVideoProduct = 1;
    if (clipSettings.groupList.length > 0) {
      groupVideoProduct = clipSettings.groupList.reduce((product, group) => {
        return product * Math.max(group.video_url.length, 1); // 每个组至少为1
      }, 1);
    }

    return musicCount * headCoverCount * tailCoverCount * groupVideoProduct;
  };

  // 获取计算后的生成总数
  const genTotal = calculateGenTotal();

  const [processing, setProcessing] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [progressValue, setProgressValue] = useState(0);
  const [progressText, setProgressText] = useState('');

  // 启动进度条
  const startProgress = (text, duration) => {
    setProgressText(text);
    setShowProgress(true);
    setProgressValue(0);
    
    const interval = setInterval(() => {
      setProgressValue(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + Math.random() * 10;
      });
    }, duration / 10);
  };

  // 选择音乐文件
  const handleSelectMusicFiles = async () => {
    try {
      console.log('选择音乐文件 - 准备导入服务模块');
      const { SystemService, handleResult } = await import('../services/api.js');
      console.log('选择音乐文件 - 服务模块导入成功');
      
      const req = {
        title: '选择音乐文件',
        display_name: '音乐文件',
        pattern: '*.mp3;*.wav;*.flac;*.aac;*.ogg;*.m4a;*.wma',
        is_multiple: true
      };
      
      const result = await SystemService.OpenFile(req);
      const data = handleResult(result);
      
      if (data.data && data.data.length > 0) {
        const newMusicFiles = data.data.filter(filePath => 
          !clipSettings.musicUrl.includes(filePath)
        );
        
        if (newMusicFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            musicUrl: [...prev.musicUrl, ...newMusicFiles]
          }));
        }
      }
    } catch (error) {
      console.error('选择音乐文件失败:', error);
      toast.error('选择音乐文件失败：' + error.message);
    }
  };

  // 选择头部封面文件
  const handleSelectHeadCoverFiles = async () => {
    try {
      const { SystemService, handleResult } = await import('../services/api.js');
      
      const req = {
        title: '选择头部封面文件',
        display_name: '图片文件',
        pattern: '*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp;*.tiff',
        is_multiple: true
      };
      
      const result = await SystemService.OpenFile(req);
      const data = handleResult(result);
      
      if (data.data && data.data.length > 0) {
        const newCoverFiles = data.data.filter(filePath => 
          !clipSettings.headCover.includes(filePath)
        );
        
        if (newCoverFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            headCover: [...prev.headCover, ...newCoverFiles]
          }));
        }
      }
    } catch (error) {
      console.error('选择头部封面失败:', error);
      toast.error('选择头部封面失败：' + error.message);
    }
  };

  // 选择尾部封面文件
  const handleSelectTailCoverFiles = async () => {
    try {
      const { SystemService, handleResult } = await import('../services/api.js');
      
      const req = {
        title: '选择尾部封面文件',
        display_name: '图片文件',
        pattern: '*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp;*.tiff',
        is_multiple: true
      };
      
      const result = await SystemService.OpenFile(req);
      const data = handleResult(result);
      
      if (data.data && data.data.length > 0) {
        const newCoverFiles = data.data.filter(filePath => 
          !clipSettings.tailCover.includes(filePath)
        );
        
        if (newCoverFiles.length > 0) {
          setClipSettings(prev => ({
            ...prev,
            tailCover: [...prev.tailCover, ...newCoverFiles]
          }));
        }
      }
    } catch (error) {
      console.error('选择尾部封面失败:', error);
      toast.error('选择尾部封面失败：' + error.message);
    }
  };

  // 添加视频分组
  const handleAddVideoGroup = async () => {
    try {
      const { SystemService, handleResult } = await import('../services/api.js');
      
      const req = {
        title: '选择分组视频文件',
        display_name: '视频文件',
        pattern: '*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm;*.m4v;*.3gp;*.f4v',
        is_multiple: true
      };
      
      const result = await SystemService.OpenFile(req);
      const data = handleResult(result);
      
      if (data.data && data.data.length > 0) {
        const newGroup = {
          id: Date.now(),
          video_url: data.data,
          is_origin_sound: true
        };
        
        setClipSettings(prev => ({
          ...prev,
          groupList: [...prev.groupList, newGroup]
        }));
      }
    } catch (error) {
      console.error('添加视频分组失败:', error);
      toast.error('添加视频分组失败：' + error.message);
    }
  };

  // 向现有分组添加视频
  const handleAddVideoToGroup = async (groupId) => {
    try {
      const { SystemService, handleResult } = await import('../services/api.js');
      
      const req = {
        title: '选择要添加的视频文件',
        display_name: '视频文件',
        pattern: '*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm;*.m4v;*.3gp;*.f4v',
        is_multiple: true
      };
      
      const result = await SystemService.OpenFile(req);
      const data = handleResult(result);
      
      if (data.data && data.data.length > 0) {
        setClipSettings(prev => ({
          ...prev,
          groupList: prev.groupList.map(group => {
            if (group.id === groupId) {
              const newVideos = data.data.filter(videoPath => 
                !group.video_url.includes(videoPath)
              );
              
              if (newVideos.length > 0) {
                return {
                  ...group,
                  video_url: [...group.video_url, ...newVideos]
                };
              }
            }
            return group;
          })
        }));
      }
    } catch (error) {
      console.error('向分组添加视频失败:', error);
      toast.error('向分组添加视频失败：' + error.message);
    }
  };

  // 删除文件函数
  const handleRemoveMusicFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      musicUrl: prev.musicUrl.filter(path => path !== filePath)
    }));
  };

  const handleRemoveHeadCoverFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      headCover: prev.headCover.filter(path => path !== filePath)
    }));
  };

  const handleRemoveTailCoverFile = (filePath) => {
    setClipSettings(prev => ({
      ...prev,
      tailCover: prev.tailCover.filter(path => path !== filePath)
    }));
  };

  const handleRemoveVideoGroup = (groupId) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.filter(group => group.id !== groupId)
    }));
  };

  const handleToggleGroupOriginSound = (groupId) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.map(group => 
        group.id === groupId 
          ? { ...group, is_origin_sound: !group.is_origin_sound }
          : group
      )
    }));
  };

  const handleRemoveVideoFromGroup = (groupId, videoPath) => {
    setClipSettings(prev => ({
      ...prev,
      groupList: prev.groupList.map(group => 
        group.id === groupId 
          ? { ...group, video_url: group.video_url.filter(path => path !== videoPath) }
          : group
      ).filter(group => group.video_url.length > 0)
    }));
  };

  // 工具函数
  const getFileName = (filePath) => {
    if (!filePath) return '';
    return filePath.split(/[/\\]/).pop() || filePath;
  };

  const getFileExtension = (filePath) => {
    if (!filePath) return '';
    const fileName = getFileName(filePath);
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
  };

  const isImageFile = (filePath) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'webp', 'tiff'];
    return imageExtensions.includes(getFileExtension(filePath));
  };

  const isAudioFile = (filePath) => {
    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'];
    return audioExtensions.includes(getFileExtension(filePath));
  };

  // 文件缩略图组件
  const FileThumbnail = ({ filePath, size = 'md', className = '' }) => {
    const [imageError, setImageError] = useState(false);
    
    const sizeClasses = {
      sm: 'w-8 h-8',
      md: 'w-12 h-12',
      lg: 'w-16 h-16',
      xl: 'w-20 h-20'
    };
    
    const iconSizes = {
      sm: 16,
      md: 20,
      lg: 24,
      xl: 28
    };
    
    const sizeClass = sizeClasses[size] || sizeClasses.md;
    const iconSize = iconSizes[size] || iconSizes.md;
    
    if (isImageFile(filePath) && !imageError) {
      return (
        <div className={`${sizeClass} ${className} bg-slate-100 rounded overflow-hidden border border-slate-200 flex items-center justify-center`}>
          <img
            src={`/api/preview?path=${encodeURIComponent(filePath)}`}
            alt={getFileName(filePath)}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
            onLoad={() => setImageError(false)}
          />
        </div>
      );
    }
    
    if (isAudioFile(filePath)) {
      return (
        <div className={`${sizeClass} ${className} bg-blue-50 rounded border border-blue-200 flex items-center justify-center`}>
          <Music size={iconSize} className="text-blue-600" />
        </div>
      );
    }
    
    return (
      <div className={`${sizeClass} ${className} bg-slate-100 rounded border border-slate-200 flex items-center justify-center`}>
        <FileText size={iconSize} className="text-slate-500" />
      </div>
    );
  };

  // 创建混剪任务
      const handleCreateClipTask = async () => {
      if (!clipSettings.clipTaskName.trim()) {
        toast.warning('请输入任务名称');
        return;
      }

    setProcessing(true);
    startProgress('正在创建混剪任务...', 10000);

    try {
      const { ClipTaskService } = await import('../services/api.js');

      const formattedGroupList = clipSettings.groupList.map(group => ({
        video_url: group.video_url,
        is_origin_sound: group.is_origin_sound
      }));

      const clipTaskData = {
        clip_task_name: clipSettings.clipTaskName,
        aspect_ratio: clipSettings.aspectRatio,
        scale_mode: clipSettings.scaleMode,
        music_mix_mode: clipSettings.musicMixMode,
        gen_total: genTotal,
        remaining_gen_count: genTotal,
        gen_status: 1,
        music_url: clipSettings.musicUrl,
        head_cover: clipSettings.headCover,
        tail_cover: clipSettings.tailCover,
        group_list: formattedGroupList
      };

      const result = await ClipTaskService.CreateClipTask(clipTaskData);

      setShowProgress(false);
      setProcessing(false);

      if (result && result.success !== false) {
        toast.success('混剪任务创建成功！');
        
        // 重置表单
        setClipSettings({
          clipTaskName: generateDefaultTaskName(),
          aspectRatio: '9:16',
          scaleMode: 'WidthFix',
          musicMixMode: 1,
          musicUrl: [],
          headCover: [],
          tailCover: [],
          groupList: []
        });
      } else {
        const errorMessage = result?.message || result?.error || '未知错误';
        toast.error('混剪任务创建失败：' + errorMessage);
      }
    } catch (error) {
      console.error('创建混剪任务失败:', error);
      setShowProgress(false);
      setProcessing(false);
      toast.error('创建混剪任务失败：' + error.message);
    }
  };

  return (
    <div className="h-screen bg-slate-50 flex flex-col">
      {/* 顶部标题栏 */}
      <div className="flex-shrink-0 bg-white border-b border-slate-200 px-6 py-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-slate-800 mb-1">视频混剪工厂</h1>
            <p className="text-sm text-slate-600">快速创建混剪任务，支持多素材组合生成</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">
              🎬 可生成 {genTotal} 个视频
            </div>
            <button
              onClick={handleCreateClipTask}
              disabled={!clipSettings.clipTaskName.trim() || processing}
              className="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed font-semibold shadow-md hover:shadow-lg flex items-center space-x-2"
            >
              {processing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>创建中...</span>
                </>
              ) : (
                <>
                  <span>🚀</span>
                  <span>创建混剪任务</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto px-6 py-4">
          <div className="max-w-7xl mx-auto space-y-4">
            
            {/* 基本设置 - 横向排列 */}
            <div className="bg-white rounded-lg border border-slate-200 p-4">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <h3 className="text-base font-semibold text-slate-700">基本设置</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* 任务名称 */}
                <div>
                  <label className="block text-xs font-medium text-slate-600 mb-1.5">任务名称</label>
                  <input
                    type="text"
                    value={clipSettings.clipTaskName}
                    onChange={(e) => setClipSettings({...clipSettings, clipTaskName: e.target.value})}
                    placeholder="请输入混剪任务名称"
                    className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all hover:border-slate-400"
                  />
                </div>

                {/* 画面比例 */}
                <div>
                  <label className="block text-xs font-medium text-slate-600 mb-1.5">画面比例</label>
                  <select
                    value={clipSettings.aspectRatio}
                    onChange={(e) => setClipSettings({...clipSettings, aspectRatio: e.target.value})}
                    className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all hover:border-slate-400"
                  >
                    <option value="16:9">📺 16:9 (横屏)</option>
                    <option value="9:16">📱 9:16 (竖屏)</option>
                    <option value="1:1">⬜ 1:1 (正方形)</option>
                    <option value="4:3">📽️ 4:3 (传统)</option>
                  </select>
                </div>

                {/* 缩放模式 */}
                <div>
                  <label className="block text-xs font-medium text-slate-600 mb-1.5">缩放模式</label>
                  <select
                    value={clipSettings.scaleMode}
                    onChange={(e) => setClipSettings({...clipSettings, scaleMode: e.target.value})}
                    className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all hover:border-slate-400"
                  >
                    <option value="ScaleToFill">🔄 填充缩放</option>
                    <option value="WidthFix">↔️ 宽度固定</option>
                    <option value="HeightFix">↕️ 高度固定</option>
                  </select>
                </div>

                {/* 音乐混合模式 */}
                <div>
                  <label className="block text-xs font-medium text-slate-600 mb-1.5">音乐混合模式</label>
                  <select
                    value={clipSettings.musicMixMode}
                    onChange={(e) => setClipSettings({...clipSettings, musicMixMode: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all hover:border-slate-400"
                  >
                    <option value={1}>🎵 BGM与视频原声混合</option>
                    <option value={2}>🎶 只要BGM</option>
                    <option value={3}>🔇 无BGM</option>
                  </select>
                </div>

                {/* 计算说明 */}
                <div>
                  <label className="block text-xs font-medium text-slate-600 mb-1.5">计算公式</label>
                  <div className="text-xs bg-slate-100 p-2 rounded border text-slate-600">
                    音乐({Math.max(clipSettings.musicUrl.length, 1)}) × 头部封面({Math.max(clipSettings.headCover.length, 1)}) × 尾部封面({Math.max(clipSettings.tailCover.length, 1)}) × 视频组({clipSettings.groupList.length > 0 ? clipSettings.groupList.reduce((product, group) => product * Math.max(group.video_url.length, 1), 1) : 1})
                  </div>
                </div>
              </div>
            </div>

            {/* 素材设置 - 横向标签页 */}
            <div className="bg-white rounded-lg border border-slate-200">
              <div className="p-4 border-b border-slate-200">
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                  <h3 className="text-base font-semibold text-slate-700">素材设置</h3>
                </div>
              </div>
              
              <div className="p-4">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  
                  {/* 背景音乐 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Music size={16} className="text-blue-500" />
                        <span className="font-medium text-slate-700">背景音乐</span>
                      </div>
                      <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                        {clipSettings.musicUrl.length} 个
                      </div>
                    </div>
                    
                    <button
                      onClick={handleSelectMusicFiles}
                      className="w-full px-3 py-2 border-2 border-dashed border-slate-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors text-slate-600 hover:text-blue-600 text-sm flex items-center justify-center space-x-2"
                    >
                      <Music size={16} />
                      <span>添加音乐</span>
                    </button>

                    {/* 音乐文件横向展示 */}
                    {clipSettings.musicUrl.length > 0 && (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {clipSettings.musicUrl.map((filePath, index) => (
                          <div key={index} className="flex items-center space-x-2 bg-blue-50 rounded p-2 border border-blue-100">
                            <FileThumbnail filePath={filePath} size="sm" />
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium text-slate-700 truncate" title={filePath}>
                                {getFileName(filePath)}
                              </div>
                            </div>
                            <button
                              onClick={() => handleRemoveMusicFile(filePath)}
                              className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors"
                              title="删除"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 头部封面 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Image size={16} className="text-green-500" />
                        <span className="font-medium text-slate-700">头部封面</span>
                      </div>
                      <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                        {clipSettings.headCover.length} 个
                      </div>
                    </div>
                    
                    <button
                      onClick={handleSelectHeadCoverFiles}
                      className="w-full px-3 py-2 border-2 border-dashed border-slate-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-slate-600 hover:text-green-600 text-sm flex items-center justify-center space-x-2"
                    >
                      <Image size={16} />
                      <span>添加封面</span>
                    </button>

                    {clipSettings.headCover.length > 0 && (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {clipSettings.headCover.map((filePath, index) => (
                          <div key={index} className="flex items-center space-x-2 bg-green-50 rounded p-2 border border-green-100">
                            <FileThumbnail filePath={filePath} size="sm" />
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium text-slate-700 truncate" title={filePath}>
                                {getFileName(filePath)}
                              </div>
                            </div>
                            <button
                              onClick={() => handleRemoveHeadCoverFile(filePath)}
                              className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors"
                              title="删除"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 尾部封面 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Image size={16} className="text-orange-500" />
                        <span className="font-medium text-slate-700">尾部封面</span>
                      </div>
                      <div className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full">
                        {clipSettings.tailCover.length} 个
                      </div>
                    </div>
                    
                    <button
                      onClick={handleSelectTailCoverFiles}
                      className="w-full px-3 py-2 border-2 border-dashed border-slate-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-colors text-slate-600 hover:text-orange-600 text-sm flex items-center justify-center space-x-2"
                    >
                      <Image size={16} />
                      <span>添加封面</span>
                    </button>

                    {clipSettings.tailCover.length > 0 && (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {clipSettings.tailCover.map((filePath, index) => (
                          <div key={index} className="flex items-center space-x-2 bg-orange-50 rounded p-2 border border-orange-100">
                            <FileThumbnail filePath={filePath} size="sm" />
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium text-slate-700 truncate" title={filePath}>
                                {getFileName(filePath)}
                              </div>
                            </div>
                            <button
                              onClick={() => handleRemoveTailCoverFile(filePath)}
                              className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors"
                              title="删除"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 视频分组 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FileText size={16} className="text-purple-500" />
                        <span className="font-medium text-slate-700">视频分组</span>
                      </div>
                      <div className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                        {clipSettings.groupList.length} 个
                      </div>
                    </div>
                    
                    <button
                      onClick={handleAddVideoGroup}
                      className="w-full px-3 py-2 border-2 border-dashed border-slate-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-slate-600 hover:text-purple-600 text-sm flex items-center justify-center space-x-2"
                    >
                      <FileText size={16} />
                      <span>添加分组</span>
                    </button>

                    {clipSettings.groupList.length > 0 && (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {clipSettings.groupList.map((group, groupIndex) => (
                          <div key={group.id} className="bg-purple-50 rounded p-2 border border-purple-100">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-xs font-medium text-slate-700">
                                  分组 {groupIndex + 1}
                                </span>
                                <span className="text-xs bg-purple-200 text-purple-700 px-1.5 py-0.5 rounded">
                                  {group.video_url.length} 个视频
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <button
                                  onClick={() => handleAddVideoToGroup(group.id)}
                                  className="text-green-600 hover:text-green-700 p-0.5 rounded hover:bg-green-50 transition-colors"
                                  title="添加视频"
                                >
                                  <Upload size={12} />
                                </button>
                                <button
                                  onClick={() => handleRemoveVideoGroup(group.id)}
                                  className="text-red-500 hover:text-red-700 p-0.5 rounded hover:bg-red-50 transition-colors"
                                  title="删除分组"
                                >
                                  <X size={12} />
                                </button>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-2 mb-2">
                              <input
                                type="checkbox"
                                id={`origin-sound-${group.id}`}
                                checked={group.is_origin_sound}
                                onChange={() => handleToggleGroupOriginSound(group.id)}
                                className="w-3 h-3 text-purple-600 rounded focus:ring-purple-500"
                              />
                              <label
                                htmlFor={`origin-sound-${group.id}`}
                                className="text-xs text-slate-600 cursor-pointer"
                              >
                                保留原声
                              </label>
                            </div>

                            <div className="space-y-1">
                              {group.video_url.slice(0, 3).map((videoPath, videoIndex) => (
                                <div key={videoIndex} className="flex items-center space-x-2 bg-white rounded p-1.5">
                                  <FileThumbnail filePath={videoPath} size="sm" />
                                  <div className="flex-1 min-w-0">
                                    <div className="text-xs font-medium text-slate-700 truncate" title={videoPath}>
                                      {getFileName(videoPath)}
                                    </div>
                                  </div>
                                  <button
                                    onClick={() => handleRemoveVideoFromGroup(group.id, videoPath)}
                                    className="text-red-500 hover:text-red-700 p-0.5 rounded hover:bg-red-50 transition-colors"
                                    title="删除"
                                  >
                                    <X size={10} />
                                  </button>
                                </div>
                              ))}
                              {group.video_url.length > 3 && (
                                <div className="text-xs text-slate-500 px-2">
                                  还有 {group.video_url.length - 3} 个视频...
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 进度条 */}
            {showProgress && (
              <div className="bg-white rounded-lg border border-blue-200 p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-blue-700 font-medium">{progressText}</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressValue}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClipPage;
