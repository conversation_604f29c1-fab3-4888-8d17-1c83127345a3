import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useAccountContext } from "../../../contexts/AccountContext";
import { useStrategyMap } from "../../../contexts/StrategyMapContext";
import { toast } from "../../../components/ui/Toast";
import * as PromotionService from "../../../services/promotionService.js";
import strategyService from "../../../services/strategyService.js";
import { INITIAL_PAGINATION, INITIAL_CONFIRM_DIALOG } from "../constants";

/**
 * PromotionManager 自定义Hook
 * 管理广告列表的状态和业务逻辑
 */
export const usePromotionManager = (selectedProject) => {
  // 使用策略映射
  const { updateStrategyMap } = useStrategyMap();

  // 基础状态 - 搜索功能由useManagerPage处理
  const [loading, setLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);
  const [selectedItems, setSelectedItems] = useState([]);
  const [confirmDialog, setConfirmDialog] = useState(INITIAL_CONFIRM_DIALOG);
  const [projectInfo, setProjectInfo] = useState(selectedProject || {});
  const [showDeleted, setShowDeleted] = useState(false);

  // 排序状态
  const SORT_KEY = 'promotionManagerSort';

  const getDefaultSort = () => {
    try {
      const saved = localStorage.getItem(SORT_KEY);
      if (saved) return JSON.parse(saved);
    } catch {}
    return { field: 'stat_cost', order: 'desc' }; // 默认值
  };

  const [sortConfig, setSortConfig] = useState(getDefaultSort());

  // 编辑状态管理
  const [editingPromotionId, setEditingPromotionId] = useState(null);
  const [editingPromotionName, setEditingPromotionName] = useState("");

  // 使用全局账户状态
  const { sharedAccount, setAccount } = useAccountContext();

  // 当前使用的账户：优先使用共享状态，其次从项目信息中获取
  const selectedAccount =
    sharedAccount ||
    (projectInfo?.accountId
      ? {
          account_id: projectInfo.accountId,
          account_name:
            projectInfo.accountName || `主账户(${projectInfo.accountId})`,
          id: `main_${projectInfo.accountId}`,
        }
      : null);

  // 使用ref跟踪查询参数变化
  const prevParamsRef = useRef(null);
  const isInitialMount = useRef(true);

  // 预加载策略映射
  const preloadStrategyMap = useCallback(async () => {
    try {
      const response = await strategyService.getStrategyList({
        page: 1,
        pageSize: 1000,
        enabled: undefined,
      });

      if (response.code === 0 && response.data && response.data.list) {
        updateStrategyMap(response.data.list);
        console.log(
          `项目广告管理页面预加载了 ${response.data.list.length} 个策略的映射`
        );
      }
    } catch (error) {
      console.warn("项目广告管理页面预加载策略映射失败:", error);
    }
  }, [updateStrategyMap]);
  
  // 保存排序配置到本地存储
  useEffect(() => {
    localStorage.setItem(SORT_KEY, JSON.stringify(sortConfig));
  }, [sortConfig]); // 当 sortConfig 变化时执行

  // 当selectedProject变化时更新projectInfo和selectedAccount
  useEffect(() => {
    setProjectInfo(selectedProject || {});

    // 如果项目信息包含账户信息，更新选中的账户
    if (
      selectedProject?.accountId &&
      (!sharedAccount || sharedAccount.account_id !== selectedProject.accountId)
    ) {
      setAccount({
        account_id: selectedProject.accountId,
        account_name:
          selectedProject.accountName || `主账户(${selectedProject.accountId})`,
        id: `main_${selectedProject.accountId}`,
      });
    }
  }, [selectedProject, sharedAccount, setAccount]);

  // 构建查询参数（用于本地数据库查询，不分页）
  const buildQueryParams = useCallback(() => {
    const params = {
      page: 0,
      page_size: 0,
      account_id: selectedAccount?.account_id || 0,
      promotion_name: "", // 移除搜索条件，获取所有数据，由前端筛选
      advertiser_id: 0,
      project_id: "",
      status: "",
      remark: "",
      order_by: "",
      order_desc: false,
      extra_filters: {},
    };

    // 如果有项目信息，添加项目筛选条件
    if (projectInfo.project_id) {
      params.project_id = String(projectInfo.project_id);
    }
    if (projectInfo.advertiser_id) {
      params.advertiser_id = parseInt(projectInfo.advertiser_id) || 0;
    }

    return params;
  }, [
    selectedAccount?.account_id,
    projectInfo.project_id,
    projectInfo.advertiser_id,
  ]);

  // 加载广告数据
  const loadPromotionData = useCallback(
    async (force = false) => {
      // 如果没有选中账户，不发送请求
      if (!selectedAccount?.account_id) {
        console.log("未选择账户，跳过加载广告数据");
        setData([]);
        setPagination((prev) => ({ ...prev, total: 0 }));
        return;
      }

      // 构建当前查询参数
      const currentParams = buildQueryParams();
      const paramsChanged =
        JSON.stringify(currentParams) !== JSON.stringify(prevParamsRef.current);

      // 如果参数没有变化且不是强制刷新，则不重新加载
      if (!force && !paramsChanged && prevParamsRef.current) {
        return;
      }

      prevParamsRef.current = currentParams;
      setLoading(true);

      try {
        // 先预加载策略映射，确保策略名称能正确显示
        await preloadStrategyMap();

        const result = await PromotionService.GetPromotionList(currentParams);

        // 打印广告查询请求参数和响应数据
        console.log("广告列表查询请求参数:", currentParams);
        console.log("广告列表查询响应数据:", result);

        if (result && result.code === 0 && result.data) {
          // 直接使用数据库返回的数据结构
          const dataList = result.data.list || [];
          const total = result.data.total || 0;

          console.log("解析后的广告数据列表:", dataList);
          console.log("广告总数:", total);

          setData(dataList);
          setPagination((prev) => ({
            ...prev,
            total: total,
          }));
        } else {
          console.error("加载广告数据失败:", result?.msg);
          toast.error("加载广告数据失败: " + (result?.msg || "未知错误"));
        }
      } catch (error) {
        console.error("加载广告数据失败:", error);
        toast.error("加载广告数据失败: " + error.message);
      } finally {
        setLoading(false);
      }
    },
    [buildQueryParams, selectedAccount?.account_id, preloadStrategyMap]
  );

  // 删除广告
  const deletePromotion = async (promotionData) => {
    try {
      // 构建DeletePromotionAsyncReq需要的请求结构
      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        advertiser_id: String(promotionData.advertiser_id || ""), // string
        promotion_ids: [String(promotionData.promotion_id || "")], // []string，即使是单个删除也是数组
      };

      console.log("调用DeletePromotionAsync，参数:", req);

      // 添加加载提示
      toast.info("正在删除广告，请稍候...");

      const result = await PromotionService.DeletePromotionAsync(req);

      // 打印删除广告响应数据
      console.log("删除广告响应数据:", result);

      if (result && result.code === 0) {
        // 立即刷新列表
        await loadPromotionData(true);
        console.log("广告删除成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 删除成功！广告"${promotionData.promotion_name}"已被删除`,
          4000
        );
      } else {
        toast.error(`❌ 删除广告失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("删除广告失败:", error);
      toast.error(`❌ 删除广告失败: ${error.message}`, 5000);
    }
  };

  // 批量删除
  const batchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.warning("请选择要删除的广告");
      return;
    }

    try {
      // 构建BatchDeletePromotionAsyncReq需要的请求结构
      const accountDetailList = selectedItems.map((promotionId) => {
        const promotionData = data.find(
          (item) => item.promotion_id === promotionId
        );
        return {
          advertiser_id: String(promotionData?.advertiser_id || ""), // string
          id: String(promotionData?.promotion_id || ""), // string
          name: String(promotionData?.promotion_name || ""), // string
        };
      });

      const req = {
        account_id: selectedAccount?.account_id || 0, // int64
        account_detail_list: accountDetailList,
      };

      console.log("调用BatchDeletePromotionAsync，参数:", req);

      // 添加加载提示
      toast.info(`正在批量删除 ${selectedItems.length} 个广告，请稍候...`);

      const result = await PromotionService.BatchDeletePromotionAsync(req);

      // 打印批量删除广告响应数据
      console.log("批量删除广告响应数据:", result);

      if (result && result.code === 0) {
        // 立即清空选中项
        setSelectedItems([]);

        // 立即刷新列表
        await loadPromotionData(true);
        console.log(`批量删除 ${selectedItems.length} 个广告成功，列表已刷新`);

        // 成功提示
        toast.success(
          `🎉 批量删除成功！已删除 ${selectedItems.length} 个广告`,
          4000
        );
      } else {
        toast.error(`❌ 批量删除广告失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("批量删除广告失败:", error);
      toast.error(`❌ 批量删除广告失败: ${error.message}`, 5000);
    }
  };

  // 更新广告状态
  const updatePromotionStatus = async (promotion, status) => {
    try {
      // 构建状态映射：promotionId -> status
      const statusMap = {};
      statusMap[String(promotion.promotion_id)] = status;

      const req = {
        account_id: selectedAccount?.account_id || 0,
        advertiser_id: parseInt(promotion.advertiser_id) || 0,
        status_map: statusMap,
      };

      console.log("调用UpdatePromotionStatus，参数:", req);

      // 添加加载提示
      toast.info("正在更新广告状态，请稍候...");

      const result = await PromotionService.UpdatePromotionStatus(req);

      // 打印更新广告状态响应数据
      console.log("更新广告状态响应数据:", result);

      if (result && result.code === 0) {
        // 立即刷新列表
        await loadPromotionData(true);
        console.log("广告状态更新成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 状态更新成功！广告"${promotion.promotion_name}"状态已更新`,
          4000
        );
      } else {
        toast.error(`❌ 状态更新失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("状态更新失败:", error);
      toast.error(`❌ 状态更新失败: ${error.message}`, 5000);
    }
  };

  // 开始编辑广告名称
  const handleEditPromotionName = (promotion) => {
    setEditingPromotionId(promotion.promotion_id);
    setEditingPromotionName(promotion.promotion_name || "");
  };

  // 取消编辑广告名称
  const handleCancelEditPromotionName = () => {
    setEditingPromotionId(null);
    setEditingPromotionName("");
  };

  // 保存广告名称
  const handleSavePromotionName = async (promotion) => {
    if (!editingPromotionName.trim()) {
      toast.warning("广告名称不能为空");
      return;
    }

    if (editingPromotionName === promotion.promotion_name) {
      // 名称没有变化，直接取消编辑
      handleCancelEditPromotionName();
      return;
    }

    try {
      const req = {
        account_id: selectedAccount?.account_id || 0,
        advertiser_id: parseInt(promotion.advertiser_id) || 0,
        promotion_id: String(promotion.promotion_id || ""),
        name: editingPromotionName.trim(),
      };

      console.log("调用UpdatePromotionName，参数:", req);

      // 添加加载提示
      toast.info("正在修改广告名称，请稍候...");

      const result = await PromotionService.UpdatePromotionName(req);

      // 打印更新广告名称响应数据
      console.log("更新广告名称响应数据:", result);

      if (result && result.code === 0) {
        // 取消编辑状态
        handleCancelEditPromotionName();

        // 立即刷新列表
        await loadPromotionData(true);
        console.log("广告名称修改成功，列表已刷新");

        // 成功提示
        toast.success(
          `🎉 修改成功！广告名称已更新为"${editingPromotionName}"`,
          4000
        );
      } else {
        toast.error(`❌ 修改广告名称失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("修改广告名称失败:", error);
      toast.error(`❌ 修改广告名称失败: ${error.message}`, 5000);
    }
  };

  // 处理编辑输入框的键盘事件
  const handleEditInputKeyDown = (e, promotion) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSavePromotionName(promotion);
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancelEditPromotionName();
    }
  };

  // 选择处理 - 与其他页面保持一致
  const toggleSelectItem = (promotionIdOrIds, isCtrlKey = false) => {
    // 如果传入的是数组（批量选择）
    if (Array.isArray(promotionIdOrIds)) {
      const newItems = promotionIdOrIds;
      if (isCtrlKey) {
        // Ctrl+拖拽：合并选择
        setSelectedItems((prev) => [...new Set([...prev, ...newItems])]);
      } else {
        // 普通拖拽：替换选择
        setSelectedItems(newItems);
      }
    } else {
      // 单项选择逻辑
      const promotionId = promotionIdOrIds;
      setSelectedItems((prev) => {
        if (isCtrlKey) {
          // Ctrl+单击：切换选择状态（多选模式）
          if (prev.includes(promotionId)) {
            return prev.filter((item) => item !== promotionId);
          } else {
            return [...prev, promotionId];
          }
        } else {
          // 普通单击：单选模式，清空其他选择，只选中当前项
          if (prev.includes(promotionId) && prev.length === 1) {
            // 如果当前项已选中且只选中了这一项，则取消选择
            return [];
          } else {
            // 否则清空其他选择，只选中当前项
            return [promotionId];
          }
        }
      });
    }
  };

  // 为toggleSelectItem添加批量选择标识，供VirtualDataTable识别
  toggleSelectItem.isBatchSelect = true;

  const toggleSelectAll = () => {
    if (
      selectedItems.length === filteredData.length &&
      filteredData.length > 0
    ) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredData.map((item) => item.promotion_id));
    }
  };

  // 账号切换处理
  const handleAccountChange = useCallback(
    (account) => {
      if (selectedAccount?.account_id !== account?.account_id) {
        // 清空选中项和项目筛选条件（搜索条件由useManagerPage处理）
        setSelectedItems([]);
        setProjectInfo({}); // 清空项目筛选条件
        setAccount(account);
      }
    },
    [selectedAccount, setAccount]
  );

  // 注意：搜索处理现在由useManagerPage提供

  // 切换显示已删除项
  const toggleShowDeleted = () => {
    setShowDeleted((prev) => !prev);
  };

  // 处理排序
  const handleSort = useCallback((field) => {
    setSortConfig((prev) => {
      // 如果点击的是当前排序字段
      if (prev.field === field) {
        // 循环切换：desc -> asc -> null
        if (prev.order === "desc") {
          return { field, order: "asc" };
        } else if (prev.order === "asc") {
          return { field: null, order: null };
        }
      }
      // 如果点击的是新字段，默认降序
      return { field, order: "desc" };
    });
  }, []);

  // 获取排序后的数据
  const getSortedData = useCallback(
    (dataToSort) => {
      if (!sortConfig.field || !sortConfig.order) {
        return dataToSort;
      }

      const sorted = [...dataToSort].sort((a, b) => {
        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];

        // 处理空值
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        // 特殊处理时间字段 - 使用字符串比较，因为ISO 8601格式可以直接字典序排序
        if (
          sortConfig.field === "create_time" ||
          sortConfig.field === "update_time"
        ) {
          const aTimeStr = String(aValue);
          const bTimeStr = String(bValue);

          return sortConfig.order === "asc"
            ? aTimeStr.localeCompare(bTimeStr)
            : bTimeStr.localeCompare(aTimeStr);
        }

        // 数值比较
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return sortConfig.order === "asc" ? aNum - bNum : bNum - aNum;
        }

        // 字符串比较（备用）
        return sortConfig.order === "asc"
          ? String(aValue).localeCompare(String(bValue))
          : String(bValue).localeCompare(String(aValue));
      });

      return sorted;
    },
    [sortConfig]
  );

  // 过滤数据 - 根据showDeleted状态过滤已删除项
  const filteredData = useMemo(() => {
    // 确保 data 不为 undefined
    const safeData = data || [];

    if (showDeleted) {
      // 显示所有数据，包括已删除的
      return safeData;
    } else {
      // 隐藏已删除的数据（delete_time字段有值的数据）
      return safeData.filter((item) => !item.delete_time);
    }
  }, [data, showDeleted]);

  // 同步广告数据
  const refreshPromotions = async () => {
    if (!selectedAccount?.account_id) {
      toast.warning("请先选择账户");
      return;
    }

    // 设置同步loading状态
    setSyncLoading(true);

    try {
      // 添加加载提示
      toast.info("正在同步广告数据，请稍候...", 3000);

      const result = await PromotionService.RefreshPromotions(
        selectedAccount.account_id
      );

      console.log("同步广告数据响应:", result);

      if (result && result.code === 0) {
        // 立即刷新列表
        await loadPromotionData(true);
        console.log("广告数据同步成功，列表已刷新");

        // 成功提示
        toast.success(`🎉 同步成功！${result.data || "广告数据已同步"}`, 4000);
      } else {
        toast.error(`❌ 同步广告数据失败: ${result?.msg || "未知错误"}`, 5000);
      }
    } catch (error) {
      console.error("同步广告数据失败:", error);
      toast.error(`❌ 同步广告数据失败: ${error.message}`, 5000);
    } finally {
      // 清除同步loading状态
      setSyncLoading(false);
    }
  };

  // 复制广告
  const copyPromotion = async (
    promotion,
    newPromotionName,
    newProjectId,
    videoMaterialInfoList = []
  ) => {
    // 参数验证
    if (!selectedAccount?.account_id) {
      toast.warning("请先选择账户");
      return;
    }

    if (!promotion) {
      toast.warning("请选择要复制的广告");
      return;
    }

    if (
      !newPromotionName ||
      typeof newPromotionName !== "string" ||
      !newPromotionName.trim()
    ) {
      toast.warning("请输入新广告名称");
      return;
    }

    if (!newProjectId) {
      toast.warning("请选择目标项目");
      return;
    }

    try {
      const req = {
        account_id: selectedAccount.account_id,
        advertiser_id: parseInt(promotion.advertiser_id) || 0,
        source_promotion_id: String(promotion.promotion_id || ""),
        new_promotion_name: newPromotionName.trim(),
        new_project_id: String(newProjectId),
        video_material_info: videoMaterialInfoList, // 传递视频素材信息
      };

      console.log("调用CopyPromotion，参数:", req);

      // 添加加载提示
      if (videoMaterialInfoList.length > 0) {
        toast.info(
          `正在复制广告并使用 ${videoMaterialInfoList.length} 个视频素材，请稍候...`
        );
      } else {
        toast.info("正在复制广告，请稍候...");
      }

      const result = await PromotionService.CopyPromotion(req);

      // 打印复制广告响应数据
      console.log("复制广告响应数据:", result);

      // 检查外层接口调用是否成功
      if (result && result.code === 0) {
        // 检查内层业务逻辑是否成功
        if (result.data && result.data.code === 0) {
          // 真正成功：外层和内层code都为0

          // 成功提示
          const promotionId = result.data?.data?.promotion_id || "";
          const successMsg =
            videoMaterialInfoList.length > 0
              ? `🎉 复制成功！新广告"${newPromotionName}"已创建并使用了 ${videoMaterialInfoList.length} 个视频素材，ID: ${promotionId}`
              : `🎉 复制成功！新广告"${newPromotionName}"已创建，ID: ${promotionId}`;

          toast.success(successMsg, 4000);

          // 等待2秒后进行数据同步
          setTimeout(async () => {
            try {
              // 显示同步提示
              toast.info("正在同步数据，请稍候...", 3000);

              // 调用数据同步
              const syncResult = await PromotionService.RefreshPromotions(
                selectedAccount.account_id
              );

              if (syncResult && syncResult.code === 0) {
                // 同步成功，刷新列表
                await loadPromotionData(true);
                toast.success(
                  `🎉 数据同步完成！${syncResult.data || "广告数据已同步"}`,
                  4000
                );
              } else {
                // 同步失败，但仍然刷新列表（因为复制已经成功）
                console.error("数据同步失败:", syncResult?.msg);
                await loadPromotionData(true);
                toast.warning(
                  `⚠️ 数据同步失败，但广告复制成功。${
                    syncResult?.msg || "未知错误"
                  }`,
                  5000
                );
              }
            } catch (syncError) {
              // 同步异常，但仍然刷新列表
              console.error("数据同步异常:", syncError);
              await loadPromotionData(true);
              toast.warning(
                `⚠️ 数据同步异常，但广告复制成功。${syncError.message}`,
                5000
              );
            }
          }, 2000);

          return result;
        } else {
          // 外层成功但内层失败：显示内层的错误信息
          const innerErrorMsg = result.data?.msg || "业务逻辑处理失败";
          console.error("复制广告业务逻辑失败:", {
            innerCode: result.data?.code,
            innerMsg: innerErrorMsg,
            requestId: result.data?.request_id,
          });
          toast.error(`❌ 复制广告失败: ${innerErrorMsg}`, 3000);
          return null;
        }
      } else {
        // 外层接口调用失败
        const outerErrorMsg = result?.msg || "接口调用失败";
        console.error("复制广告接口调用失败:", {
          outerCode: result?.code,
          outerMsg: outerErrorMsg,
        });
        toast.error(`❌ 复制广告失败: ${outerErrorMsg}`, 5000);
        return null;
      }
    } catch (error) {
      console.error("复制广告失败:", error);
      toast.error(`❌ 复制广告失败: ${error.message}`, 5000);
      return null;
    }
  };

  // 监听查询条件变化，自动加载数据 - 搜索由前端处理
  useEffect(() => {
    // 只有在有选中账户时才加载数据
    if (!selectedAccount?.account_id) return;

    // 跳过组件初始化时的多次渲染导致的重复请求
    if (isInitialMount.current) {
      isInitialMount.current = false;
      loadPromotionData(true);
      return;
    }

    // 仅当参数变化时加载数据
    const timer = setTimeout(() => {
      loadPromotionData();
    }, 100);

    return () => clearTimeout(timer);
  }, [
    selectedAccount?.account_id,
    projectInfo?.project_id,
    loadPromotionData,
  ]);

  return {
    // 状态
    loading,
    syncLoading,
    data: filteredData, // 使用过滤后的数据
    pagination,
    selectedItems,
    selectedAccount,
    projectInfo,
    editingPromotionId,
    editingPromotionName,
    setEditingPromotionName,
    confirmDialog,
    showDeleted,
    sortConfig,

    // 操作函数
    loadPromotionData,
    deletePromotion,
    batchDelete,
    updatePromotionStatus,
    refreshPromotions,
    copyPromotion,
    toggleSelectItem,
    toggleSelectAll,
    handleAccountChange,
    handleEditPromotionName,
    handleCancelEditPromotionName,
    handleSavePromotionName,
    handleEditInputKeyDown,
    toggleShowDeleted,
    handleSort,
    getSortedData,

    // 项目相关
    setProjectInfo,

    // 对话框控制
    setConfirmDialog,

    // 常量
    INITIAL_CONFIRM_DIALOG,
  };
};
