import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import {
  RefreshCw,
  Trash2,
  Download,
  ToggleLeft,
  ToggleRight,
  Edit,
  Copy,
  Eye,
  EyeOff,
  Search,
  CheckSquare,
  Square,
  X,
  Target,
  Info,
  DollarSign,
} from "lucide-react";
import {
  Button,
  Card,
  ConfirmDialog,
  VirtualDataTable,
  EmptyState,
  StatusBar,
  MobileStatsBar,
  HighlightText,
  FloatingSearchBox,
  Toolbar,
  SearchInput,
} from "../../components/ui";
import { usePromotionManager } from "./hooks";
import { createTableColumns } from "./components";
import {
  INITIAL_CONFIRM_DIALOG,
  PROMOTION_STATUS_START,
  DELIVERY_MODE,
} from "./constants";
import { getPromotionStartStatus } from "./utils";
import CopyPromotionDialog from "./components/CopyPromotionDialog";
import { toast } from "../../components/ui/Toast";
import { useManagerPage } from "../../hooks/useManagerPage";
import StrategyBindDialog from "../../components/ui/StrategyBindDialog";
import promotionService from "../../services/promotionService";
import UpdatePromotionBidDialog from "./components/UpdatePromotionBidDialog";
import { useCopyPromotionDetail } from "../../contexts/CopyPromotionContext";

/**
 * PromotionManager 广告管理页面
 * 支持广告列表查看、搜索、编辑、删除等功能
 */
function PromotionManager({ preSelectedAccount, selectedProject }) {
  // 添加样式定义
  React.useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .deleted-promotion-row {
        background: repeating-linear-gradient(
          45deg,
          #fef2f2 0px,
          #fef2f2 10px,
          #fee2e2 10px,
          #fee2e2 20px
        ) !important;
        border: 3px solid #dc2626 !important;
        border-left: 8px solid #dc2626 !important;
        border-radius: 4px !important;
        box-shadow: 
          inset 0 0 0 2px rgba(220, 38, 38, 0.3),
          0 2px 8px rgba(220, 38, 38, 0.2),
          0 0 0 1px rgba(220, 38, 38, 0.4) !important;
        opacity: 0.9;
        position: relative;
        margin: 2px 0 !important;
        transform: scale(1.01);
      }
      .deleted-promotion-row::before {
        content: '🗑️ 已删除';
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%) rotate(-15deg);
        font-size: 10px;
        font-weight: bold;
        z-index: 2;
        background: linear-gradient(135deg, #dc2626, #ef4444);
        color: white;
        border-radius: 12px;
        padding: 2px 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        border: 1px solid #991b1b;
        white-space: nowrap;
      }
      .deleted-promotion-row::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          135deg,
          transparent,
          transparent 5px,
          rgba(220, 38, 38, 0.1) 5px,
          rgba(220, 38, 38, 0.1) 10px
        );
        pointer-events: none;
        border-radius: 2px;
      }
      .deleted-promotion-row:hover {
        background: repeating-linear-gradient(
          45deg,
          #fee2e2 0px,
          #fee2e2 10px,
          #fecaca 10px,
          #fecaca 20px
        ) !important;
        border-color: #b91c1c !important;
        transform: scale(1.02) translateX(3px);
        transition: all 0.3s ease;
        box-shadow: 
          inset 0 0 0 2px rgba(185, 28, 28, 0.4),
          0 4px 12px rgba(185, 28, 28, 0.3),
          0 0 0 2px rgba(185, 28, 28, 0.5) !important;
      }
      .deleted-promotion-row .text-slate-900,
      .deleted-promotion-row .text-slate-500,
      .deleted-promotion-row .text-xs,
      .deleted-promotion-row span,
      .deleted-promotion-row div {
        color: #991b1b !important;
        text-decoration: line-through;
        text-decoration-color: rgba(153, 27, 27, 0.7);
        text-decoration-thickness: 2px;
        font-weight: 600 !important;
        text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
      }
      .deleted-promotion-row td {
        border-color: rgba(220, 38, 38, 0.3) !important;
        padding: 8px 6px !important;
        background: rgba(254, 242, 242, 0.5) !important;
      }
      .deleted-promotion-row .inline-flex {
        background: rgba(220, 38, 38, 0.1) !important;
        border-color: rgba(220, 38, 38, 0.4) !important;
      }
      /* 为删除时间列添加特殊样式 */
      .deleted-promotion-row [title*="删除于"] {
        background: linear-gradient(135deg, #dc2626, #ef4444) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 6px !important;
        font-weight: bold !important;
        text-decoration: none !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const {
    loading,
    syncLoading,
    data,
    pagination,
    selectedItems,
    selectedAccount,
    projectInfo,
    editingPromotionId,
    editingPromotionName,
    setEditingPromotionName,
    confirmDialog,
    sortConfig,

    loadPromotionData,
    deletePromotion,
    batchDelete,
    updatePromotionStatus,
    refreshPromotions,
    copyPromotion,
    toggleSelectItem,
    toggleSelectAll,
    handleAccountChange,
    handleEditPromotionName,
    handleCancelEditPromotionName,
    handleSavePromotionName,
    handleEditInputKeyDown,
    handleSort,
    getSortedData,

    setProjectInfo,

    setConfirmDialog,
  } = usePromotionManager(selectedProject);

  // 获取复制广告Context的方法
  const {
    copyPromotion: copyPromotionToGlobal,
    pastePromotion: pastePromotionFromGlobal,
    hasCopiedPromotion,
    clearCopiedPromotion,
  } = useCopyPromotionDetail();

  // 复制广告对话框状态
  const [copyDialog, setCopyDialog] = useState({
    isOpen: false,
    promotion: null,
    promotionDetail: null,
    targetProjectId: selectedProject?.project_id,
  });

  // 修改广告出价对话框状态
  const [updateBidDialog, setUpdateBidDialog] = useState({
    isOpen: false,
    promotions: [],
  });
  const [bidUpdateLoading, setBidUpdateLoading] = useState(false);

  // 使用通用管理页面Hook - 纯前端筛选
  const {
    searchTerm,
    showDeleted,
    showSearchDialog,
    scrollContainerRef,
    filteredData,
    handleSearch,
    handleShowSearch,
    handleCloseSearch,
    handleScroll,
    handleShowDeleted,
  } = useManagerPage({
    data,
    onSearch: null, // 不触发服务器端搜索，纯前端筛选
    searchFields: ["promotion_name", "promotion_id", "project_id"],
    itemLabel: "个广告",
    pageKey: "promotion",
    onClearSelection: () => {
      if (selectedItems.length > 0) {
        toggleSelectAll();
      }
    },
    onToggleSelectAll: toggleSelectAll,
    onBatchDeleteConfirm: () => {
      if (selectedItems.length === 0) return;
      setConfirmDialog({
        isOpen: true,
        title: "批量删除",
        message: `确定要删除选中的 ${selectedItems.length} 个广告吗？此操作无法撤销。`,
        onConfirm: () => {
          batchDelete();
          setConfirmDialog(INITIAL_CONFIRM_DIALOG);
        },
      });
    },
  });

  // 删除确认
  const handleDeleteConfirm = (promotion) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认删除",
      message: `确定要删除广告"${promotion.promotion_name}"吗？此操作无法撤销。`,
      onConfirm: () => {
        deletePromotion(promotion);
        setConfirmDialog(INITIAL_CONFIRM_DIALOG);
      },
    });
  };

  // 批量删除确认
  const handleBatchDeleteConfirm = () => {
    if (selectedItems.length === 0) return;

    setConfirmDialog({
      isOpen: true,
      title: "批量删除",
      message: `确定要删除选中的 ${selectedItems.length} 个广告吗？此操作无法撤销。`,
      onConfirm: () => {
        batchDelete();
        setConfirmDialog(INITIAL_CONFIRM_DIALOG);
      },
    });
  };

  // 处理复制按钮点击（存储广告信息到全局key）
  const handleCopyPromotion = async (promotion) => {
    try {
      console.log("开始复制广告:", promotion);

      // 获取广告详情
      const accountId = parseInt(promotion.account_id);
      const advertiserId = parseInt(promotion.advertiser_id);
      const promotionId = promotion.promotion_id;

      console.log("获取广告详情用于复制，参数:", {
        accountId,
        advertiserId,
        promotionId,
      });

      const response = await promotionService.GetPromotionInfo({
        account_id: accountId,
        advertiser_id: advertiserId,
        promotion_ids: [promotionId.toString()],
      });
      console.log("广告详情返回数据:", response);

      // 解析广告详情数据
      let promotionData = null;
      console.log("开始解析广告详情数据...");

      if (
        response &&
        response.code === 0 &&
        response.data &&
        typeof response.data === "object" &&
        !Array.isArray(response.data)
      ) {
        // 检查是否有双层嵌套的数据结构
        if (response.data.code === 0 && response.data.data) {
          // 双层嵌套格式：response.data.data 包含广告详情
          console.log("使用双层嵌套数据格式：response.data.data");
          const promotionIdStr = promotionId.toString();
          promotionData = response.data.data[promotionIdStr];

          if (!promotionData) {
            console.error("在返回数据中找不到广告ID:", promotionIdStr);
            console.error(
              "可用的广告ID:",
              Object.keys(response.data.data || {})
            );
          }
        } else {
          // 单层格式：response.data 直接包含广告详情
          console.log("使用单层数据格式：response.data");
          const promotionIdStr = promotionId.toString();
          promotionData = response.data[promotionIdStr];

          if (!promotionData) {
            console.error("在返回数据中找不到广告ID:", promotionIdStr);
            console.error("可用的广告ID:", Object.keys(response.data || {}));
          }
        }
      } else if (
        response &&
        response.code === 0 &&
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        promotionData = response.data[0];
      } else if (
        response &&
        response.code === 0 &&
        Array.isArray(response.data)
      ) {
        promotionData = response.data;
      } else {
        console.log("无法解析数据格式，response:", response);
      }

      console.log("解析后的 promotionData:", promotionData);

      if (promotionData) {
        // 存储广告信息到全局key
        console.log("准备存储广告到全局key");
        copyPromotionToGlobal(promotionData, promotion);
        console.log("已存储广告到全局key:", { promotionData, promotion });
        toast.success(
          `已复制广告"${promotion.promotion_name}"，可在右键菜单中粘贴`
        );
      } else {
        console.error("promotionData 为空，无法复制");
        toast.error("获取广告详情失败，无法复制");
      }
    } catch (error) {
      console.error("获取广告详情失败:", error);
      console.error("错误详情:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      toast.error(`获取广告详情失败: ${error.message}`);
    }
  };

  // 处理粘贴广告（从全局key获取广告信息并打开复制对话框）
  const handlePastePromotion = () => {
    const { promotionDetail, promotionInfo } = pastePromotionFromGlobal();
    if (promotionDetail && promotionInfo) {
      console.log(`handlePastePromotion - selectedProject:`, selectedProject);
      console.log(
        `handlePastePromotion - targetProjectId:`,
        selectedProject?.project_id
      );
      setCopyDialog({
        isOpen: true,
        promotion: promotionInfo,
        promotionDetail: promotionDetail,
        targetProjectId: selectedProject?.project_id,
      });
    } else {
      toast.error("没有可粘贴的广告信息");
    }
  };

  // 关闭复制广告对话框
  const handleCloseCopyDialog = () => {
    setCopyDialog({
      isOpen: false,
      promotion: null,
      promotionDetail: null,
      targetProjectId: selectedProject?.project_id,
    });
  };

  // 处理修改出价确认
  const handleUpdateBidConfirm = async (bidData) => {
    try {
      setBidUpdateLoading(true);

      // 获取广告信息以构建请求参数
      const firstPromotion = updateBidDialog.promotions[0];
      if (!firstPromotion) {
        throw new Error("没有选中的广告");
      }

      // 调用修改出价接口
      const result = await promotionService.UpdatePromotionBid({
        account_id: parseInt(firstPromotion.account_id),
        advertiser_id: parseInt(firstPromotion.advertiser_id),
        bids: bidData.bids,
      });

      if (result.code === 0) {
        toast.success(
          `成功修改 ${updateBidDialog.promotions.length} 个广告的出价`
        );
        setUpdateBidDialog({ isOpen: false, promotions: [] });
        // 刷新数据
        loadPromotionData();
      } else {
        throw new Error(result.msg || "修改出价失败");
      }
    } catch (error) {
      console.error("修改出价失败:", error);
      toast.error(`修改出价失败: ${error.message}`);
    } finally {
      setBidUpdateLoading(false);
    }
  };

  // 打开修改出价对话框
  const handleShowUpdateBid = () => {
    const selectedPromotions = data.filter((item) =>
      selectedItems.includes(item.promotion_id)
    );

    if (selectedPromotions.length === 0) {
      toast.warning("请先选择要修改出价的广告");
      return;
    }

    setUpdateBidDialog({
      isOpen: true,
      promotions: selectedPromotions,
    });
  };

  // 执行复制广告
  const handleCopySuccess = async (copyTasksOrSingleTask) => {
    const copyTasks = Array.isArray(copyTasksOrSingleTask)
      ? copyTasksOrSingleTask
      : [copyTasksOrSingleTask];

    console.log(`开始处理${copyTasks.length}个复制任务`);

    const results = await processCopyTasks(copyTasks);
    displayCopyResults(results, copyTasks.length);

    // 复制成功后，不清空全局key中的广告信息，允许重复粘贴
    toast.success("复制完成，可继续粘贴该广告");
  };

  // 单独的视频上传函数
  const uploadVideosOnce = async (videoFiles, promotion) => {
    console.log("开始统一上传视频文件:", videoFiles.length);
    const uploadedMaterials = [];

    if (!videoFiles || videoFiles.length === 0) {
      return uploadedMaterials;
    }

    toast.info(`⚡ 正在上传 ${videoFiles.length} 个视频文件...`);

    for (let i = 0; i < videoFiles.length; i++) {
      const videoFile = videoFiles[i];
      console.log(
        `正在上传视频 ${i + 1}/${videoFiles.length}:`,
        videoFile.name
      );

      try {
        const { AdvertiserService } = await import("../../services/api.js");
        const uploadResult = await AdvertiserService.UploadVideoAsMaterial(
          selectedAccount.account_id,
          parseInt(promotion.advertiser_id),
          videoFile.path
        );

        console.log(`视频上传结果:`, uploadResult);

        if (uploadResult && uploadResult.code === 0 && uploadResult.data) {
          const videoMaterialInfo = {
            image_mode: uploadResult.data.image_mode,
            image_info: uploadResult.data.image_info,
            video_info: uploadResult.data.video_info,
            copy_mode: 0,
          };

          uploadedMaterials.push(videoMaterialInfo);
          console.log(
            `视频素材添加成功 ${i + 1}/${videoFiles.length}:`,
            videoFile.name
          );

          // 显示上传进度
          toast.info(
            `⚡ 上传进度: ${i + 1}/${videoFiles.length} - ${videoFile.name}`
          );
        } else {
          const errorMsg = `视频上传失败: ${videoFile.name} - ${
            uploadResult?.msg || "未知错误"
          }`;
          console.error(errorMsg);
          toast.error(errorMsg);
          throw new Error(errorMsg);
        }
      } catch (error) {
        const errorMsg = `视频上传异常: ${videoFile.name} - ${error.message}`;
        console.error(errorMsg, error);
        toast.error(errorMsg);
        throw new Error(errorMsg);
      }
    }

    if (uploadedMaterials.length > 0) {
      toast.success(
        `✅ 视频上传完成！成功上传 ${uploadedMaterials.length}/${videoFiles.length} 个视频`
      );
    }

    return uploadedMaterials;
  };

  // 处理复制任务列表
  const processCopyTasks = async (copyTasks) => {
    let successCount = 0;
    let failedTasks = [];

    // 检查是否有新视频需要上传（"原样复制"模式的优化）
    // 排除裂变复制任务，因为裂变复制的每个任务都有不同的变体视频，不能合并处理
    const tasksWithNewVideos = copyTasks.filter(
      (task) =>
        task.videoFiles && task.videoFiles.length > 0 && !task.isVariationCopy
    );

    if (tasksWithNewVideos.length > 0 && copyTasks.length > 1) {
      console.log("检测到原样复制模式下有新视频需要上传，使用优化流程...");
      const optimizedResult = await processOriginalCopyWithUpload(copyTasks);

      successCount += optimizedResult.successCount;
      failedTasks.push(...optimizedResult.failedTasks);
    } else {
      // 原有逻辑：逐个处理复制任务（单个任务或不涉及新视频上传）
      for (let i = 0; i < copyTasks.length; i++) {
        const taskResult = await processSingleCopyTask(copyTasks[i], i + 1);

        if (taskResult.success) {
          successCount++;
        } else {
          failedTasks.push(taskResult.error);
        }
      }
    }

    return { successCount, failedTasks };
  };

  // 处理原样复制模式的上传优化
  const processOriginalCopyWithUpload = async (copyTasks) => {
    console.log("开始原样复制模式的上传优化流程...");

    let successCount = 0;
    let failedTasks = [];

    try {
      // 第一步：收集所有需要上传的视频文件（去重）
      const allVideoFiles = [];
      const filePathSet = new Set();

      copyTasks.forEach((task) => {
        if (task.videoFiles && task.videoFiles.length > 0) {
          task.videoFiles.forEach((file) => {
            if (!filePathSet.has(file.path)) {
              filePathSet.add(file.path);
              allVideoFiles.push(file);
            }
          });
        }
      });

      // 第二步：统一上传所有新视频（只上传一次）
      let sharedUploadedMaterials = [];
      if (allVideoFiles.length > 0) {
        const firstTask = copyTasks[0];
        console.log(`开始统一上传 ${allVideoFiles.length} 个新视频文件...`);
        toast.info(
          `⚡ 原样复制优化：正在上传 ${allVideoFiles.length} 个视频文件，将用于所有 ${copyTasks.length} 个广告`
        );

        try {
          sharedUploadedMaterials = await uploadVideosOnce(
            allVideoFiles,
            firstTask.promotion
          );
          console.log(
            "统一上传完成，上传的素材数量:",
            sharedUploadedMaterials.length
          );

          if (sharedUploadedMaterials.length > 0) {
            toast.success(
              `✅ 统一上传成功！${sharedUploadedMaterials.length} 个视频文件已上传，正在创建 ${copyTasks.length} 个广告...`
            );
          }
        } catch (uploadError) {
          console.error("统一上传失败:", uploadError);
          toast.error(`❌ 统一上传失败: ${uploadError.message}`);

          // 上传失败时，所有广告都失败
          copyTasks.forEach((task, index) => {
            failedTasks.push({
              index: index + 1,
              name: task.newPromotionName,
              error: `统一上传失败: ${uploadError.message}`,
            });
          });

          return { successCount: 0, failedTasks };
        }
      }

      // 第三步：为每个任务创建广告（使用共享的上传素材）
      for (let i = 0; i < copyTasks.length; i++) {
        const task = copyTasks[i];
        console.log(
          `创建第 ${i + 1}/${copyTasks.length} 个广告: ${task.newPromotionName}`
        );

        try {
          // 准备该任务的视频素材列表
          let taskVideoMaterials = [];

          // 添加已有视频素材
          if (
            task.existingVideoMaterials &&
            task.existingVideoMaterials.length > 0
          ) {
            taskVideoMaterials.push(...task.existingVideoMaterials);
            console.log(
              `任务${i + 1}: 添加已有视频素材数量:`,
              task.existingVideoMaterials.length
            );
          }

          // 添加共享的上传素材
          if (sharedUploadedMaterials.length > 0) {
            taskVideoMaterials.push(...sharedUploadedMaterials);
            console.log(
              `任务${i + 1}: 添加共享上传素材数量:`,
              sharedUploadedMaterials.length
            );
          }

          console.log(`任务${i + 1}: 视频素材总数:`, taskVideoMaterials.length);

          // 复制广告（无需再次上传视频）
          const result = await copyPromotion(
            task.promotion,
            task.newPromotionName,
            task.targetProjectId,
            taskVideoMaterials
          );

          if (result) {
            console.log(
              `✅ 第 ${i + 1} 个广告创建成功: ${task.newPromotionName}`
            );
            successCount++;
          } else {
            console.error(
              `❌ 第 ${i + 1} 个广告创建失败: ${task.newPromotionName}`
            );
            failedTasks.push({
              index: i + 1,
              name: task.newPromotionName,
              error: "广告创建失败",
            });
          }
        } catch (error) {
          console.error(
            `❌ 第 ${i + 1} 个广告创建异常: ${task.newPromotionName}`,
            error
          );
          failedTasks.push({
            index: i + 1,
            name: task.newPromotionName,
            error: error.message,
          });
        }
      }

      // 显示优化结果
      const savedUploads =
        allVideoFiles.length > 0
          ? (copyTasks.length - 1) * allVideoFiles.length
          : 0;
      if (successCount === copyTasks.length) {
        toast.success(
          `🎉 原样复制优化完成！成功创建 ${successCount} 个广告${
            savedUploads > 0 ? `，节省 ${savedUploads} 次重复上传` : ""
          }`
        );
      } else if (successCount > 0) {
        toast.warning(
          `⚠️ 部分成功：创建了 ${successCount}/${copyTasks.length} 个广告`
        );
      } else {
        toast.error(`❌ 原样复制失败：没有成功创建任何广告`);
      }
    } catch (error) {
      console.error("原样复制优化流程异常:", error);
      toast.error(`❌ 原样复制优化流程异常: ${error.message}`);

      // 异常情况下，所有广告都失败
      copyTasks.forEach((task, index) => {
        failedTasks.push({
          index: index + 1,
          name: task.newPromotionName,
          error: `原样复制优化流程异常: ${error.message}`,
        });
      });
    }

    return { successCount, failedTasks };
  };

  // 处理单个复制任务
  const processSingleCopyTask = async (copyParams, taskIndex) => {
    const {
      promotion,
      newPromotionName,
      targetProjectId,
      videoFiles,
      existingVideoMaterials,
    } = copyParams;

    console.log(`处理第${taskIndex}个任务: ${newPromotionName}`);

    // 参数验证
    const validationError = validateCopyParams(copyParams, taskIndex);
    if (validationError) {
      return { success: false, error: validationError };
    }

    try {
      // 准备视频素材
      const videoMaterialInfoList = await prepareVideoMaterials(
        existingVideoMaterials,
        videoFiles,
        taskIndex,
        promotion
      );

      console.log(
        `任务${taskIndex}: 开始复制广告，视频素材总数:`,
        videoMaterialInfoList.length
      );

      // 复制广告
      const result = await copyPromotion(
        promotion,
        newPromotionName,
        targetProjectId,
        videoMaterialInfoList
      );

      if (result) {
        console.log(`任务${taskIndex}: 复制成功 - ${newPromotionName}`);
        return { success: true };
      } else {
        return {
          success: false,
          error: {
            index: taskIndex,
            name: newPromotionName,
            error: "复制失败",
          },
        };
      }
    } catch (error) {
      console.error(`任务${taskIndex} 复制失败:`, error);
      return {
        success: false,
        error: {
          index: taskIndex,
          name: newPromotionName,
          error: error.message,
        },
      };
    }
  };

  // 验证复制参数
  const validateCopyParams = (params, taskIndex) => {
    const { promotion, newPromotionName, targetProjectId } = params;

    if (!promotion) {
      console.error(`任务${taskIndex}: promotion参数为空`);
      return {
        index: taskIndex,
        name: "undefined",
        error: "promotion参数为空",
      };
    }

    if (
      !newPromotionName ||
      typeof newPromotionName !== "string" ||
      !newPromotionName.trim()
    ) {
      console.error(
        `任务${taskIndex}: newPromotionName参数无效:`,
        newPromotionName
      );
      return {
        index: taskIndex,
        name: newPromotionName || "undefined",
        error: "newPromotionName参数无效",
      };
    }

    if (!targetProjectId) {
      console.error(`任务${taskIndex}: targetProjectId参数为空`);
      return {
        index: taskIndex,
        name: newPromotionName,
        error: "targetProjectId参数为空",
      };
    }

    return null;
  };

  // 准备视频素材
  const prepareVideoMaterials = async (
    existingVideoMaterials,
    videoFiles,
    taskIndex,
    promotion,
    skipUpload = false
  ) => {
    let allVideoMaterialInfoList = [];

    // 添加已有视频素材
    if (existingVideoMaterials && existingVideoMaterials.length > 0) {
      allVideoMaterialInfoList = [...existingVideoMaterials];
      console.log(
        `任务${taskIndex}: 使用已有视频素材，数量:`,
        existingVideoMaterials.length
      );
    }

    // 仅在非跳过上传模式下上传新视频文件
    if (!skipUpload && videoFiles && videoFiles.length > 0) {
      console.log(
        `任务${taskIndex}: 开始上传新视频素材，数量:`,
        videoFiles.length
      );

      const uploadedMaterials = await uploadVideoFiles(
        videoFiles,
        taskIndex,
        promotion
      );
      allVideoMaterialInfoList.push(...uploadedMaterials);
    } else if (skipUpload && videoFiles && videoFiles.length > 0) {
      console.log(
        `任务${taskIndex}: 跳过视频上传（已在优化流程中上传），视频文件数量:`,
        videoFiles.length
      );
    }

    return allVideoMaterialInfoList;
  };

  // 上传视频文件
  const uploadVideoFiles = async (videoFiles, taskIndex, promotion) => {
    const uploadedMaterials = [];

    // 显示开始上传的总体提示
    toast.info(`开始上传 ${videoFiles.length} 个视频文件...`);

    for (let i = 0; i < videoFiles.length; i++) {
      const videoFile = videoFiles[i];
      console.log(
        `任务${taskIndex}: 正在上传视频 ${i + 1}/${videoFiles.length}:`,
        videoFile.name
      );

      try {
        const { AdvertiserService } = await import("../../services/api.js");
        const uploadResult = await AdvertiserService.UploadVideoAsMaterial(
          selectedAccount.account_id,
          parseInt(promotion.advertiser_id),
          videoFile.path
        );

        console.log(`任务${taskIndex}: 视频上传结果:`, uploadResult);

        if (uploadResult && uploadResult.code === 0 && uploadResult.data) {
          const videoMaterialInfo = {
            image_mode: uploadResult.data.image_mode,
            image_info: uploadResult.data.image_info,
            video_info: uploadResult.data.video_info,
            copy_mode: 0,
          };

          uploadedMaterials.push(videoMaterialInfo);
          console.log(
            `任务${taskIndex}: 视频素材添加成功 ${i + 1}/${videoFiles.length}:`,
            videoFile.name
          );
        } else {
          const errorMsg = `视频上传失败: ${videoFile.name} - ${
            uploadResult?.msg || "未知错误"
          }`;
          console.error(`任务${taskIndex}: ${errorMsg}`);

          // 立即显示视频上传失败的toast提示
          toast.error(errorMsg);

          throw new Error(errorMsg);
        }
      } catch (error) {
        const errorMsg = `视频上传异常: ${videoFile.name} - ${error.message}`;
        console.error(`任务${taskIndex}: ${errorMsg}`, error);

        // 立即显示视频上传异常的toast提示
        toast.error(errorMsg);

        throw new Error(errorMsg);
      }
    }

    // 所有视频上传完成后的提示
    if (uploadedMaterials.length > 0) {
      toast.success(
        `视频上传完成！成功上传 ${uploadedMaterials.length}/${videoFiles.length} 个视频`
      );
    }

    return uploadedMaterials;
  };

  // 显示复制结果
  const displayCopyResults = (results, totalTasks) => {
    const { successCount, failedTasks } = results;

    if (totalTasks !== 1) {
      // 批量复制
      if (successCount === totalTasks) {
        toast.success(`批量复制成功！共复制了 ${successCount} 个广告`);
      } else if (successCount > 0) {
        toast.warning(
          `部分复制成功！成功 ${successCount} 个，失败 ${failedTasks.length} 个`
        );
        logFailedTasks(failedTasks);
      } else {
        toast.error(`批量复制失败！所有 ${totalTasks} 个广告都复制失败`);
      }
    }
  };

  // 记录失败任务
  const logFailedTasks = (failedTasks) => {
    failedTasks.forEach((task) => {
      console.error(`任务${task.index} (${task.name}) 复制失败:`, task.error);
    });
  };

  // 获取排序后的数据
  const sortedData = useMemo(() => {
    // 确保 filteredData 不为 undefined
    const safeFilteredData = filteredData || [];
    return getSortedData(safeFilteredData);
  }, [filteredData, getSortedData]);

  // 计算搜索匹配数量（仅考虑搜索，不考虑其他筛选）
  const searchMatchCount = useMemo(() => {
    if (!searchTerm || !searchTerm.trim()) {
      return data.length;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    const searchFields = ["promotion_name", "promotion_id", "project_id"];

    return data.filter((item) => {
      return searchFields.some((field) => {
        const value = item[field];
        if (!value) return false;
        return value.toString().toLowerCase().includes(searchLower);
      });
    }).length;
  }, [data, searchTerm]);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+A 全选功能
      if (event.ctrlKey && event.key === "a") {
        event.preventDefault();
        // 确保当前焦点在表格区域内或没有其他输入框聚焦
        const activeElement = document.activeElement;
        const isInputElement =
          activeElement &&
          (activeElement.tagName === "INPUT" ||
            activeElement.tagName === "TEXTAREA" ||
            activeElement.isContentEditable);

        // 如果当前没有输入框聚焦，且有数据可选择，则执行全选
        if (!isInputElement && sortedData.length > 0) {
          toggleSelectAll();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [toggleSelectAll, sortedData]);

  // 创建表格列配置
  const columns = createTableColumns({
    selectedItems,
    editingPromotionId,
    editingPromotionName,
    setEditingPromotionName,
    pagination,
    onSelectItem: toggleSelectItem,
    onSelectAll: toggleSelectAll,
    onUpdateStatus: updatePromotionStatus,
    onDeletePromotion: handleDeleteConfirm,
    onEditPromotionName: handleEditPromotionName,
    onCancelEditPromotionName: handleCancelEditPromotionName,
    onSavePromotionName: handleSavePromotionName,
    onEditInputKeyDown: handleEditInputKeyDown,
    data: sortedData,
    showDeleted,
    sortConfig,
    handleSort,
    searchTerm,
  });

  /**
   * 复制到剪贴板的工具函数
   */
  const copyToClipboard = async (text, fieldName = "值") => {
    try {
      await navigator.clipboard.writeText(text);
      console.log(`已复制${fieldName}: ${text}`);
    } catch (err) {
      console.error("复制失败:", err);
      // 降级方案
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand("copy");
        console.log(`已复制${fieldName}(降级): ${text}`);
      } catch (fallbackErr) {
        console.error("降级复制也失败:", fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  // 在组件内增加状态
  const [showStrategyDialog, setShowStrategyDialog] = useState(false);
  const [selectedPromotionIds, setSelectedPromotionIds] = useState([]);
  const [currentStrategyIds, setCurrentStrategyIds] = useState("");
  const [currentPromotionName, setCurrentPromotionName] = useState("");

  // 右键菜单
  const contextMenuItems = (item, cellData) => {
    const copyMenuItems = buildCopyMenuItems(cellData);
    const itemSpecificMenuItems = buildItemSpecificMenuItems(item);
    const globalMenuItems = buildGlobalMenuItems(item);

    return [...copyMenuItems, ...itemSpecificMenuItems, ...globalMenuItems];
  };

  // 构建复制值菜单项
  const buildCopyMenuItems = (cellData) => {
    if (
      !cellData ||
      cellData.fieldValue === undefined ||
      cellData.fieldValue === null ||
      cellData.fieldValue === ""
    ) {
      return [];
    }

    const copyValue = cellData.fieldValue.toString();

    return [
      {
        key: "copyValue",
        label: "复制值",
        icon: <Copy size={14} />,
        color: "#3b82f6",
        onClick: () => copyToClipboard(copyValue, cellData.fieldName),
      },
      { type: "divider" },
    ];
  };

  // 构建项目特定的菜单项
  const buildItemSpecificMenuItems = (item) => {
    const hasCopied = hasCopiedPromotion();
    const promotionInfo = hasCopied
      ? `${hasCopied.promotion_name} (${hasCopied.promotion_id})`
      : "";

    return [
      {
        key: "edit",
        label: "编辑名称",
        icon: <Edit size={14} />,
        color: "#3b82f6",
        disabled: false,
        onClick: () => handleEditPromotionName(item),
      },
      {
        key: "copy",
        label: "复制广告",
        icon: <Copy size={14} />,
        color: "#10b981",
        disabled: false,
        onClick: () => handleCopyPromotion(item),
      },
      { type: "divider" },
      {
        key: "toggleStatus",
        label: getPromotionStatusInfo(item).toggleLabel,
        icon: getPromotionStatusInfo(item).toggleIcon,
        color: "#10b981",
        onClick: () =>
          updatePromotionStatus(item, getPromotionStatusInfo(item).newStatus),
      },
      { type: "divider" },
      {
        key: "delete",
        label: "删除广告",
        icon: <Trash2 size={14} />,
        color: "#ef4444",
        onClick: () => handleDeleteConfirm(item),
      },
      { type: "divider" },
    ];
  };

  // 构建全局菜单项
  const buildGlobalMenuItems = (item) => {
    // 全局复制/粘贴广告相关菜单项
    const globalCopyMenuItems = [];

    // 检查是否有复制的广告，如果有则显示粘贴按钮
    const hasCopied = hasCopiedPromotion();
    if (hasCopied) {
      const { promotionInfo } = pastePromotionFromGlobal();
      globalCopyMenuItems.push({
        key: "pastePromotion",
        label: `粘贴广告 (${promotionInfo?.promotion_name || "未知广告"})`,
        icon: <Copy size={14} />,
        color: "#8b5cf6",
        onClick: () => handlePastePromotion(),
      });
      globalCopyMenuItems.push({ type: "divider" });
    }

    return [
      {
        key: "search",
        label: "搜索 (Ctrl+F)",
        icon: <Search size={14} />,
        color: "#3b82f6",
        disabled: loading,
        onClick: () => handleShowSearch(),
      },
      { type: "divider" },
      {
        key: "refresh",
        label: "刷新数据",
        icon: <RefreshCw size={14} className={loading ? "animate-spin" : ""} />,
        color: "#3b82f6",
        disabled: loading,
        onClick: () => loadPromotionData(true),
      },
      {
        key: "sync",
        label: "同步数据",
        icon: (
          <Download size={14} className={syncLoading ? "animate-spin" : ""} />
        ),
        color: "#10b981",
        disabled: syncLoading || !selectedAccount,
        onClick: () => refreshPromotions(),
      },
      { type: "divider" },
      ...globalCopyMenuItems, // 添加全局复制/粘贴菜单项
      ...buildSelectionMenuItems(),
      { type: "divider" },
      {
        key: "toggleDeleted",
        label: showDeleted ? "隐藏已删除" : "显示已删除",
        icon: showDeleted ? <EyeOff size={14} /> : <Eye size={14} />,
        color: "#8b5cf6",
        onClick: () => handleShowDeleted(),
      },

      {
        key: "bindStrategy",
        label: "绑定策略",
        icon: <Target size={14} />,
        color: "#3b82f6",
        onClick: () => {
          handleBindStrategyDialog(item);
        },
      },
      { type: "divider" },
    ];
  };

  // 构建选择相关菜单项
  const buildSelectionMenuItems = () => {
    const isAllSelected = selectedItems.length === sortedData.length;

    return [
      {
        key: "toggleSelectAll",
        label: isAllSelected ? "取消全选 (Ctrl+A)" : "全选 (Ctrl+A)",
        icon: isAllSelected ? <Square size={14} /> : <CheckSquare size={14} />,
        color: "#8b5cf6",
        disabled: loading || sortedData.length === 0,
        onClick: () => toggleSelectAll(),
      },
      {
        key: "clearSelection",
        label: `清空选择 (${selectedItems.length})`,
        icon: <Trash2 size={14} />,
        color: "#ef4444",
        disabled: selectedItems.length === 0,
        onClick: () => {
          if (selectedItems.length > 0) {
            toggleSelectAll();
          }
        },
      },
      {
        key: "updateBid",
        label: `修改出价 (${selectedItems.length})`,
        icon: <DollarSign size={14} />,
        color: "#10b981",
        disabled: (() => {
          if (selectedItems.length === 0) return true;

          // 检查选中的广告中是否包含自动投放的广告
          const selectedPromotions = data.filter((item) =>
            selectedItems.includes(item.promotion_id)
          );

          const hasAutoDelivery = selectedPromotions.some(
            (promotion) => promotion.delivery_mode === DELIVERY_MODE.AUTOMATIC
          );

          return hasAutoDelivery;
        })(),
        onClick: () => handleShowUpdateBid(),
      },
      {
        key: "batchDelete",
        label: `批量删除 (${selectedItems.length})`,
        icon: <Trash2 size={14} />,
        color: "#ef4444",
        disabled: selectedItems.length === 0,
        onClick: () => handleBatchDeleteConfirm(),
      },
    ];
  };

  // 获取广告状态信息
  const getPromotionStatusInfo = (item) => {
    const currentStartStatus = getPromotionStartStatus(item);
    const isActive = currentStartStatus === PROMOTION_STATUS_START.ACTIVE;

    return {
      isActive,
      toggleLabel: isActive ? "禁用" : "启用",
      toggleIcon: isActive ? (
        <ToggleLeft size={14} />
      ) : (
        <ToggleRight size={14} />
      ),
      newStatus: isActive
        ? PROMOTION_STATUS_START.PAUSED
        : PROMOTION_STATUS_START.ACTIVE,
    };
  };

  // 处理绑定策略对话框
  const handleBindStrategyDialog = (item) => {
    const ids = selectedItems.length > 0 ? selectedItems : [item?.promotion_id];
    setSelectedPromotionIds(ids);
    setCurrentStrategyIds(
      item?.strategy_ids ? item.strategy_ids.join(",") : ""
    );
    setCurrentPromotionName(item?.promotion_name || "");
    setShowStrategyDialog(true);
  };

  // 处理绑定策略
  const handleBindStrategy = async (strategyIdsStr) => {
    if (selectedPromotionIds.length > 1) {
      await batchBindStrategy(strategyIdsStr);
    } else {
      await singleBindStrategy(strategyIdsStr);
    }
  };

  // 批量绑定策略
  const batchBindStrategy = async (strategyIdsStr) => {
    const entities = selectedPromotionIds.map((id) => {
      const promotion = data.find((item) => item.promotion_id === id);
      return {
        binding_id: id.toString(),
        binding_name: promotion ? promotion.promotion_name : `广告#${id}`,
      };
    });

    const result = await promotionService.batchBindStrategyToEntities({
      strategy_ids: strategyIdsStr.split(",").map((id) => Number(id)),
      binding_type: "promotion",
      entities: entities,
      priority: 0,
      description: "",
    });

    if (result.code === 0) {
      setShowStrategyDialog(false);
      // 强制刷新数据以显示最新的策略绑定
      await loadPromotionData(true);
      toast.success(`成功为 ${selectedPromotionIds.length} 个广告绑定策略！`);
    } else {
      console.error("批量绑定策略失败:", result.msg);
      toast.error("批量绑定策略失败: " + result.msg);
    }
  };

  // 单个绑定策略
  const singleBindStrategy = async (strategyIdsStr) => {
    const result = await promotionService.bindStrategyToEntity({
      strategy_ids: strategyIdsStr.split(",").map((id) => Number(id)),
      binding_type: "promotion",
      binding_id: selectedPromotionIds[0].toString(),
      binding_name: currentPromotionName,
    });

    if (result.code === 0) {
      setShowStrategyDialog(false);
      // 强制刷新数据以显示最新的策略绑定
      await loadPromotionData(true);
      toast.success("策略绑定成功！");
    } else {
      console.error("绑定策略失败:", result.msg);
      toast.error("绑定策略失败: " + result.msg);
    }
  };

  // 绑定策略回调
  const handleStrategyConfirm = async (strategyIdsStr) => {
    try {
      if (isEmptyStrategy(strategyIdsStr)) {
        await handleUnbindStrategy();
      } else {
        await handleBindStrategy(strategyIdsStr);
      }
    } catch (error) {
      console.error("绑定策略异常:", error);
      toast.error("绑定策略失败: " + error.message);
    }
  };

  // 检查是否为空策略
  const isEmptyStrategy = (strategyIdsStr) => {
    return !strategyIdsStr || strategyIdsStr.trim() === "";
  };

  // 处理解绑策略
  const handleUnbindStrategy = async () => {
    if (selectedPromotionIds.length > 1) {
      await batchUnbindStrategy();
    } else {
      await singleUnbindStrategy();
    }
  };

  // 批量解绑策略
  const batchUnbindStrategy = async () => {
    let successCount = 0;
    let errorCount = 0;

    for (const promotionId of selectedPromotionIds) {
      try {
        const result = await promotionService.unbindStrategyFromEntity({
          strategy_ids: [],
          binding_type: "promotion",
          binding_id: promotionId.toString(),
        });

        if (result.code === 0) {
          successCount++;
        } else {
          errorCount++;
          console.error(`广告 ${promotionId} 清空策略绑定失败:`, result.msg);
        }
      } catch (error) {
        errorCount++;
        console.error(`广告 ${promotionId} 清空策略绑定异常:`, error);
      }
    }

    setShowStrategyDialog(false);
    // 强制刷新数据以显示最新的策略绑定
    await loadPromotionData(true);

    showUnbindResults(successCount, errorCount);
  };

  // 单个解绑策略
  const singleUnbindStrategy = async () => {
    const result = await promotionService.unbindStrategyFromEntity({
      strategy_ids: [],
      binding_type: "promotion",
      binding_id: selectedPromotionIds[0].toString(),
    });

    if (result.code === 0) {
      setShowStrategyDialog(false);
      // 强制刷新数据以显示最新的策略绑定
      await loadPromotionData(true);
      toast.success("已清空策略绑定");
    } else {
      console.error("清空策略绑定失败:", result.msg);
      toast.error("清空策略绑定失败: " + result.msg);
    }
  };

  // 显示解绑结果
  const showUnbindResults = (successCount, errorCount) => {
    if (errorCount === 0) {
      toast.success(`已清空 ${successCount} 个广告的策略绑定`);
    } else if (successCount > 0) {
      toast.warning(
        `成功清空 ${successCount} 个广告的策略绑定，${errorCount} 个失败`
      );
    } else {
      toast.error(`清空策略绑定失败，共 ${errorCount} 个错误`);
    }
  };

  // 对话框关闭时重置状态
  useEffect(() => {
    if (!showStrategyDialog) {
      setSelectedPromotionIds([]);
      setCurrentStrategyIds("");
      setCurrentPromotionName("");
    }
  }, [showStrategyDialog]);

  return (
    <div
      className="flex flex-col p-4 min-h-0 h-full"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      {/* 统一的卡片容器 */}
      <Card className="overflow-hidden flex flex-col overflow-hidden h-full">
        {/* 状态提示栏 - 显示项目筛选和搜索状态 */}
        {(projectInfo.project_id || searchTerm) && (
          <StatusBar
            filters={[
              ...(projectInfo.project_id
                ? [
                    {
                      icon: null,
                      label: "筛选项目",
                      value: projectInfo.project_name,
                      textColor: "#7c3aed",
                      clearColor: "#a78bfa",
                      clearTitle: "清除项目筛选",
                      onClear: () => setProjectInfo({}),
                    },
                  ]
                : []),
              ...(searchTerm
                ? [
                    {
                      icon: Search,
                      iconClass: "text-blue-600",
                      label: "搜索",
                      value: `"${searchTerm}"`,
                      textColor: "#1e40af",
                      clearColor: "#60a5fa",
                      clearTitle: "清除搜索",
                      onClear: () => handleSearch(""),
                    },
                  ]
                : []),
            ]}
            totalCount={data.length}
            filteredCount={sortedData.length}
            selectedCount={selectedItems.length}
            itemLabel="个广告"
            showDeleted={showDeleted}
            onClearAll={() => {
              handleSearch("");
              setProjectInfo({});
            }}
          />
        )}

        {/* 数据表格 - 使用虚拟滚动优化性能 */}
        <div
          ref={scrollContainerRef}
          className="h-full overflow-auto"
          onScroll={handleScroll}
        >
          <VirtualDataTable
            columns={columns}
            data={sortedData}
            loading={loading}
            selectable={true}
            selectedItems={selectedItems}
            onSelectItem={toggleSelectItem}
            onSelectAll={toggleSelectAll}
            rowKey="promotion_id"
            contextMenuItems={contextMenuItems}
            rowClassName={(item) => {
              // 为已删除的项添加特殊样式
              if (item && item.delete_time) {
                return "deleted-row";
              }
              return "";
            }}
            emptyState={
              <EmptyState
                type="empty"
                title="暂无数据"
                description={
                  !selectedAccount
                    ? "请先选择账户"
                    : "右键菜单可进行搜索、刷新等操作"
                }
              />
            }
            loadingState={
              <EmptyState
                type="loading"
                title="加载中..."
                description="正在获取广告数据，请稍候"
              />
            }
            itemHeight={28} // 舒适行高
            className="promotion-table-virtual"
          />
        </div>

        {/* 移动端统计信息栏 */}
        <MobileStatsBar
          totalCount={data.length}
          filteredCount={sortedData.length}
          selectedCount={selectedItems.length}
          itemLabel="个广告"
          showDeleted={showDeleted}
        />
      </Card>

      {/* 浮动搜索框 */}
      <FloatingSearchBox
        isOpen={showSearchDialog}
        onClose={handleCloseSearch}
        searchTerm={searchTerm}
        onSearch={handleSearch}
        placeholder="搜索广告名称或ID"
        matchCount={searchMatchCount}
        totalCount={data.length}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(INITIAL_CONFIRM_DIALOG)}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
      />

      {/* 复制广告对话框 */}
      <CopyPromotionDialog
        isOpen={copyDialog.isOpen}
        onClose={handleCloseCopyDialog}
        promotion={copyDialog.promotion}
        promotionDetail={copyDialog.promotionDetail}
        selectedAccount={selectedAccount}
        onCopySuccess={handleCopySuccess}
        targetProjectId={copyDialog.targetProjectId}
      />

      {/* 绑定策略对话框 */}
      <StrategyBindDialog
        visible={showStrategyDialog}
        onClose={() => setShowStrategyDialog(false)}
        onConfirm={handleStrategyConfirm}
        currentStrategyIds={currentStrategyIds}
        bindingType="promotion"
        bindingId={selectedPromotionIds[0]}
        bindingName={currentPromotionName}
        advertiserId={(() => {
          // 获取当前选中广告的广告主ID
          const promotion = data.find(
            (item) => item.promotion_id === selectedPromotionIds[0]
          );
          return promotion?.advertiser_id?.toString() || "";
        })()}
        projectId={(() => {
          // 获取当前选中广告的项目ID
          const promotion = data.find(
            (item) => item.promotion_id === selectedPromotionIds[0]
          );
          return promotion?.project_id?.toString() || "";
        })()}
      />

      {/* 修改出价对话框 */}
      <UpdatePromotionBidDialog
        isOpen={updateBidDialog.isOpen}
        onClose={() => setUpdateBidDialog({ isOpen: false, promotions: [] })}
        promotions={updateBidDialog.promotions}
        onConfirm={handleUpdateBidConfirm}
        loading={bidUpdateLoading}
      />

      {/* 工具栏 */}
      <Toolbar
        title="广告管理"
        subtitle={`共 ${data.length} 个广告`}
        loading={loading}
        onRefresh={() => loadPromotionData(true)}
        extra={
          <div className="flex items-center space-x-2">
            <SearchInput
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="搜索广告..."
              className="w-64"
            />
            <Button
              onClick={handleBatchDeleteConfirm}
              disabled={selectedItems.length === 0}
              variant="danger"
              size="small"
            >
              批量删除 ({selectedItems.length})
            </Button>
          </div>
        }
      />
    </div>
  );
}

export default PromotionManager;
