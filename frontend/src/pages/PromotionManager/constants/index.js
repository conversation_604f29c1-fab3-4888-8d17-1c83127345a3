// 广告状态常量 - 对应巨量引擎API状态码
export const PROMOTION_STATUS = {
  ACTIVE: 0, // 投放中
  PAUSED: 61, // 已暂停
  AUDIT_FAILED: 41, // 审核不通过
  INSUFFICIENT_FUNDS: 43, // 账户余额不足
  AUDIT_PENDING: 22, // 新建审核中
  TERMINATED: 31, // 已终止
};

// 广告启用状态（二级状态使用）
export const PROMOTION_STATUS_START = {
  ACTIVE: 0,
  PAUSED: 1,
};

// 广告二级状态 - 对应巨量引擎API二级状态码
export const PROMOTION_SECOND_STATUS = {
  AUDIT_FAILED: 41, // 审核不通过
  PAUSED: 61, // 已暂停
};

// 状态对应的颜色配置
export const STATUS_COLORS = {
  [PROMOTION_STATUS.ACTIVE]:
    "text-emerald-600 bg-emerald-50 border-emerald-200",
  [PROMOTION_STATUS.PAUSED]: "text-amber-600 bg-amber-50 border-amber-200",
  [PROMOTION_STATUS.AUDIT_FAILED]: "text-red-600 bg-red-50 border-red-200",
  [PROMOTION_STATUS.INSUFFICIENT_FUNDS]:
    "text-orange-600 bg-orange-50 border-orange-200",
  [PROMOTION_STATUS.AUDIT_PENDING]: "text-blue-600 bg-blue-50 border-blue-200",
  [PROMOTION_STATUS.TERMINATED]: "text-gray-600 bg-gray-50 border-gray-200",
};

// 启用状态颜色配置
export const START_STATUS_COLORS = {
  [PROMOTION_STATUS_START.ACTIVE]:
    "text-emerald-600 bg-emerald-50 border-emerald-200",
  [PROMOTION_STATUS_START.PAUSED]:
    "text-amber-600 bg-amber-50 border-amber-200",
};

// 状态对应的UI变体（用于StatusBadge组件）
export const STATUS_VARIANTS = {
  [PROMOTION_STATUS.ACTIVE]: "success",
  [PROMOTION_STATUS.PAUSED]: "warning",
  [PROMOTION_STATUS.AUDIT_FAILED]: "error",
  [PROMOTION_STATUS.INSUFFICIENT_FUNDS]: "warning",
  [PROMOTION_STATUS.AUDIT_PENDING]: "info",
  [PROMOTION_STATUS.TERMINATED]: "default",
};

// 启用状态变体
export const START_STATUS_VARIANTS = {
  [PROMOTION_STATUS_START.ACTIVE]: "success",
  [PROMOTION_STATUS_START.PAUSED]: "warning",
};

// 状态文本 - 对应巨量引擎API状态名称
export const STATUS_TEXT = {
  [PROMOTION_STATUS.ACTIVE]: "投放中",
  [PROMOTION_STATUS.PAUSED]: "已暂停",
  [PROMOTION_STATUS.AUDIT_FAILED]: "审核不通过",
  [PROMOTION_STATUS.INSUFFICIENT_FUNDS]: "账户余额不足",
  [PROMOTION_STATUS.AUDIT_PENDING]: "新建审核中",
  [PROMOTION_STATUS.TERMINATED]: "已终止",
};

// 启用状态文本
export const START_STATUS_TEXT = {
  [PROMOTION_STATUS_START.ACTIVE]: "启用中",
  [PROMOTION_STATUS_START.PAUSED]: "已暂停",
};

// 投放模式常量
export const DELIVERY_MODE = {
  MANUAL: 1, // 手动投放
  AUTOMATIC: 3, // 自动投放
};

// 投放模式文本
export const DELIVERY_MODE_TEXT = {
  [DELIVERY_MODE.MANUAL]: "手动投放",
  [DELIVERY_MODE.AUTOMATIC]: "自动投放",
};

// 投放模式颜色配置
export const DELIVERY_MODE_COLORS = {
  [DELIVERY_MODE.MANUAL]: "text-blue-600 bg-blue-50 border-blue-200", // 手动投放：蓝色
  [DELIVERY_MODE.AUTOMATIC]:
    "text-emerald-600 bg-emerald-50 border-emerald-200", // 自动投放：绿色
};

// 初始分页状态（取消分页，查询全部数据）
export const INITIAL_PAGINATION = {
  page: 0,
  pageSize: 0,
  total: 0,
};

// 初始确认对话框状态
export const INITIAL_CONFIRM_DIALOG = {
  isOpen: false,
  title: "",
  message: "",
  onConfirm: null,
};

// 表格列宽度配置（像素值，紧凑布局）
export const TABLE_COLUMN_WIDTHS = {
  INDEX: 60,
  ENABLE_STATUS: 80,
  NAME: 180,
  PROMOTION_ID: 180, // 增加广告ID列宽度以完整显示长ID
  PROJECT_ID: 160, // 增加项目ID列宽度以完整显示长ID
  STATUS: 90,
  DELIVERY_MODE: 100, // 投放模式列宽度
  CONVERSION_COST: 120, // 增加宽度以显示排序图标
  STAT_COST: 80,
  SHOW_COUNT: 80,
  CONVERT_COUNT: 80,
  CONVERSION_RATE: 80,
  AD_BUDGET: 100, // 增加宽度以显示排序图标
  AD_BID: 80,
  CREATE_TIME: 140, // 创建时间列宽度
  ACTIONS: 60,
};
