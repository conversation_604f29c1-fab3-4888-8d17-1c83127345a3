# PromotionManager 广告管理页面

## 概述

PromotionManager 是一个现代化的广告管理页面，提供广告列表查看、搜索、编辑、删除等功能。采用模块化架构设计，具有良好的可维护性和扩展性。

## 功能特性

- ✅ 广告列表展示（支持分页，14列数据）
- ⚠️ 搜索功能（暂时禁用，等待backend API支持）
- ✅ 广告状态管理（启用/暂停切换）
- ✅ 在线编辑广告名称
- ✅ 单个/批量删除广告
- ✅ 账户选择器集成
- ✅ 项目筛选支持
- ✅ 现代化UI设计（紫粉渐变背景、毛玻璃效果）
- ✅ 响应式布局
- ✅ 数据格式化显示（金额、百分比、数量）

## 目录结构

```
PromotionManager/
├── components/           # 组件模块
│   ├── TableColumns.jsx  # 表格列配置（14列）
│   └── index.js          # 组件导出
├── constants/            # 常量定义
│   └── index.js          # 状态常量、UI配置等
├── hooks/                # 自定义hooks
│   ├── usePromotionManager.js  # 主业务逻辑hook
│   └── index.js          # hooks导出
├── utils/                # 工具函数
│   └── index.js          # 格式化、计算等工具函数
├── PromotionManager.jsx  # 主组件 (748→180行，-76%)
├── index.js             # 入口文件
└── README.md            # 文档
```

## 组件架构

### 1. 主组件 (PromotionManager.jsx)
- 负责整体布局和UI渲染
- 集成各个子模块
- 处理用户交互和事件分发
- 支持项目筛选功能

### 2. 业务逻辑 (hooks/usePromotionManager.js)
- 状态管理（数据、分页、选择等）
- API调用（直接使用bindings）
- 业务逻辑处理（搜索、编辑、删除等）
- 项目筛选逻辑

### 3. 表格配置 (components/TableColumns.jsx)
- 14列动态表格列定义
- 单元格渲染逻辑
- 交互处理（编辑、删除等）
- 状态切换功能

### 4. 常量定义 (constants/index.js)
- 广告状态枚举
- UI颜色配置
- 表格列宽度设置

### 5. 工具函数 (utils/index.js)
- 数据格式化（时间、金额、百分比、预算）
- 状态判断和转换
- 通用计算函数

## 使用方法

### 基础使用

```jsx
import PromotionManager from './pages/PromotionManager';

function App() {
  return (
    <PromotionManager />
  );
}
```

### 带项目筛选

```jsx
import PromotionManager from './pages/PromotionManager';

function App() {
  const selectedProject = {
    project_id: '123456',
    project_name: '测试项目',
    advertiser_id: '789',
    accountId: '456'
  };

  return (
    <PromotionManager selectedProject={selectedProject} />
  );
}
```

## API集成

### 直接使用Bindings

组件直接使用 `@services/promotionservice.js` bindings，避免中间层封装：

```javascript
import * as PromotionService from '@services/promotionservice.js';

// 获取广告列表
const result = await PromotionService.GetPromotionListAsync(params);

// 删除广告
const result = await PromotionService.DeletePromotionAsync(req);

// 批量删除广告
const result = await PromotionService.BatchDeletePromotionAsync(req);

// 更新广告状态
const result = await PromotionService.UpdatePromotionStatus(req);

// 修改广告名称
const result = await PromotionService.UpdatePromotionName(req);
```

### 请求参数格式

```javascript
// 获取广告列表
const listParams = {
  account_id: 123,           // 主账户ID
  keyword: '456',            // 关键词（通常是广告主ID）
  project_id: '789',         // 项目ID筛选
  promotion_id: '',          // 广告ID筛选
  promotionName: '广告名',    // 广告名称搜索
  page: 1,                   // 页码
  page_size: 10             // 每页大小
};

// 删除广告
const deleteParams = {
  account_id: 123,                    // 主账户ID
  advertiser_id: "456",              // 广告主ID
  promotion_ids: ["789", "101112"]   // 广告ID数组
};

// 更新状态
const statusParams = {
  account_id: 123,           // 主账户ID
  advertiser_id: 456,        // 广告主ID
  status_map: {              // 状态映射
    "789": 0,                // 广告ID: 状态值
    "101112": 1
  }
};
```

## 状态管理

### 广告状态类型

```javascript
// 广告基础状态
PROMOTION_STATUS = {
  ACTIVE: 1,      // 投放中
  PAUSED: 2,      // 已暂停
  DELETED: 3,     // 已删除
  AUDIT: 4,       // 审核中
  REJECTED: 5     // 审核拒绝
}

// 广告启用状态
PROMOTION_STATUS_START = {
  ACTIVE: 0,      // 启用中
  PAUSED: 1       // 已暂停
}

// 广告二级状态
PROMOTION_SECOND_STATUS = {
  NOPASS: 41,     // 未通过
  PAUSED: 61      // 暂停
}
```

### 状态颜色配置

```javascript
STATUS_COLORS = {
  1: 'text-emerald-600 bg-emerald-50 border-emerald-200',  // 投放中
  2: 'text-amber-600 bg-amber-50 border-amber-200',       // 已暂停
  3: 'text-red-600 bg-red-50 border-red-200',            // 已删除
  4: 'text-blue-600 bg-blue-50 border-blue-200',         // 审核中
  5: 'text-red-600 bg-red-50 border-red-200'            // 审核拒绝
}
```

## 表格列说明

| 列名 | 字段 | 说明 | 功能 |
|-----|------|------|------|
| 序号 | - | 行号显示 | 全选/单选 |
| 启用状态 | promotion_status_second | 启用/暂停状态 | 点击切换 |
| 广告名称 | promotion_name | 广告名称 | 可编辑 |
| 广告ID | promotion_id | 广告唯一标识 | 显示 |
| 项目ID | project_id | 所属项目ID | 显示 |
| 广告状态 | promotion_status_first | 广告投放状态 | 显示 |
| 平均转化成本 | conversion_cost | 转化成本 | 格式化显示 |
| 消耗数 | stat_cost | 消费金额 | 格式化显示 |
| 展示数 | show_cnt | 展示次数 | 格式化显示 |
| 转化数 | convert_cnt | 转化次数 | 格式化显示 |
| 转化率 | conversion_rate | 转化百分比 | 格式化显示 |
| 广告预算 | ad_budget | 预算金额 | 格式化显示 |
| 出价 | ad_bid | 出价金额 | 格式化显示 |
| 操作 | - | 删除操作 | 功能按钮 |

## 样式设计

### 现代化UI特性

- **渐变背景**: `bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/20`
- **毛玻璃效果**: `backdrop-blur-sm bg-white/80`
- **圆角设计**: `rounded-xl` 大圆角设计
- **阴影层次**: `shadow-sm` 轻微阴影
- **交互反馈**: hover状态和过渡动画
- **紫粉主题**: 使用紫色和粉色作为主题色调

### 响应式设计

- 自适应表格布局（14列）
- 灵活的工具栏布局
- 移动端友好的交互设计

## 性能优化

1. **防抖搜索**: 搜索输入100ms防抖
2. **参数缓存**: 避免重复相同参数的API调用
3. **状态优化**: 合理的state更新策略
4. **组件记忆**: 使用useCallback优化函数引用
5. **数据格式化**: 缓存格式化结果

## 数据格式化

### 金额格式化

```javascript
formatAmount(12345.67)     // ¥12,345.67
formatAmount(0)            // ¥0.00
formatAmount(null)         // ¥0.00
```

### 百分比格式化

```javascript
formatConversionRate(0.1234)  // 12.34%
formatConversionRate(12.34)   // 12.34%
formatConversionRate(0)       // 0%
```

### 数字格式化

```javascript
formatNumber(123456)       // 123,456
formatNumber(0)            // 0
formatNumber(null)         // 0
```

## 开发规范

### 代码组织
- 单一职责原则：每个文件专注特定功能
- 模块化设计：便于测试和维护
- 类型安全：充分的参数验证

### 命名规范
- 组件：PascalCase (PromotionManager)
- 函数：camelCase (handleSearch)
- 常量：UPPER_SNAKE_CASE (PROMOTION_STATUS)
- 文件：kebab-case (table-columns.jsx)

### 注释规范
- JSDoc格式的函数注释
- 重要业务逻辑的行内注释
- 组件和模块的顶部说明注释

## 扩展指南

### 添加新的表格列

1. 在 `constants/index.js` 中添加列宽配置
2. 在 `utils/index.js` 中添加格式化函数（如需要）
3. 在 `components/TableColumns.jsx` 中添加列定义

### 添加新的操作功能

1. 在 `hooks/usePromotionManager.js` 中添加业务逻辑
2. 在 `components/TableColumns.jsx` 中添加操作按钮
3. 在主组件中集成新功能

### 自定义样式主题

修改 `constants/index.js` 中的颜色配置：

```javascript
STATUS_COLORS = {
  1: 'text-green-600 bg-green-50 border-green-200',  // 自定义成功色
  // ... 其他状态
}
```

## 项目集成

### 与ProjectManager联动

```jsx
import { useState } from 'react';
import ProjectManager from './pages/ProjectManager';
import PromotionManager from './pages/PromotionManager';

function App() {
  const [selectedProject, setSelectedProject] = useState(null);

  return (
    <div>
      {selectedProject ? (
        <PromotionManager 
          selectedProject={selectedProject}
          onBack={() => setSelectedProject(null)}
        />
      ) : (
        <ProjectManager 
          onProjectSelect={setSelectedProject}
        />
      )}
    </div>
  );
}
```

## 错误处理

- API调用失败时显示友好提示
- 数据格式异常时显示默认值
- 网络错误时提供重试机制
- 操作失败时保持用户状态

## 维护指南

- 定期更新依赖版本
- 关注性能监控指标
- 及时修复用户反馈的问题
- 保持代码风格一致性
- 定期优化表格渲染性能

## 常见问题

### Q: 为什么有14列表格？
A: 广告管理需要展示完整的广告信息，包括状态、成本、效果等多维度数据。

### Q: 如何优化大数据量表格性能？
A: 使用虚拟滚动、分页加载、缓存机制等技术。

### Q: 如何处理状态复杂的广告？
A: 通过二级状态映射和工具函数统一处理状态逻辑。

### Q: 为什么搜索功能被禁用了？
A: 根据backend AdvertiserReq类型定义，当前API不支持按广告名称搜索。搜索框已暂时禁用，等待backend添加相应字段支持。

### Q: 如何恢复搜索功能？
A: 当backend API添加广告名称搜索字段后，可以在hooks/usePromotionManager.js中的buildQueryParams函数里恢复searchTerm参数映射。 