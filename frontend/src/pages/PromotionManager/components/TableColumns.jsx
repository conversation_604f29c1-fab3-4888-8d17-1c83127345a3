import React from "react";
import { Edit, Check, X, Trash2, Chev<PERSON>Up, ChevronDown } from "lucide-react";
import StatusBadge from "../../../components/ui/StatusBadge";
import ActionButtons from "../../../components/ui/ActionButtons";
import HighlightText from "../../../components/ui/HighlightText";
import {
  STATUS_COLORS,
  STATUS_VARIANTS,
  START_STATUS_COLORS,
  START_STATUS_VARIANTS,
  TABLE_COLUMN_WIDTHS,
  PROMOTION_STATUS_START,
  PROMOTION_SECOND_STATUS,
  DELIVERY_MODE_COLORS,
} from "../constants";
import {
  formatAmount,
  formatConversionRate,
  formatNumber,
  formatBudgetOrBid,
  getRowIndex,
  getPromotionStartStatus,
  getPromotionStartStatusText,
  getPromotionStatusText,
  getDeliveryModeText,
  truncateText,
  formatCreateTime,
} from "../utils";
import StrategyNameDisplay from "../../../components/strategy/StrategyNameDisplay";

/**
 * 排序标题组件
 */
const SortableTitle = ({ title, field, sortConfig, handleSort }) => {
  const isActive = sortConfig.field === field;
  const isAsc = isActive && sortConfig.order === "asc";
  const isDesc = isActive && sortConfig.order === "desc";

  return (
    <div
      className="flex items-center gap-1 cursor-pointer select-none hover:text-blue-600 transition-colors"
      onClick={() => handleSort(field)}
    >
      <span>{title}</span>
      <div className="flex flex-col -space-y-1.5">
        <ChevronUp
          size={12}
          className={`transition-colors ${
            isDesc ? "text-blue-600" : "text-slate-300"
          }`}
        />
        <ChevronDown
          size={12}
          className={`transition-colors ${
            isAsc ? "text-blue-600" : "text-slate-300"
          }`}
        />
      </div>
    </div>
  );
};

/**
 * 表格列配置
 * 根据传入的状态和处理函数动态生成表格列
 */
export const createTableColumns = ({
  selectedItems,
  editingPromotionId,
  editingPromotionName,
  setEditingPromotionName,
  pagination,
  onSelectItem,
  onSelectAll,
  onUpdateStatus,
  onDeletePromotion,
  onEditPromotionName,
  onCancelEditPromotionName,
  onSavePromotionName,
  onEditInputKeyDown,
  data,
  showDeleted,
  sortConfig,
  handleSort,
  searchTerm,
}) => {
  // 序号列
  const indexColumn = {
    key: "index",
    title: "序号",
    width: 60,
    render: (item, index) => (
      <span className="text-xs text-slate-900">
        {getRowIndex(index, pagination.page, pagination.pageSize)}
      </span>
    ),
  };

  // 启用状态列（仅展示）
  const enableStatusColumn = {
    key: "enable_status",
    title: "启用状态",
    width: TABLE_COLUMN_WIDTHS.ENABLE_STATUS,
    render: (item) => {
      if (!item) return <span className="text-sm text-slate-500">-</span>;

      const startStatus = getPromotionStartStatus(item);
      const statusText = getPromotionStartStatusText(item);

      return (
        <span
          className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${
            START_STATUS_COLORS[startStatus] ||
            "text-gray-600 bg-gray-50 border-gray-200"
          }`}
          style={{ fontSize: "10px" }}
        >
          {statusText}
        </span>
      );
    },
  };

  // 广告名称列（支持编辑）
  const nameColumn = {
    key: "promotion_name",
    title: "广告名称",
    width: TABLE_COLUMN_WIDTHS.NAME,
    render: (item) => {
      if (!item) return <span className="text-sm text-slate-500">-</span>;

      if (editingPromotionId === item.promotion_id) {
        return (
          <div className="flex items-center space-x-0.5 max-w-[112px]">
            <input
              type="text"
              value={editingPromotionName}
              onChange={(e) => setEditingPromotionName(e.target.value)}
              onKeyDown={(e) => onEditInputKeyDown(e, item)}
              className="flex-1 px-1 py-0.5 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 min-w-0"
              placeholder="输入广告名称..."
              autoFocus
            />
            <button
              onClick={() => onSavePromotionName(item)}
              className="text-green-600 hover:text-green-800 transition-colors flex-shrink-0"
              title="保存"
            >
              <Check size={10} />
            </button>
            <button
              onClick={onCancelEditPromotionName}
              className="text-gray-600 hover:text-gray-800 transition-colors flex-shrink-0"
              title="取消"
            >
              <X size={10} />
            </button>
          </div>
        );
      }

      return (
        <div
          className="text-xs font-medium text-slate-900 truncate max-w-[180px]"
          title={item?.promotion_name}
        >
          <HighlightText
            text={item?.promotion_name || "未命名广告"}
            searchTerm={searchTerm}
          />
        </div>
      );
    },
  };

  // 广告ID列
  const promotionIdColumn = {
    key: "promotion_id",
    title: "广告ID",
    width: TABLE_COLUMN_WIDTHS.PROMOTION_ID,
    render: (item) => (
      <div className="text-xs text-slate-500" title={item?.promotion_id}>
        <HighlightText
          text={item?.promotion_id || "-"}
          searchTerm={searchTerm}
        />
      </div>
    ),
  };

  // 项目ID列
  const projectIdColumn = {
    key: "project_id",
    title: "项目ID",
    width: TABLE_COLUMN_WIDTHS.PROJECT_ID,
    render: (item) => (
      <div className="text-xs text-slate-500" title={item?.project_id}>
        <HighlightText text={item?.project_id || "-"} searchTerm={searchTerm} />
      </div>
    ),
  };

  // 广告状态列
  const statusColumn = {
    key: "promotion_status",
    title: "广告状态",
    width: TABLE_COLUMN_WIDTHS.STATUS,
    render: (item) => {
      if (!item) return <span className="text-sm text-slate-500">-</span>;

      const status = item.promotion_status_first;
      const statusText = getPromotionStatusText(item);

      return (
        <StatusBadge
          variant={STATUS_VARIANTS[status] || "default"}
          className="text-xs"
          style={{ fontSize: "10px" }}
        >
          {statusText}
        </StatusBadge>
      );
    },
  };

  // 投放模式列
  const deliveryModeColumn = {
    key: "delivery_mode",
    title: "投放模式",
    width: TABLE_COLUMN_WIDTHS.DELIVERY_MODE,
    render: (item) => {
      if (!item) return <span className="text-xs text-slate-500">-</span>;

      const deliveryMode = item.delivery_mode;
      const modeText = getDeliveryModeText(item);

      return (
        <span
          className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${
            DELIVERY_MODE_COLORS[deliveryMode] ||
            "text-gray-600 bg-gray-50 border-gray-200"
          }`}
          style={{ fontSize: "10px" }}
        >
          {modeText}
        </span>
      );
    },
  };

  // 删除时间列（仅在显示已删除项时显示）
  const deleteTimeColumn = {
    key: "delete_time",
    title: "删除时间",
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (item) => {
      if (!item?.delete_time)
        return <span className="text-xs text-slate-500">-</span>;

      const deleteTime = new Date(item.delete_time * 1000);
      const now = new Date();
      const diffDays = Math.floor((now - deleteTime) / (1000 * 60 * 60 * 24));

      let displayText;

      if (diffDays === 0) {
        // 今天：今天 14:30
        displayText = `今天 ${deleteTime.toLocaleString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })}`;
      } else if (diffDays === 1) {
        // 昨天：昨天 14:30
        displayText = `昨天 ${deleteTime.toLocaleString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })}`;
      } else if (diffDays < 30) {
        // 30天内：01/15 14:30
        displayText = deleteTime.toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      } else {
        // 更久前：2024/01/15 14:30
        displayText = deleteTime.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      const fullTime = deleteTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      return (
        <span
          className="text-xs text-red-600 font-bold whitespace-nowrap delete-time-cell"
          title={`删除于: ${fullTime}`}
        >
          {displayText}
        </span>
      );
    },
  };

  // 平均转化成本列
  const conversionCostColumn = {
    key: "conversion_cost",
    title: (
      <SortableTitle
        title="平均转化成本"
        field="conversion_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERSION_COST,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatAmount(item?.conversion_cost)}
      </span>
    ),
  };

  // 消耗数列
  const statCostColumn = {
    key: "stat_cost",
    title: (
      <SortableTitle
        title="消耗数"
        field="stat_cost"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.STAT_COST,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatAmount(item?.stat_cost)}
      </span>
    ),
  };

  // 展示数列
  const showCountColumn = {
    key: "show_cnt",
    title: (
      <SortableTitle
        title="展示数"
        field="show_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.SHOW_COUNT,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatNumber(item?.show_cnt)}
      </span>
    ),
  };

  // 转化数列
  const convertCountColumn = {
    key: "convert_cnt",
    title: (
      <SortableTitle
        title="转化数"
        field="convert_cnt"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERT_COUNT,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatNumber(item?.convert_cnt)}
      </span>
    ),
  };

  // 转化率列
  const conversionRateColumn = {
    key: "conversion_rate",
    title: (
      <SortableTitle
        title="转化率"
        field="conversion_rate"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CONVERSION_RATE,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatConversionRate(item?.conversion_rate)}
      </span>
    ),
  };

  // 广告预算列
  const adBudgetColumn = {
    key: "ad_budget",
    title: (
      <SortableTitle
        title="广告预算"
        field="ad_budget"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.AD_BUDGET,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatBudgetOrBid(item?.ad_budget)}
      </span>
    ),
  };

  // 出价列
  const adBidColumn = {
    key: "ad_bid",
    title: (
      <SortableTitle
        title="出价"
        field="ad_bid"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.AD_BID,
    render: (item) => (
      <span className="text-xs text-slate-500">
        {formatBudgetOrBid(item?.ad_bid)}
      </span>
    ),
  };

  // 创建时间列
  const createTimeColumn = {
    key: "create_time",
    title: (
      <SortableTitle
        title="创建时间"
        field="create_time"
        sortConfig={sortConfig}
        handleSort={handleSort}
      />
    ),
    width: TABLE_COLUMN_WIDTHS.CREATE_TIME,
    render: (item) => {
      if (!item?.create_time)
        return <span className="text-xs text-slate-500">-</span>;

      // 解析时间并减去8小时用于显示
      const createTime = new Date(item.create_time);
      createTime.setHours(createTime.getHours() - 8);

      const fullTime = createTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      return (
        <span
          className="text-xs text-slate-600 whitespace-nowrap"
          title={`创建于: ${fullTime}`}
        >
          {formatCreateTime(item.create_time)}
        </span>
      );
    },
  };

  // 策略列
  const strategyColumn = {
    key: "strategies",
    title: "已绑定策略",
    // 不设置width，让此列占据剩余空间，minWidth通过CSS控制
    render: (item) => (
      <StrategyNameDisplay
        strategyIds={item.strategy_ids || []}
        showAsTags={true}
        maxDisplay={2}
      />
    ),
  };

  // 操作列
  const actionsColumn = {
    key: "actions",
    title: "操作",
    width: TABLE_COLUMN_WIDTHS.ACTIONS,
    render: (item) => {
      if (!item) return <span className="text-sm text-slate-500">-</span>;

      return (
        <div className="flex items-center space-x-2">
          <ActionButtons
            actions={[
              {
                key: "delete",
                type: "delete",
                onClick: () => onDeletePromotion(item),
                title: "删除",
              },
            ]}
            size="sm"
          />
        </div>
      );
    },
  };

  // 基础列配置
  const baseColumns = [
    indexColumn,
    enableStatusColumn,
    nameColumn,
    promotionIdColumn,
    projectIdColumn,
    statusColumn,
    deliveryModeColumn,
    conversionCostColumn,
    statCostColumn,
    showCountColumn,
    convertCountColumn,
    conversionRateColumn,
    adBudgetColumn,
    adBidColumn,
    createTimeColumn,
    strategyColumn,
  ];

  // 如果显示已删除项，在最后添加删除时间列
  if (showDeleted) {
    baseColumns.push(deleteTimeColumn);
  }

  return baseColumns;
};
