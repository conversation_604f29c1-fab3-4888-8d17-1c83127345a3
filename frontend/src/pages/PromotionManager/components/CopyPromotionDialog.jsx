import React, { useState, useEffect } from "react";
import {
  Co<PERSON>,
  X,
  ChevronDown,
  Upload,
  File,
  Play,
  CheckCircle,
} from "lucide-react";
import Button from "../../../components/ui/Button";
import { toast } from "../../../components/ui/Toast";
import * as ProjectService from "../../../services/projectservice.js";
import {
  SystemService,
  PromotionService,
  FfmpegService,
} from "../../../services/api.js";
import AdvertiserService from "../../../services/advertiserService.js";

/**
 * CopyPromotionDialog 复制广告对话框
 */
const CopyPromotionDialog = ({
  isOpen,
  onClose,
  promotion,
  promotionDetail,
  selectedAccount,
  onCopySuccess,
  targetProjectId,
}) => {
  const [newPromotionName, setNewPromotionName] = useState("");
  const [copyCount, setCopyCount] = useState(1); // 复制数量，默认为1
  const [copying, setCopying] = useState(false);

  // 视频素材相关状态
  const [selectedVideoFiles, setSelectedVideoFiles] = useState([]);
  const [selectingVideo, setSelectingVideo] = useState(false);

  // 已有视频素材相关状态
  const [existingVideos, setExistingVideos] = useState([]);
  const [loadingPromotionInfo, setLoadingPromotionInfo] = useState(false);
  const [selectedExistingVideos, setSelectedExistingVideos] = useState([]);

  // 新增：复制方式状态
  const [copyMode, setCopyMode] = useState("original"); // 'original' | 'variation'

  // 重置表单
  const resetForm = () => {
    setNewPromotionName("");
    setCopyCount(1);
    setSelectedVideoFiles([]);
    setSelectedExistingVideos([]);
    setExistingVideos([]);
    setCopyMode("original"); // 重置为原样复制
    setSelectingVideo(false);
    setLoadingPromotionInfo(false);
    setCopying(false);
  };

  // 新增：选择视频文件
  const handleSelectVideo = async () => {
    setSelectingVideo(true);
    try {
      // 使用正确的OpenFileReq格式
      const openFileReq = {
        title: "选择视频文件",
        display_name: "视频文件",
        pattern: "*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.flv;*.webm;*.m4v;*.3gp;*.f4v",
        is_multiple: true, // 支持多选
      };

      console.log("调用SystemService.OpenFile，参数:", openFileReq);
      const result = await SystemService.OpenFile(openFileReq);
      console.log("SystemService.OpenFile返回结果:", result);

      if (
        result &&
        result.code === 0 &&
        result.data &&
        result.data.length > 0
      ) {
        const newFiles = result.data.map((filePath) => ({
          id: Date.now() + Math.random(), // 生成唯一ID
          path: filePath,
          name: getFileName(filePath),
          size: null, // 可以后续获取文件大小
        }));

        // 添加到已选择的文件列表中，避免重复
        setSelectedVideoFiles((prev) => {
          const existingPaths = prev.map((f) => f.path);
          const uniqueNewFiles = newFiles.filter(
            (f) => !existingPaths.includes(f.path)
          );
          return [...prev, ...uniqueNewFiles];
        });

        toast.success(`成功选择 ${newFiles.length} 个视频文件`);
      } else {
        console.log("用户取消了文件选择或未选择文件");
      }
    } catch (error) {
      console.error("选择视频文件失败:", error);
      toast.error("选择视频文件失败: " + error.message);
    } finally {
      setSelectingVideo(false);
    }
  };

  // 新增：移除单个视频文件
  const handleRemoveVideo = (fileId) => {
    setSelectedVideoFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  // 新增：清除所有视频文件
  const handleClearAllVideos = () => {
    setSelectedVideoFiles([]);
  };

  // 新增：获取文件名（从完整路径中提取）
  const getFileName = (filePath) => {
    if (!filePath) return "";
    return filePath.split(/[/\\]/).pop() || filePath;
  };

  // 新增：加载广告详情，获取已有视频素材
  const loadPromotionInfo = async () => {
    if (
      !selectedAccount?.account_id ||
      !promotion?.promotion_id ||
      !promotion?.advertiser_id
    )
      return;

    setLoadingPromotionInfo(true);
    try {
      // 如果有传入的promotionDetail，直接使用
      if (promotionDetail) {
        console.log("使用传入的promotionDetail数据:", promotionDetail);
        // promotionDetail已经是解析后的广告数据，直接使用
        await parsePromotionDataDirectly(promotionDetail);
        return;
      }

      const params = {
        account_id: selectedAccount.account_id,
        advertiser_id: parseInt(promotion.advertiser_id),
        promotion_ids: [promotion.promotion_id.toString()],
        need_invisible_material: false,
      };

      console.log("调用GetPromotionInfo，参数:", params.promotion_ids);
      const result = await PromotionService.GetPromotionInfo(params);
      console.log("GetPromotionInfo返回结果:", result);

      if (result && result.code === 0 && result.data) {
        await parsePromotionData(result.data);
      } else {
        console.error("获取广告详情失败:", result);
        toast.error("获取广告详情失败: " + (result?.msg || "未知错误"));
      }
    } catch (error) {
      console.error("加载广告详情异常:", error);
      toast.error("加载广告详情失败: " + error.message);
    } finally {
      setLoadingPromotionInfo(false);
    }
  };

  // 解析广告详情数据（从API响应中解析）
  const parsePromotionData = async (data) => {
    console.log("=== 开始解析视频数据（从API响应） ===");
    console.log("data:", data);
    console.log("promotion.promotion_id:", promotion.promotion_id);

    // 修正数据解析路径：处理双层嵌套结构
    let promotionData = null;
    if (data.data && data.data[promotion.promotion_id]) {
      // 双层嵌套格式：data.data[promotion.promotion_id]
      promotionData = data.data[promotion.promotion_id];
    } else if (data[promotion.promotion_id]) {
      // 单层格式：data[promotion.promotion_id]
      promotionData = data[promotion.promotion_id];
    }
    console.log("获取到的广告数据 promotionData:", promotionData);

    if (!promotionData) {
      console.error(
        "无法找到广告数据，可用的promotion_id:",
        Object.keys(data.data || {})
      );
      toast.error("无法找到指定广告的数据");
      return;
    }

    await parsePromotionDataDirectly(promotionData);
  };

  // 直接解析广告详情数据（promotionData已经是解析后的广告对象）
  const parsePromotionDataDirectly = async (promotionData) => {
    console.log("=== 开始解析视频数据（直接解析） ===");
    console.log("promotionData:", promotionData);

    console.log("promotionData.material_group:", promotionData.material_group);

    // 解析已有视频素材 - 使用正确的嵌套路径
    const videos = [];
    if (
      promotionData &&
      promotionData.material_group &&
      promotionData.material_group.video_material_info
    ) {
      const videoMaterialInfo =
        promotionData.material_group.video_material_info;
      console.log(
        "找到video_material_info数组，长度:",
        videoMaterialInfo.length
      );
      console.log("video_material_info详细数据:", videoMaterialInfo);

      videoMaterialInfo.forEach((videoMaterial, index) => {
        console.log(`处理第${index + 1}个视频素材:`, videoMaterial);
        if (videoMaterial && videoMaterial.video_info) {
          const videoInfo = videoMaterial.video_info;
          console.log(`第${index + 1}个视频的video_info:`, videoInfo);

          const video = {
            id: videoInfo.video_id || videoInfo.vid || `video_${index}`,
            name: `视频_${videoInfo.video_id || videoInfo.vid || index + 1}`,
            duration: videoInfo.duration || videoInfo.video_duration || 0,
            size: videoInfo.initial_size || videoInfo.size || 0,
            cover_uri: videoInfo.cover_uri || videoInfo.thumb_uri || "",
            width: videoInfo.width || videoInfo.thumb_width || 0,
            height: videoInfo.height || videoInfo.thumb_height || 0,
            materialInfo: videoMaterial, // 保存完整的素材信息用于复制
          };

          videos.push(video);
          console.log(`第${index + 1}个视频解析结果:`, video);
        } else {
          console.log(`第${index + 1}个视频素材数据格式异常:`, videoMaterial);
        }
      });
    } else {
      console.log(
        "promotionData.material_group.video_material_info不存在，详细检查:",
        {
          hasPromotionData: !!promotionData,
          hasMaterialGroup: !!(promotionData && promotionData.material_group),
          hasVideoMaterialInfo: !!(
            promotionData &&
            promotionData.material_group &&
            promotionData.material_group.video_material_info
          ),
          materialGroup: promotionData
            ? promotionData.material_group
            : "promotionData为空",
        }
      );
    }

    console.log("=== 视频解析完成 ===");
    console.log("最终解析到的视频列表:", videos);
    console.log("视频数量:", videos.length);

    setExistingVideos(videos);

    // 如果有视频，调用GetMaterialVideoInfo获取视频详情
    if (videos.length > 0) {
      await getVideoDetails(videos, promotionData);
    } else {
      console.log("没有找到视频，设置复制方式为original");
      setCopyMode("original");
    }
  };

  // 获取视频详情
  const getVideoDetails = async (videos, promotionData) => {
    // 收集所有的vid
    const vids = [];
    if (
      promotionData &&
      promotionData.material_group &&
      promotionData.material_group.video_material_info
    ) {
      const videoMaterialInfo =
        promotionData.material_group.video_material_info;
      videoMaterialInfo.forEach((videoMaterial, index) => {
        if (videoMaterial && videoMaterial.video_info) {
          const videoInfo = videoMaterial.video_info;
          const vid = videoInfo.vid || videoInfo.video_id;
          if (vid) {
            vids.push(vid);
          }
        }
      });
    }

    console.log("=== 开始获取视频素材详情 ===");
    console.log("收集到的vids:", vids);

    if (vids.length > 0) {
      try {
        // 构建GetMaterialVideoInfo请求参数
        const videoInfoParams = {
          accountId: selectedAccount.account_id,
          advertiserId: parseInt(promotion.advertiser_id),
          vids: vids,
        };

        console.log("=== GetMaterialVideoInfo 请求数据 ===");
        console.log("请求参数:", videoInfoParams);

        // 调用GetMaterialVideoInfo方法
        const videoInfoResult = await AdvertiserService.getMaterialVideoInfo(
          videoInfoParams
        );

        console.log("=== GetMaterialVideoInfo 响应数据 ===");
        console.log("响应结果:", videoInfoResult);

        if (
          videoInfoResult &&
          videoInfoResult.code === 0 &&
          videoInfoResult.data
        ) {
          console.log("视频素材详情获取成功");
          console.log(
            "详细响应数据:",
            JSON.stringify(videoInfoResult.data, null, 2)
          );
        } else {
          console.log("视频素材详情获取失败:", videoInfoResult?.msg);
        }

        // 如果有视频素材，调用GetMaterialTitle获取素材标题
        if (
          promotionData &&
          promotionData.material_group &&
          promotionData.material_group.video_material_info
        ) {
          const videoMaterials = [];

          // 收集material_id和lego_mid用于获取素材标题
          promotionData.material_group.video_material_info.forEach(
            (videoMaterial) => {
              if (
                videoMaterial &&
                videoMaterial.material_id &&
                videoMaterial.lego_mid
              ) {
                videoMaterials.push({
                  material_id: videoMaterial.material_id,
                  lego_mid: videoMaterial.lego_mid,
                });
              }
            }
          );

          console.log("=== 开始获取素材标题 ===");
          console.log("收集到的素材信息:", videoMaterials);

          if (videoMaterials.length > 0) {
            try {
              // 构建GetMaterialTitle请求参数
              const materialTitleParams = {
                accountId: selectedAccount.account_id,
                advertiserId: parseInt(promotion.advertiser_id),
                videoMaterials: videoMaterials,
                imageMaterials: [],
                awemePhotoMaterials: [],
              };

              console.log("=== GetMaterialTitle 请求数据 ===");
              console.log("请求参数:", materialTitleParams);

              // 调用GetMaterialTitle方法
              const materialTitleResult =
                await AdvertiserService.getMaterialTitle(materialTitleParams);

              console.log("=== GetMaterialTitle 响应数据 ===");
              console.log("响应结果:", materialTitleResult);

              if (
                materialTitleResult &&
                materialTitleResult.code === 0 &&
                materialTitleResult.data
              ) {
                console.log("素材标题获取成功");
                console.log(
                  "素材标题详细数据:",
                  JSON.stringify(materialTitleResult.data, null, 2)
                );

                // 将素材标题信息合并到视频数据中
                const titleData =
                  materialTitleResult.data.data || materialTitleResult.data;
                if (titleData && titleData.video_materials) {
                  const titleMap = {};
                  titleData.video_materials.forEach((videoTitle) => {
                    if (videoTitle.material_id) {
                      titleMap[videoTitle.material_id] =
                        videoTitle.material_name;
                    }
                  });

                  // 更新现有视频数据，添加标题信息
                  const updatedVideos = videos.map((video) => {
                    if (video.materialInfo && video.materialInfo.material_id) {
                      const materialTitle =
                        titleMap[video.materialInfo.material_id];
                      if (materialTitle) {
                        return {
                          ...video,
                          name: materialTitle, // 使用真实的素材标题
                          originalName: video.name, // 保存原始名称
                        };
                      }
                    }
                    return video;
                  });

                  console.log("=== 素材标题合并完成 ===");
                  console.log("更新后的视频列表:", updatedVideos);
                  setExistingVideos(updatedVideos);

                  toast.success(
                    `获取到 ${titleData.video_materials.length} 个视频素材标题`
                  );
                }
              } else {
                console.log("素材标题获取失败:", materialTitleResult?.msg);
                toast.warning("获取素材标题失败，但不影响复制流程");
              }
            } catch (titleError) {
              console.error("获取素材标题异常:", titleError);
              toast.warning("获取素材标题异常: " + titleError.message);
            }
          } else {
            console.log("没有有效的素材信息，跳过获取素材标题");
          }
        }

        // 如果有已有视频，默认选择使用已有视频
        setCopyMode("original");
        // 默认选中所有已有视频
        setSelectedExistingVideos(videos.map((v) => v.id));
        console.log(
          "设置复制方式为original，选中的视频ID:",
          videos.map((v) => v.id)
        );
      } catch (error) {
        console.error("获取视频素材详情异常:", error);
      }
    } else {
      console.log("没有有效的vid，跳过获取视频详情");
    }

    // 如果有已有视频，默认选择使用已有视频
    setCopyMode("original");
    // 默认选中所有已有视频
    setSelectedExistingVideos(videos.map((v) => v.id));
    console.log(
      "设置复制方式为original，选中的视频ID:",
      videos.map((v) => v.id)
    );
  };

  // 新增：处理已有视频选择
  const handleExistingVideoToggle = (videoId) => {
    setSelectedExistingVideos((prev) => {
      if (prev.includes(videoId)) {
        return prev.filter((id) => id !== videoId);
      } else {
        return [...prev, videoId];
      }
    });
  };

  // 新增：全选/取消全选已有视频
  const handleToggleAllExistingVideos = () => {
    if (selectedExistingVideos.length === existingVideos.length) {
      setSelectedExistingVideos([]);
    } else {
      setSelectedExistingVideos(existingVideos.map((v) => v.id));
    }
  };

  // 新增：格式化视频时长
  const formatDuration = (seconds) => {
    if (!seconds) return "00:00";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // 新增：格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return "未知";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  // 处理视频裂变流程
  const handleVideoVariation = async () => {
    try {
      console.log("开始视频裂变流程...");

      // 0. 验证项目选择并计算targetProjectId
      let targetProjectId = "";
      if (projectSource === "current") {
        targetProjectId = promotion?.project_id;
      } else if (projectSource === "new") {
        if (!newProjectName.trim()) {
          toast.warning("请输入新项目名称");
          return;
        }
        // TODO: 需要先创建新项目，这里暂时使用原项目ID
        toast.warning("创建新项目功能暂未实现，请选择其他选项");
        return;
      } else if (projectSource === "other") {
        if (!selectedProjectId) {
          toast.warning("请选择目标项目");
          return;
        }
        targetProjectId = selectedProjectId;
      }

      if (!targetProjectId) {
        toast.warning("请选择目标项目");
        return;
      }

      // 1. 收集所有视频源（原视频 + 新上传视频）
      const videoSources = [];

      // 添加选中的原视频
      const selectedVideos = existingVideos.filter((video) =>
        selectedExistingVideos.includes(video.id)
      );
      for (const video of selectedVideos) {
        videoSources.push({
          type: "existing",
          source: video,
          name: video.name,
        });
      }

      // 添加新上传的视频
      for (const videoFile of selectedVideoFiles) {
        videoSources.push({
          type: "upload",
          source: videoFile,
          name: videoFile.name,
        });
      }

      if (videoSources.length === 0) {
        toast.error("请至少选择一种视频素材进行裂变");
        return;
      }

      console.log("收集到的视频源:", videoSources);

      // 2. 为每个视频源生成变体
      const allVariationsBySource = []; // 每个元素是一个视频源的所有变体
      toast.info(`正在为 ${videoSources.length} 个视频源生成变体...`);

      for (
        let sourceIndex = 0;
        sourceIndex < videoSources.length;
        sourceIndex++
      ) {
        const videoSource = videoSources[sourceIndex];
        console.log(
          `处理第 ${sourceIndex + 1}/${videoSources.length} 个视频源: ${
            videoSource.name
          }`
        );

        let localVideoPath = "";
        let originalInfo = null;

        if (videoSource.type === "existing") {
          // 处理原视频：需要先下载
          const video = videoSource.source;
          const vid =
            video.materialInfo?.video_info?.vid ||
            video.materialInfo?.video_info?.video_id ||
            video.id;

          if (!vid) {
            console.error(`无法获取视频ID:`, video);
            toast.warning(`跳过无效的原视频: ${video.name}`);
            continue;
          }

          // 获取视频详细信息
          const videoInfoResult = await AdvertiserService.getMaterialVideoInfo({
            accountId: selectedAccount.account_id,
            advertiserId: parseInt(promotion.advertiser_id),
            vids: [vid],
          });

          if (
            !videoInfoResult ||
            videoInfoResult.code !== 0 ||
            !videoInfoResult.data
          ) {
            console.error(`获取视频信息失败:`, vid);
            toast.warning(`跳过无法获取信息的原视频: ${video.name}`);
            continue;
          }

          const videoData = videoInfoResult.data.data || {};
          const videoInfo = videoData[vid];

          if (!videoInfo || !videoInfo.original_video_url) {
            console.error(`视频信息无效:`, videoInfo);
            toast.warning(`跳过信息无效的原视频: ${video.name}`);
            continue;
          }

          // 下载视频
          const downloadResult = await SystemService.DownloadFile({
            url: videoInfo.original_video_url,
            account_id: selectedAccount.account_id,
            format: videoInfo.format,
          });

          if (
            !downloadResult ||
            downloadResult.code !== 0 ||
            !downloadResult.data?.local_file_name
          ) {
            console.error(`视频下载失败:`, downloadResult?.msg);
            toast.warning(`跳过下载失败的原视频: ${video.name}`);
            continue;
          }

          localVideoPath = downloadResult.data.local_file_name;
          originalInfo = videoInfo;
          console.log(`原视频 ${video.name} 下载成功: ${localVideoPath}`);
        } else if (videoSource.type === "upload") {
          // 处理新上传视频：直接使用本地路径
          localVideoPath = videoSource.source.path;
          console.log(
            `使用新上传视频: ${videoSource.name} -> ${localVideoPath}`
          );

          // 新上传视频的原始信息可以设置默认值或从文件获取
          originalInfo = {
            vwidth: 1080, // 默认分辨率，可以后续优化为从文件获取
            vheight: 1920,
            format: "mp4",
          };
        }

        // 为当前视频源生成变体
        try {
          const aspectRatio = originalInfo
            ? `${originalInfo.vwidth}:${originalInfo.vheight}`
            : "9:16";
          const resolution = originalInfo
            ? {
                width: originalInfo.vwidth,
                height: originalInfo.vheight,
              }
            : { width: 1080, height: 1920 };

          console.log(
            `为视频源 ${videoSource.name} 生成 ${copyCount} 个变体...`
          );

          const variationResult = await FfmpegService.GenerateVideoVariations({
            videoFile: localVideoPath,
            count: copyCount,
            aspectRatio: aspectRatio,
            resolution: resolution,
            effects: ["rotation", "brightness", "color", "sharpen"],
            isOriginSound: true,
          });

          if (
            variationResult &&
            variationResult.code === 0 &&
            variationResult.data?.video_paths
          ) {
            const sourceVariations = variationResult.data.video_paths.map(
              (videoPath, index) => ({
                id: Date.now() + Math.random() + sourceIndex * 1000 + index,
                path: videoPath,
                name: `${videoSource.name}_变体${index + 1}`,
                size: null,
                sourceIndex: sourceIndex,
                variationIndex: index,
              })
            );

            allVariationsBySource.push(sourceVariations);
            console.log(
              `视频源 ${videoSource.name} 变体生成成功，生成了 ${sourceVariations.length} 个变体`
            );
            toast.success(
              `✅ ${videoSource.name} 生成 ${sourceVariations.length} 个变体`
            );
          } else {
            console.error(
              `视频源 ${videoSource.name} 变体生成失败:`,
              variationResult
            );
            toast.warning(`❌ ${videoSource.name} 变体生成失败，跳过该视频源`);
          }
        } catch (error) {
          console.error(`视频源 ${videoSource.name} 变体生成异常:`, error);
          toast.warning(
            `❌ ${videoSource.name} 变体生成异常: ${error.message}`
          );
        }
      }

      if (allVariationsBySource.length === 0) {
        throw new Error("没有成功生成任何视频变体");
      }

      console.log("所有视频源的变体生成完成:", allVariationsBySource);

      // 3. 为每个广告分配变体
      const copyTasks = [];
      toast.info(`正在为 ${copyCount} 个广告分配变体视频...`);

      for (let adIndex = 0; adIndex < copyCount; adIndex++) {
        const finalPromotionName =
          copyCount === 1
            ? newPromotionName.trim()
            : `${newPromotionName.trim()}_${adIndex + 1}`;

        // 为当前广告收集变体视频（每个视频源的第adIndex个变体）
        const adVariationVideos = [];
        for (
          let sourceIndex = 0;
          sourceIndex < allVariationsBySource.length;
          sourceIndex++
        ) {
          const sourceVariations = allVariationsBySource[sourceIndex];
          if (sourceVariations && sourceVariations[adIndex]) {
            adVariationVideos.push(sourceVariations[adIndex]);
          }
        }

        console.log(
          `广告 ${adIndex + 1} (${finalPromotionName}) 分配到 ${
            adVariationVideos.length
          } 个变体视频`
        );

        const copyParams = {
          promotion,
          newPromotionName: finalPromotionName,
          target_project_id: targetProjectId,
          videoFiles: adVariationVideos, // 当前广告的变体视频
          existingVideoMaterials: [],
          isVariationCopy: true, // 标记这是裂变复制任务，避免被优化流程处理
        };

        copyTasks.push(copyParams);
      }

      console.log("开始使用裂变变体批量复制广告:", copyTasks);
      await onCopySuccess(copyTasks);

      const totalVariations = allVariationsBySource.reduce(
        (sum, variations) => sum + variations.length,
        0
      );
      toast.success(
        `🎉 视频裂变完成！生成了 ${totalVariations} 个变体视频，创建了 ${copyCount} 个广告，每个广告都有不同的变体组合`
      );

      // 重置表单并关闭对话框
      resetForm();
      onClose();
    } catch (error) {
      console.error("视频裂变流程失败:", error);
      toast.error("视频裂变失败: " + error.message);
    }
  };

  // 对话框打开时加载数据
  useEffect(() => {
    if (isOpen && promotion) {
      // 设置默认的广告名称
      const timestamp = new Date()
        .toLocaleString("zh-CN")
        .replace(/[\/:\s]/g, "_");
      setNewPromotionName(`复制_${promotion.promotion_name}_${timestamp}`);

      // 加载广告详情，获取已有视频素材
      loadPromotionInfo();
    } else {
      resetForm();
    }
  }, [isOpen, promotion, selectedAccount]);

  // 处理复制操作
  const handleCopy = async () => {
    if (!newPromotionName.trim()) {
      toast.warning("请输入新广告名称");
      return;
    }

    // 验证目标项目ID
    if (!targetProjectId) {
      toast.warning("目标项目ID不能为空");
      return;
    }

    // 验证素材选择
    if (copyMode === "variation") {
      // 裂变复制：必须选择至少一种视频素材（原视频或新上传）
      if (
        selectedExistingVideos.length === 0 &&
        selectedVideoFiles.length === 0
      ) {
        toast.warning(
          "视频裂变需要至少选择一种视频素材：原视频素材或上传新视频"
        );
        return;
      }
    } else {
      // 原样复制：至少要有一种素材（原视频或新上传）
      if (
        selectedExistingVideos.length === 0 &&
        selectedVideoFiles.length === 0
      ) {
        toast.warning("请至少选择一种素材：原视频素材或上传新视频");
        return;
      }
    }

    setCopying(true);
    try {
      // 处理视频裂变的情况
      if (copyMode === "variation") {
        await handleVideoVariation();
        return;
      }

      // 原样复制模式的优化逻辑
      if (copyMode === "original") {
        // 准备公共的视频素材
        const existingVideoMaterials = existingVideos
          .filter((video) => selectedExistingVideos.includes(video.id))
          .map((video) => video.materialInfo);

        // 构建批量复制参数
        const copyTasks = [];

        // 构建标准的复制任务列表，后端会自动优化多任务的视频上传
        for (let i = 1; i <= copyCount; i++) {
          const finalPromotionName =
            copyCount === 1
              ? newPromotionName.trim()
              : `${newPromotionName.trim()}_${i}`;

          const copyParams = {
            promotion,
            newPromotionName: finalPromotionName,
            targetProjectId: targetProjectId,
            videoFiles: [...selectedVideoFiles],
            existingVideoMaterials,
          };

          copyTasks.push(copyParams);
        }

        // 显示优化提示
        if (selectedVideoFiles.length > 0 && copyCount > 1) {
          toast.info(
            `⚡ 自动启用批量优化：${selectedVideoFiles.length}个新视频只上传一次，用于所有${copyCount}个广告`
          );
        }

        // 执行批量复制
        console.log(`开始批量复制${copyCount}个广告:`, copyTasks);
        console.log(`targetProjectId:`, targetProjectId);
        await onCopySuccess(copyTasks);

        // 重置表单并关闭对话框
        resetForm();
        onClose();
      }
    } catch (error) {
      console.error("批量复制广告失败:", error);
      toast.error("批量复制广告失败: " + error.message);
    } finally {
      setCopying(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-3"
      style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-xl"
        style={{
          backgroundColor: "var(--panel-bg)",
          border: "1px solid var(--border-color)",
          maxHeight: "95vh",
          overflow: "hidden",
        }}
      >
        {/* 标题栏 */}
        <div
          className="flex items-center justify-between px-4 py-3 border-b"
          style={{
            borderColor: "var(--border-color)",
            backgroundColor: "var(--sidebar-bg)",
          }}
        >
          <div className="flex items-center space-x-2">
            <Copy size={18} style={{ color: "var(--primary-color)" }} />
            <h3
              className="text-lg font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              复制广告
            </h3>
          </div>
          <button
            onClick={onClose}
            className="w-7 h-7 flex items-center justify-center hover:bg-gray-100 rounded transition-colors"
            style={{ ":hover": { backgroundColor: "var(--hover-bg)" } }}
          >
            <X size={16} style={{ color: "var(--text-secondary)" }} />
          </button>
        </div>

        {/* 内容区域 */}
        <div
          className="overflow-y-auto"
          style={{ maxHeight: "calc(95vh - 120px)" }}
        >
          <div className="px-4 py-4 space-y-4">
            {/* 源广告信息 */}
            <div
              className="border rounded p-3"
              style={{
                backgroundColor: "var(--sidebar-bg)",
                borderColor: "var(--border-color)",
              }}
            >
              <div
                className="text-sm font-medium mb-2"
                style={{ color: "var(--text-primary)" }}
              >
                源广告信息
              </div>
              <div className="grid grid-cols-3 gap-3 text-xs">
                <div>
                  <span
                    className="block"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    名称
                  </span>
                  <span
                    className="font-medium truncate block"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {promotion?.promotion_name}
                  </span>
                </div>
                <div>
                  <span
                    className="block"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    ID
                  </span>
                  <span
                    className="font-mono"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {promotion?.promotion_id}
                  </span>
                </div>
                <div>
                  <span
                    className="block"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    广告主
                  </span>
                  <span
                    className="font-mono"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {promotion?.advertiser_id}
                  </span>
                </div>
              </div>
            </div>

            {/* 新广告配置 */}
            <div className="space-y-3">
              <div
                className="text-sm font-medium"
                style={{ color: "var(--text-primary)" }}
              >
                新广告配置
              </div>

              {/* 新广告名称 */}
              <div>
                <label
                  className="block text-xs font-medium mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  新广告名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={newPromotionName}
                  onChange={(e) => setNewPromotionName(e.target.value)}
                  placeholder="请输入新广告名称"
                  className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  style={{
                    backgroundColor: "var(--input-bg)",
                    borderColor: "var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                  maxLength={100}
                />
              </div>

              {/* 复制数量 */}
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label
                    className="block text-xs font-medium mb-2"
                    style={{ color: "var(--text-primary)" }}
                  >
                    复制数量
                  </label>
                  <input
                    type="number"
                    value={copyCount}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      setCopyCount(Math.max(1, Math.min(50, value)));
                    }}
                    min="1"
                    max="50"
                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                    style={{
                      backgroundColor: "var(--input-bg)",
                      borderColor: "var(--border-color)",
                      color: "var(--text-primary)",
                    }}
                  />
                </div>
                <div className="col-span-2">
                  <div
                    className="text-xs mb-2"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    预览
                  </div>
                  <div
                    className="px-3 py-2 border rounded"
                    style={{
                      backgroundColor: "var(--sidebar-bg)",
                      borderColor: "var(--border-color)",
                    }}
                  >
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      {copyCount === 1
                        ? "复制1个广告"
                        : `批量复制${copyCount}个广告`}
                    </div>
                    {copyCount > 1 && (
                      <div
                        className="text-xs mt-1 truncate"
                        style={{ color: "var(--text-secondary)" }}
                      >
                        {newPromotionName || "新广告名称"}_1、
                        {newPromotionName || "新广告名称"}_2...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 复制方式选择 */}
            <div className="space-y-3">
              <div
                className="text-sm font-medium"
                style={{ color: "var(--text-primary)" }}
              >
                复制方式 <span className="text-red-500">*</span>
              </div>

              <div className="space-y-2">
                {/* 原样复制 */}
                <label
                  className={`flex items-center p-3 border rounded cursor-pointer transition-colors ${
                    copyMode === "original"
                      ? "border-green-400 bg-green-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  style={{
                    backgroundColor:
                      copyMode === "original"
                        ? "var(--success-bg, #ecfdf5)"
                        : "var(--panel-bg)",
                    borderColor:
                      copyMode === "original"
                        ? "var(--success-color, #10b981)"
                        : "var(--border-color)",
                  }}
                >
                  <input
                    type="radio"
                    name="copyMode"
                    value="original"
                    checked={copyMode === "original"}
                    onChange={(e) => setCopyMode(e.target.value)}
                    className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500"
                  />
                  <div className="ml-3 flex-1">
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      原样复制
                    </div>
                    <div
                      className="text-xs"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      每个广告下的素材都相同，可使用原视频素材和/或上传新视频
                      {copyCount > 1 && selectedVideoFiles.length > 0 && (
                        <div
                          className="mt-1 px-2 py-1 rounded text-xs"
                          style={{
                            backgroundColor: "var(--primary-color)",
                            color: "white",
                          }}
                        >
                          ✓ 优化：新视频只上传一次，用于所有{copyCount}个广告
                        </div>
                      )}
                    </div>
                  </div>
                </label>

                {/* 裂变复制 */}
                <label
                  className={`flex items-center p-3 border rounded cursor-pointer transition-colors ${
                    copyMode === "variation"
                      ? "border-orange-400 bg-orange-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  style={{
                    backgroundColor:
                      copyMode === "variation"
                        ? "var(--orange-bg, #fff7ed)"
                        : "var(--panel-bg)",
                    borderColor:
                      copyMode === "variation"
                        ? "var(--orange-color, #f97316)"
                        : "var(--border-color)",
                  }}
                >
                  <input
                    type="radio"
                    name="copyMode"
                    value="variation"
                    checked={copyMode === "variation"}
                    onChange={(e) => setCopyMode(e.target.value)}
                    className="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 focus:ring-orange-500"
                  />
                  <div className="ml-3 flex-1">
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      裂变复制
                    </div>
                    <div
                      className="text-xs"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      基于视频素材（原视频或新上传）生成变体，每个广告分配到不同的变体组合
                      {copyCount > 1 && (
                        <div
                          className="mt-1 text-xs"
                          style={{ color: "var(--orange-color, #f97316)" }}
                        >
                          💡 {copyCount}个广告将分别使用不同的变体视频
                        </div>
                      )}
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* 素材来源 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div
                  className="text-sm font-medium"
                  style={{ color: "var(--text-primary)" }}
                >
                  素材来源
                </div>
                {loadingPromotionInfo && (
                  <div
                    className="flex items-center space-x-1 text-xs"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    <div className="w-3 h-3 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                    <span>加载中...</span>
                  </div>
                )}
              </div>

              {/* 原视频素材列表 */}
              {existingVideos.length > 0 && (
                <div
                  className="border rounded p-3"
                  style={{
                    backgroundColor: "var(--sidebar-bg)",
                    borderColor: "var(--border-color)",
                  }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div
                      className="text-xs font-medium"
                      style={{ color: "var(--text-primary)" }}
                    >
                      原视频素材 ({selectedExistingVideos.length}/
                      {existingVideos.length})
                    </div>
                    <button
                      type="button"
                      onClick={handleToggleAllExistingVideos}
                      className="text-xs px-2 py-1 rounded border hover:bg-gray-100 transition-colors"
                      style={{
                        color: "var(--primary-color)",
                        borderColor: "var(--primary-color)",
                        ":hover": { backgroundColor: "var(--hover-bg)" },
                      }}
                    >
                      {selectedExistingVideos.length === existingVideos.length
                        ? "取消全选"
                        : "全选原视频"}
                    </button>
                  </div>

                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {existingVideos.map((video) => (
                      <div
                        key={video.id}
                        className={`border rounded p-2 cursor-pointer transition-colors ${
                          selectedExistingVideos.includes(video.id)
                            ? "border-green-400 bg-green-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        style={{
                          backgroundColor: selectedExistingVideos.includes(
                            video.id
                          )
                            ? "var(--success-bg, #ecfdf5)"
                            : "var(--panel-bg)",
                          borderColor: selectedExistingVideos.includes(video.id)
                            ? "var(--success-color, #10b981)"
                            : "var(--border-color)",
                        }}
                        onClick={() => handleExistingVideoToggle(video.id)}
                      >
                        <div className="flex items-center space-x-2">
                          <div
                            className="flex-shrink-0 w-6 h-6 rounded flex items-center justify-center"
                            style={{
                              backgroundColor: selectedExistingVideos.includes(
                                video.id
                              )
                                ? "var(--success-color, #10b981)"
                                : "var(--sidebar-bg)",
                            }}
                          >
                            {selectedExistingVideos.includes(video.id) ? (
                              <CheckCircle size={14} color="white" />
                            ) : (
                              <Play
                                size={14}
                                style={{ color: "var(--text-secondary)" }}
                              />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div
                              className="text-xs font-medium truncate"
                              style={{ color: "var(--text-primary)" }}
                            >
                              {video.name}
                            </div>
                            <div
                              className="flex items-center space-x-2 text-xs mt-1"
                              style={{ color: "var(--text-secondary)" }}
                            >
                              <span>
                                {video.width}×{video.height}
                              </span>
                              <span>•</span>
                              <span>{formatDuration(video.duration)}</span>
                              <span>•</span>
                              <span>{formatFileSize(video.initial_size)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 上传视频素材区域 */}
              <div
                className="border rounded p-3"
                style={{
                  backgroundColor: "var(--sidebar-bg)",
                  borderColor: "var(--border-color)",
                }}
              >
                <div className="flex items-center justify-between mb-3">
                  <div
                    className="text-xs font-medium"
                    style={{ color: "var(--text-primary)" }}
                  >
                    上传视频素材{" "}
                    {selectedVideoFiles.length > 0 &&
                      `(${selectedVideoFiles.length}个)`}
                  </div>
                  {selectedVideoFiles.length > 0 && (
                    <button
                      type="button"
                      onClick={handleClearAllVideos}
                      className="text-xs px-2 py-1 rounded border hover:bg-gray-100 transition-colors"
                      style={{
                        color: "var(--text-secondary)",
                        borderColor: "var(--text-secondary)",
                        ":hover": { backgroundColor: "var(--hover-bg)" },
                      }}
                    >
                      清空
                    </button>
                  )}
                </div>

                {/* 选择文件按钮 */}
                <button
                  type="button"
                  onClick={handleSelectVideo}
                  disabled={selectingVideo}
                  className="w-full px-4 py-4 border-2 border-dashed rounded hover:border-blue-400 transition-colors flex flex-col items-center justify-center space-y-2 mb-3"
                  style={{
                    borderColor: selectingVideo
                      ? "var(--text-secondary)"
                      : "var(--border-color)",
                    backgroundColor: "var(--input-bg)",
                    ":hover": { borderColor: "var(--primary-color)" },
                  }}
                >
                  <Upload
                    size={20}
                    style={{
                      color: selectingVideo
                        ? "var(--text-secondary)"
                        : "var(--primary-color)",
                    }}
                  />
                  <div
                    className="text-sm font-medium"
                    style={{ color: "var(--text-primary)" }}
                  >
                    {selectingVideo ? "选择中..." : "点击选择视频文件"}
                  </div>
                  <div
                    className="text-xs text-center"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    支持 MP4、AVI、MOV、MKV 等格式
                  </div>
                </button>

                {/* 已选择新文件列表 */}
                {selectedVideoFiles.length > 0 && (
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {selectedVideoFiles.map((file) => (
                      <div
                        key={file.id}
                        className="border rounded p-2 hover:bg-gray-50 transition-colors"
                        style={{
                          backgroundColor: "var(--panel-bg)",
                          borderColor: "var(--border-color)",
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 flex-1 min-w-0">
                            <div
                              className="w-6 h-6 rounded flex items-center justify-center"
                              style={{
                                backgroundColor: "var(--primary-color)",
                              }}
                            >
                              <File size={12} color="white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div
                                className="text-xs font-medium truncate"
                                style={{ color: "var(--text-primary)" }}
                              >
                                {file.name}
                              </div>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveVideo(file.id)}
                            className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-100 transition-colors ml-2"
                            style={{
                              color: "var(--text-secondary)",
                              ":hover": { backgroundColor: "var(--hover-bg)" },
                            }}
                          >
                            <X size={12} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div
                className="text-xs px-2 py-1 rounded"
                style={{
                  backgroundColor: "var(--sidebar-bg)",
                  color: "var(--text-secondary)",
                }}
              >
                {copyMode === "original" && (
                  <div>
                    原样复制：使用选中的原视频素材和上传的新视频素材
                    {copyCount > 1 && selectedVideoFiles.length > 0 && (
                      <div className="mt-1 text-green-600 font-medium">
                        ⚡ 已启用批量优化：{selectedVideoFiles.length}
                        个新视频文件只上传一次，节省
                        {(copyCount - 1) * selectedVideoFiles.length}次重复上传
                      </div>
                    )}
                  </div>
                )}
                {copyMode === "variation" &&
                  "裂变复制：基于选中的视频素材（原视频+新上传）生成变体视频，每个广告分配到不同的变体"}
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div
          className="px-4 py-3 border-t"
          style={{
            borderColor: "var(--border-color)",
            backgroundColor: "var(--sidebar-bg)",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="text-xs" style={{ color: "var(--text-secondary)" }}>
              {copyCount > 1 && `将创建 ${copyCount} 个广告副本`}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={onClose}
                variant="secondary"
                disabled={copying}
                className="px-4 py-2 text-sm"
              >
                取消
              </Button>
              <Button
                onClick={handleCopy}
                variant="primary"
                disabled={
                  copying || !newPromotionName.trim() || !targetProjectId
                }
                loading={copying}
                icon={<Copy size={14} />}
                className="px-4 py-2 text-sm"
              >
                {copying
                  ? copyMode === "variation"
                    ? "视频裂变中..."
                    : copyCount === 1
                    ? "复制中..."
                    : `批量复制中...`
                  : copyMode === "variation"
                  ? "开始视频裂变"
                  : copyCount === 1
                  ? "确认复制"
                  : `确认复制 ${copyCount} 个`}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CopyPromotionDialog;
