import React from "react";
import {
  HashRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ToastContainer } from "./components/ui/Toast";
import { AccountProvider } from "./contexts/AccountContext";
import { StrategyMapProvider } from "./contexts/StrategyMapContext";
import { CopyProjectDetailProvider } from "./contexts/CopyProjectContext";
import { CopyPromotionDetailProvider } from "./contexts/CopyPromotionContext";
import { CopyPageProvider } from "./contexts/CopyPageContext";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import OALoginDialog from "./components/auth/OALoginDialog";
import MainPage from "./pages/MainPage";
import ProxyManager from "./pages/ProxyManager";
import VideoProcessor from "./pages/VideoProcessor";
import TextPresetPage from "./pages/TextPresetPage";
import ClipPage from "./pages/ClipPage";
import "./index.css";

// 应用内容组件 - 需要认证后才能访问
const AppContent = () => {
  const { isAuthenticated } = useAuth();

  // 如果未认证，显示登录界面
  if (!isAuthenticated) {
    return <OALoginDialog />;
  }

  // 已认证，显示主应用
  return (
    <AccountProvider>
      <StrategyMapProvider>
        <CopyProjectDetailProvider>
          <CopyPromotionDetailProvider>
            <CopyPageProvider>
              <Router>
                <Routes>
                  <Route path="/" element={<MainPage />} />
                  <Route path="/proxy" element={<ProxyManager />} />
                  <Route path="/video" element={<VideoProcessor />} />
                  <Route path="/text" element={<TextPresetPage />} />
                  <Route path="/clip" element={<ClipPage />} />
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
                <ToastContainer />
              </Router>
            </CopyPageProvider>
          </CopyPromotionDetailProvider>
        </CopyProjectDetailProvider>
      </StrategyMapProvider>
    </AccountProvider>
  );
};

// 主应用组件 - 提供认证上下文
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
