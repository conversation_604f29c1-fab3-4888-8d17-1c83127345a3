import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LogoutConfirmDialog from './LogoutConfirmDialog';

const UserInfo = ({ className = '' }) => {
  const { user, logout } = useAuth();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);

  const handleLogoutClick = () => {
    setShowDropdown(false);
    setShowLogoutDialog(true);
  };

  const handleLogoutConfirm = () => {
    logout();
    setShowLogoutDialog(false);
  };

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false);
  };

  if (!user) return null;

  return (
    <div className={`relative ${className}`}>
      <div
        className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"
        onClick={() => setShowDropdown(!showDropdown)}
      >
        {/* 用户头像 */}
        <div className="relative">
          {user.avatar ? (
            <img
              src={user.avatar}
              alt={user.name}
              className="w-8 h-8 rounded-full object-cover border-2 border-gray-200"
              onError={(e) => {
                // 如果头像加载失败，显示默认头像
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
          ) : null}
          <div
            className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium border-2 border-gray-200"
            style={{ display: user.avatar ? 'none' : 'flex' }}
          >
            {user.name?.charAt(0)?.toUpperCase() || 'U'}
          </div>
          {/* 在线状态指示器 */}
          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
        </div>

        {/* 用户信息 */}
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {user.name}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {user.department}
          </div>
        </div>

        {/* 下拉箭头 */}
        <svg
          className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
            showDropdown ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {/* 下拉菜单 */}
      {showDropdown && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setShowDropdown(false)}
          />
          
          {/* 菜单内容 */}
          <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
            {/* 用户详细信息 */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-lg">
                      {user.name?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">
                    {user.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {user.department}
                  </div>
                  <div className="text-xs text-gray-400">
                    ID: {user.id}
                  </div>
                </div>
              </div>
              
              {user.email && (
                <div className="mt-2 text-xs text-gray-500">
                  邮箱: {user.email}
                </div>
              )}
            </div>

            {/* 权限信息 */}
            {user.permissions && (
              <div className="p-3 border-b border-gray-100">
                <div className="text-xs text-gray-500 mb-2">权限信息</div>
                <div className="grid grid-cols-2 gap-1 text-xs">
                  <div className={`flex items-center ${user.permissions.canManageUsers ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${user.permissions.canManageUsers ? 'bg-green-400' : 'bg-gray-300'}`}></div>
                    用户管理
                  </div>
                  <div className={`flex items-center ${user.permissions.canManageRoles ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${user.permissions.canManageRoles ? 'bg-green-400' : 'bg-gray-300'}`}></div>
                    角色管理
                  </div>
                  <div className={`flex items-center ${user.permissions.canViewLogs ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${user.permissions.canViewLogs ? 'bg-green-400' : 'bg-gray-300'}`}></div>
                    查看日志
                  </div>
                  <div className={`flex items-center ${user.permissions.canResetPassword ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${user.permissions.canResetPassword ? 'bg-green-400' : 'bg-gray-300'}`}></div>
                    重置密码
                  </div>
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="p-2">
              <button
                onClick={handleLogoutClick}
                className="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                退出登录
              </button>
            </div>
          </div>
        </>
      )}

      {/* 退出登录确认对话框 */}
      <LogoutConfirmDialog
        isOpen={showLogoutDialog}
        onConfirm={handleLogoutConfirm}
        onCancel={handleLogoutCancel}
        user={user}
      />
    </div>
  );
};

export default UserInfo; 