import React from 'react';
import { Button } from '../ui';

const LogoutConfirmDialog = ({ isOpen, onConfirm, onCancel, user }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md mx-4">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">确认退出登录</h2>
          <p className="text-gray-600">退出后需要重新登录才能继续使用</p>
        </div>

        <div className="flex space-x-4">
          <Button
            variant="secondary"
            onClick={onCancel}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            variant="danger"
            onClick={onConfirm}
            className="flex-1"
          >
            确认退出
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LogoutConfirmDialog; 