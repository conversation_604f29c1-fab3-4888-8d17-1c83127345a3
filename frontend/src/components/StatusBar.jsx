import React from 'react';
import { FileVideo, Wand2, CheckCir<PERSON>, Clock, AlertCircle } from 'lucide-react';

const StatusBar = ({ 
  // 基础状态信息
  totalVideos = 0,
  completedVideos = 0,
  processingVideos = 0,
  selectedVideo = null,
  
  // 进度条相关
  showProgress = false,
  progress = 0,
  progressText = '',
  isRealProgress = false,
  currentVideoIndex = 1,
  totalProgressVideos = 1,
  completedProgressVideos = 0
}) => {
  return (
    <div className="h-6 bg-slate-100 border-t border-slate-300 flex items-center justify-between px-4 text-xs text-slate-600 flex-shrink-0">
      {/* 左侧状态信息 */}
      <div className="flex items-center space-x-4">
        {/* 视频统计 */}
        <div className="flex items-center space-x-1">
          <FileVideo size={12} className="text-slate-500" />
          <span>视频: {totalVideos}</span>
        </div>
        
        {/* 完成状态 */}
        {completedVideos > 0 && (
          <div className="flex items-center space-x-1">
            <CheckCircle size={12} className="text-green-600" />
            <span>已完成: {completedVideos}</span>
          </div>
        )}
        
        {/* 处理中状态 */}
        {processingVideos > 0 && (
          <div className="flex items-center space-x-1">
            <Clock size={12} className="text-blue-600" />
            <span>处理中: {processingVideos}</span>
          </div>
        )}
        
        {/* 当前选中视频 */}
        {selectedVideo && (
          <div className="flex items-center space-x-1 text-slate-700">
            <span>|</span>
            <span>当前: {selectedVideo.name}</span>
            <span className="text-slate-500">({selectedVideo.resolution})</span>
          </div>
        )}
      </div>
      
      {/* 右侧进度条 */}
      <div className="flex items-center space-x-3">
        {/* 实时进度状态标签 */}
        {showProgress && isRealProgress && (
          <div className="flex items-center space-x-2">
            <span className="px-2 py-0.5 bg-green-100 text-green-700 rounded text-xs border border-green-200">
              第 {currentVideoIndex}/{totalProgressVideos}
            </span>
            <span className="px-2 py-0.5 bg-blue-100 text-blue-700 rounded text-xs border border-blue-200">
              完成 {completedProgressVideos}
            </span>
          </div>
        )}
        
        {/* 进度条 */}
        {showProgress && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin">
              <Wand2 size={12} className="text-blue-600" />
            </div>
            <span className="text-slate-700 min-w-0 max-w-48 truncate">{progressText}</span>
            <div className="w-24 bg-slate-300 rounded-full h-1.5 flex-shrink-0">
              <div 
                className={`h-1.5 rounded-full transition-all duration-300 ease-out ${
                  isRealProgress 
                    ? 'bg-gradient-to-r from-green-500 to-blue-600' 
                    : 'bg-gradient-to-r from-blue-500 to-purple-600'
                }`}
                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
              ></div>
            </div>
            <span className="text-slate-700 font-medium min-w-[2.5rem] text-right">
              {Math.round(Math.max(0, Math.min(100, progress)))}%
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatusBar; 