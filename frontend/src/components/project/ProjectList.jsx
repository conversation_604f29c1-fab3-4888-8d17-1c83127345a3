import React from 'react';
import { Trash2, RefreshCw, Edit, Check, X } from 'lucide-react';

// 常量定义
export const PROJECT_STATUS = {
  ACTIVE: 0,
  PAUSED: 1,
  DELETED: 2
};
export const PROJECT_START_STATUS = {
  START: 0,
  STOP: 1,
};
export const PROJECT_SECOND_STATUS = {
  PAUSED: 2,
};
export const STATUS_COLORS = {
  [PROJECT_STATUS.ACTIVE]: 'text-emerald-600 bg-emerald-50 border-emerald-200',
  [PROJECT_STATUS.PAUSED]: 'text-amber-600 bg-amber-50 border-amber-200',
  [PROJECT_STATUS.DELETED]: 'text-red-600 bg-red-50 border-red-200'
};
export const START_STATUS_COLORS = {
  [PROJECT_START_STATUS.START]: 'text-emerald-600 bg-emerald-50 border-emerald-200',
  [PROJECT_START_STATUS.STOP]: 'text-amber-600 bg-amber-50 border-amber-200',
};
export const STATUS_TEXT = {
  [PROJECT_STATUS.ACTIVE]: '投放中',
  [PROJECT_STATUS.PAUSED]: '已暂停',
  [PROJECT_STATUS.DELETED]: '已删除'
};
export const START_STATUS_TEXT = {
  [PROJECT_START_STATUS.START]: '已开启',
  [PROJECT_START_STATUS.STOP]: '已暂停'
};

// 格式化工具函数
const formatTime = (timeStr) => {
  if (!timeStr) return '-';
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
  } catch {
    return timeStr;
  }
};

const formatAmount = (amount) => {
  if (!amount) return '¥0.00';
  return `¥${amount}`;
};

const ProjectList = ({
  loading,
  data,
  pagination,
  selectedItems,
  editingProjectId,
  editingProjectName,
  setEditingProjectName,
  onSelectItem,
  onSelectAll,
  onUpdateStatus,
  onDeleteProject,
  onProjectSelect,
  onEditProjectName,
  onCancelEditProjectName,
  onSaveProjectName,
  onEditInputKeyDown
}) => {
  // 计算序号
  const getRowIndex = (index) => {
    return index + 1 + (pagination.page - 1) * pagination.pageSize;
  };

  if (loading) {
    return (
      <tr>
        <td colSpan="12" className="px-6 py-8 text-center text-gray-500">
          <div className="flex items-center justify-center space-x-2">
            <RefreshCw size={16} className="animate-spin" />
            <span>加载中...</span>
          </div>
        </td>
      </tr>
    );
  }

  if (data.length === 0) {
    return (
      <tr>
        <td colSpan="12" className="px-6 py-8 text-center text-gray-500">
          暂无项目数据
        </td>
      </tr>
    );
  }

  return data.map((item, index) => {
    const status = item.project_status_first;
    const statusText = item.project_status_first_name;
    const startStatus = item.project_status_second.includes(PROJECT_SECOND_STATUS.PAUSED) ? PROJECT_START_STATUS.STOP : PROJECT_START_STATUS.START;
    const startStatusText = START_STATUS_TEXT[startStatus];
    
    const handleProjectClick = (project) => {
      if (onProjectSelect) {
        onProjectSelect({
          project_id: project.project_id,
          project_name: project.project_name,
          advertiser_id: project.advertiser_id,
          advertiser_name: project.advertiser_name || `广告主(${project.advertiser_id})`,
          // 注意：项目列表中不包含account_id，需要从共享状态中获取
          // 这里暂时不传递account_id，由MainPage处理
        });
      }
    };
    
    return (
      <tr key={item.project_id} className="group hover:bg-slate-50 transition-colors">
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedItems.includes(item.project_id)}
              onChange={() => onSelectItem(item.project_id)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
            />
            <span className="text-sm text-slate-900">{getRowIndex(index)}</span>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <button
            onClick={() => {
              // 切换启用状态：0(启用) <-> 1(暂停)
              const newStatus = startStatus === PROJECT_START_STATUS.START ? PROJECT_START_STATUS.STOP : PROJECT_START_STATUS.START;
              onUpdateStatus(item, newStatus);
            }}
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border cursor-pointer hover:opacity-80 transition-opacity ${START_STATUS_COLORS[startStatus]}`}
            title="点击切换状态"
          >
            {startStatusText}
          </button>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          {editingProjectId === item.project_id ? (
            <div className="flex items-center space-x-2 max-w-[300px]">
              <input
                type="text"
                value={editingProjectName}
                onChange={(e) => setEditingProjectName(e.target.value)}
                onKeyDown={(e) => onEditInputKeyDown(e, item)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入项目名称..."
                autoFocus
              />
              <button
                onClick={() => onSaveProjectName(item)}
                className="text-green-600 hover:text-green-800 transition-colors"
                title="保存"
              >
                <Check size={16} />
              </button>
              <button
                onClick={onCancelEditProjectName}
                className="text-gray-600 hover:text-gray-800 transition-colors"
                title="取消"
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-2 max-w-[300px]">
              <div 
                className="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate flex-1" 
                title={item.project_name}
                onClick={() => handleProjectClick(item)}
              >
                {item.project_name || '未命名项目'}
              </div>
              <button
                onClick={() => onEditProjectName(item)}
                className="text-blue-600 hover:text-blue-800 transition-colors opacity-0 group-hover:opacity-100"
                title="编辑名称"
              >
                <Edit size={14} />
              </button>
            </div>
          )}
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm text-slate-500 max-w-[180px] truncate" title={item.project_id}>
            {item.project_id || '-'}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm text-slate-500 max-w-[200px] truncate" title={item.advertiser_id}>
            {item.advertiser_id || '-'}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${STATUS_COLORS[status]}`}>
            {statusText}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          {formatAmount(item.conversion_cost)}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          {formatAmount(item.stat_cost)}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          {item.show_cnt || "0"}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          {item.convert_cnt || "0"}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          {item.conversion_rate || "0%"}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onDeleteProject(item)}
              className="text-red-600 hover:text-red-800 transition-colors"
              title="删除"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </td>
      </tr>
    );
  });
};

export default ProjectList;