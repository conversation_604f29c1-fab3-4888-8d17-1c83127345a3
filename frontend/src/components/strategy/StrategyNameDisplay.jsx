import React, { useState, useEffect } from "react";
import { useStrategyMap } from "../../contexts/StrategyMapContext";

/**
 * 策略名称显示组件
 * @param {string|number} strategyId - 策略ID
 * @param {string|Array} strategyIds - 策略ID或策略ID数组
 * @param {string} separator - 多个策略名称的分隔符，默认为 "、"
 * @param {string} className - 自定义样式类名
 * @param {object} style - 自定义样式
 * @param {boolean} showEmpty - 是否显示空状态
 * @param {boolean} showAsTags - 是否以标签形式显示
 * @param {number} maxDisplay - 最大显示数量，超出部分显示 +N
 */
const StrategyNameDisplay = ({
  strategyId,
  strategyIds,
  separator = "、",
  className = "",
  style = {},
  showEmpty = true,
  showAsTags = false,
  maxDisplay = 1,
}) => {
  const { getStrategyName, getStrategyNames, strategyMap } = useStrategyMap();
  const [isLoading, setIsLoading] = useState(false);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);

  // 检查策略映射是否已加载
  const isStrategyMapLoaded = Object.keys(strategyMap).length > 0;

  // 如果提供了 strategyIds 数组，则显示多个策略名称
  if (strategyIds) {
    const ids = Array.isArray(strategyIds) ? strategyIds : [strategyIds];
    const names = getStrategyNames(ids);

    if (names.length === 0) {
      // 如果策略映射未加载且有策略ID，显示加载状态
      if (
        !isStrategyMapLoaded &&
        ids.length > 0 &&
        ids.some((id) => id && id !== "") &&
        !hasAttemptedLoad
      ) {
        // 只在第一次显示加载状态，避免频繁触发
        if (!hasAttemptedLoad) {
          setHasAttemptedLoad(true);
        }
        return showEmpty ? (
          <span className={className} style={{ ...style, color: "#999" }}>
            加载中...
          </span>
        ) : null;
      }

      return showEmpty ? (
        <span className={className} style={{ ...style, color: "#999" }}>
          暂无策略
        </span>
      ) : null;
    }

    // 如果使用标签样式显示
    if (showAsTags) {
      const displayNames = names.slice(0, maxDisplay);
      const remainingCount = names.length - maxDisplay;

      return (
        <div className={`flex flex-wrap gap-0.5 ${className}`} style={style}>
          {displayNames.map((name, index) => (
            <span
              key={index}
              className="inline-flex items-center px-0.5 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 whitespace-nowrap max-w-[70px] overflow-hidden"
              style={{
                fontSize: "8px",
                lineHeight: "1.0",
                textOverflow: "ellipsis",
              }}
              title={name}
            >
              {name.length > 6 ? `${name.slice(0, 6)}...` : name}
            </span>
          ))}
          {remainingCount > 0 && (
            <span
              className="inline-flex items-center px-0.5 py-0.5 rounded text-xs font-medium bg-gray-50 text-gray-600 border border-gray-200 whitespace-nowrap"
              style={{
                fontSize: "8px",
                lineHeight: "1.0",
              }}
              title={`还有${remainingCount}个策略：${names
                .slice(maxDisplay)
                .join("、")}`}
            >
              +{remainingCount}
            </span>
          )}
        </div>
      );
    }

    // 默认文本显示
    return (
      <span className={className} style={style} title={names.join(separator)}>
        {names.join(separator)}
      </span>
    );
  }

  // 如果提供了单个 strategyId，则显示单个策略名称
  if (strategyId) {
    const name = getStrategyName(strategyId);

    // 如果策略映射未加载且有策略ID，显示加载状态
    if (
      !isStrategyMapLoaded &&
      strategyId &&
      strategyId !== "" &&
      !hasAttemptedLoad
    ) {
      // 只在第一次显示加载状态，避免频繁触发
      if (!hasAttemptedLoad) {
        setHasAttemptedLoad(true);
      }
      return showEmpty ? (
        <span className={className} style={{ ...style, color: "#999" }}>
          加载中...
        </span>
      ) : null;
    }

    if (!name && !showEmpty) {
      return null;
    }

    if (showAsTags) {
      return (
        <div className={`flex flex-wrap gap-0.5 ${className}`} style={style}>
          <span
            className="inline-flex items-center px-0.5 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 whitespace-nowrap max-w-[70px] overflow-hidden"
            style={{
              fontSize: "8px",
              lineHeight: "1.0",
              textOverflow: "ellipsis",
            }}
            title={name}
          >
            {name.length > 6 ? `${name.slice(0, 6)}...` : name}
          </span>
        </div>
      );
    }

    return (
      <span className={className} style={style} title={name}>
        {name || "未知策略"}
      </span>
    );
  }

  // 如果既没有 strategyId 也没有 strategyIds
  return showEmpty ? (
    <span className={className} style={{ ...style, color: "#999" }}>
      暂无策略
    </span>
  ) : null;
};

export default StrategyNameDisplay;
