import React from 'react';
import { Search, X } from 'lucide-react';

const SearchInput = ({ 
  value = '',
  onChange,
  placeholder = '搜索...',
  disabled = false,
  className = '',
  size = 'default',
  showClearButton = true,
  onClear,
  style = {},
  ...props 
}) => {
  const sizeClasses = {
    small: 'h-8 text-sm',
    default: 'h-10 text-sm',
    large: 'h-12 text-base'
  };

  // 计算精确的padding和位置
  const sizeConfig = {
    small: {
      iconSize: 14,
      leftPadding: 32,  // 14px icon + 8px spacing + 10px margin = 32px
      rightPadding: 32,
      iconLeft: 10,     // 10px from left edge
      clearRight: 10    // 10px from right edge
    },
    default: {
      iconSize: 16,
      leftPadding: 40,  // 16px icon + 8px spacing + 16px margin = 40px
      rightPadding: 40,
      iconLeft: 12,     // 12px from left edge
      clearRight: 12    // 12px from right edge
    },
    large: {
      iconSize: 18,
      leftPadding: 48,  // 18px icon + 8px spacing + 22px margin = 48px
      rightPadding: 48,
      iconLeft: 15,     // 15px from left edge
      clearRight: 15    // 15px from right edge
    }
  };

  const config = sizeConfig[size];

  const baseClasses = `
    relative w-full
    transition-wechat
    ${sizeClasses[size]}
    ${className}
  `;

  const inputClasses = `
    w-full h-full
    bg-transparent
    border-none outline-none
  `;

  const containerStyle = {
    backgroundColor: 'var(--panel-bg)',
    border: '1px solid var(--border-color)',
    borderRadius: '4px',
    ...style
  };

  const inputStyle = {
    color: 'var(--text-primary)',
    fontSize: '14px',
    paddingLeft: `${config.leftPadding}px`, 
    paddingRight: `${config.rightPadding}px`
  };

  if (disabled) {
    containerStyle.backgroundColor = 'var(--hover-bg)';
    containerStyle.cursor = 'not-allowed';
    inputStyle.color = 'var(--text-disabled)';
  }

  const handleClear = () => {
    if (onClear) {
      onClear();
    } else if (onChange) {
      onChange({ target: { value: '' } });
    }
  };

  const handleFocus = (e) => {
    if (!disabled) {
      e.target.parentElement.style.borderColor = 'var(--primary-color)';
    }
  };

  const handleBlur = (e) => {
    if (!disabled) {
      e.target.parentElement.style.borderColor = 'var(--border-color)';
    }
  };

  return (
    <div 
      className={baseClasses}
      style={containerStyle}
    >
      {/* 搜索图标 */}
      <Search 
        size={config.iconSize} 
        className="absolute top-1/2 transform -translate-y-1/2 pointer-events-none"
        style={{ 
          left: `${config.iconLeft}px`,
          color: 'var(--text-secondary)'
        }}
      />
      
      {/* 输入框 */}
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        className={inputClasses}
        style={inputStyle}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      />
      
      {/* 清除按钮 */}
      {showClearButton && value && !disabled && (
        <button
          type="button"
          onClick={handleClear}
          className="absolute top-1/2 transform -translate-y-1/2 transition-wechat p-1 rounded"
          style={{ 
            right: `${config.clearRight}px`,
            color: 'var(--text-secondary)',
            backgroundColor: 'transparent'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
            e.currentTarget.style.color = 'var(--text-primary)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = 'var(--text-secondary)';
          }}
          tabIndex={-1}
        >
          <X size={config.iconSize - 2} />
        </button>
      )}
    </div>
  );
};

export default SearchInput; 