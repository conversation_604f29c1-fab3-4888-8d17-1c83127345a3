import React from 'react';
import { User } from 'lucide-react';

/**
 * 头像组件
 * @param {string} src - 头像图片链接
 * @param {string} alt - 图片alt文本
 * @param {string} name - 用于生成文字头像的名称
 * @param {'xs'|'sm'|'md'|'lg'|'xl'} size - 头像大小
 * @param {boolean} showFallback - 是否显示默认图标
 * @param {'online'|'offline'|'busy'|null} status - 状态指示器
 * @param {string} className - 额外的样式类
 */
function Avatar({ 
  src, 
  alt, 
  name, 
  size = 'md', 
  showFallback = true, 
  status = null,
  className = '' 
}) {
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl'
  };

  const iconSizes = {
    xs: 12,
    sm: 14,
    md: 18,
    lg: 20,
    xl: 24
  };

  const sizeClass = sizeClasses[size] || sizeClasses.md;
  const iconSize = iconSizes[size] || iconSizes.md;

  // 状态指示器配置
  const statusConfig = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    busy: 'bg-red-500'
  };

  const statusSizes = {
    xs: 'w-2 h-2',
    sm: 'w-2.5 h-2.5',
    md: 'w-3 h-3',
    lg: 'w-3.5 h-3.5',
    xl: 'w-4 h-4'
  };

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    if (!status) return null;
    
    const statusSize = statusSizes[size] || statusSizes.md;
    const statusColor = statusConfig[status] || statusConfig.offline;
    
    return (
      <div className={`absolute -bottom-0.5 -right-0.5 ${statusSize} ${statusColor} rounded-full border-2 border-white`}></div>
    );
  };

  // 包装组件
  const wrapWithStatus = (content) => {
    if (!status) return content;
    
    return (
      <div className="relative inline-block">
        {content}
        {renderStatusIndicator()}
      </div>
    );
  };

  // 生成文字头像
  const getInitials = (name) => {
    if (!name) return '';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // 生成背景颜色
  const getAvatarColor = (name) => {
    if (!name) return 'from-blue-500 to-blue-600';
    
    const colors = [
      'from-blue-500 to-blue-600',
      'from-purple-500 to-purple-600',
      'from-green-500 to-green-600',
      'from-orange-500 to-orange-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
      'from-teal-500 to-teal-600',
      'from-red-500 to-red-600'
    ];
    
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  if (src) {
    return wrapWithStatus(
      <img
        src={src}
        alt={alt || name || '头像'}
        className={`${sizeClass} rounded-full object-cover ${className}`}
      />
    );
  }

  if (name) {
    const initials = getInitials(name);
    const colorClass = getAvatarColor(name);
    
    return wrapWithStatus(
      <div 
        className={`${sizeClass} rounded-full bg-gradient-to-r ${colorClass} flex items-center justify-center text-white font-medium ${className}`}
      >
        {initials}
      </div>
    );
  }

  if (showFallback) {
    return wrapWithStatus(
      <div 
        className={`${sizeClass} rounded-full bg-gradient-to-r from-gray-400 to-gray-500 flex items-center justify-center ${className}`}
      >
        <User size={iconSize} className="text-white" />
      </div>
    );
  }

  return null;
}

export default Avatar; 