import React, { useState, useRef, useEffect } from 'react';
import { ChevronRight } from 'lucide-react';

const MenuBar = ({ menus = [], className = '' }) => {
  const [activeMenu, setActiveMenu] = useState(null);
  const [openSubmenu, setOpenSubmenu] = useState(null);
  const [isMenuMode, setIsMenuMode] = useState(false); // 追踪是否处于菜单模式
  const menuBarRef = useRef(null);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuBarRef.current && !menuBarRef.current.contains(event.target)) {
        setActiveMenu(null);
        setOpenSubmenu(null);
        setIsMenuMode(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuClick = (menuKey) => {
    if (activeMenu === menuKey) {
      setActiveMenu(null);
      setOpenSubmenu(null);
      setIsMenuMode(false);
    } else {
      setActiveMenu(menuKey);
      setOpenSubmenu(null);
      setIsMenuMode(true); // 进入菜单模式
    }
  };

  const handleMenuHover = (menuKey) => {
    // 只有在菜单模式下才响应鼠标悬停
    if (isMenuMode && activeMenu !== menuKey) {
      setActiveMenu(menuKey);
      setOpenSubmenu(null);
    }
  };

  const handleMenuItemClick = (item) => {
    if (item.onClick && typeof item.onClick === 'function') {
      item.onClick();
    }
    setActiveMenu(null);
    setOpenSubmenu(null);
    setIsMenuMode(false); // 退出菜单模式
  };

  const handleSubmenuHover = (submenuKey) => {
    setOpenSubmenu(submenuKey);
  };

  const renderMenuItem = (item, isSubmenu = false) => {
    if (item.type === 'divider') {
      return (
        <div 
          key={item.key || Math.random()} 
          className="h-px bg-slate-200 my-1 mx-2"
        />
      );
    }

    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const isSubmenuOpen = openSubmenu === item.key;

    return (
      <div
        key={item.key}
        className="relative"
        onMouseEnter={() => hasSubmenu && handleSubmenuHover(item.key)}
      >
        <button
          onClick={() => handleMenuItemClick(item)}
          disabled={item.disabled}
          className={`
            w-full px-3 py-1.5 text-left text-sm flex items-center justify-between
            hover:bg-blue-50 hover:text-blue-700 transition-colors
            ${item.disabled ? 'text-slate-400 cursor-not-allowed' : 'text-slate-700'}
            ${isSubmenu ? 'text-xs' : ''}
          `}
        >
          <div className="flex items-center space-x-2">
            {item.icon && (
              <span className="flex-shrink-0">
                {item.icon}
              </span>
            )}
            <span>{item.label}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {item.shortcut && (
              <span className="text-xs text-slate-500">{item.shortcut}</span>
            )}
            {hasSubmenu && (
              <ChevronRight size={12} className="text-slate-400" />
            )}
          </div>
        </button>

        {/* 子菜单 */}
        {hasSubmenu && isSubmenuOpen && (
          <div className="absolute left-full top-0 ml-1 z-50">
            <div className="bg-white border border-slate-200 rounded-md shadow-lg py-1 min-w-48">
              {item.submenu.map(subItem => renderMenuItem(subItem, true))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div 
      ref={menuBarRef}
      className={`bg-slate-50 border-b border-slate-200 ${className}`}
      onMouseLeave={() => {
        // 当鼠标离开菜单栏时，如果没有子菜单打开，则退出菜单模式
        if (isMenuMode && !openSubmenu) {
          setActiveMenu(null);
          setIsMenuMode(false);
        }
      }}
    >
      <div className="flex h-8">
        {menus.map((menu) => (
          <div key={menu.key} className="relative">
            <button
              onClick={() => handleMenuClick(menu.key)}
              onMouseEnter={() => handleMenuHover(menu.key)}
              className={`
                px-3 h-full text-sm font-medium transition-colors flex items-center
                ${activeMenu === menu.key 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                }
              `}
            >
              {menu.label}
            </button>

            {/* 下拉菜单 */}
            {activeMenu === menu.key && menu.items && (
              <div className="absolute top-full left-0 z-40">
                <div className="bg-white border border-slate-200 rounded-md shadow-lg py-1 min-w-48 mt-1">
                  {menu.items.map(item => renderMenuItem(item))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MenuBar; 