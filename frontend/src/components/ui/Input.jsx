import React from 'react';

const Input = ({ 
  type = 'text',
  value,
  onChange,
  placeholder = '',
  disabled = false,
  className = '',
  size = 'default',
  error = false,
  style = {},
  ...props 
}) => {
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm h-8',
    default: 'px-3 py-2 text-sm h-10',
    large: 'px-4 py-3 text-base h-12'
  };

  // 基础样式
  const baseStyle = {
    border: '1px solid var(--border-color)',
    borderRadius: '4px',
    transition: 'all 150ms ease-out',
    backgroundColor: 'var(--panel-bg)',
    color: 'var(--text-primary)',
    width: '100%',
    outline: 'none',
    ...style
  };

  // 错误状态样式
  if (error) {
    baseStyle.borderColor = '#FF4747';
  }

  // 禁用状态样式
  if (disabled) {
    baseStyle.backgroundColor = 'var(--hover-bg)';
    baseStyle.color = 'var(--text-disabled)';
    baseStyle.cursor = 'not-allowed';
  }

  const handleFocus = (e) => {
    if (!disabled) {
      if (error) {
        e.currentTarget.style.borderColor = '#FF4747';
        e.currentTarget.style.boxShadow = '0 0 0 3px rgba(255, 71, 71, 0.1)';
      } else {
        e.currentTarget.style.borderColor = 'var(--primary-color)';
        e.currentTarget.style.boxShadow = '0 0 0 3px rgba(7, 193, 96, 0.1)';
      }
    }
  };

  const handleBlur = (e) => {
    if (!disabled) {
      e.currentTarget.style.boxShadow = 'none';
      if (error) {
        e.currentTarget.style.borderColor = '#FF4747';
      } else {
        e.currentTarget.style.borderColor = 'var(--border-color)';
      }
    }
  };

  const handleMouseEnter = (e) => {
    if (!disabled && !e.currentTarget.matches(':focus')) {
      if (error) {
        e.currentTarget.style.borderColor = '#E63946';
      } else {
        e.currentTarget.style.borderColor = 'var(--primary-hover)';
      }
    }
  };

  const handleMouseLeave = (e) => {
    if (!disabled && !e.currentTarget.matches(':focus')) {
      if (error) {
        e.currentTarget.style.borderColor = '#FF4747';
      } else {
        e.currentTarget.style.borderColor = 'var(--border-color)';
      }
    }
  };

  return (
    <input
      type={type}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      className={`${sizeClasses[size]} ${className}`}
      style={baseStyle}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    />
  );
};

export default Input; 