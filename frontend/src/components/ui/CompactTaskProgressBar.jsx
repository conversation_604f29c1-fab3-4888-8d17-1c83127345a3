import React, { useState, useEffect, useRef } from 'react';

/**
 * 紧凑版任务进度条组件 - 适合嵌入到底部状态栏
 * 特性：
 * - 紧凑的设计，适合状态栏高度
 * - 显示当前子任务的进度（每个子任务进度条都会从0%走到100%）
 * - 支持子任务内的进度动画（立即跳转到当前进度，然后缓慢增长到下一进度）
 * - 显示任务名称、子任务描述和当前子任务进度
 */
const CompactTaskProgressBar = ({ 
  taskData, 
  className = '',
  ...props 
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef(null);
  const startTimeRef = useRef(0);
  const startProgressRef = useRef(0);

  // 当任务数据更新时，处理进度动画
  // step_progress: 当前子任务的当前进度 (0-100)
  // next_step_progress: 当前子任务的下一个进度点 (0-100)
  // step_time: 从当前进度到下一个进度的预估耗时(ms)
  useEffect(() => {
    if (!taskData) return;

    const currentProgress = taskData.step_progress || 0;      // 当前子任务进度
    const nextProgress = taskData.next_step_progress || currentProgress; // 下一个进度点
    const stepTime = taskData.step_time || 3000; // 预估耗时，默认3秒

    // 立即跳转到当前进度
    setDisplayProgress(currentProgress);
    
    // 如果有下一步进度且不同于当前进度，开始缓慢增长动画
    if (nextProgress > currentProgress && stepTime > 0) {
      setIsAnimating(true);
      startTimeRef.current = Date.now();
      startProgressRef.current = currentProgress;

      const animateProgress = () => {
        const elapsed = Date.now() - startTimeRef.current;
        const progress = Math.min(elapsed / stepTime, 1);
        
        // 使用缓动函数让动画更平滑
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const newProgress = startProgressRef.current + 
          (nextProgress - startProgressRef.current) * easeProgress;
        
        setDisplayProgress(newProgress);
        
        if (progress < 1) {
          animationRef.current = requestAnimationFrame(animateProgress);
        } else {
          setIsAnimating(false);
          setDisplayProgress(nextProgress);
        }
      };

      animationRef.current = requestAnimationFrame(animateProgress);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [taskData?.step_progress, taskData?.next_step_progress, taskData?.step_time]);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!taskData) return null;

  const {
    task_name,
    step_description,
    current_step,
    total_steps,
    status,
  } = taskData;

  // 根据状态确定颜色
  const getStatusColor = () => {
    switch (status) {
      case 'start':
        return 'var(--primary-color)';
      case 'progress':
        return 'var(--primary-color)';
      case 'end':
        return 'var(--success-color)';
      case 'error':
        return 'var(--error-color)';
      default:
        return 'var(--primary-color)';
    }
  };

  return (
    <div
      className={`compact-task-progress-bar flex items-center space-x-2 ${className}`}
      style={{
        fontFamily: 'Microsoft YaHei UI, Segoe UI, sans-serif',
      }}
      {...props}
    >
      {/* 进度条 */}
      <div
        className="flex items-center space-x-3"
        title={step_description || task_name}
      >
        {/* 任务信息 - 放在左边 */}
        <div className="flex items-center space-x-2 text-xs min-w-0">
          <span
            style={{ color: 'var(--text-primary)' }}
            className="font-medium whitespace-nowrap"
          >
            {task_name}
          </span>
          <span style={{ color: 'var(--text-secondary)' }} className="whitespace-nowrap">
            {step_description}
          </span>
        </div>

        {/* 长条形进度条 - 更长的宽度 */}
        <div
          style={{
            width: '120px',
            height: '8px',
            borderRadius: '4px',
            backgroundColor: 'var(--bg-tertiary)',
            position: 'relative',
            overflow: 'hidden',
            flexShrink: 0,
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: `${Math.min(displayProgress, 100)}%`,
              height: '100%',
              backgroundColor: getStatusColor(),
              borderRadius: '4px',
              transition: isAnimating ? 'none' : 'width 0.3s ease',
            }}
          />
        </div>

        {/* 进度信息 - 放在右边 */}
        <div className="flex items-center space-x-2 text-xs whitespace-nowrap">
          <span style={{ color: 'var(--text-secondary)' }}>
            {Math.round(displayProgress)}%
          </span>
          <span style={{ color: 'var(--text-secondary)' }}>
            ({(() => {
              // 根据当前步骤进度计算已完成的步骤数
              const completedSteps = displayProgress >= 100 || status === 'end' 
                ? current_step 
                : Math.max(0, current_step - 1);
              return `${completedSteps}/${total_steps}`;
            })()})
          </span>
        </div>
      </div>
    </div>
  );
};

export default CompactTaskProgressBar; 