import React from 'react';

// 预设的状态颜色（使用CSS变量）
const STATUS_VARIANTS = {
  success: {
    color: 'var(--primary-color)',
    backgroundColor: '#E9F8F1',
    borderColor: 'rgba(7, 193, 96, 0.3)'
  },
  warning: {
    color: '#F59E0B',
    backgroundColor: '#FEF3C7',
    borderColor: 'rgba(245, 158, 11, 0.3)'
  },
  error: {
    color: '#FF4757',
    backgroundColor: '#FEE2E2',
    borderColor: 'rgba(255, 71, 87, 0.3)'
  },
  info: {
    color: '#3B82F6',
    backgroundColor: '#DBEAFE',
    borderColor: 'rgba(59, 130, 246, 0.3)'
  },
  default: {
    color: 'var(--text-secondary)',
    backgroundColor: 'var(--hover-bg)',
    borderColor: 'var(--border-color)'
  }
};

/**
 * 状态标签组件
 * @param {string} children - 状态文本
 * @param {'success'|'warning'|'error'|'info'|'default'} variant - 状态类型
 * @param {string} className - 额外的样式类
 * @param {object} style - 内联样式
 */
function StatusBadge({ children, variant = 'default', className = '', style = {} }) {
  const variantConfig = STATUS_VARIANTS[variant] || STATUS_VARIANTS.default;
  
  const badgeStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    padding: '0.125rem 0.625rem',
    borderRadius: '9999px',
    fontSize: '12px',
    fontWeight: '500',
    border: '1px solid',
    transition: 'all 150ms ease-out',
    color: variantConfig.color,
    backgroundColor: variantConfig.backgroundColor,
    borderColor: variantConfig.borderColor,
    ...style
  };
  
  return (
    <span 
      className={`transition-wechat ${className}`}
      style={badgeStyle}
    >
      {children}
    </span>
  );
}

export default StatusBadge; 