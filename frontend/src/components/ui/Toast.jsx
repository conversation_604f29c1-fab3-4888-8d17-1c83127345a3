import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react';

const Toast = ({ message, type = 'info', duration = 3000, onClose, index = 0 }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // 延迟显示动画，避免闪烁
    const showTimer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    const hideTimer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => {
        onClose?.();
      }, 300); // 等待退出动画完成
    }, duration);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(hideTimer);
    };
  }, [duration, onClose]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose?.();
    }, 300);
  };

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600',
          emoji: '✅'
        };
      case 'error':
        return {
          icon: XCircle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
          emoji: '❌'
        };
      case 'warning':
        return {
          icon: AlertCircle,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          emoji: '⚠️'
        };
      default:
        return {
          icon: Info,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          emoji: 'ℹ️'
        };
    }
  };

  const config = getToastConfig();

  // 使用内联样式确保显示正确，避免Tailwind冲突
  const toastStyle = {
    transform: isExiting 
      ? 'translateX(100%) scale(0.95)' 
      : isVisible 
        ? 'translateX(0) scale(1)' 
        : 'translateX(100%) scale(0.95)',
    opacity: isExiting ? 0 : isVisible ? 1 : 0,
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    marginBottom: '8px',
    maxWidth: '400px',
    minWidth: '300px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    zIndex: 1000 - index, // 确保新消息在上层
  };

  const contentStyle = {
    display: 'flex',
    alignItems: 'center',
    padding: '12px 16px',
    borderRadius: '8px',
    background: getBackgroundColor(type),
    color: getTextColor(type),
    fontSize: '14px',
    fontWeight: '500',
    lineHeight: '1.4',
  };

  const iconStyle = {
    marginRight: '12px',
    fontSize: '18px',
    flexShrink: 0,
  };

  const messageStyle = {
    flex: 1,
    wordBreak: 'break-word',
  };

  const closeButtonStyle = {
    marginLeft: '12px',
    padding: '2px',
    border: 'none',
    background: 'transparent',
    cursor: 'pointer',
    opacity: 0.7,
    transition: 'opacity 0.2s',
    fontSize: '14px',
    color: getTextColor(type),
    flexShrink: 0,
  };

  function getBackgroundColor(type) {
    switch (type) {
      case 'success': return 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
      case 'error': return 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)';
      case 'warning': return 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)';
      default: return 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)';
    }
  }

  function getTextColor(type) {
    switch (type) {
      case 'success': return '#15803d';
      case 'error': return '#dc2626';
      case 'warning': return '#d97706';
      default: return '#2563eb';
    }
  }

  return (
    <div style={toastStyle}>
      <div style={contentStyle}>
        <span style={iconStyle}>
          {config.emoji}
        </span>
        <span style={messageStyle}>
          {message}
        </span>
        <button
          style={closeButtonStyle}
          onClick={handleClose}
          onMouseEnter={(e) => e.target.style.opacity = '1'}
          onMouseLeave={(e) => e.target.style.opacity = '0.7'}
        >
          ✕
        </button>
      </div>
    </div>
  );
};

// Toast管理器
class ToastManager {
  constructor() {
    this.toasts = [];
    this.listeners = [];
    this.maxToasts = 5; // 最大同时显示的Toast数量
  }

  show(message, type = 'info', duration = 3000) {
    const id = Date.now() + Math.random();
    const toast = { id, message, type, duration };
    
    // 如果超过最大数量，移除最旧的Toast
    if (this.toasts.length >= this.maxToasts) {
      const oldestToast = this.toasts.shift();
      // 立即移除，不等待动画
    }
    
    this.toasts.push(toast);
    this.notifyListeners();
    
    console.log(`Toast显示: [${type.toUpperCase()}] ${message}`);
    return id;
  }

  success(message, duration = 3000) {
    return this.show(message, 'success', duration);
  }

  error(message, duration = 5000) {
    return this.show(message, 'error', duration);
  }

  warning(message, duration = 4000) {
    return this.show(message, 'warning', duration);
  }

  info(message, duration = 3000) {
    return this.show(message, 'info', duration);
  }

  remove(id) {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.notifyListeners();
  }

  clear() {
    this.toasts = [];
    this.notifyListeners();
  }

  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  notifyListeners() {
    this.listeners.forEach(listener => listener([...this.toasts]));
  }
}

export const toast = new ToastManager();

// Toast容器组件
export const ToastContainer = () => {
  const [toasts, setToasts] = useState([]);

  useEffect(() => {
    const unsubscribe = toast.subscribe(setToasts);
    return unsubscribe;
  }, []);

  // 容器样式
  const containerStyle = {
    position: 'fixed',
    top: '20px',
    right: '20px',
    zIndex: 999999,
    pointerEvents: 'none', // 允许点击穿透到下层
    maxHeight: 'calc(100vh - 40px)',
    overflow: 'hidden',
  };

  return (
    <div style={containerStyle}>
      {toasts.map((toastItem, index) => (
        <div key={toastItem.id} style={{ pointerEvents: 'auto' }}>
          <Toast
            message={toastItem.message}
            type={toastItem.type}
            duration={toastItem.duration}
            index={index}
            onClose={() => toast.remove(toastItem.id)}
          />
        </div>
      ))}
    </div>
  );
};

export default Toast; 