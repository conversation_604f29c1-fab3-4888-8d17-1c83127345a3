import React, { useState, useEffect } from "react";
import { X, Search, Target } from "lucide-react";
import { useStrategyMap } from "../../contexts/StrategyMapContext";
import strategyService from "../../services/strategyService";

/**
 * 策略选择对话框
 * @param {boolean} visible - 是否显示对话框
 * @param {Function} onClose - 关闭对话框回调
 * @param {Function} onConfirm - 确认选择回调
 * @param {string} currentStrategyIds - 当前策略ID（逗号分隔）
 * @param {string} projectId - 项目ID
 * @param {string} projectName - 项目名称
 */
const StrategySelectDialog = ({
  visible,
  onClose,
  onConfirm,
  currentStrategyIds = "",
  projectId,
  projectName,
}) => {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStrategyIds, setSelectedStrategyIds] = useState([]);
  const [hasLoadedStrategies, setHasLoadedStrategies] = useState(false); // 标记是否已加载过策略
  const { getStrategyName } = useStrategyMap();

  // 处理当前策略ID
  useEffect(() => {
    if (visible && currentStrategyIds) {
      // 解析当前策略ID，支持逗号分隔的字符串
      const ids = currentStrategyIds
        .toString()
        .split(",")
        .map((id) => id.trim())
        .filter(Boolean);
      setSelectedStrategyIds(ids);
    } else if (visible) {
      setSelectedStrategyIds([]);
    }
  }, [visible, currentStrategyIds]);

  // 统一的策略加载逻辑 - 只在对话框打开且未加载过策略时调用一次
  useEffect(() => {
    if (visible && !hasLoadedStrategies && !loading) {
      loadStrategies();
    }
  }, [visible, hasLoadedStrategies, loading]);

  // 对话框关闭时重置状态
  useEffect(() => {
    if (!visible) {
      setHasLoadedStrategies(false);
      setSearchTerm("");
    }
  }, [visible]);

  const loadStrategies = async () => {
    if (loading) {
      return;
    }

    setLoading(true);
    try {
      const response = await strategyService.getStrategyList({
        page: 1,
        pageSize: 1000, // 获取所有策略
        enabled: undefined, // 不筛选状态
        name: searchTerm,
      });

      if (response.code === 0) {
        setStrategies(response.data.list || []);
        setHasLoadedStrategies(true); // 标记已加载
      } else {
        console.error("获取策略列表失败:", response.msg);
        setHasLoadedStrategies(true); // 即使失败也标记已加载
      }
    } catch (error) {
      console.error("加载策略列表失败:", error);
      setHasLoadedStrategies(true); // 即使失败也标记已加载
    } finally {
      setLoading(false);
    }
  };

  // 搜索策略 - 只在有搜索词且已加载过策略时重新搜索
  useEffect(() => {
    if (visible && hasLoadedStrategies && searchTerm && !loading) {
      const debounceTimer = setTimeout(() => {
        loadStrategies();
      }, 300);

      return () => clearTimeout(debounceTimer);
    }
  }, [searchTerm, visible, hasLoadedStrategies, loading]);

  // 切换策略选择状态
  const toggleStrategy = (strategyId) => {
    setSelectedStrategyIds((prev) => {
      if (prev.includes(strategyId)) {
        return prev.filter((id) => id !== strategyId);
      } else {
        return [...prev, strategyId];
      }
    });
  };

  // 处理确认选择
  const handleConfirm = () => {
    const strategyIdsStr = selectedStrategyIds.join(",");
    onConfirm(strategyIdsStr);
    onClose();
  };

  // 过滤策略列表
  const filteredStrategies = strategies.filter((strategy) =>
    strategy.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!visible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 遮罩层 */}
      <div
        className="absolute inset-0 bg-black/50 transition-opacity"
        onClick={onClose}
      />

      {/* 对话框 */}
      <div
        className="relative bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden"
        style={{
          backgroundColor: "var(--panel-bg)",
          border: "1px solid var(--border-color)",
        }}
      >
        {/* 标题栏 */}
        <div
          className="flex items-center justify-between p-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center space-x-2">
            <Target size={20} style={{ color: "var(--primary-color)" }} />
            <div>
              <h3
                className="text-lg font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                选择策略
              </h3>
              <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                为项目 "{projectName}" 选择执行策略
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded transition-wechat"
            style={{ color: "var(--text-secondary)" }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "transparent";
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* 搜索栏 */}
        <div
          className="p-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="relative">
            <Search
              size={16}
              className="absolute left-3 top-1/2 transform -translate-y-1/2"
              style={{ color: "var(--text-secondary)" }}
            />
            <input
              type="text"
              placeholder="搜索策略名称..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 text-sm rounded border transition-wechat"
              style={{
                backgroundColor: "var(--sidebar-bg)",
                borderColor: "var(--border-color)",
                color: "var(--text-primary)",
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = "var(--primary-color)";
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = "var(--border-color)";
              }}
            />
          </div>
          <div
            className="mt-2 text-xs"
            style={{ color: "var(--text-secondary)" }}
          >
            已选择 {selectedStrategyIds.length} 个策略
            {selectedStrategyIds.length > 0 && (
              <div className="mt-1 flex flex-wrap gap-1">
                {selectedStrategyIds.slice(0, 3).map((id) => {
                  const name = getStrategyName(id);
                  return (
                    <span
                      key={id}
                      className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200"
                      title={name}
                    >
                      {name.length > 8 ? `${name.slice(0, 8)}...` : name}
                    </span>
                  );
                })}
                {selectedStrategyIds.length > 3 && (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-gray-50 text-gray-600 border border-gray-200">
                    +{selectedStrategyIds.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 策略列表 */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div
                className="text-sm"
                style={{ color: "var(--text-secondary)" }}
              >
                加载中...
              </div>
            </div>
          ) : filteredStrategies.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Target size={32} className="mx-auto mb-2 opacity-50" />
                <div
                  className="text-sm"
                  style={{ color: "var(--text-secondary)" }}
                >
                  暂无策略
                </div>
              </div>
            </div>
          ) : (
            <div
              className="divide-y"
              style={{ borderColor: "var(--border-color)" }}
            >
              {filteredStrategies.map((strategy) => (
                <div
                  key={strategy.id}
                  className="p-4 cursor-pointer transition-wechat"
                  onClick={() => toggleStrategy(strategy.id.toString())}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedStrategyIds.includes(
                        strategy.id.toString()
                      )}
                      onChange={() => toggleStrategy(strategy.id.toString())}
                      className="rounded"
                      style={{ accentColor: "var(--primary-color)" }}
                    />
                    <div className="flex-1">
                      <div
                        className="text-sm font-medium"
                        style={{ color: "var(--text-primary)" }}
                      >
                        {strategy.name}
                      </div>
                      {strategy.description && (
                        <div
                          className="text-xs mt-1"
                          style={{ color: "var(--text-secondary)" }}
                        >
                          {strategy.description}
                        </div>
                      )}
                      <div className="flex items-center space-x-4 mt-1">
                        <span
                          className={`text-xs px-2 py-1 rounded ${
                            strategy.enabled
                              ? "bg-green-100 text-green-600"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          {strategy.enabled ? "已启用" : "已禁用"}
                        </span>
                        <span
                          className="text-xs"
                          style={{ color: "var(--text-secondary)" }}
                        >
                          ID: {strategy.id}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div
          className="flex items-center justify-between p-4 border-t"
          style={{ borderColor: "var(--border-color)" }}
        >
          <button
            onClick={() => setSelectedStrategyIds([])}
            className="text-sm px-3 py-1 rounded transition-wechat"
            style={{
              color: "var(--text-secondary)",
              border: "1px solid var(--border-color)",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "transparent";
            }}
          >
            清空选择
          </button>

          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm rounded transition-wechat"
              style={{
                color: "var(--text-secondary)",
                border: "1px solid var(--border-color)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "var(--hover-bg)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 text-sm rounded transition-wechat text-white"
              style={{
                backgroundColor: "var(--primary-color)",
                border: "1px solid var(--primary-color)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = "0.9";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = "1";
              }}
            >
              {selectedStrategyIds.length === 0
                ? "清空策略"
                : `确定选择 (${selectedStrategyIds.length})`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategySelectDialog;
