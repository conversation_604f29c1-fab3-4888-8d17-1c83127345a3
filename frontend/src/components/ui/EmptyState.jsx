import React from 'react';
import { RefreshCw, Search, Database, FileX } from 'lucide-react';

/**
 * 空状态组件
 * @param {'empty'|'loading'|'error'|'search'} type - 状态类型
 * @param {string} title - 标题
 * @param {string} description - 描述
 * @param {React.ReactNode} icon - 自定义图标
 * @param {React.ReactNode} action - 操作按钮
 * @param {string} className - 额外的样式类
 */
function EmptyState({ 
  type = 'empty', 
  title, 
  description, 
  icon, 
  action, 
  className = '' 
}) {
  // 预设图标
  const defaultIcons = {
    empty: Database,
    loading: RefreshCw,
    error: FileX,
    search: Search
  };

  // 预设内容
  const presets = {
    empty: {
      title: title || '暂无数据',
      description: description || '当前没有任何数据',
      iconColor: 'var(--text-disabled)'
    },
    loading: {
      title: title || '加载中...',
      description: description || '正在获取数据，请稍候',
      iconColor: 'var(--primary-color)',
      animate: true
    },
    error: {
      title: title || '加载失败',
      description: description || '数据加载出错，请重试',
      iconColor: '#FF4757'
    },
    search: {
      title: title || '无搜索结果',
      description: description || '没有找到匹配的内容',
      iconColor: 'var(--text-disabled)'
    }
  };

  const preset = presets[type] || presets.empty;
  const Icon = icon || defaultIcons[type] || defaultIcons.empty;

  return (
    <div className={`empty-state ${className}`}>
      <div 
        className={`empty-state-icon ${preset.animate ? 'loading-spinner' : ''}`}
        style={{ color: preset.iconColor }}
      >
        <Icon size={48} />
      </div>
      
      <h3 className="empty-state-title">
        {preset.title}
      </h3>
      
      {preset.description && (
        <p className="empty-state-description">
          {preset.description}
        </p>
      )}
      
      {action && (
        <div style={{ marginTop: '1rem' }}>
          {action}
        </div>
      )}
    </div>
  );
}

// 便捷方法
EmptyState.Loading = (props) => <EmptyState type="loading" {...props} />;
EmptyState.Empty = (props) => <EmptyState type="empty" {...props} />;
EmptyState.Error = (props) => <EmptyState type="error" {...props} />;
EmptyState.Search = (props) => <EmptyState type="search" {...props} />;

export default EmptyState; 