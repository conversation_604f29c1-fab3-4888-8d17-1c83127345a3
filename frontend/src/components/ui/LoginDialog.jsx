import React, { useState, useEffect, useRef } from 'react';
import { X, Eye, EyeOff, Mail, Phone, Cookie, Globe } from 'lucide-react';
import { toast } from './Toast';

const LoginDialog = ({ isOpen, onClose, onLogin, onSendCode }) => {
  const [loginType, setLoginType] = useState('email'); // 'email', 'phone' 或 'cookie'
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    phone: '',
    code: '',
    cookie: '',
    useProxy: false,
    selectedProxyId: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);
  const [proxyList, setProxyList] = useState([]);
  const [loadingProxies, setLoadingProxies] = useState(false);
  const countdownTimerRef = useRef(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // 获取代理列表
  const fetchProxyList = async () => {
    if (proxyList.length > 0) return; // 如果已经有数据，不重复请求
    
    setLoadingProxies(true);
    try {
      const { ProxyService, handleResult } = await import('../../services/api.js');
      const result = await ProxyService.GetProxyList({
        page: 0,
        pageSize: 0,
        status: 2 // 只获取启用状态的代理
      });
      console.log('获取代理列表结果:', result);
      const data = handleResult(result);
      if (data && data.data && data.data.list) {
        setProxyList(data.data.list);
      }
    } catch (error) {
      console.error('获取代理列表失败:', error);
      toast.error('获取代理列表失败: ' + error.message);
    } finally {
      setLoadingProxies(false);
    }
  };

  // 当选择使用代理时，获取代理列表
  useEffect(() => {
    if (formData.useProxy && isOpen) {
      fetchProxyList();
    }
  }, [formData.useProxy, isOpen]);

  const handleSendCode = async () => {
    // 验证手机号格式
    if (!formData.phone) {
      toast.warning('请输入手机号码');
      return;
    }
    
    // 简单的手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast.warning('请输入正确的手机号码');
      return;
    }

    // 防止重复发送
    if (countdown > 0 || sendingCode) {
      console.log('防抖生效：', { countdown, sendingCode });
      return;
    }

    console.log('开始发送验证码');
    setSendingCode(true);
    
    try {
      const result = await onSendCode(formData.phone);
      console.log('验证码发送结果：', result);
      
      if (result && result.code === 0) {
        setCodeSent(true);
        setCountdown(60);
        
        // 清除之前的定时器
        if (countdownTimerRef.current) {
          clearInterval(countdownTimerRef.current);
          countdownTimerRef.current = null;
        }
        
        // 开始倒计时
        countdownTimerRef.current = setInterval(() => {
          setCountdown(prev => {
            console.log('倒计时：', prev);
            if (prev <= 1) {
              clearInterval(countdownTimerRef.current);
              countdownTimerRef.current = null;
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        toast.success(result.msg || '验证码发送成功，请注意查收');
      } else {
        toast.error(result?.msg || '验证码发送失败');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error('验证码发送失败: ' + error.message);
    } finally {
      setSendingCode(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      let result;
      
      // 准备代理配置
      let proxyConfig = null;
      if (formData.useProxy && formData.selectedProxyId) {
        const selectedProxy = proxyList.find(p => p.id == formData.selectedProxyId);
        if (selectedProxy) {
          proxyConfig = {
            type: selectedProxy.type.toLowerCase(),
            host: selectedProxy.ip_address,
            port: selectedProxy.port,
            username: selectedProxy.username || '',
            password: selectedProxy.password || ''
          };
        }
      }

      if (loginType === 'email') {
        if (!formData.email || !formData.password) {
          toast.warning('请输入邮箱和密码');
          return;
        }
        result = await onLogin.email(formData.email, formData.password, proxyConfig);
      } else if (loginType === 'phone') {
        if (!formData.phone || !formData.code) {
          toast.warning('请输入手机号和验证码');
          return;
        }
        result = await onLogin.phone(formData.phone, formData.code, proxyConfig);
      } else if (loginType === 'cookie') {
        if (!formData.cookie) {
          toast.warning('请输入Cookie');
          return;
        }
        result = await onLogin.cookie(formData.cookie, proxyConfig);
      }

      // 立即关闭对话框
      handleClose();
      
      // 显示登录结果
      if (result.code === 0) {
        toast.success(result.msg || '登录成功');
      } else {
        toast.error(result.msg || '登录失败');
      }
    } catch (error) {
      // 立即关闭对话框
      handleClose();
      toast.error('登录失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // 清除倒计时定时器
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
    
    onClose();
    
    // 重置表单和状态
    setFormData({
      email: '',
      password: '',
      phone: '',
      code: '',
      cookie: '',
      useProxy: false,
      selectedProxyId: ''
    });
    setCodeSent(false);
    setCountdown(0);
    setSendingCode(false);
    setLoading(false);
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
        countdownTimerRef.current = null;
      }
    };
  }, []);

  // 监听对话框关闭，清理状态 - 只在对话框真正关闭时清理
  useEffect(() => {
    if (!isOpen) {
      // 延迟清理，避免影响正在进行的倒计时
      const timer = setTimeout(() => {
        if (countdownTimerRef.current) {
          clearInterval(countdownTimerRef.current);
          countdownTimerRef.current = null;
        }
        setCountdown(0);
        setSendingCode(false);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(107, 114, 128, 0.3)' }}>
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">账户登录</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 登录类型切换 */}
        <div className="p-6 pb-4">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              type="button"
              onClick={() => setLoginType('email')}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                loginType === 'email'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Mail size={16} />
              <span>邮箱登录</span>
            </button>
            <button
              type="button"
              onClick={() => setLoginType('phone')}
              className={`flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                loginType === 'phone'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Phone size={16} />
              <span>手机登录</span>
            </button>
            <button
              type="button"
              onClick={() => setLoginType('cookie')}
              className={`flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                loginType === 'cookie'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Cookie size={16} />
              <span>Cookie登录</span>
            </button>
          </div>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="px-6 pb-6 space-y-4">
          {loginType === 'email' ? (
            <>
              {/* 邮箱登录 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱地址
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入邮箱地址"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  密码
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入密码"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>
            </>
          ) : loginType === 'phone' ? (
            <>
              {/* 手机登录 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  手机号码
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入手机号码"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  验证码
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入验证码"
                    required
                  />
                  <button
                    type="button"
                    onClick={handleSendCode}
                    disabled={loading || countdown > 0 || sendingCode}
                    className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-w-[100px]"
                  >
                    {sendingCode ? '发送中...' : countdown > 0 ? `重新发送(${countdown}s)` : '发送验证码'}
                  </button>
                </div>
              </div>
            </>
          ) : (
            <>
              {/* Cookie登录 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cookie
                </label>
                <textarea
                  name="cookie"
                  value={formData.cookie}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                  placeholder="请输入完整的Cookie字符串，例如：sessionid=abc123; csrftoken=xyz789; ..."
                  required
                />
                <p className="mt-1 text-xs text-gray-500">
                  从浏览器开发者工具中复制完整的Cookie字符串
                </p>
              </div>
            </>
          )}

          {/* 代理配置 */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center space-x-2 mb-3">
              <input
                type="checkbox"
                id="useProxy"
                name="useProxy"
                checked={formData.useProxy}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
              <label htmlFor="useProxy" className="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <Globe size={16} />
                <span>使用代理</span>
              </label>
            </div>

            {formData.useProxy && (
              <div className="space-y-3 bg-gray-50 p-3 rounded-md">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择代理服务器
                  </label>
                  {loadingProxies ? (
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 text-sm">
                      正在加载代理列表...
                    </div>
                  ) : (
                    <select
                      name="selectedProxyId"
                      value={formData.selectedProxyId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required={formData.useProxy}
                    >
                      <option value="">请选择代理服务器</option>
                      {proxyList.map((proxy) => (
                        <option key={proxy.id} value={proxy.id}>
                          {proxy.ip_address}:{proxy.port} ({proxy.type}) 
                          {proxy.remark && ` - ${proxy.remark}`}
                        </option>
                      ))}
                    </select>
                  )}
                  
                  {!loadingProxies && proxyList.length === 0 && (
                    <p className="mt-1 text-xs text-gray-500">
                      暂无可用的代理服务器，请先在代理管理中添加并启用代理
                    </p>
                  )}
                  
                  {formData.selectedProxyId && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-md">
                      {(() => {
                        const selectedProxy = proxyList.find(p => p.id == formData.selectedProxyId);
                        if (selectedProxy) {
                          return (
                            <div className="text-xs text-blue-800">
                              <p><strong>代理信息:</strong></p>
                              <p>地址: {selectedProxy.ip_address}:{selectedProxy.port}</p>
                              <p>类型: {selectedProxy.type}</p>
                              {selectedProxy.username && (
                                <p>认证: {selectedProxy.username}:******</p>
                              )}
                              {selectedProxy.remark && (
                                <p>备注: {selectedProxy.remark}</p>
                              )}
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? '登录中...' : '登录'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginDialog; 