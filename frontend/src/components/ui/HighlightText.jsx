import React from 'react';

/**
 * 高亮文本组件
 * 在文本中高亮显示匹配的关键词，类似浏览器页面搜索功能
 */
const HighlightText = ({ 
  text, 
  searchTerm, 
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-900 px-0.5 rounded-sm font-medium'
}) => {
  // 如果没有搜索词或文本，直接返回原文本
  if (!searchTerm || !text) {
    return <span className={className}>{text}</span>;
  }

  // 转换为字符串并处理空值
  const textStr = String(text);
  const searchStr = String(searchTerm).trim();
  
  if (!searchStr) {
    return <span className={className}>{textStr}</span>;
  }

  try {
    // 使用正则表达式进行大小写不敏感的搜索
    const regex = new RegExp(`(${searchStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = textStr.split(regex);

    return (
      <span className={className}>
        {parts.map((part, index) => {
          // 如果这部分匹配搜索词，则高亮显示
          if (part.toLowerCase() === searchStr.toLowerCase()) {
            return (
              <mark
                key={index}
                className={highlightClassName}
                style={{
                  background: 'linear-gradient(135deg, #fef08a, #fbbf24)',
                  color: '#92400e',
                  fontWeight: '600',
                  padding: '1px 3px',
                  borderRadius: '3px',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(251, 191, 36, 0.3)'
                }}
              >
                {part}
              </mark>
            );
          }
          return part;
        })}
      </span>
    );
  } catch (error) {
    // 如果正则表达式出错，返回原文本
    console.warn('HighlightText: 正则表达式错误', error);
    return <span className={className}>{textStr}</span>;
  }
};

export default HighlightText; 