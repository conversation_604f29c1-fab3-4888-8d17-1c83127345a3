import React, { useEffect, useRef } from "react";
import { Search, Filter, X } from "lucide-react";
import SearchInput from "./SearchInput";
import Button from "./Button";

/**
 * 通用搜索对话框组件
 */
export const SearchDialog = ({
  isOpen,
  onClose,
  searchTerm,
  onSearch,
  onClear,
  title = "搜索",
  placeholder = "请输入搜索关键词...",
}) => {
  const inputRef = useRef(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50"
      style={{
        backgroundColor: "rgba(0, 0, 0, 0.4)",
        backdropFilter: "blur(4px)",
      }}
    >
      <div className="bg-white rounded-xl p-6 w-96 shadow-2xl border border-gray-200">
        <h3 className="text-lg font-medium mb-4 text-gray-900">{title}</h3>
        <SearchInput
          ref={inputRef}
          value={searchTerm}
          onChange={(e) => onSearch(e.target.value)}
          placeholder={placeholder}
          className="w-full mb-4"
          showClearButton={true}
          onClear={onClear}
          onKeyDown={handleKeyDown}
        />
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            确定
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * 通用筛选对话框组件
 */
export const FilterDialog = ({
  isOpen,
  onClose,
  filterValue,
  onFilterChange,
  filterOptions = [],
  title = "筛选条件",
}) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50"
      style={{
        backgroundColor: "rgba(0, 0, 0, 0.4)",
        backdropFilter: "blur(4px)",
      }}
    >
      <div className="bg-white rounded-xl p-6 w-96 shadow-2xl border border-gray-200">
        <h3 className="text-lg font-medium mb-4 text-gray-900">{title}</h3>
        <div className="space-y-2 mb-4 max-h-64 overflow-y-auto">
          {filterOptions.map((option) => (
            <label
              key={option.value}
              className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
            >
              <input
                type="radio"
                name="filter"
                value={option.value}
                checked={filterValue === option.value}
                onChange={(e) => onFilterChange(e.target.value)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{option.label}</span>
            </label>
          ))}
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button variant="primary" onClick={onClose}>
            确定
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * 通用状态提示栏组件
 */
export const StatusBar = ({
  filters = [],
  onClearFilter,
  totalCount = 0,
  filteredCount = 0,
  selectedCount = 0,
  itemLabel = "项",
  showDeleted = false,
  onClearAll,
  selectedAccount = null,
}) => {
  if (filters.length === 0 && !selectedAccount) return null;

  return (
    <div
      className="flex items-center justify-between px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200"
      style={{ borderColor: "var(--border-color)" }}
    >
      <div className="flex items-center space-x-6">
        {/* 当前选中账户显示 */}
        {selectedAccount && (
          <div className="flex items-center space-x-2 bg-white rounded-lg px-3 py-2 shadow-sm">
            <span className="text-xs font-medium text-gray-700">
              当前账户:{" "}
              <span className="font-semibold text-blue-600">
                {selectedAccount.account_name}
              </span>
              <span className="text-gray-500 ml-1">
                (ID: {selectedAccount.account_id})
              </span>
            </span>
          </div>
        )}

        {filters.map((filter, index) => (
          <div
            key={index}
            className="flex items-center space-x-2 bg-white rounded-lg px-3 py-2 shadow-sm"
          >
            {filter.icon && (
              <filter.icon size={14} className={filter.iconClass} />
            )}
            <span
              className="text-xs font-medium"
              style={{ color: filter.textColor }}
            >
              {filter.label}:{" "}
              <span className="font-normal">{filter.value}</span>
            </span>
            {filter.onClear && (
              <button
                onClick={filter.onClear}
                className="ml-2 hover:text-opacity-80 transition-colors"
                style={{ color: filter.clearColor }}
                title={filter.clearTitle}
              >
                <X size={12} />
              </button>
            )}
          </div>
        ))}
        <div className="flex items-center space-x-2">
          <span className="text-xs font-medium text-gray-700">
            显示{" "}
            <span className="text-blue-600 font-semibold">{filteredCount}</span>{" "}
            / <span className="text-gray-500">{totalCount}</span> {itemLabel}
          </span>
          {showDeleted && (
            <span
              className="ml-2 text-xs px-2 py-1 rounded"
              style={{
                backgroundColor: "#fef2f2",
                color: "#991b1b",
                border: "1px solid #fecaca",
              }}
            >
              包含已删除
            </span>
          )}
          {selectedCount > 0 && (
            <span
              className="ml-2 px-2 py-1 rounded text-xs font-medium"
              style={{
                backgroundColor: "var(--primary-color)",
                color: "white",
              }}
            >
              已选择 {selectedCount}
            </span>
          )}
        </div>
      </div>
      <div className="flex items-center space-x-4">
        {onClearAll && (
          <button
            onClick={onClearAll}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-white"
            title="清除所有筛选条件"
          >
            <X size={14} />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * 通用移动端统计栏组件
 */
export const MobileStatsBar = ({
  totalCount,
  filteredCount,
  selectedCount,
  itemLabel = "项",
  showDeleted = false,
}) => (
  <div
    className="md:hidden p-4 border-t"
    style={{
      borderColor: "var(--border-color)",
    }}
  >
    <div className="text-sm" style={{ color: "var(--text-secondary)" }}>
      显示 {filteredCount} / {totalCount} {itemLabel}
      {showDeleted && (
        <span
          className="ml-2 text-xs px-2 py-1 rounded"
          style={{
            backgroundColor: "#fef2f2",
            color: "#991b1b",
            border: "1px solid #fecaca",
          }}
        >
          包含已删除
        </span>
      )}
      {selectedCount > 0 && (
        <span
          className="ml-2 font-medium"
          style={{ color: "var(--primary-color)" }}
        >
          (已选择 {selectedCount} 个)
        </span>
      )}
    </div>
  </div>
);
