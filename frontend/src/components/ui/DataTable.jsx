import React from "react";
import EmptyState from "./EmptyState";

/**
 * 数据表格组件
 * @param {Array} columns - 表格列配置
 * @param {Array} data - 表格数据
 * @param {boolean} loading - 加载状态
 * @param {boolean} selectable - 是否可选择
 * @param {Array} selectedItems - 已选择的项目
 * @param {Function} onSelectItem - 选择单个项目的回调
 * @param {Function} onSelectAll - 全选回调
 * @param {Function} onRowClick - 行点击回调
 * @param {Function} onRowContextMenu - 行右键菜单回调
 * @param {string} rowKey - 行的唯一标识字段，默认为 'id'
 * @param {string} className - 额外的样式类
 * @param {React.ReactNode} emptyContent - 自定义空状态内容
 */
function DataTable({
  columns = [],
  data = [],
  loading = false,
  selectable = false,
  selectedItems = [],
  onSelectItem,
  onSelectAll,
  onRowClick,
  onRowContextMenu,
  rowKey = "id",
  className = "",
  emptyContent,
}) {
  const handleRowClick = (item, event) => {
    // 如果点击的是选择框或操作按钮，不触发行点击
    if (event.target.type === "checkbox" || event.target.closest("button")) {
      return;
    }
    onRowClick?.(item);
  };

  const handleRowContextMenu = (item, event) => {
    // 如果点击的是选择框或操作按钮，不触发右键菜单
    if (event.target.type === "checkbox" || event.target.closest("button")) {
      return;
    }
    onRowContextMenu?.(event, item);
  };

  const isAllSelected = data.length > 0 && selectedItems.length === data.length;
  const isIndeterminate =
    selectedItems.length > 0 && selectedItems.length < data.length;

  // 计算表格最小宽度
  const calculateTableMinWidth = () => {
    let minWidth = 0;

    // 选择框列宽度
    if (selectable) {
      minWidth += 60; // 选择框列宽度
    }

    // 计算所有列的宽度
    columns.forEach((column) => {
      if (column.width) {
        // 如果列设置了固定宽度，使用该宽度
        const width =
          typeof column.width === "string"
            ? parseInt(column.width)
            : column.width;
        minWidth += width;
      } else {
        // 如果没有设置宽度，使用默认宽度
        minWidth += 150; // 默认列宽度
      }
    });

    return minWidth;
  };

  const tableMinWidth = calculateTableMinWidth();

  // 计算自适应宽度
  const calculateAdaptiveWidth = () => {
    // 获取容器实际宽度（如果可能的话）
    const containerWidth = 1200; // 默认容器宽度

    // 计算固定宽度列的总宽度
    const totalFixedWidth = columns.reduce((total, column) => {
      if (column.width) {
        const width =
          typeof column.width === "string"
            ? parseInt(column.width)
            : column.width;
        return total + width;
      }
      return total;
    }, 0);

    // 计算需要自适应宽度的列数量
    const flexibleColumns = columns.filter((col) => !col.width).length;

    // 如果没有自适应列，直接返回固定宽度
    if (flexibleColumns === 0) return tableMinWidth;

    // 计算剩余可用宽度
    const remainingWidth = Math.max(
      0,
      containerWidth - totalFixedWidth - (selectable ? 60 : 0)
    );

    // 为每个自适应列分配宽度，最小150px
    const flexibleWidth = Math.max(150, remainingWidth / flexibleColumns);

    // 返回总宽度
    return (
      totalFixedWidth + flexibleColumns * flexibleWidth + (selectable ? 60 : 0)
    );
  };

  const adaptiveWidth = calculateAdaptiveWidth();

  // 渲染表头
  const renderHeader = () => (
    <thead
      className="sticky top-0"
      style={{ backgroundColor: "var(--sidebar-bg)" }}
    >
      <tr>
        {selectable && (
          <th
            className="px-6 py-3 text-left"
            style={{
              borderBottom: "1px solid var(--border-color)",
              width: "60px",
              minWidth: "60px",
            }}
          >
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={isAllSelected}
                ref={(input) => {
                  if (input) input.indeterminate = isIndeterminate;
                }}
                onChange={onSelectAll}
                className="rounded transition-wechat"
                style={{
                  accentColor: "var(--primary-color)",
                  width: "16px",
                  height: "16px",
                }}
              />
            </div>
          </th>
        )}
        {columns.map((column, index) => (
          <th
            key={column.key || index}
            className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
              column.headerClassName || ""
            }`}
            style={{
              width: column.width ? `${column.width}px` : "auto",
              minWidth: column.width ? `${column.width}px` : "150px",
              maxWidth: column.maxWidth ? `${column.maxWidth}px` : "none",
              color: "var(--text-secondary)",
              borderBottom: "1px solid var(--border-color)",
              fontSize: "12px",
            }}
          >
            {column.title}
          </th>
        ))}
      </tr>
    </thead>
  );

  // 渲染表格行
  const renderRow = (item, index) => {
    const isSelected = selectedItems.includes(item[rowKey]);

    return (
      <tr
        key={item[rowKey] || index}
        className={`
          transition-wechat group 
          ${onRowClick ? "cursor-pointer" : "cursor-default"}
          ${isSelected ? "table-row-selected" : "table-row-hover"}
        `}
        style={{
          backgroundColor: isSelected ? "#E9F8F1" : "var(--panel-bg)",
          borderBottom: "1px solid var(--border-color)",
        }}
        onClick={(e) => handleRowClick(item, e)}
        onContextMenu={(e) => handleRowContextMenu(item, e)}
      >
        {selectable && (
          <td
            className="px-6 py-4 whitespace-nowrap"
            style={{ width: "60px", minWidth: "60px" }}
          >
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onSelectItem?.(item[rowKey])}
              className="rounded transition-wechat"
              style={{
                accentColor: "var(--primary-color)",
                width: "16px",
                height: "16px",
              }}
            />
          </td>
        )}
        {columns.map((column, colIndex) => (
          <td
            key={column.key || colIndex}
            className={`px-6 py-4 whitespace-nowrap ${column.className || ""}`}
            style={{
              width: column.width ? `${column.width}px` : "auto",
              minWidth: column.width ? `${column.width}px` : "150px",
              maxWidth: column.maxWidth ? `${column.maxWidth}px` : "none",
              color: "var(--text-primary)",
              fontSize: "14px",
            }}
          >
            {column.render ? (
              // 向后兼容：如果有dataIndex，使用旧的调用方式；否则使用新的调用方式
              column.dataIndex ? (
                column.render(item[column.dataIndex], item, index)
              ) : (
                column.render(item[column.key], item, index)
              )
            ) : (
              <span>{item[column.dataIndex || column.key] || ""}</span>
            )}
          </td>
        ))}
      </tr>
    );
  };

  // 渲染表格内容
  const renderBody = () => {
    if (loading) {
      return (
        <tbody>
          <tr>
            <td
              colSpan={columns.length + (selectable ? 1 : 0)}
              className="p-8 text-center"
              style={{ backgroundColor: "var(--panel-bg)" }}
            >
              <div className="flex items-center justify-center space-x-2">
                <div
                  className="animate-spin rounded-full h-6 w-6 border-b-2"
                  style={{ borderColor: "var(--primary-color)" }}
                ></div>
                <span
                  className="text-sm"
                  style={{ color: "var(--text-secondary)" }}
                >
                  加载中...
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      );
    }

    if (data.length === 0) {
      return (
        <tbody>
          <tr>
            <td
              colSpan={columns.length + (selectable ? 1 : 0)}
              className="p-8"
              style={{ backgroundColor: "var(--panel-bg)" }}
            >
              {emptyContent || (
                <div className="text-center">
                  <div
                    className="text-sm"
                    style={{ color: "var(--text-secondary)" }}
                  >
                    暂无数据
                  </div>
                </div>
              )}
            </td>
          </tr>
        </tbody>
      );
    }

    return (
      <tbody style={{ backgroundColor: "var(--panel-bg)" }}>
        {data.map(renderRow)}
      </tbody>
    );
  };

  return (
    <div
      className={`overflow-auto ${className}`}
      style={{
        width: "100%",
        height: "100%",
        minHeight: "0",
      }}
    >
      <table
        className="w-full"
        style={{
          minWidth: `${tableMinWidth}px`,
          tableLayout: "auto",
          borderCollapse: "separate",
          borderSpacing: "0",
          width: "max-content",
        }}
      >
        {renderHeader()}
        {renderBody()}
      </table>
    </div>
  );
}

export default DataTable;
