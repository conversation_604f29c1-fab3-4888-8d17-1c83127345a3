import React, { useState, useEffect } from 'react';
import { X, Eye, EyeOff, RefreshCw } from 'lucide-react';

const AccountDialog = ({ isOpen, onClose, onSubmit, account = null, title = "添加账户" }) => {
  const [formData, setFormData] = useState({
    account_name: '',
    email: '',
    password: '',
    mobile: '',
    login_type: 'Email',
    remark: '',
    proxy_id: -1
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [proxyList, setProxyList] = useState([]);
  const [proxyLoading, setProxyLoading] = useState(false);

  // 加载代理列表
  const loadProxyList = async () => {
    setProxyLoading(true);
    try {
      const { ProxyService } = await import('../../services/api.js');
      const result = await ProxyService.GetProxyList({
        page: 1,
        pageSize: 1000, // 获取所有代理
        status: 2 // 只获取启用状态的代理
      });
      
      if (result && result.code === 0 && result.data) {
        const proxies = result.data.list || [];
        setProxyList(proxies);
      }
    } catch (error) {
      console.error('加载代理列表失败:', error);
      setProxyList([]);
    } finally {
      setProxyLoading(false);
    }
  };

  // 组件加载时获取代理列表
  useEffect(() => {
    if (isOpen) {
      loadProxyList();
    }
  }, [isOpen]);

  // 当账户数据变化时更新表单
  useEffect(() => {
    if (account) {
      setFormData({
        account_name: account.account_name || '',
        email: account.email || '',
        password: account.password || '',
        mobile: account.mobile || '',
        login_type: account.login_type || 'Email',
        remark: account.remark || '',
        proxy_id: account.proxy_id || -1
      });
    } else {
      setFormData({
        account_name: '',
        email: '',
        password: '',
        mobile: '',
        login_type: 'Email',
        remark: '',
        proxy_id: -1
      });
    }
  }, [account]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'proxy_id' ? parseInt(value) || -1 : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const submitData = {
        ...formData,
        proxy_id: parseInt(formData.proxy_id) || -1
      };
      
      if (account) {
        submitData.id = account.id;
        submitData.account_id = account.account_id;
      }
      
      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(107, 114, 128, 0.3)' }}>
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* 账户名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              账户名称 <span className="text-red-500">*</span>
              {account && <span className="text-xs text-gray-500 ml-1">(编辑时不可修改)</span>}
            </label>
            <input
              type="text"
              name="account_name"
              value={formData.account_name}
              onChange={handleInputChange}
              disabled={!!account}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none ${
                account 
                  ? 'bg-gray-50 text-gray-600 cursor-not-allowed' 
                  : 'focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              }`}
              placeholder="请输入账户名称"
              required
            />
          </div>

          {/* 登录类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              登录类型
              {account && <span className="text-xs text-gray-500 ml-1">(编辑时不可修改)</span>}
            </label>
            <select
              name="login_type"
              value={formData.login_type}
              onChange={handleInputChange}
              disabled={!!account}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none ${
                account 
                  ? 'bg-gray-50 text-gray-600 cursor-not-allowed' 
                  : 'focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              }`}
            >
              <option value="Email">邮箱登录</option>
              <option value="SMS">手机登录</option>
              <option value="Cookie">Cookie登录</option>
            </select>
          </div>

          {/* 邮箱 */}
          {(formData.login_type === 'Email' || formData.login_type === 'Cookie') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入邮箱地址"
              />
            </div>
          )}

          {/* 手机号 */}
          {(formData.login_type === 'SMS' || formData.login_type === 'Cookie') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                手机号码
              </label>
              <input
                type="tel"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入手机号码"
              />
            </div>
          )}

          {/* 密码 */}
          {formData.login_type === 'Email' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                密码
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入密码"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>
          )}

          {/* 代理选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              代理设置
            </label>
            <div className="flex space-x-2">
              <select
                name="proxy_id"
                value={formData.proxy_id}
                onChange={handleInputChange}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={proxyLoading}
              >
                <option value="-1">不使用代理</option>
                {proxyList.map(proxy => (
                  <option key={proxy.id} value={proxy.id}>
                    {proxy.ip_address}:{proxy.port} ({proxy.type}) {proxy.remark ? `- ${proxy.remark}` : ''}
                  </option>
                ))}
              </select>
              <button
                type="button"
                onClick={loadProxyList}
                disabled={proxyLoading}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                title="刷新代理列表"
              >
                <RefreshCw size={16} className={proxyLoading ? 'animate-spin' : ''} />
              </button>
            </div>
            {proxyLoading && (
              <div className="text-xs text-gray-500 mt-1">正在加载代理列表...</div>
            )}
            {!proxyLoading && proxyList.length === 0 && (
              <div className="text-xs text-gray-500 mt-1">暂无可用代理</div>
            )}
          </div>

          {/* 备注 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              备注
            </label>
            <textarea
              name="remark"
              value={formData.remark}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="请输入备注信息（可选）"
            />
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? '提交中...' : (account ? '更新' : '添加')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AccountDialog; 