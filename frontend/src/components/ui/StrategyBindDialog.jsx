import React, { useState, useEffect, useRef } from "react";
import { useStrategyMap } from "../../contexts/StrategyMapContext";
import { X, Search, Target, Plus, Trash2, Filter } from "lucide-react";
import strategyService from "../../services/strategyService";
import strategyBindingService from "../../services/strategyBindingService";
import {
  STRATEGY_TYPES,
  STRATEGY_TYPE_LABELS,
  BINDING_TYPES,
  BINDING_TYPE_LABELS,
  STRATEGY_BINDING_MAPPING,
  isValidStrategyBinding,
} from "../../constants/strategyConstants";

/**
 * 策略绑定对话框
 *
 * @param {boolean} visible - 是否显示对话框
 * @param {Function} onClose - 关闭对话框回调
 * @param {Function} onConfirm - 确认选择回调，参数为策略ID字符串（逗号分隔）
 * @param {string} currentStrategyIds - 当前策略ID（逗号分隔）
 * @param {string} bindingType - 绑定类型 (advertiser/project/promotion)
 * @param {string} bindingId - 绑定对象ID
 * @param {string} bindingName - 绑定对象名称
 * @param {string} advertiserId - 广告主ID（用于获取继承策略）
 * @param {string} projectId - 项目ID（用于获取继承策略）
 */
const StrategyBindDialog = ({
  visible,
  onClose,
  onConfirm,
  currentStrategyIds = "",
  bindingType = "project", // 默认为项目绑定
  bindingId,
  bindingName,
  advertiserId = "",
  projectId = "",
}) => {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStrategyIds, setSelectedStrategyIds] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [filteredStrategies, setFilteredStrategies] = useState([]);

  // 继承策略相关状态
  const [inheritedStrategies, setInheritedStrategies] = useState([]);
  const [loadingInherited, setLoadingInherited] = useState(false);
  const [hasLoadedInherited, setHasLoadedInherited] = useState(false);

  // 根据绑定类型设置默认过滤器
  const getDefaultFilter = () => {
    // 现在"all"表示"所有兼容策略"，对所有绑定类型都是合适的默认值
    return "all";
  };

  const [strategyTypeFilter, setStrategyTypeFilter] = useState(
    getDefaultFilter()
  ); // 策略类型过滤
  const [hasLoadedStrategies, setHasLoadedStrategies] = useState(false); // 标记是否已加载过策略
  const dropdownRef = useRef(null);
  const searchRef = useRef(null);
  const { getStrategyName, updateStrategyMap, strategyMap } = useStrategyMap();

  // 处理当前策略ID - 需要在继承策略加载完成后过滤
  useEffect(() => {
    if (visible && currentStrategyIds) {
      const ids = currentStrategyIds
        .toString()
        .split(",")
        .map((id) => id.trim())
        .filter(Boolean);
      setSelectedStrategyIds(ids);
    } else if (visible) {
      setSelectedStrategyIds([]);
    }
  }, [visible, currentStrategyIds, bindingType, bindingId, bindingName]);

  // 在继承策略加载完成后，从已选策略中移除继承的策略
  useEffect(() => {
    if (
      hasLoadedInherited &&
      selectedStrategyIds.length > 0 &&
      inheritedStrategies.length > 0
    ) {
      const inheritedStrategyIds = inheritedStrategies.map(
        (inheritedStrategy) => inheritedStrategy.strategy.id.toString()
      );

      // 过滤掉继承的策略ID
      const filteredSelectedIds = selectedStrategyIds.filter(
        (id) => !inheritedStrategyIds.includes(id)
      );

      // 如果过滤后的数组与原数组不同，更新状态
      if (filteredSelectedIds.length !== selectedStrategyIds.length) {
        setSelectedStrategyIds(filteredSelectedIds);
      }
    }
  }, [hasLoadedInherited, inheritedStrategies, selectedStrategyIds]);

  // 统一的策略加载逻辑 - 只在对话框打开且未加载过策略时调用一次
  useEffect(() => {
    if (visible && !hasLoadedStrategies && !loading) {
      loadStrategies();
    }
  }, [visible, hasLoadedStrategies, loading]);

  // 加载继承策略 - 只在对话框打开且未加载过继承策略时调用一次
  useEffect(() => {
    if (visible && !hasLoadedInherited && !loadingInherited && bindingId) {
      loadInheritedStrategies();
    }
  }, [visible, hasLoadedInherited, loadingInherited, bindingId]);

  // 过滤策略列表
  useEffect(() => {
    if (strategies.length > 0) {
      const filtered = strategies.filter((strategy) => {
        // 搜索过滤
        const matchesSearch = strategy.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

        // 已选择过滤
        const notSelected = !selectedStrategyIds.includes(
          strategy.id.toString()
        );

        // 排除继承的策略 - 继承的策略不能在子级页面操作
        const isNotInherited = !inheritedStrategies.some(
          (inheritedStrategy) =>
            inheritedStrategy.strategy.id.toString() === strategy.id.toString()
        );

        // 策略类型过滤
        const strategyType = strategy.type || STRATEGY_TYPES.GLOBAL; // 默认为全局策略

        // 强制兼容性检查：始终只显示与当前绑定类型兼容的策略
        const isCompatible = isValidStrategyBinding(strategyType, bindingType);

        // 如果不兼容，直接过滤掉，不管用户选择什么过滤器
        if (!isCompatible) {
          return false;
        }

        // 在兼容的策略中，根据用户选择的过滤器进一步过滤
        let matchesTypeFilter = true;
        if (strategyTypeFilter !== "all") {
          matchesTypeFilter = strategyType === strategyTypeFilter;
        }

        return (
          matchesSearch && notSelected && isNotInherited && matchesTypeFilter
        );
      });
      setFilteredStrategies(filtered);
    } else {
      // 如果没有策略数据，清空过滤结果
      setFilteredStrategies([]);
    }
  }, [
    strategies,
    searchTerm,
    selectedStrategyIds,
    strategyTypeFilter,
    bindingType,
    inheritedStrategies, // 添加继承策略依赖
  ]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (visible) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [visible]);

  // 当绑定类型改变时重置过滤器
  useEffect(() => {
    setStrategyTypeFilter(getDefaultFilter());
  }, [bindingType]);

  // 对话框关闭时重置状态
  useEffect(() => {
    if (!visible) {
      setHasLoadedStrategies(false);
      setHasLoadedInherited(false);
      setInheritedStrategies([]);
      setSearchTerm("");
      setStrategyTypeFilter(getDefaultFilter());
      setShowDropdown(false);
    }
  }, [visible]);

  const loadStrategies = async () => {
    // 如果正在加载，则跳过
    if (loading) {
      return;
    }

    setLoading(true);
    try {
      const response = await strategyService.getStrategyList({
        page: 1,
        pageSize: 1000,
        // 不传 enabled 参数，获取所有策略（包括禁用的）
        name: "",
      });

      if (response.code === 0) {
        const strategiesList = response.data.list || [];
        setStrategies(strategiesList);
        // 更新策略映射
        updateStrategyMap(strategiesList);
        setHasLoadedStrategies(true); // 标记已加载
      } else {
        console.error("StrategyBindDialog - 获取策略列表失败:", response.msg);
        // 即使失败也设置空数组，避免重复请求
        setStrategies([]);
        setHasLoadedStrategies(true); // 标记已加载，避免重复请求
      }
    } catch (error) {
      console.error("StrategyBindDialog - 加载策略列表失败:", error);
      // 即使失败也设置空数组，避免重复请求
      setStrategies([]);
      setHasLoadedStrategies(true); // 标记已加载，避免重复请求
    } finally {
      setLoading(false);
    }
  };

  // 加载继承的策略
  const loadInheritedStrategies = async () => {
    if (loadingInherited) {
      return;
    }

    setLoadingInherited(true);
    try {
      const response =
        await strategyBindingService.getInheritedStrategiesByBinding({
          bindingType,
          bindingId,
          bindingName,
          advertiserId,
          projectId,
        });

      if (response && response.code === 0) {
        const inheritedData = response.data || {};
        setInheritedStrategies(inheritedData.inherited_strategies || []);
        setHasLoadedInherited(true);
      } else {
        console.error("StrategyBindDialog - 获取继承策略失败:", response?.msg);
        setInheritedStrategies([]);
        setHasLoadedInherited(true);
      }
    } catch (error) {
      console.error("StrategyBindDialog - 加载继承策略失败:", error);
      setInheritedStrategies([]);
      setHasLoadedInherited(true);
    } finally {
      setLoadingInherited(false);
    }
  };

  // 添加策略到已绑定列表
  const addStrategy = (strategy) => {
    if (!selectedStrategyIds.includes(strategy.id.toString())) {
      setSelectedStrategyIds((prev) => [...prev, strategy.id.toString()]);
      setSearchTerm("");
      setShowDropdown(false);
    }
  };

  // 从已绑定列表中移除策略
  const removeStrategy = (strategyId) => {
    setSelectedStrategyIds((prev) => prev.filter((id) => id !== strategyId));
  };

  // 处理确认选择
  const handleConfirm = () => {
    const strategyIdsStr = selectedStrategyIds.join(",");
    onConfirm(strategyIdsStr);
    onClose();
  };

  // 获取已绑定策略的详细信息
  const getBoundStrategies = () => {
    return selectedStrategyIds.map((id) => {
      // 首先从已加载的策略列表中查找
      const strategy = strategies.find((s) => s.id.toString() === id);

      if (strategy) {
        // 如果找到了策略，返回完整信息
        return {
          id,
          name: strategy.name,
          description: strategy.description,
          enabled: strategy.enabled,
          type: strategy.type || STRATEGY_TYPES.GLOBAL,
          isLoaded: true,
        };
      } else {
        // 如果没找到策略，从策略映射中获取名称
        const strategyName = getStrategyName(id);
        return {
          id,
          name: strategyName || `策略#${id}`,
          description: "策略信息加载中...",
          enabled: true, // 默认显示为启用状态
          type: STRATEGY_TYPES.GLOBAL, // 默认类型
          isLoaded: false,
        };
      }
    });
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 遮罩层 */}
      <div
        className="absolute inset-0 bg-black/50 transition-opacity"
        onClick={onClose}
      />

      {/* 对话框 */}
      <div
        className="relative bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[85vh] overflow-hidden"
        style={{
          backgroundColor: "var(--panel-bg)",
          border: "1px solid var(--border-color)",
        }}
      >
        {/* 标题栏 */}
        <div
          className="flex items-center justify-between p-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center space-x-2">
            <Target size={20} style={{ color: "var(--primary-color)" }} />
            <div>
              <h3
                className="text-lg font-semibold"
                style={{ color: "var(--text-primary)" }}
              >
                绑定策略
              </h3>
              <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                为{BINDING_TYPE_LABELS[bindingType]} "{bindingName}"
                绑定执行策略
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded transition-wechat"
            style={{ color: "var(--text-secondary)" }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "transparent";
            }}
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex flex-col h-full">
          {/* 可搜索下拉菜单区域 */}
          <div
            className="p-4 border-b"
            style={{ borderColor: "var(--border-color)" }}
          >
            <div className="flex items-center justify-between mb-2">
              <label
                className="text-sm font-medium"
                style={{ color: "var(--text-primary)" }}
              >
                添加策略
                <span className="ml-2 text-xs px-2 py-1 rounded bg-green-100 text-green-600">
                  仅显示兼容策略
                </span>
              </label>
              <div className="flex items-center space-x-2">
                <Filter size={14} style={{ color: "var(--text-secondary)" }} />
                <select
                  value={strategyTypeFilter}
                  onChange={(e) => setStrategyTypeFilter(e.target.value)}
                  className="text-xs px-2 py-1 rounded border"
                  style={{
                    backgroundColor: "var(--sidebar-bg)",
                    borderColor: "var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                >
                  {(() => {
                    // 获取当前绑定类型支持的策略类型
                    const supportedTypes = Object.entries(
                      STRATEGY_BINDING_MAPPING
                    )
                      .filter(([_, bindingTypes]) =>
                        bindingTypes.includes(bindingType)
                      )
                      .map(([strategyType, _]) => strategyType);

                    return (
                      <>
                        <option value="all">所有兼容策略</option>
                        {supportedTypes.map((strategyType) => (
                          <option key={strategyType} value={strategyType}>
                            {STRATEGY_TYPE_LABELS[strategyType]}
                          </option>
                        ))}
                      </>
                    );
                  })()}
                </select>
              </div>
            </div>
            <div className="relative" ref={dropdownRef}>
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2"
                  style={{ color: "var(--text-secondary)" }}
                />
                <input
                  ref={searchRef}
                  type="text"
                  placeholder="搜索并选择策略..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setShowDropdown(true);
                  }}
                  onFocus={(e) => {
                    setShowDropdown(true);
                    e.currentTarget.style.borderColor = "var(--primary-color)";
                  }}
                  className="w-full pl-10 pr-4 py-2 text-sm rounded border transition-wechat"
                  style={{
                    backgroundColor: "var(--sidebar-bg)",
                    borderColor: "var(--border-color)",
                    color: "var(--text-primary)",
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = "var(--border-color)";
                  }}
                />
                <button
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
                  style={{ color: "var(--text-secondary)" }}
                >
                  <Plus size={16} />
                </button>
              </div>

              {/* 下拉菜单 */}
              {showDropdown && (
                <div
                  className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto"
                  style={{
                    backgroundColor: "var(--panel-bg)",
                    borderColor: "var(--border-color)",
                  }}
                >
                  {loading ? (
                    <div
                      className="p-3 text-center text-sm"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      加载中...
                    </div>
                  ) : filteredStrategies.length === 0 ? (
                    <div
                      className="p-3 text-center text-sm"
                      style={{ color: "var(--text-secondary)" }}
                    >
                      {searchTerm ? "未找到匹配的策略" : "暂无可用策略"}
                    </div>
                  ) : (
                    filteredStrategies.map((strategy) => (
                      <div
                        key={strategy.id}
                        className="p-3 cursor-pointer transition-wechat border-b last:border-b-0"
                        style={{ borderColor: "var(--border-color)" }}
                        onClick={() => addStrategy(strategy)}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor =
                            "var(--hover-bg)";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = "transparent";
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div
                              className="text-sm font-medium"
                              style={{ color: "var(--text-primary)" }}
                            >
                              {strategy.name}
                            </div>
                            {strategy.description && (
                              <div
                                className="text-xs mt-1"
                                style={{ color: "var(--text-secondary)" }}
                              >
                                {strategy.description}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-600">
                              {
                                STRATEGY_TYPE_LABELS[
                                  strategy.type || STRATEGY_TYPES.GLOBAL
                                ]
                              }
                            </span>
                            <span
                              className={`text-xs px-2 py-1 rounded ${
                                strategy.enabled
                                  ? "bg-green-100 text-green-600"
                                  : "bg-gray-100 text-gray-600"
                              }`}
                            >
                              {strategy.enabled ? "已启用" : "已禁用"}
                            </span>
                            <Plus
                              size={14}
                              style={{ color: "var(--primary-color)" }}
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 继承策略展示区域 - 只读显示 */}
          {inheritedStrategies.length > 0 && (
            <div
              className="p-4 border-b"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center justify-between mb-3">
                <label
                  className="text-sm font-medium"
                  style={{ color: "var(--text-primary)" }}
                >
                  继承的策略 ({inheritedStrategies.length})
                  <span className="ml-2 text-xs px-2 py-1 rounded bg-blue-100 text-blue-600">
                    来自上级·只读
                  </span>
                </label>
                <div className="text-xs text-gray-500">需要在上级页面管理</div>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {inheritedStrategies.map((strategyWithSource) => (
                  <div
                    key={`inherited-${strategyWithSource.strategy.id}`}
                    className="flex items-center justify-between p-3 rounded border bg-blue-50 opacity-75"
                    style={{
                      borderColor: "var(--border-color)",
                      cursor: "not-allowed",
                    }}
                    title="继承的策略不能在此处修改，请到上级页面进行管理"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <div
                          className="text-sm font-medium"
                          style={{ color: "var(--text-primary)" }}
                        >
                          {strategyWithSource.strategy.name}
                        </div>
                        <span className="text-xs px-1.5 py-0.5 rounded bg-gray-200 text-gray-600">
                          只读
                        </span>
                      </div>
                      <div className="text-xs mt-1 text-blue-600">
                        来自
                        {strategyWithSource.source_type === "advertiser"
                          ? "广告主"
                          : strategyWithSource.source_type === "project"
                          ? "项目"
                          : "其他"}
                        ：{strategyWithSource.source_name}
                      </div>
                      {strategyWithSource.strategy.description && (
                        <div
                          className="text-xs mt-1"
                          style={{ color: "var(--text-secondary)" }}
                        >
                          {strategyWithSource.strategy.description}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-600">
                        {strategyWithSource.strategy.type === "global"
                          ? "全局策略"
                          : "策略"}
                      </span>
                      <span
                        className={`text-xs px-2 py-1 rounded ${
                          strategyWithSource.strategy.enabled
                            ? "bg-green-100 text-green-600"
                            : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        {strategyWithSource.strategy.enabled
                          ? "已启用"
                          : "已禁用"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* 继承策略说明 */}
              <div
                className="mt-3 p-2 rounded bg-gray-50 border"
                style={{ borderColor: "var(--border-color)" }}
              >
                <div className="text-xs text-gray-600">
                  <strong>说明：</strong>
                  继承的策略不能在此处修改或删除。如需调整这些策略，请前往对应的上级页面：
                  <br />
                  • 来自广告主的策略 → 广告主管理页面
                  <br />• 来自项目的策略 → 项目管理页面
                </div>
              </div>
            </div>
          )}

          {/* 已绑定策略展示区域 */}
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="flex items-center justify-between mb-3">
              <label
                className="text-sm font-medium"
                style={{ color: "var(--text-primary)" }}
              >
                直接绑定策略 ({selectedStrategyIds.length})
              </label>
              {selectedStrategyIds.length > 0 && (
                <button
                  onClick={() => setSelectedStrategyIds([])}
                  className="text-xs px-2 py-1 rounded transition-wechat"
                  style={{
                    color: "var(--text-secondary)",
                    border: "1px solid var(--border-color)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  清空全部
                </button>
              )}
            </div>

            {selectedStrategyIds.length === 0 ? (
              <div className="text-center py-8">
                <Target size={32} className="mx-auto mb-2 opacity-50" />
                <div
                  className="text-sm"
                  style={{ color: "var(--text-secondary)" }}
                >
                  暂未绑定任何策略
                </div>
                <div
                  className="text-xs mt-1"
                  style={{ color: "var(--text-secondary)" }}
                >
                  请在上方搜索并添加策略
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {getBoundStrategies().map((strategy) => (
                  <div
                    key={strategy.id}
                    className={`flex items-center justify-between p-3 rounded border transition-wechat ${
                      !strategy.isLoaded ? "opacity-75" : ""
                    }`}
                    style={{
                      borderColor: "var(--border-color)",
                      backgroundColor: strategy.isLoaded
                        ? "var(--sidebar-bg)"
                        : "rgba(156, 163, 175, 0.1)",
                    }}
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`text-sm font-medium ${
                            !strategy.isLoaded ? "italic" : ""
                          }`}
                          style={{ color: "var(--text-primary)" }}
                        >
                          {strategy.name}
                          {!strategy.isLoaded && (
                            <span className="ml-1 text-xs text-blue-500">
                              (加载中...)
                            </span>
                          )}
                        </div>
                        <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-600">
                          {STRATEGY_TYPE_LABELS[strategy.type] || "策略"}
                        </span>
                        <span
                          className={`text-xs px-2 py-1 rounded ${
                            strategy.enabled
                              ? "bg-green-100 text-green-600"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          {strategy.enabled ? "已启用" : "已禁用"}
                        </span>
                      </div>
                      {strategy.description && (
                        <div
                          className={`text-xs mt-1 ${
                            !strategy.isLoaded ? "italic" : ""
                          }`}
                          style={{ color: "var(--text-secondary)" }}
                        >
                          {strategy.description}
                        </div>
                      )}
                      <div
                        className="text-xs mt-1"
                        style={{ color: "var(--text-secondary)" }}
                      >
                        ID: {strategy.id}
                      </div>
                    </div>
                    <button
                      onClick={() => removeStrategy(strategy.id)}
                      className="p-1 rounded transition-wechat ml-2"
                      style={{ color: "var(--text-secondary)" }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          "var(--hover-bg)";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                      }}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div
            className="flex items-center justify-between p-4 border-t"
            style={{ borderColor: "var(--border-color)" }}
          >
            <div className="text-sm" style={{ color: "var(--text-secondary)" }}>
              直接绑定策略: {selectedStrategyIds.length} 个
              {inheritedStrategies.length > 0 && (
                <span className="ml-2 text-blue-600">
                  | 继承策略: {inheritedStrategies.length} 个（不受影响）
                </span>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm rounded transition-wechat"
                style={{
                  color: "var(--text-secondary)",
                  border: "1px solid var(--border-color)",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                取消
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 text-sm rounded transition-wechat text-white"
                style={{
                  backgroundColor: "var(--primary-color)",
                  border: "1px solid var(--primary-color)",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = "0.9";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = "1";
                }}
              >
                {selectedStrategyIds.length === 0
                  ? "清空直接绑定策略"
                  : `确定绑定 (${selectedStrategyIds.length})`}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyBindDialog;
