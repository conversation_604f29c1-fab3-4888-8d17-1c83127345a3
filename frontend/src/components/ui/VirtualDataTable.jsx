import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { FixedSizeList as List } from "react-window";
import ContextMenu from "./ContextMenu";
import { useContextMenu } from "../../hooks/useContextMenu";
import "./VirtualDataTable.css";

/**
 * 虚拟滚动数据表格组件
 * 优化大量数据渲染性能
 */
const VirtualDataTable = React.memo(
  ({
    columns = [],
    data = [],
    loading = false,
    selectable = false,
    selectedItems = [],
    onSelectItem,
    onRowClick,
    onRowContextMenu, // 行右键回调
    contextMenuItems = [], // 右键菜单项配置 (可以是数组或函数)
    rowKey = "id",
    className = "",
    rowClassName, // 新增：行样式类名函数
    emptyState,
    loadingState,
    itemHeight = 36, // 增加行高，提供更好的视觉体验
    overscan = 10, // 预渲染的行数
  }) => {
    const listRef = useRef();
    const [containerWidth, setContainerWidth] = useState(0);
    const [containerHeight, setContainerHeight] = useState(0);
    const containerRef = useRef();
    const listContainerRef = useRef();

    // 拖拽选择状态
    const [isDragging, setIsDragging] = useState(false);
    const [dragStartIndex, setDragStartIndex] = useState(-1);
    const [dragEndIndex, setDragEndIndex] = useState(-1);
    const [dragStartY, setDragStartY] = useState(0);

    // 列宽调整状态
    const [isResizing, setIsResizing] = useState(false);
    const [resizingColumnIndex, setResizingColumnIndex] = useState(-1);
    const [columnWidthsState, setColumnWidthsState] = useState({});
    const [resizeStartX, setResizeStartX] = useState(0);
    const [resizeStartWidth, setResizeStartWidth] = useState(0);

    // 右键菜单管理
    const { contextMenu, showContextMenu, hideContextMenu } = useContextMenu();

    // 空状态的右键菜单处理
    const handleEmptyContextMenu = useCallback(
      (e) => {
        e.preventDefault();

        // 空状态时显示所有菜单项，但禁用选择相关的操作
        const menuItems =
          typeof contextMenuItems === "function"
            ? contextMenuItems(null)
            : contextMenuItems;

        if (menuItems && menuItems.length > 0) {
          // 在空状态时，禁用选择相关的菜单项
          const adjustedMenuItems = menuItems.map((menuItem) => {
            if (menuItem.type === "divider") return menuItem;

            // 选择相关的操作在空状态时禁用
            const isSelectionRelated =
              menuItem.key === "toggleSelectAll" ||
              menuItem.key === "clearSelection";

            return {
              ...menuItem,
              disabled: isSelectionRelated ? true : menuItem.disabled, // 保持原有禁用状态，但选择相关的强制禁用
              onClick: isSelectionRelated
                ? undefined
                : () => menuItem.onClick?.(null),
            };
          });

          showContextMenu(e, adjustedMenuItems, null);
        }
      },
      [contextMenuItems, showContextMenu]
    );

    // 监听容器宽度和高度变化
    useEffect(() => {
      const resizeObserver = new ResizeObserver((entries) => {
        if (entries[0]) {
          setContainerWidth(entries[0].contentRect.width);
        }
      });

      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
        setContainerWidth(containerRef.current.clientWidth);
      }

      return () => resizeObserver.disconnect();
    }, []);

    // 监听List容器高度变化
    useEffect(() => {
      const measureHeight = () => {
        if (listContainerRef.current) {
          const height = listContainerRef.current.clientHeight;
          if (height > 0) {
            setContainerHeight(height);
          }
        }
      };

      // 立即测量一次
      measureHeight();

      const resizeObserver = new ResizeObserver((entries) => {
        if (entries[0] && entries[0].contentRect.height > 0) {
          setContainerHeight(entries[0].contentRect.height);
        }
      });

      // 延迟一下确保DOM已经挂载
      const timer = setTimeout(() => {
        if (listContainerRef.current) {
          resizeObserver.observe(listContainerRef.current);
          measureHeight(); // 再次测量
        }
      }, 100);

      return () => {
        clearTimeout(timer);
        resizeObserver.disconnect();
      };
    }, [data]); // 当数据变化时重新监听

    // 拖拽选择的全局事件监听
    useEffect(() => {
      const handleMouseMove = (e) => {
        if (
          !isDragging ||
          !selectable ||
          !listContainerRef.current ||
          !listRef.current
        )
          return;

        const rect = listContainerRef.current.getBoundingClientRect();
        const y = e.clientY - rect.top;

        // 获取当前滚动位置
        const scrollTop = listRef.current.state.scrollOffset || 0;

        // 计算考虑滚动偏移的实际行索引
        const actualY = y + scrollTop;
        const currentIndex = Math.floor(actualY / itemHeight);

        if (currentIndex >= 0 && currentIndex < data.length) {
          setDragEndIndex(currentIndex);
        }
      };

      const handleMouseUp = (e) => {
        if (
          isDragging &&
          selectable &&
          dragStartIndex >= 0 &&
          dragEndIndex >= 0
        ) {
          // 计算选中范围
          const startIndex = Math.min(dragStartIndex, dragEndIndex);
          const endIndex = Math.max(dragStartIndex, dragEndIndex);

          // 批量选中
          const newSelectedItems = [];
          for (let i = startIndex; i <= endIndex; i++) {
            if (data[i]) {
              newSelectedItems.push(data[i][rowKey]);
            }
          }

          // 触发批量选择回调
          if (onSelectItem && newSelectedItems.length > 0) {
            // 如果父组件支持批量选择，使用批量选择
            if (onSelectItem.isBatchSelect) {
              onSelectItem(newSelectedItems, e.ctrlKey || e.metaKey);
            } else {
              // 否则逐个选择
              newSelectedItems.forEach((id) => {
                if (!selectedItems.includes(id)) {
                  onSelectItem(id);
                }
              });
            }
          }
        }

        setIsDragging(false);
        setDragStartIndex(-1);
        setDragEndIndex(-1);
        setDragStartY(0);
      };

      if (isDragging) {
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      }

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }, [
      isDragging,
      dragStartIndex,
      dragEndIndex,
      selectable,
      data,
      itemHeight,
      rowKey,
      selectedItems,
      onSelectItem,
    ]);

    // 列宽调整的全局事件监听
    useEffect(() => {
      const handleMouseMove = (e) => {
        if (!isResizing || resizingColumnIndex < 0) return;

        const deltaX = e.clientX - resizeStartX;
        const newWidth = Math.max(80, resizeStartWidth + deltaX); // 最小宽度80px

        setColumnWidthsState(prev => ({
          ...prev,
          [resizingColumnIndex]: newWidth
        }));
      };

      const handleMouseUp = () => {
        if (isResizing) {
          setIsResizing(false);
          setResizingColumnIndex(-1);
          setResizeStartX(0);
          setResizeStartWidth(0);
        }
      };

      if (isResizing) {
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      }

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }, [isResizing, resizingColumnIndex, resizeStartX, resizeStartWidth]);

    // 计算列宽 - 确保精确对齐，支持手动调整
    const columnWidths = useMemo(() => {
      if (containerWidth === 0) return columns.map(() => 120); // 默认宽度

      // 滚动条宽度补偿
      const scrollbarWidth = 8; // 减少滚动条宽度估计
      const availableWidth = containerWidth - scrollbarWidth;

      // 生成最终宽度数组，优先使用手动调整的宽度
      return columns.map((col, index) => {
        // 如果有手动调整的宽度，优先使用
        if (columnWidthsState[index] != null) {
          return columnWidthsState[index];
        }
        // 否则使用原有逻辑
        if (col.width) {
          return parseInt(col.width);
        }
        
        // 计算剩余宽度分配给弹性列
        const fixedColumns = columns.filter((c, i) => c.width || columnWidthsState[i] != null);
        const totalFixedWidth = fixedColumns.reduce((sum, c, i) => {
          return sum + (columnWidthsState[i] != null ? columnWidthsState[i] : parseInt(c.width || 0));
        }, 0);
        
        const flexColumns = columns.filter((c, i) => !c.width && columnWidthsState[i] == null);
        const remainingWidth = Math.max(0, availableWidth - totalFixedWidth);
        const flexWidth = flexColumns.length > 0 ? Math.floor(remainingWidth / flexColumns.length) : 120;
        
        return Math.max(100, flexWidth); // 最小宽度100px
      });
    }, [columns, containerWidth, columnWidthsState]);

    // 判断行是否在拖拽选择范围内
    const isInDragSelection = useCallback(
      (index) => {
        if (!isDragging || dragStartIndex < 0 || dragEndIndex < 0) return false;
        const startIndex = Math.min(dragStartIndex, dragEndIndex);
        const endIndex = Math.max(dragStartIndex, dragEndIndex);
        return index >= startIndex && index <= endIndex;
      },
      [isDragging, dragStartIndex, dragEndIndex]
    );

    // 开始列宽调整
    const handleColumnResizeStart = useCallback((e, columnIndex) => {
      // 检查是否点击在右边缘调整区域，且不是最后一列
      if (columnIndex < columns.length - 1) {
        const rect = e.currentTarget.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const isRightEdge = clickX >= rect.width - 4;
        
        if (isRightEdge) {
          e.preventDefault();
          e.stopPropagation();
          
          setIsResizing(true);
          setResizingColumnIndex(columnIndex);
          setResizeStartX(e.clientX);
          setResizeStartWidth(columnWidths[columnIndex]);
        }
      }
    }, [columnWidths, columns.length]);

    // 表头组件 - 优化样式
    const TableHeader = useMemo(
      () => (
        <div
          className="virtual-table-header"
          style={{
            display: "flex",
            alignItems: "center",
            position: "sticky",
            top: 0,
            zIndex: 10,
            height: itemHeight + 4, // 表头稍微高一点
            minWidth: "fit-content",
            backgroundColor: "var(--table-header-bg, #fafbfc)",
            borderBottom: "2px solid var(--border-color)",
            boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
            backdropFilter: "blur(8px)",
          }}
        >
          {columns.map((column, index) => (
            <div
              key={column.key || index}
              className={`virtual-table-header-cell ${
                resizingColumnIndex === index ? "resizing-column" : ""
              }`}
              style={{
                width: columnWidths[index],
                minWidth: columnWidths[index],
                maxWidth: columnWidths[index],
                height: "100%",
                display: "flex",
                alignItems: "center",
                padding: "0 12px",
                fontSize: "12px",
                fontWeight: 600,
                color: "var(--text-secondary)",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
                boxSizing: "border-box",
                borderRight: "1px solid var(--border-light, rgba(0,0,0,0.06))",
              }}
              onMouseDown={(e) => handleColumnResizeStart(e, index)}
            >
              <span className="virtual-table-truncate">{column.title}</span>
            </div>
          ))}
        </div>
      ),
      [columns, columnWidths, itemHeight, resizingColumnIndex, handleColumnResizeStart]
    );

    // 行组件 - 优化样式
    const Row = React.memo(({ index, style }) => {
      const item = data[index];
      const isSelected = selectedItems.includes(item[rowKey]);
      const isEven = index % 2 === 0;
      const inDragSelection = isInDragSelection(index);

      // 获取自定义行类名
      const customRowClassName =
        typeof rowClassName === "function"
          ? rowClassName(item, index)
          : rowClassName || "";

      const handleRowClick = useCallback(
        (e) => {
          // 如果正在拖拽，不触发行点击
          if (isDragging) return;

          // 排除有交互功能的元素
          if (
            e.target.closest("button") ||
            e.target.closest("a") ||
            e.target.closest('[role="link"]') ||
            e.target.closest("[data-clickable]") ||
            e.target.closest(".cursor-pointer") ||
            e.target.closest("[onclick]")
          ) {
            return;
          }

          // 如果启用了选择功能，处理选择逻辑
          if (selectable && onSelectItem) {
            const isCtrlKey = e.ctrlKey || e.metaKey;
            const itemId = item[rowKey];
            console.log("VirtualDataTable handleRowClick:", {
              itemId,
              isCtrlKey,
              selectable,
              hasOnSelectItem: !!onSelectItem,
              item,
              rowKey,
            });
            // 传递广告主ID而不是整个对象
            onSelectItem(itemId, isCtrlKey);
          }

          onRowClick?.(item);
        },
        [item, isDragging, selectable, onSelectItem, onRowClick, rowKey]
      );

      const handleRowContextMenu = useCallback(
        (e) => {
          // 排除有交互功能的元素
          if (
            e.target.closest("button") ||
            e.target.closest("[data-clickable]")
          ) {
            return;
          }

          // 从事件中获取当前行数据（因为现在是从单元格触发的）
          // 通过查找最近的行元素来确定当前行的索引
          const rowElement = e.target.closest(".virtual-table-row");
          if (!rowElement) return;

          // 获取行索引（从数据属性中获取）
          const rowIndex = parseInt(rowElement.dataset.rowIndex);
          if (isNaN(rowIndex) || !data[rowIndex]) return;

          const currentItem = data[rowIndex];
          const currentIsSelected = selectedItems.includes(currentItem[rowKey]);

          // 获取当前单元格信息
          const cellElement = e.target.closest(".virtual-table-cell");
          let currentCellData = null;

          if (cellElement) {
            // 获取列索引
            const cells = Array.from(
              rowElement.querySelectorAll(".virtual-table-cell")
            );
            const cellIndex = cells.indexOf(cellElement);

            if (cellIndex >= 0 && cellIndex < columns.length) {
              const column = columns[cellIndex];
              const fieldKey = column.dataIndex || column.key;
              const fieldValue = currentItem[fieldKey];
              const fieldName =
                typeof column.title === "string" ? column.title : column.key;

              currentCellData = {
                fieldKey,
                fieldValue,
                fieldName,
                columnIndex: cellIndex,
                column,
              };
            }
          }

          // 右键选中逻辑：
          // 1. 如果当前行已被选中，保持所有选中项不变
          // 2. 如果当前行未被选中，清空其他选中项，只选中当前行
          if (selectable && onSelectItem && !currentIsSelected) {
            // 当前行未被选中，清空其他选择并选中当前行
            // 注意：需要传递 false 作为第二个参数，表示这不是 Ctrl 键操作（即需要清空其他选择）
            onSelectItem(currentItem[rowKey], false);
          }
          // 如果当前行已被选中，不做任何操作，保持原有选中状态

          // 支持动态菜单项生成
          const menuItems =
            typeof contextMenuItems === "function"
              ? contextMenuItems(currentItem, currentCellData)
              : contextMenuItems;

          if (menuItems && menuItems.length > 0) {
            // 生成菜单项（传入当前行数据和单元格数据）
            const finalMenuItems = menuItems.map((menuItem) => ({
              ...menuItem,
              onClick: () => menuItem.onClick?.(currentItem, currentCellData),
            }));

            showContextMenu(e, finalMenuItems, {
              item: currentItem,
              cellData: currentCellData,
            });
          }

          onRowContextMenu?.(e, currentItem);
        },
        [
          data,
          selectedItems,
          rowKey,
          contextMenuItems,
          showContextMenu,
          onRowContextMenu,
          selectable,
          onSelectItem,
        ]
      );

      // 处理拖拽开始
      const handleMouseDown = useCallback(
        (e) => {
          if (!selectable || e.button !== 0) return; // 只响应左键

          // 排除有交互功能的元素：按钮、链接、可点击元素
          if (
            e.target.closest("button") ||
            e.target.closest("a") ||
            e.target.closest('[role="link"]') ||
            e.target.closest("[data-clickable]") ||
            e.target.closest(".cursor-pointer") ||
            e.target.closest("[onclick]")
          ) {
            return;
          }

          // 开始拖拽选择
          const rect = listContainerRef.current?.getBoundingClientRect();
          if (rect) {
            setIsDragging(true);
            setDragStartIndex(index);
            setDragEndIndex(index);
            setDragStartY(e.clientY - rect.top);

            // 只有在真正开始拖拽时才阻止默认行为
            e.preventDefault();
          }
        },
        [selectable, index]
      );

      return (
        <div
          style={{
            ...style,
            minWidth: "fit-content",
          }}
          className={`virtual-table-row ${isSelected ? "selected" : ""} ${
            isEven ? "even" : "odd"
          } ${onRowClick ? "clickable" : ""} ${
            inDragSelection ? "drag-selecting" : ""
          } ${customRowClassName}`}
          data-row-index={index}
          onClick={handleRowClick}
          onMouseDown={handleMouseDown}
        >
          {columns.map((column, colIndex) => (
            <div
              key={column.key || colIndex}
              className="virtual-table-cell"
              onContextMenu={handleRowContextMenu}
              style={{
                width: columnWidths[colIndex],
                minWidth: columnWidths[colIndex],
                maxWidth: columnWidths[colIndex],
                height: "100%",
                display: "flex",
                alignItems: "center",
                padding: "0 12px",
                fontSize: "13px",
                lineHeight: "1.4",
                color: "var(--text-primary)",
                boxSizing: "border-box",
                borderRight: "1px solid var(--border-light, rgba(0,0,0,0.04))",
              }}
            >
              {column.render ? (
                <div className="virtual-table-cell-content">
                  {column.dataIndex
                    ? column.render(item[column.dataIndex], item, index)
                    : column.render(item, index)}
                </div>
              ) : (
                <span
                  className="virtual-table-truncate"
                  title={item[column.dataIndex || column.key]}
                >
                  {item[column.dataIndex || column.key]}
                </span>
              )}
            </div>
          ))}
        </div>
      );
    });

    // 渲染内容的决定逻辑，但不是提前return
    const renderContent = () => {
      // 加载状态
      if (loading) {
        return (
          <div
            className="virtual-table-content"
            style={{ position: "relative", height: "100%" }}
          >
            <div className="virtual-table-loading">
              {loadingState || (
                <div className="virtual-table-loading-content">
                  <div className="virtual-table-spinner">
                    <div className="virtual-table-spinner-ring"></div>
                    <div className="virtual-table-spinner-ring"></div>
                    <div className="virtual-table-spinner-ring"></div>
                  </div>
                  <span className="virtual-table-loading-text">加载中...</span>
                </div>
              )}
            </div>
          </div>
        );
      }

      // 空状态
      if (data.length === 0) {
        return (
          <div
            ref={listContainerRef}
            className="virtual-table-content"
            onContextMenu={handleEmptyContextMenu}
            style={{ position: "relative", height: "100%" }}
          >
            <div className="virtual-table-empty">
              {emptyState || (
                <div className="virtual-table-empty-content">
                  <div className="virtual-table-empty-icon">
                    <svg
                      width="48"
                      height="48"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                    >
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                      <line x1="9" y1="9" x2="15" y2="15" />
                      <line x1="15" y1="9" x2="9" y2="15" />
                    </svg>
                  </div>
                  <span className="virtual-table-empty-text">暂无数据</span>
                </div>
              )}
            </div>
          </div>
        );
      }

      // 正常数据状态
      return (
        <div
          ref={listContainerRef}
          className="virtual-table-content"
          onContextMenu={(e) => {
            // 检查是否点击在空白区域（不是行上）
            if (!e.target.closest(".virtual-table-row")) {
              handleEmptyContextMenu(e);
            }
          }}
        >
          {(() => {
            // 使用实际高度或fallback高度
            const listHeight = containerHeight > 0 ? containerHeight : 400;

            return (
              <List
                ref={listRef}
                height={listHeight}
                itemCount={data.length}
                itemSize={itemHeight}
                overscanCount={overscan}
                itemData={data}
                className="virtual-table-list"
              >
                {Row}
              </List>
            );
          })()}
        </div>
      );
    };

    return (
      <div
        ref={containerRef}
        className={`virtual-data-table ${className} ${
          isDragging ? "dragging" : ""
        } ${isResizing ? "resizing" : ""}`}
      >
        {TableHeader}
        {renderContent()}

        {/* 右键菜单 */}
        <ContextMenu
          visible={contextMenu.visible}
          x={contextMenu.x}
          y={contextMenu.y}
          items={contextMenu.items}
          onClose={hideContextMenu}
        />
      </div>
    );
  }
);

VirtualDataTable.displayName = "VirtualDataTable";

export default VirtualDataTable;
