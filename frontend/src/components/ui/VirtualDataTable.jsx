import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from "react";
import { FixedSizeList as List } from "react-window";
import ContextMenu from "./ContextMenu";
import { useContextMenu } from "../../hooks/useContextMenu";
import { toast } from "./Toast";
import "./VirtualDataTable.css";

/**
 * 虚拟滚动数据表格组件 - 表头固定，内容虚拟滚动
 */
const VirtualDataTable = forwardRef(
  (
    {
      data = [],
      columns = [],
      height = 400,
      itemHeight = 48,
      overscan = 5,
      loading = false,
      selectable = false,
      selectedItems = [],
      onSelectItem,
      onRowClick,
      onRowContextMenu, // 行右键回调
      contextMenuItems, // 右键菜单项配置 (可以是数组或函数)
      rowKey = "id",
      rowClassName, // 新增：行样式类名函数
      className = "",
      loadingState,
      emptyState,
      ...props
    },
    ref
  ) => {
    const listRef = useRef();
    const containerRef = useRef();
    const scrollContainerRef = useRef();
    const headerRef = useRef();

    // 拖拽选择状态
    const [isDragging, setIsDragging] = useState(false);
    const [dragStartIndex, setDragStartIndex] = useState(-1);
    const [dragEndIndex, setDragEndIndex] = useState(-1);
    const [dragStartY, setDragStartY] = useState(0);
    const [dragSelection, setDragSelection] = useState(new Set());
    const [hoveredRowIndex, setHoveredRowIndex] = useState(null);

    // 列宽调整状态
    const [isResizing, setIsResizing] = useState(false);
    const [resizingColumnIndex, setResizingColumnIndex] = useState(-1);
    const [resizeStartX, setResizeStartX] = useState(0);
    const [resizeStartWidth, setResizeStartWidth] = useState(0);
    const [columnWidthsState, setColumnWidthsState] = useState({});

    // 容器尺寸状态
    const [containerWidth, setContainerWidth] = useState(0);
    const [containerHeight, setContainerHeight] = useState(height || 400);

    // 右键菜单
    const { showContextMenu, hideContextMenu, contextMenu } = useContextMenu();

    // 对外暴露的方法
    useImperativeHandle(ref, () => ({
      scrollToItem: (index) => {
        listRef.current?.scrollToItem(index);
      },
      scrollToTop: () => {
        listRef.current?.scrollTo(0);
      },
      getVisibleRange: () => {
        const listInstance = listRef.current;
        if (!listInstance) return { start: 0, end: 0 };

        const scrollTop = listInstance._outerRef?.scrollTop || 0;
        const visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight));
        const visibleEnd = Math.min(
          visibleStart + Math.ceil(containerHeight / itemHeight),
          data.length - 1
        );

        return { start: visibleStart, end: visibleEnd };
      },
    }));

    // 监听容器尺寸变化
    useEffect(() => {
      if (!containerRef.current) return;

      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          setContainerWidth(width);
          // 确保高度至少为默认值
          setContainerHeight(Math.max(height, 400));
        }
      });

      resizeObserver.observe(containerRef.current);

      // 立即获取一次尺寸
      const rect = containerRef.current.getBoundingClientRect();
      setContainerWidth(rect.width);
      setContainerHeight(Math.max(rect.height, 400));

      return () => resizeObserver.disconnect();
    }, [height]);

    // 处理空右键菜单
    const handleEmptyContextMenu = useCallback(
      (e) => {
        e.preventDefault();

        // 构建空白区域的右键菜单
        let menuItems = [];

        // 如果有选中项，添加复制选中项数量的选项
        if (selectable && selectedItems.length > 0) {
          menuItems.push({
            label: `复制选中项数量 (${selectedItems.length})`,
            icon: "📊",
            onClick: () => {
              navigator.clipboard
                .writeText(selectedItems.length.toString())
                .then(() => {
                  console.log(
                    "已复制选中项数量到剪贴板:",
                    selectedItems.length
                  );
                  toast.success(`已复制选中项数量: ${selectedItems.length}`);
                })
                .catch((err) => {
                  console.error("复制失败:", err);
                  toast.error("复制失败，请重试");
                });
            },
          });

          // 添加清空选择选项
          menuItems.push({
            label: "清空选择",
            icon: "❌",
            onClick: () => {
              if (onSelectItem) {
                onSelectItem([], false);
              }
            },
          });

          if (contextMenuItems) {
            menuItems.push({ type: "divider" });
          }
        }

        // 添加原有的空白区域右键菜单项
        if (contextMenuItems) {
          const originalMenuItems =
            typeof contextMenuItems === "function"
              ? contextMenuItems(null, null)
              : contextMenuItems;

          if (originalMenuItems && originalMenuItems.length > 0) {
            const finalMenuItems = originalMenuItems.map((menuItem) => ({
              ...menuItem,
              onClick: () => menuItem.onClick?.(null, null),
            }));
            menuItems = [...menuItems, ...finalMenuItems];
          }
        }

        if (menuItems.length > 0) {
          showContextMenu(e, menuItems, { item: null, cellData: null });
        }
      },
      [
        selectable,
        onSelectItem,
        selectedItems,
        contextMenuItems,
        showContextMenu,
      ]
    );

    // 计算列宽
    const columnWidths = useMemo(() => {
      if (containerWidth === 0) return columns.map(() => 120);

      const scrollbarWidth = 12; // 匹配CSS中的滚动条宽度
      const availableWidth = containerWidth - scrollbarWidth;

      return columns.map((col, index) => {
        if (columnWidthsState[index] != null) {
          return columnWidthsState[index];
        }
        if (col.width) {
          return parseInt(col.width);
        }

        const fixedColumns = columns.filter(
          (c, i) => c.width || columnWidthsState[i] != null
        );
        const totalFixedWidth = fixedColumns.reduce((sum, c, i) => {
          return (
            sum +
            (columnWidthsState[i] != null
              ? columnWidthsState[i]
              : parseInt(c.width || 0))
          );
        }, 0);

        const flexColumns = columns.filter(
          (c, i) => !c.width && columnWidthsState[i] == null
        );
        const remainingWidth = Math.max(0, availableWidth - totalFixedWidth);
        const flexWidth =
          flexColumns.length > 0
            ? Math.floor(remainingWidth / flexColumns.length)
            : 120;

        return Math.max(100, flexWidth);
      });
    }, [columns, containerWidth, columnWidthsState]);

    // 开始列宽调整
    const handleColumnResizeStart = useCallback(
      (e, columnIndex) => {
        if (columnIndex < columns.length - 1) {
          const rect = e.currentTarget.getBoundingClientRect();
          const clickX = e.clientX - rect.left;
          const isRightEdge = clickX >= rect.width - 4;

          if (isRightEdge) {
            e.preventDefault();
            e.stopPropagation();

            setIsResizing(true);
            setResizingColumnIndex(columnIndex);
            setResizeStartX(e.clientX);
            setResizeStartWidth(columnWidths[columnIndex]);
          }
        }
      },
      [columnWidths, columns.length]
    );

    // 列宽调整的全局事件监听
    useEffect(() => {
      const handleMouseMove = (e) => {
        if (!isResizing || resizingColumnIndex < 0) return;
        const deltaX = e.clientX - resizeStartX;
        const newWidth = Math.max(80, resizeStartWidth + deltaX);
        setColumnWidthsState((prev) => ({
          ...prev,
          [resizingColumnIndex]: newWidth,
        }));
      };

      const handleMouseUp = () => {
        if (isResizing) {
          setIsResizing(false);
          setResizingColumnIndex(-1);
          setResizeStartX(0);
          setResizeStartWidth(0);
        }
      };

      if (isResizing) {
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      }

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }, [isResizing, resizingColumnIndex, resizeStartX, resizeStartWidth]);

    // 拖拽选择相关函数
    const getRowIndexFromY = useCallback(
      (clientY) => {
        if (!scrollContainerRef.current) return -1;

        const containerRect =
          scrollContainerRef.current.getBoundingClientRect();
        const relativeY = clientY - containerRect.top - itemHeight; // 减去表头高度

        if (relativeY < 0) return -1;

        const rowIndex = Math.floor(relativeY / itemHeight);
        return Math.max(0, Math.min(rowIndex, data.length - 1));
      },
      [data.length, itemHeight]
    );

    const handleDragStart = useCallback(
      (e, rowIndex) => {
        if (!selectable || !onSelectItem) return;

        e.preventDefault();
        e.stopPropagation();

        console.log("开始拖拽选择:", {
          rowIndex,
          itemId: data[rowIndex]?.[rowKey],
        });

        setIsDragging(true);
        setDragStartIndex(rowIndex);
        setDragEndIndex(rowIndex);
        setDragStartY(e.clientY);

        // 初始化拖拽选择
        const itemId = data[rowIndex]?.[rowKey];
        if (itemId) {
          setDragSelection(new Set([itemId]));
        }
      },
      [selectable, onSelectItem, data, rowKey]
    );

    const handleDragMove = useCallback(
      (e) => {
        if (!isDragging || !selectable || !onSelectItem) return;

        const currentRowIndex = getRowIndexFromY(e.clientY);
        if (currentRowIndex === -1) return;

        setDragEndIndex(currentRowIndex);

        // 计算拖拽范围
        const start = Math.min(dragStartIndex, currentRowIndex);
        const end = Math.max(dragStartIndex, currentRowIndex);

        // 收集拖拽范围内的所有项目ID
        const dragItemIds = [];
        for (let i = start; i <= end; i++) {
          const itemId = data[i]?.[rowKey];
          if (itemId) {
            dragItemIds.push(itemId);
          }
        }

        setDragSelection(new Set(dragItemIds));

        // 自动滚动处理
        if (scrollContainerRef.current) {
          const containerRect =
            scrollContainerRef.current.getBoundingClientRect();
          const scrollThreshold = 50; // 距离边缘50px时开始滚动
          const scrollSpeed = 5;

          if (e.clientY < containerRect.top + scrollThreshold) {
            // 向上滚动
            scrollContainerRef.current.scrollTop -= scrollSpeed;
          } else if (e.clientY > containerRect.bottom - scrollThreshold) {
            // 向下滚动
            scrollContainerRef.current.scrollTop += scrollSpeed;
          }
        }
      },
      [
        isDragging,
        selectable,
        onSelectItem,
        dragStartIndex,
        getRowIndexFromY,
        data,
        rowKey,
      ]
    );

    const handleDragEnd = useCallback(() => {
      if (!isDragging || !selectable || !onSelectItem) return;

      console.log("结束拖拽选择:", {
        dragSelectionSize: dragSelection.size,
        dragItemIds: Array.from(dragSelection),
        isBatchSelect: onSelectItem.isBatchSelect,
      });

      // 应用拖拽选择结果
      if (dragSelection.size > 0) {
        const isCtrlKey = false; // 拖拽选择默认替换选择
        const dragItemIds = Array.from(dragSelection);

        // 检查onSelectItem是否支持批量选择
        if (onSelectItem.isBatchSelect) {
          onSelectItem(dragItemIds, isCtrlKey);
        } else {
          // 如果不支持批量选择，只选择最后一个
          const lastItemId = dragItemIds[dragItemIds.length - 1];
          onSelectItem(lastItemId, isCtrlKey);
        }
      }

      // 重置拖拽状态
      setIsDragging(false);
      setDragStartIndex(-1);
      setDragEndIndex(-1);
      setDragStartY(0);
      setDragSelection(new Set());
    }, [isDragging, selectable, onSelectItem, dragSelection]);

    // 拖拽选择的全局事件监听
    useEffect(() => {
      const handleGlobalMouseMove = (e) => {
        handleDragMove(e);
      };

      const handleGlobalMouseUp = () => {
        handleDragEnd();
      };

      if (isDragging) {
        document.addEventListener("mousemove", handleGlobalMouseMove);
        document.addEventListener("mouseup", handleGlobalMouseUp);
      }

      return () => {
        document.removeEventListener("mousemove", handleGlobalMouseMove);
        document.removeEventListener("mouseup", handleGlobalMouseUp);
      };
    }, [isDragging, handleDragMove, handleDragEnd]);

    // 数据行组件
    const Row = ({ index, style }) => {
      const dataItem = data[index];
      if (!dataItem) return null;

      const isSelected = selectedItems.includes(dataItem[rowKey]);
      const isInDragSelection = dragSelection.has(dataItem[rowKey]);
      const isEven = index % 2 === 0;
      const isInDragRange =
        isDragging &&
        index >= Math.min(dragStartIndex, dragEndIndex) &&
        index <= Math.max(dragStartIndex, dragEndIndex);

      const handleRowClick = useCallback(
        (e) => {
          // 如果正在拖拽，不处理点击事件
          if (isDragging) {
            return;
          }

          if (
            e.target.closest("button") ||
            e.target.closest("a") ||
            e.target.closest('[role="link"]') ||
            e.target.closest("[data-clickable]") ||
            e.target.closest(".cursor-pointer") ||
            e.target.closest("[onclick]")
          ) {
            return;
          }

          if (selectable && onSelectItem) {
            const isCtrlKey = e.ctrlKey || e.metaKey;
            const itemId = dataItem[rowKey];
            onSelectItem(itemId, isCtrlKey);
          }

          onRowClick?.(dataItem);
        },
        [dataItem, selectable, onSelectItem, onRowClick, rowKey, isDragging]
      );

      const handleRowContextMenu = useCallback(
        (e) => {
          e.preventDefault();
          e.stopPropagation();

          // 获取当前鼠标位置下的单元格数据
          const targetCell = e.target.closest(".virtual-table-cell");
          let cellData = null;
          let cellValue = "";

          if (targetCell) {
            // 找到当前单元格对应的列
            const cellIndex = Array.from(
              targetCell.parentElement.children
            ).indexOf(targetCell);
            const column = columns[cellIndex];

            if (column) {
              // 获取单元格的值
              if (column.render) {
                // 如果有自定义渲染，尝试获取原始值
                cellValue = dataItem[column.dataIndex || column.key] || "";
              } else {
                cellValue = dataItem[column.dataIndex || column.key] || "";
              }

              cellData = {
                column,
                value: cellValue,
                cellIndex,
              };
            }
          }

          // 构建右键菜单项
          let menuItems = [];

          // 如果有单元格数据，添加复制选项
          if (cellData && cellValue) {
            menuItems.push({
              label: `复制"${cellData.column.title}"`,
              icon: "📋",
              onClick: () => {
                navigator.clipboard
                  .writeText(cellValue)
                  .then(() => {
                    console.log("已复制到剪贴板:", cellValue);
                    toast.success(
                      `已复制"${cellData.column.title}": ${cellValue}`
                    );
                  })
                  .catch((err) => {
                    console.error("复制失败:", err);
                    toast.error("复制失败，请重试");
                  });
              },
            });

            // 添加分隔线
            if (contextMenuItems) {
              menuItems.push({ type: "divider" });
            }
          }

          // 添加原有的右键菜单项
          if (contextMenuItems) {
            const originalMenuItems =
              typeof contextMenuItems === "function"
                ? contextMenuItems(dataItem, cellData)
                : contextMenuItems;

            if (originalMenuItems && originalMenuItems.length > 0) {
              menuItems = [...menuItems, ...originalMenuItems];
            }
          }

          if (menuItems.length > 0) {
            showContextMenu(e, menuItems, { item: dataItem, cellData });
          }
        },
        [dataItem, contextMenuItems, showContextMenu, columns]
      );

      const handleRowMouseDown = useCallback(
        (e) => {
          // 只有在可选择模式下才启用拖拽选择
          if (!selectable || !onSelectItem) {
            return;
          }

          // 只响应鼠标左键，忽略右键和中键
          if (e.button !== 0) {
            return;
          }

          // 如果点击的是可交互元素，不开始拖拽
          if (
            e.target.closest("button") ||
            e.target.closest("a") ||
            e.target.closest('[role="link"]') ||
            e.target.closest("[data-clickable]") ||
            e.target.closest(".cursor-pointer") ||
            e.target.closest("[onclick]")
          ) {
            return;
          }

          // 开始拖拽选择
          handleDragStart(e, index);
        },
        [selectable, onSelectItem, handleDragStart, index]
      );

      return (
        <div
          style={{
            ...style,
            minWidth: "fit-content",
          }}
          className={`virtual-table-row ${isSelected ? "selected" : ""} ${
            isEven ? "even" : "odd"
          } ${onRowClick ? "clickable" : ""} ${
            isInDragRange ? "drag-range" : ""
          } ${isInDragSelection ? "drag-selected" : ""}`}
          data-row-index={index}
          onClick={handleRowClick}
          onContextMenu={handleRowContextMenu}
          onMouseDown={handleRowMouseDown}
        >
          {columns.map((column, colIndex) => (
            <div
              key={column.key || colIndex}
              className="virtual-table-cell"
              style={{
                width: columnWidths[colIndex],
                minWidth: columnWidths[colIndex],
                maxWidth: columnWidths[colIndex],
                height: "100%",
                display: "flex",
                alignItems: "center",
                padding: "0 12px",
                fontSize: "13px",
                lineHeight: "1.4",
                color: "var(--text-primary)",
                boxSizing: "border-box",
                borderRight: "1px solid var(--border-light, rgba(0,0,0,0.04))",
              }}
              title={`右键复制"${column.title}"`}
            >
              {column.render ? (
                <div className="virtual-table-cell-content">
                  {column.dataIndex
                    ? column.render(dataItem[column.dataIndex], dataItem, index)
                    : column.render(dataItem, index)}
                </div>
              ) : (
                <span
                  className="virtual-table-truncate"
                  title={dataItem[column.dataIndex || column.key]}
                >
                  {dataItem[column.dataIndex || column.key]}
                </span>
              )}
            </div>
          ))}
        </div>
      );
    };

    // 表头组件
    const TableHeader = React.memo(() => (
      <div
        ref={headerRef}
        className="virtual-table-header"
        style={{
          minWidth: "fit-content",
          backgroundColor: "var(--table-header-bg, #fafbfc)",
          borderBottom: "2px solid var(--border-color)",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
          backdropFilter: "blur(8px)",
          height: itemHeight,
          display: "flex",
          alignItems: "center",
          position: "sticky",
          top: 0,
          zIndex: 10,
        }}
      >
        {columns.map((column, colIndex) => (
          <div
            key={column.key || colIndex}
            className={`virtual-table-header-cell ${
              resizingColumnIndex === colIndex ? "resizing-column" : ""
            }`}
            style={{
              width: columnWidths[colIndex],
              minWidth: columnWidths[colIndex],
              maxWidth: columnWidths[colIndex],
              height: "100%",
              display: "flex",
              alignItems: "center",
              padding: "0 12px",
              fontSize: "12px",
              fontWeight: 600,
              color: "var(--text-secondary)",
              textTransform: "uppercase",
              letterSpacing: "0.05em",
              boxSizing: "border-box",
              borderRight: "1px solid var(--border-light, rgba(0,0,0,0.06))",
            }}
            onMouseDown={(e) => handleColumnResizeStart(e, colIndex)}
          >
            <span className="virtual-table-truncate">{column.title}</span>
          </div>
        ))}
      </div>
    ));

    // 渲染内容
    const renderContent = () => {
      // 总是显示表头和滚动容器
      return (
        <div
          ref={scrollContainerRef}
          className="virtual-table-scroll-container"
          style={{
            flex: 1,
            overflow: "auto",
            overflowY: "hidden",
            minHeight: 0,
          }}
          onContextMenu={handleEmptyContextMenu}
        >
          <div
            className="virtual-table-content"
            style={{
              minWidth: "fit-content",
              height: "100%",
              position: "relative",
            }}
          >
            {/* 固定表头 */}
            <TableHeader />

            {/* 内容区域 */}
            <div
              className="virtual-table-body"
              style={{
                height: Math.max(containerHeight - itemHeight, 100), // 减去表头高度，最小100px
                overflow: "hidden",
                position: "relative",
              }}
            >
              {loading ? (
                <div className="virtual-table-loading">
                  {loadingState || (
                    <div className="virtual-table-loading-content">
                      <div className="virtual-table-spinner">
                        <div className="virtual-table-spinner-ring"></div>
                        <div className="virtual-table-spinner-ring"></div>
                        <div className="virtual-table-spinner-ring"></div>
                      </div>
                      <span className="virtual-table-loading-text">
                        加载中...
                      </span>
                    </div>
                  )}
                </div>
              ) : data.length === 0 ? (
                <div className="virtual-table-empty">
                  {emptyState || (
                    <div className="virtual-table-empty-content">
                      <div className="virtual-table-empty-icon">
                        <svg
                          width="48"
                          height="48"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                        >
                          <rect
                            x="3"
                            y="3"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          />
                          <line x1="9" y1="9" x2="15" y2="15" />
                          <line x1="15" y1="9" x2="9" y2="15" />
                        </svg>
                      </div>
                      <span className="virtual-table-empty-text">暂无数据</span>
                    </div>
                  )}
                </div>
              ) : (
                <List
                  ref={listRef}
                  height={Math.max(containerHeight - itemHeight, 100)}
                  itemCount={data.length}
                  itemSize={itemHeight}
                  overscanCount={overscan}
                  itemData={data}
                  className="virtual-table-list"
                  style={{ overflow: "auto" }}
                >
                  {Row}
                </List>
              )}
            </div>
          </div>
        </div>
      );
    };

    return (
      <div
        ref={containerRef}
        className={`virtual-data-table ${className} ${
          isDragging ? "dragging" : ""
        } ${isResizing ? "resizing" : ""}`}
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          position: "relative",
        }}
      >
        {renderContent()}

        {/* 右键菜单 */}
        <ContextMenu
          visible={contextMenu.visible}
          x={contextMenu.x}
          y={contextMenu.y}
          items={contextMenu.items}
          onClose={hideContextMenu}
        />
      </div>
    );
  }
);

VirtualDataTable.displayName = "VirtualDataTable";

export default VirtualDataTable;
