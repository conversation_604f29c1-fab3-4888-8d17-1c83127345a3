import React, { useEffect, useRef } from 'react';
import { X, Search } from 'lucide-react';

/**
 * 浮动搜索框组件
 * 类似浏览器Ctrl+F的搜索体验
 */
const FloatingSearchBox = ({
  isOpen,
  onClose,
  searchTerm = '',
  onSearch,
  placeholder = '搜索...',
  matchCount = 0,
  totalCount = 0
}) => {
  const inputRef = useRef(null);

  // 当搜索框打开时自动聚焦
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isOpen]);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        onSearch(''); // 清空搜索词
        onClose();    // 关闭搜索框
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose, onSearch]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-3 min-w-[320px]"
      style={{
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05)'
      }}
    >
      <div className="flex items-center gap-2">
        {/* 搜索图标 */}
        <Search size={16} className="text-gray-400 flex-shrink-0" />
        
        {/* 搜索输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={(e) => onSearch(e.target.value)}
          placeholder={placeholder}
          className="flex-1 text-sm border-none outline-none bg-transparent text-gray-900 placeholder-gray-400"
          autoComplete="off"
          spellCheck="false"
        />
        
        {/* 搜索结果统计 */}
        {searchTerm && (
          <div className="text-xs text-gray-500 whitespace-nowrap">
            {matchCount} / {totalCount}
          </div>
        )}
        
        {/* 关闭按钮 */}
        <button
          onClick={() => {
            onSearch(''); // 清空搜索词
            onClose();    // 关闭搜索框
          }}
          className="text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-100 rounded"
          title="关闭搜索 (Esc)"
        >
          <X size={14} />
        </button>
      </div>
      
      {/* 搜索提示 */}
      {searchTerm && matchCount === 0 && (
        <div className="text-xs text-gray-500 mt-2 border-t border-gray-100 pt-2">
          未找到匹配项
        </div>
      )}
    </div>
  );
};

export default FloatingSearchBox; 