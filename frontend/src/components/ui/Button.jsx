import React from 'react';

const Button = ({ 
  children, 
  onClick, 
  disabled = false, 
  variant = 'default', 
  size = 'default',
  icon = null,
  iconPosition = 'left',
  loading = false,
  className = '',
  type = 'button',
  style = {},
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-wechat focus:outline-none disabled:cursor-not-allowed';
  
  const getVariantStyles = (variant) => {
    const styles = {
      default: {
        backgroundColor: 'var(--panel-bg)',
        color: 'var(--text-primary)',
        border: '1px solid var(--border-color)',
        borderRadius: '4px'
      },
      primary: {
        backgroundColor: 'var(--primary-color)',
        color: 'white',
        borderRadius: '4px',
        border: 'none'
      },
      danger: {
        backgroundColor: '#FF4747',
        color: 'white',
        borderRadius: '4px',
        border: 'none'
      },
      secondary: {
        backgroundColor: 'var(--hover-bg)',
        color: 'var(--text-primary)',
        border: '1px solid var(--border-color)',
        borderRadius: '4px'
      },
      success: {
        backgroundColor: 'var(--primary-color)',
        color: 'white',
        borderRadius: '4px',
        border: 'none'
      },
      outline: {
        backgroundColor: 'transparent',
        color: 'var(--text-primary)',
        border: '1px solid var(--border-color)',
        borderRadius: '4px'
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'var(--text-primary)',
        border: 'none',
        borderRadius: '4px'
      }
    };
    return styles[variant] || styles.default;
  };
  
  const sizes = {
    small: 'px-3 py-1.5 text-sm h-8',
    default: 'px-4 py-2 text-sm h-9',
    large: 'px-6 py-3 text-base h-11',
  };
  
  const classes = `${baseClasses} ${sizes[size]} ${className}`;
  const variantStyle = getVariantStyles(variant);
  const combinedStyle = { ...variantStyle, ...style };
  
  // 禁用状态样式
  if (disabled) {
    combinedStyle.opacity = '0.5';
    combinedStyle.backgroundColor = 'var(--hover-bg)';
    combinedStyle.color = 'var(--text-disabled)';
  }
  
  const handleMouseEnter = (e) => {
    if (disabled || loading) return;
    
    switch (variant) {
      case 'primary':
        e.currentTarget.style.backgroundColor = 'var(--primary-hover)';
        break;
      case 'danger':
        e.currentTarget.style.backgroundColor = '#E63946';
        break;
      case 'default':
      case 'secondary':
      case 'outline':
        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
        break;
      case 'ghost':
        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
        break;
    }
  };
  
  const handleMouseLeave = (e) => {
    if (disabled || loading) return;
    
    switch (variant) {
      case 'primary':
        e.currentTarget.style.backgroundColor = 'var(--primary-color)';
        break;
      case 'danger':
        e.currentTarget.style.backgroundColor = '#FF4747';
        break;
      case 'default':
        e.currentTarget.style.backgroundColor = 'var(--panel-bg)';
        break;
      case 'secondary':
        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
        break;
      case 'outline':
      case 'ghost':
        e.currentTarget.style.backgroundColor = 'transparent';
        break;
    }
  };
  
  const handleMouseDown = (e) => {
    if (disabled || loading) return;
    
    switch (variant) {
      case 'primary':
        e.currentTarget.style.backgroundColor = 'var(--primary-active)';
        e.currentTarget.style.boxShadow = 'var(--shadow-inset)';
        break;
      case 'danger':
        e.currentTarget.style.backgroundColor = '#DC2F02';
        e.currentTarget.style.boxShadow = 'var(--shadow-inset)';
        break;
      case 'secondary':
        e.currentTarget.style.backgroundColor = 'var(--pressed-bg)';
        e.currentTarget.style.boxShadow = 'var(--shadow-inset)';
        break;
      default:
        e.currentTarget.style.backgroundColor = 'var(--pressed-bg)';
        e.currentTarget.style.boxShadow = 'var(--shadow-inset)';
        break;
    }
  };
  
  const handleMouseUp = (e) => {
    if (disabled || loading) return;
    e.currentTarget.style.boxShadow = 'none';
    handleMouseEnter(e); // 恢复到hover状态
  };
  
  const renderContent = () => {
    if (loading) {
      return (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2" />
          {children}
        </>
      );
    }

    if (icon && iconPosition === 'left') {
      return (
        <>
          <span className={children ? 'mr-2' : ''}>{icon}</span>
          {children}
        </>
      );
    }

    if (icon && iconPosition === 'right') {
      return (
        <>
          {children}
          <span className={children ? 'ml-2' : ''}>{icon}</span>
        </>
      );
    }

    return children;
  };
  
  return (
    <button
      type={type}
      className={classes}
      style={combinedStyle}
      onClick={onClick}
      disabled={disabled || loading}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      {...props}
    >
      {renderContent()}
    </button>
  );
};

export default Button; 