import React, { useState, useEffect, useRef } from 'react';
import { X, ChevronDown, ChevronUp } from 'lucide-react';

/**
 * 任务进度条组件 - 类似GoLand的进度条
 * 特性：
 * - 支持子任务显示
 * - 进度立即跳转到当前值，然后缓慢增长到下一个值
 * - 显示当前步骤描述和预估耗时
 * - 支持展开/收起详细信息
 * - 位于客户端最下方
 */
const TaskProgressBar = ({ 
  taskData, 
  onClose, 
  className = '',
  ...props 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef(null);
  const startTimeRef = useRef(0);
  const startProgressRef = useRef(0);

  // 当任务数据更新时，处理进度动画
  useEffect(() => {
    if (!taskData) return;

    const currentProgress = taskData.step_progress || 0;
    const nextProgress = taskData.next_step_progress || currentProgress;
    const stepTime = taskData.step_time || 3000; // 默认3秒

    // 立即跳转到当前进度
    setDisplayProgress(currentProgress);
    
    // 如果有下一步进度且不同于当前进度，开始缓慢增长动画
    if (nextProgress > currentProgress && stepTime > 0) {
      setIsAnimating(true);
      startTimeRef.current = Date.now();
      startProgressRef.current = currentProgress;

      const animateProgress = () => {
        const elapsed = Date.now() - startTimeRef.current;
        const progress = Math.min(elapsed / stepTime, 1);
        
        // 使用缓动函数让动画更平滑
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const newProgress = startProgressRef.current + 
          (nextProgress - startProgressRef.current) * easeProgress;
        
        setDisplayProgress(newProgress);
        
        if (progress < 1) {
          animationRef.current = requestAnimationFrame(animateProgress);
        } else {
          setIsAnimating(false);
          setDisplayProgress(nextProgress);
        }
      };

      animationRef.current = requestAnimationFrame(animateProgress);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [taskData?.step_progress, taskData?.next_step_progress, taskData?.step_time]);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!taskData) return null;

  const {
    task_name,
    task_description,
    step_description,
    current_step,
    total_steps,
    status,
    error_message
  } = taskData;

  // 根据状态确定颜色
  const getStatusColor = () => {
    switch (status) {
      case 'start':
        return 'var(--primary-color)';
      case 'progress':
        return 'var(--primary-color)';
      case 'end':
        return 'var(--success-color)';
      case 'error':
        return 'var(--error-color)';
      default:
        return 'var(--primary-color)';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'start':
        return '开始';
      case 'progress':
        return '进行中';
      case 'end':
        return '完成';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  };

  const formatTime = (ms) => {
    if (!ms) return '';
    const seconds = Math.ceil(ms / 1000);
    return `预计 ${seconds}s`;
  };

  return (
    <div
      className={`task-progress-bar ${className}`}
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'var(--panel-bg)',
        borderTop: '1px solid var(--border-color)',
        boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.08)',
        zIndex: 1000,
        fontFamily: 'Microsoft YaHei UI, Segoe UI, sans-serif',
      }}
      {...props}
    >
      {/* 主进度条区域 */}
      <div
        className="flex items-center px-4 py-2 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          minHeight: '40px',
          backgroundColor: 'var(--panel-bg)',
        }}
      >
        {/* 展开/收起按钮 */}
        <button
          className="flex items-center justify-center w-5 h-5 mr-2 transition-colors"
          style={{
            color: 'var(--text-secondary)',
            backgroundColor: 'transparent',
            border: 'none',
            borderRadius: '2px',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          {isExpanded ? <ChevronDown size={14} /> : <ChevronUp size={14} />}
        </button>

        {/* 进度条区域 */}
        <div className="flex-1 flex items-center space-x-3">
          {/* 进度条 */}
          <div
            className="flex-1 max-w-md"
            style={{
              height: '6px',
              backgroundColor: 'var(--bg-tertiary)',
              borderRadius: '3px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                width: `${Math.min(displayProgress, 100)}%`,
                height: '100%',
                backgroundColor: getStatusColor(),
                borderRadius: '3px',
                transition: isAnimating ? 'none' : 'width 0.3s ease',
              }}
            />
          </div>

          {/* 进度文本 */}
          <div
            className="text-sm font-medium min-w-0"
            style={{ color: 'var(--text-primary)' }}
          >
            {Math.round(displayProgress)}%
          </div>

          {/* 任务信息 */}
          <div className="flex-1 min-w-0">
            <div
              className="text-sm font-medium truncate"
              style={{ color: 'var(--text-primary)' }}
            >
              {task_name}
            </div>
            {step_description && (
              <div
                className="text-xs truncate"
                style={{ color: 'var(--text-secondary)' }}
              >
                {step_description}
              </div>
            )}
          </div>

          {/* 步骤信息 */}
          <div
            className="text-xs whitespace-nowrap"
            style={{ color: 'var(--text-secondary)' }}
          >
            {(() => {
              // 根据当前步骤进度计算已完成的步骤数
              const completedSteps = displayProgress >= 100 || status === 'end' 
                ? current_step 
                : Math.max(0, current_step - 1);
              return `${completedSteps}/${total_steps}`;
            })()}
          </div>

          {/* 预估时间 */}
          {taskData.step_time && (
            <div
              className="text-xs whitespace-nowrap"
              style={{ color: 'var(--text-secondary)' }}
            >
              {formatTime(taskData.step_time)}
            </div>
          )}

          {/* 状态 */}
          <div
            className="text-xs px-2 py-1 rounded"
            style={{
              color: getStatusColor(),
              backgroundColor: getStatusColor() + '20',
            }}
          >
            {getStatusText()}
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          className="flex items-center justify-center w-6 h-6 ml-3 transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            onClose?.();
          }}
          style={{
            color: 'var(--text-secondary)',
            backgroundColor: 'transparent',
            border: 'none',
            borderRadius: '2px',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
            e.currentTarget.style.color = 'var(--text-primary)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = 'var(--text-secondary)';
          }}
        >
          <X size={14} />
        </button>
      </div>

      {/* 展开的详细信息 */}
      {isExpanded && (
        <div
          className="px-4 py-3 border-t"
          style={{
            backgroundColor: 'var(--bg-secondary)',
            borderTopColor: 'var(--border-color)',
          }}
        >
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div
                className="font-medium mb-1"
                style={{ color: 'var(--text-primary)' }}
              >
                任务描述
              </div>
              <div style={{ color: 'var(--text-secondary)' }}>
                {task_description || '无描述'}
              </div>
            </div>
            <div>
              <div
                className="font-medium mb-1"
                style={{ color: 'var(--text-primary)' }}
              >
                当前步骤
              </div>
              <div style={{ color: 'var(--text-secondary)' }}>
                {step_description || '无描述'}
              </div>
            </div>
            {error_message && (
              <div className="col-span-2">
                <div
                  className="font-medium mb-1"
                  style={{ color: 'var(--error-color)' }}
                >
                  错误信息
                </div>
                <div
                  className="text-sm p-2 rounded"
                  style={{
                    color: 'var(--error-color)',
                    backgroundColor: 'var(--error-color)' + '10',
                  }}
                >
                  {error_message}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskProgressBar; 