import React from 'react';

/**
 * 统计信息显示组件
 * @param {Object} stats - 统计数据对象
 * @param {boolean} inline - 是否内联显示
 * @param {string} className - 额外的样式类
 * @param {object} style - 内联样式
 */
function StatsDisplay({ stats = {}, inline = true, className = '', style = {} }) {
  const {
    total = 0,
    current = 0,
    selected = 0,
    label = '项目'
  } = stats;

  const baseStyle = {
    fontSize: '14px',
    color: 'var(--text-secondary)',
    ...style
  };

  const numberStyle = {
    fontWeight: '500',
    color: 'var(--text-primary)'
  };

  const selectedStyle = {
    color: 'var(--primary-color)',
    fontWeight: '500'
  };

  const baseClass = inline 
    ? 'flex items-center space-x-4'
    : 'space-y-2';

  return (
    <div 
      className={`${baseClass} transition-wechat ${className}`}
      style={baseStyle}
    >
      {/* 当前显示/总数 */}
      <div className="flex items-center space-x-1">
        <span>显示</span>
        <span style={numberStyle}>{current}</span>
        <span>/</span>
        <span style={numberStyle}>{total}</span>
        <span>{label}</span>
      </div>

      {/* 已选择数量 */}
      {selected > 0 && (
        <div className="flex items-center space-x-1">
          <div 
            className="w-2 h-2 rounded-full transition-wechat"
            style={{ backgroundColor: 'var(--primary-color)' }}
          ></div>
          <span style={selectedStyle}>
            已选择 {selected} 个
          </span>
        </div>
      )}
    </div>
  );
}

export default StatsDisplay; 