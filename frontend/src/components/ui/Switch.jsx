import React from "react";

const Switch = ({
  checked = false,
  onChange,
  disabled = false,
  size = "medium",
  className = "",
  ...props
}) => {
  const handleClick = () => {
    if (!disabled && onChange) {
      onChange(!checked);
    }
  };

  const sizeClasses = {
    small: "w-8 h-4",
    medium: "w-10 h-5",
    large: "w-12 h-6",
  };

  const thumbSizeClasses = {
    small: "w-3 h-3",
    medium: "w-4 h-4",
    large: "w-5 h-5",
  };

  const baseClasses = `
    relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out cursor-pointer
    ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
    ${sizeClasses[size]}
    ${className}
  `;

  const trackClasses = `
    ${checked ? "bg-blue-600" : "bg-gray-200"}
    ${disabled ? "opacity-50" : ""}
  `;

  const thumbClasses = `
    ${checked ? "translate-x-5 bg-white" : "translate-x-0.5 bg-white"}
    ${thumbSizeClasses[size]}
    rounded-full shadow-sm transition-transform duration-200 ease-in-out
  `;

  return (
    <button
      type="button"
      className={`${baseClasses} ${trackClasses}`}
      onClick={handleClick}
      disabled={disabled}
      role="switch"
      aria-checked={checked}
      {...props}
    >
      <span className={thumbClasses} />
    </button>
  );
};

export default Switch;
