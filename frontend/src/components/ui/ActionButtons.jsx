import React from 'react';
import { Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react';

/**
 * 操作按钮组件
 * @param {Array} actions - 操作配置数组
 * @param {string} size - 按钮大小 'sm' | 'md' | 'lg'
 * @param {string} className - 额外的样式类
 */
function ActionButtons({ actions = [], size = 'md', className = '' }) {
  const sizeClasses = {
    sm: 'p-1',
    md: 'p-1.5', 
    lg: 'p-2'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  const buttonSize = sizeClasses[size] || sizeClasses.md;
  const iconSize = iconSizes[size] || iconSizes.md;

  // 预设图标映射
  const iconMap = {
    edit: Edit,
    delete: Trash2,
    view: Eye,
    more: MoreHorizontal
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {actions.map((action, index) => {
        const Icon = action.icon || iconMap[action.type] || Edit;
        const colorClass = action.danger 
          ? 'text-red-600 hover:text-red-800 hover:bg-red-50'
          : action.color || 'text-slate-600 hover:text-slate-800 hover:bg-slate-50';

        return (
          <button
            key={action.key || index}
            onClick={action.onClick}
            disabled={action.disabled}
            title={action.title || action.label}
            className={`
              ${buttonSize} 
              ${colorClass}
              rounded transition-colors 
              disabled:opacity-50 disabled:cursor-not-allowed
              hover:scale-105 active:scale-95
            `}
          >
            <Icon size={iconSize} />
          </button>
        );
      })}
    </div>
  );
}

// 便捷的预设操作
ActionButtons.presets = {
  edit: (onClick, options = {}) => ({
    type: 'edit',
    onClick,
    title: '编辑',
    color: 'text-blue-600 hover:text-blue-800 hover:bg-blue-50',
    ...options
  }),
  
  delete: (onClick, options = {}) => ({
    type: 'delete',
    onClick,
    title: '删除',
    danger: true,
    ...options
  }),
  
  view: (onClick, options = {}) => ({
    type: 'view',
    onClick,
    title: '查看',
    color: 'text-gray-600 hover:text-gray-800 hover:bg-gray-50',
    ...options
  })
};

export default ActionButtons; 