/* =============================================================================
   虚拟数据表格样式 - 现代化设计
   ============================================================================= */

/* 基础容器样式 */
.virtual-data-table {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid var(--border-color, #e2e8f0);
  background: var(--panel-bg, #ffffff);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 表头样式 */
.virtual-table-header {
  background: linear-gradient(180deg, var(--table-header-bg, #f8fafc) 0%, var(--table-header-bg-end, #f1f5f9) 100%);
  border-bottom: 2px solid var(--border-color, #e2e8f0);
  position: relative;
}

.virtual-table-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--primary-color, #3b82f6) 50%, transparent 100%);
  opacity: 0.3;
}

.virtual-table-header-cell {
  position: relative;
  transition: background-color 0.2s ease;
  user-select: none;
}

/* 列宽拖拽调整器 */
.virtual-table-header-cell:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.virtual-table-header-cell:not(:last-child):hover::after {
  background-color: var(--primary-color, #3b82f6);
  opacity: 0.3;
}

/* 拖拽时的视觉反馈 */
.virtual-data-table.resizing {
  user-select: none;
  cursor: col-resize;
}

.virtual-data-table.resizing .virtual-table-header-cell {
  pointer-events: none;
}

.virtual-data-table.resizing .virtual-table-header-cell.resizing-column {
  pointer-events: auto;
}

.virtual-data-table.resizing .virtual-table-header-cell.resizing-column::after {
  background-color: var(--primary-color, #3b82f6);
  opacity: 0.8;
  width: 2px;
}



.virtual-table-header-cell:last-child {
  border-right: none !important;
}

/* 表格内容区域 */
.virtual-table-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.virtual-table-list {
  outline: none;
}

/* 行样式 */
.virtual-table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-light, rgba(226, 232, 240, 0.6));
  transition: background-color 0.15s ease;
  position: relative;
  background-color: var(--panel-bg, #ffffff);
  cursor: default;
  user-select: none;
}

/* 奇偶行样式 */
.virtual-table-row.even {
  background-color: var(--panel-bg, #ffffff);
}

.virtual-table-row.odd {
  background-color: var(--table-stripe-bg, #fafbfc);
}

/* 单元格也保持一致的光标样式 */
.virtual-table-cell {
  position: relative;
  cursor: default;
  user-select: none;
}

/* 行悬停样式 */
.virtual-table-row:hover {
  background-color: var(--table-hover-bg, #f8fafc) !important;
  transition: background-color 0.15s ease;
}

/* 行选中样式 */
.virtual-table-row.selected {
  background-color: var(--table-selected-bg, #dbeafe) !important;
  position: relative;
}

/* 选中行的悬停样式 */  
.virtual-table-row.selected:hover {
  background-color: var(--table-selected-hover-bg, #bfdbfe) !important;
}



/* 行焦点样式 */
.virtual-table-row:focus-within {
  outline: 2px solid var(--primary-color, #3b82f6);
  outline-offset: -2px;
  z-index: 1;
}


.virtual-table-cell:last-child {
  border-right: none !important;
}

.virtual-table-cell-content {
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

/* 数字列右对齐 */
.virtual-table-number-cell {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: 'tnum';
}

/* 状态徽章样式 */
.virtual-table-status-cell {
  display: flex;
  align-items: center;
}

/* 操作按钮列样式 */
.virtual-table-actions-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 加载状态样式 */
.virtual-table-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--panel-bg, #ffffff);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e2e8f0);
}

.virtual-table-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.virtual-table-spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.virtual-table-spinner-ring {
  position: absolute;
  width: 32px;
  height: 32px;
  margin: 4px;
  border: 3px solid transparent;
  border-top-color: var(--primary-color, #3b82f6);
  border-radius: 50%;
  animation: virtual-table-spin 1.2s linear infinite;
}

.virtual-table-spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.virtual-table-spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
}

.virtual-table-spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes virtual-table-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.virtual-table-loading-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
}

/* 空状态样式 */
.virtual-table-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--panel-bg, #ffffff);
  border-radius: 8px;
  border: 1px solid var(--border-color, #e2e8f0);
}

.virtual-table-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
}

.virtual-table-empty-icon {
  color: var(--text-secondary, #9ca3af);
  opacity: 0.6;
}

.virtual-table-empty-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
}

/* 滚动条优化 */
.virtual-data-table ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.virtual-data-table ::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-data-table ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb, #cbd5e1);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.virtual-data-table ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover, #94a3b8);
}

.virtual-data-table ::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox滚动条样式 */
.virtual-data-table {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb, #cbd5e1) transparent;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .virtual-table-cell,
  .virtual-table-header-cell {
    padding: 0 8px;
    font-size: 12px;
  }
}

/* 打印样式 */
@media print {
  .virtual-data-table {
    overflow: visible !important;
    height: auto !important;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .virtual-table-row {
    break-inside: avoid;
    border-bottom: 1px solid #000;
  }
  
  .virtual-table-header {
    background: #f0f0f0 !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .virtual-data-table {
    border: 2px solid;
  }
  
  .virtual-table-row {
    border-bottom: 1px solid;
  }
  
  .virtual-table-header-cell,
  .virtual-table-cell {
    border-right: 1px solid;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .virtual-table-row,
  .virtual-table-header-cell {
    transition: none;
  }
  
  .virtual-table-spinner-ring {
    animation: none;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .virtual-data-table {
    --table-header-bg: #1f2937;
    --table-header-bg-end: #111827;
    --table-stripe-bg: #1f2937;
    --table-hover-bg: #374151;
    --table-selected-bg: #1e3a8a;
    --table-selected-hover-bg: #1e40af;
    --table-drag-selecting-bg: #1e40af;
    --border-light: rgba(75, 85, 99, 0.4);
    --scrollbar-thumb: #4b5563;
    --scrollbar-thumb-hover: #6b7280;
  }
}

/* 确保选中状态的样式优先级高于奇偶行样式 */
.virtual-table-row.selected.even,
.virtual-table-row.selected.odd {
  background-color: var(--table-selected-bg, #dbeafe) !important;
}

.virtual-table-row.selected.even:hover,
.virtual-table-row.selected.odd:hover {
  background-color: var(--table-selected-hover-bg, #bfdbfe) !important;
}

/* 拖拽选择相关样式 */
.virtual-data-table.dragging {
  user-select: none;
}

.virtual-data-table.dragging .virtual-table-row {
  pointer-events: none;
}

.virtual-table-row.drag-selecting {
  background-color: var(--table-drag-selecting-bg, #dbeafe) !important;
}


/* 文本截断样式 */
.virtual-table-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: block;
}

/* 已删除行的特殊样式 */
.virtual-table-row.deleted-row {
  position: relative;
  background: repeating-linear-gradient(
    45deg,
    rgba(220, 38, 38, 0.05),
    rgba(220, 38, 38, 0.05) 10px,
    rgba(220, 38, 38, 0.1) 10px,
    rgba(220, 38, 38, 0.1) 20px
  ) !important;
  border: 1px solid #dc2626 !important;
  border-left: 3px solid #dc2626 !important;
  opacity: 0.8;
}

.virtual-table-row.deleted-row:hover {
  background: repeating-linear-gradient(
    45deg,
    rgba(220, 38, 38, 0.08),
    rgba(220, 38, 38, 0.08) 10px,
    rgba(220, 38, 38, 0.15) 10px,
    rgba(220, 38, 38, 0.15) 20px
  ) !important;
  transition: background 0.2s ease;
}

.virtual-table-row.deleted-row .virtual-table-cell {
  color: #dc2626 !important;
  font-weight: 600 !important;
  text-decoration: line-through !important;
  position: relative;
}

.virtual-table-row.deleted-row::before {
  content: '🗑️ 已删除';
  position: absolute;
  top: 2px;
  right: 8px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 0.5px;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.4);
  text-decoration: none !important;
}

.virtual-table-row.deleted-row .delete-time-cell {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(185, 28, 28, 0.15)) !important;
  border-radius: 6px;
  margin: 2px;
  padding: 4px 8px !important;
  font-weight: 700 !important;
  color: #991b1b !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(220, 38, 38, 0.3);
}

@keyframes deleted-row-pulse {
  0%, 100% {
    box-shadow: 
      0 0 0 1px rgba(220, 38, 38, 0.3),
      0 2px 4px rgba(220, 38, 38, 0.2);
  }
  50% {
    box-shadow: 
      0 0 0 2px rgba(220, 38, 38, 0.5),
      0 4px 8px rgba(220, 38, 38, 0.3);
  }
} 

/* 最后一列的最小宽度控制 */
.virtual-table-header-cell:last-child,
.virtual-table-cell:last-child {
  min-width: 120px !important;
}

/* 特殊列的最小宽度控制 */
.virtual-table-row .virtual-table-cell:last-child .delete-time-cell {
  min-width: 140px !important;
}