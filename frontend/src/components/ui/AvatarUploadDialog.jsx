import React, { useState, useRef, useCallback } from "react";
import {
  Upload,
  X,
  User,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON>, Card } from "./index";
import { toast } from "./Toast";

const AvatarUploadDialog = ({
  visible,
  onClose,
  onConfirm,
  advertiserId,
  advertiserName,
  title = "更改头像",
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadError, setUploadError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const fileInputRef = useRef(null);

  // 处理文件选择
  const handleFileSelect = useCallback((event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error("请选择有效的图片文件（JPG、PNG、GIF、WebP）");
      return;
    }

    // 验证文件大小（最大5MB）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error("图片文件大小不能超过5MB");
      return;
    }

    setSelectedFile(file);

    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  }, []);

  // 处理拖拽上传
  const handleDrop = useCallback((event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error("请选择有效的图片文件（JPG、PNG、GIF、WebP）");
        return;
      }

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error("图片文件大小不能超过5MB");
        return;
      }

      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  }, []);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
  }, []);

  // 处理确认上传
  const handleConfirm = useCallback(async () => {
    if (!selectedFile) {
      toast.error("请先选择图片文件");
      return;
    }

    setUploading(true);
    setUploadSuccess(false);
    setUploadError(false);
    setSuccessMessage("");
    setErrorMessage("");

    try {
      // 读取文件为base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target.result.split(",")[1]; // 移除data:image/xxx;base64,前缀

        // 获取图片实际尺寸
        const img = new Image();
        img.onload = async () => {
          try {
            // 调用上传接口
            await onConfirm({
              file: selectedFile,
              base64Data,
              fileName: selectedFile.name,
              width: img.width,
              height: img.height,
            });

            // 显示成功状态
            setUploadSuccess(true);
            setSuccessMessage("头像上传成功！");
            setUploading(false);

            // 延迟关闭弹窗，让用户看到成功提示
            setTimeout(() => {
              setSelectedFile(null);
              setPreviewUrl(null);
              setUploadSuccess(false);
              setUploadError(false);
              setSuccessMessage("");
              setErrorMessage("");
              onClose();
            }, 2000);
          } catch (error) {
            console.error("上传头像失败:", error);
            // 显示错误状态
            setUploadError(true);
            setErrorMessage("上传头像失败: " + error.message);
            setUploading(false);
          }
        };
        img.onerror = () => {
          // 如果无法获取图片尺寸，使用默认值
          onConfirm({
            file: selectedFile,
            base64Data,
            fileName: selectedFile.name,
            width: 300,
            height: 300,
          });

          // 显示成功状态
          setUploadSuccess(true);
          setSuccessMessage("头像上传成功！");
          setUploading(false);

          // 延迟关闭弹窗，让用户看到成功提示
          setTimeout(() => {
            setSelectedFile(null);
            setPreviewUrl(null);
            setUploadSuccess(false);
            setUploadError(false);
            setSuccessMessage("");
            setErrorMessage("");
            onClose();
          }, 2000);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(selectedFile);
    } catch (error) {
      console.error("上传头像失败:", error);
      // 显示错误状态
      setUploadError(true);
      setErrorMessage("上传头像失败: " + error.message);
      setUploading(false);
    }
  }, [selectedFile, onConfirm, onClose]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setUploadSuccess(false);
    setUploadError(false);
    setSuccessMessage("");
    setErrorMessage("");
    onClose();
  }, [onClose]);

  // 清理预览URL
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  if (!visible) return null;

  return (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={uploading}
          >
            <X size={20} />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-4">
          {/* 广告主信息 */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <User size={16} className="text-gray-500" />
              <span className="text-sm text-gray-600">广告主：</span>
              <span className="text-sm font-medium text-gray-900">
                {advertiserName}
              </span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-gray-600">ID：</span>
              <span className="text-sm font-medium text-gray-900">
                {advertiserId}
              </span>
            </div>
          </div>

          {/* 成功提示 */}
          {uploadSuccess && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-green-500" />
                <span className="text-sm font-medium text-green-800">
                  {successMessage}
                </span>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {uploadError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} className="text-red-500" />
                <span className="text-sm font-medium text-red-800">
                  {errorMessage}
                </span>
              </div>
            </div>
          )}

          {/* 上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              previewUrl
                ? "border-blue-300 bg-blue-50"
                : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            {previewUrl ? (
              <div className="space-y-3">
                <div className="relative inline-block">
                  <img
                    src={previewUrl}
                    alt="预览"
                    className="w-24 h-24 object-cover rounded-lg border border-gray-200"
                  />
                  <button
                    onClick={() => {
                      setSelectedFile(null);
                      setPreviewUrl(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = "";
                      }
                    }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    disabled={uploading}
                  >
                    <X size={12} />
                  </button>
                </div>
                <div className="text-sm text-gray-600">
                  <p className="font-medium">{selectedFile?.name}</p>
                  <p>{(selectedFile?.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <ImageIcon size={24} className="text-gray-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    点击选择图片或拖拽图片到此处
                  </p>
                  <p className="text-xs text-gray-500">
                    支持 JPG、PNG、GIF、WebP 格式，最大 5MB
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                >
                  <Upload size={14} className="mr-2" />
                  选择图片
                </Button>
              </div>
            )}
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-2 p-4 border-t border-gray-200">
          <Button variant="outline" onClick={handleCancel} disabled={uploading}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedFile || uploading}
            loading={uploading}
          >
            {uploading ? "上传中..." : "确认上传"}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AvatarUploadDialog;
