import React, { useEffect, useRef } from 'react';

/**
 * 右键菜单组件
 * @param {boolean} visible - 是否显示菜单
 * @param {number} x - 菜单X坐标
 * @param {number} y - 菜单Y坐标  
 * @param {Array} items - 菜单项数组
 * @param {Function} onClose - 关闭菜单回调
 */
const ContextMenu = ({ visible, x, y, items, onClose }) => {
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    const handleScroll = () => {
      onClose();
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  // 计算菜单位置，确保不超出屏幕边界
  const menuStyle = {
    position: 'fixed',
    left: Math.min(x, window.innerWidth - 200),
    top: Math.min(y, window.innerHeight - items.length * 40),
    zIndex: 9999,
    backgroundColor: 'var(--panel-bg)',
    border: '1px solid var(--border-color)',
    borderRadius: '6px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    padding: '4px 0',
    minWidth: '160px',
    maxWidth: '220px'
  };

  const handleItemClick = (item) => {
    if (item.disabled) {
      return; // 禁用的菜单项不执行任何操作
    }
    if (item.onClick) {
      item.onClick();
    }
    onClose();
  };

  return (
    <div ref={menuRef} style={menuStyle} className="transition-wechat">
      {items.map((item, index) => {
        if (item.type === 'divider') {
          return (
            <div
              key={index}
              style={{
                height: '1px',
                backgroundColor: 'var(--border-color)',
                margin: '4px 0'
              }}
            />
          );
        }

        return (
          <div
            key={index}
            onClick={() => handleItemClick(item)}
            className="transition-wechat"
            style={{
              padding: '8px 16px',
              cursor: item.disabled ? 'not-allowed' : 'pointer',
              color: item.disabled ? 'var(--text-disabled)' : 'var(--text-primary)',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              opacity: item.disabled ? 0.5 : 1,
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              if (!item.disabled) {
                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            {item.icon && (
              <span style={{ color: item.color || 'inherit' }}>
                {item.icon}
              </span>
            )}
            <span>{item.label}</span>
            {item.shortcut && (
              <span
                style={{
                  marginLeft: 'auto',
                  fontSize: '12px',
                  color: 'var(--text-secondary)'
                }}
              >
                {item.shortcut}
              </span>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ContextMenu; 