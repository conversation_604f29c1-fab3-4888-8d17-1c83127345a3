import React from 'react';

const Card = ({ 
  children, 
  interactive = false,
  className = '',
  onClick,
  style = {},
  ...props 
}) => {
  // 基础卡片样式
  const baseStyle = {
    backgroundColor: 'var(--panel-bg)',
    borderRadius: '4px',
    boxShadow: 'var(--shadow-card)',
    border: '1px solid var(--border-color)',
    transition: 'all 150ms ease-out',
    ...style
  };

  // 交互式卡片的事件处理
  const handleMouseEnter = (e) => {
    if (interactive) {
      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
      e.currentTarget.style.transform = 'translateY(-2px)';
    } else {
      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
    }
  };

  const handleMouseLeave = (e) => {
    e.currentTarget.style.boxShadow = 'var(--shadow-card)';
    if (interactive) {
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  const Component = interactive && onClick ? 'button' : 'div';
  
  const cardProps = {
    className: `${interactive ? 'cursor-pointer' : ''} ${className}`,
    style: baseStyle,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    onClick: onClick,
    ...props
  };

  // 如果是交互式按钮，添加button特有的样式
  if (Component === 'button') {
    cardProps.style = {
      ...baseStyle,
      border: '1px solid var(--border-color)',
      padding: 0,
      background: 'none'
    };
  }

  return (
    <Component {...cardProps}>
      {children}
    </Component>
  );
};

export default Card; 