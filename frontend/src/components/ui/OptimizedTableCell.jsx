import React from 'react';

/**
 * 优化的表格单元格组件
 * 使用React.memo避免不必要的重渲染
 */

// 文本单元格组件
export const TextCell = React.memo(({ value, title, className = '', maxWidth = 200 }) => (
  <div 
    className={`truncate ${className}`}
    style={{ maxWidth }}
    title={title || value}
  >
    {value || '-'}
  </div>
));

// 数字单元格组件
export const NumberCell = React.memo(({ value, format = 'normal', className = '' }) => {
  const formatNumber = (num) => {
    if (!num || num === '-') return '-';
    const number = parseFloat(num);
    if (isNaN(number)) return num;
    
    switch (format) {
      case 'currency':
        return `¥${number.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      case 'percentage':
        return `${(number * 100).toFixed(2)}%`;
      case 'compact':
        if (number >= 10000) {
          return `${(number / 10000).toFixed(1)}万`;
        }
        return number.toLocaleString('zh-CN');
      default:
        return number.toLocaleString('zh-CN');
    }
  };

  return (
    <div className={`text-right ${className}`} title={value}>
      {formatNumber(value)}
    </div>
  );
});

// 状态单元格组件
export const StatusCell = React.memo(({ status, statusName, variant = 'default' }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 1:
      case 'active':
        return 'text-green-600 bg-green-50';
      case 0:
      case 'inactive':
        return 'text-gray-600 bg-gray-50';
      case 2:
      case 'suspended':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status)}`}>
      {statusName || status}
    </span>
  );
});

// 操作按钮单元格组件
export const ActionCell = React.memo(({ actions, item }) => (
  <div className="flex items-center space-x-1">
    {actions.map((action, index) => (
      <button
        key={index}
        onClick={(e) => {
          e.stopPropagation();
          action.onClick(item);
        }}
        className={`p-1 rounded hover:bg-gray-100 ${action.className || ''}`}
        title={action.title}
        disabled={action.disabled}
      >
        {action.icon}
      </button>
    ))}
  </div>
));

// 链接单元格组件
export const LinkCell = React.memo(({ value, onClick, className = '' }) => (
  <button
    className={`text-blue-600 hover:text-blue-800 hover:underline truncate text-left ${className}`}
    onClick={onClick}
    title={value}
  >
    {value}
  </button>
));

// 图标文本单元格组件
export const IconTextCell = React.memo(({ icon, text, className = '' }) => (
  <div className={`flex items-center space-x-2 ${className}`}>
    {icon && <span className="flex-shrink-0">{icon}</span>}
    <span className="truncate" title={text}>
      {text}
    </span>
  </div>
));

// 标签组单元格组件
export const TagsCell = React.memo(({ tags, maxTags = 2 }) => {
  if (!tags || tags.length === 0) return <span>-</span>;
  
  const displayTags = tags.slice(0, maxTags);
  const remainingCount = tags.length - maxTags;

  return (
    <div className="flex flex-wrap gap-1">
      {displayTags.map((tag, index) => (
        <span
          key={index}
          className="inline-flex px-1 py-0.5 text-xs bg-blue-100 text-blue-800 rounded"
        >
          {tag}
        </span>
      ))}
      {remainingCount > 0 && (
        <span className="inline-flex px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
          +{remainingCount}
        </span>
      )}
    </div>
  );
});

// 默认导出优化的单元格组件集合
export default {
  TextCell,
  NumberCell,
  StatusCell,
  ActionCell,
  LinkCell,
  IconTextCell,
  TagsCell
}; 