import React from "react";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

// 分页组件
function Pagination({
  current = 1,
  total = 0,
  pageSize = 10,
  onChange,
  onPageSizeChange,
  showSizeChanger = true,
  showQuickJumper = true,
  showTotal = true,
  pageSizeOptions = [10, 20, 50, 100],
  className = "",
}) {
  const totalPages = Math.ceil(total / pageSize);
  const startItem = (current - 1) * pageSize + 1;
  const endItem = Math.min(current * pageSize, total);

  // 生成页码数组
  const getPageNumbers = () => {
    const pages = [];
    const maxVisible = 7; // 最多显示7个页码

    if (totalPages <= maxVisible) {
      // 如果总页数小于等于最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 复杂的页码显示逻辑
      if (current <= 4) {
        // 当前页在前面
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        // 当前页在后面
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push("...");
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== current) {
      onChange?.(page);
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    const newCurrent = Math.min(current, Math.ceil(total / newPageSize));
    onPageSizeChange?.(newPageSize, newCurrent);
  };

  const handleQuickJump = (e) => {
    if (e.key === "Enter") {
      const page = parseInt(e.target.value);
      if (page >= 1 && page <= totalPages) {
        handlePageChange(page);
        e.target.value = "";
      }
    }
  };

  if (total === 0) {
    return null;
  }

  return (
    <div
      className={`flex items-center justify-between px-4 py-3 border-t ${className}`}
      style={{
        backgroundColor: "var(--panel-bg)",
        borderColor: "var(--border-color)",
      }}
    >
      {/* 左侧：总数信息 */}
      <div className="flex items-center space-x-4">
        {showTotal && (
          <div className="text-sm" style={{ color: "var(--text-secondary)" }}>
            显示{" "}
            <span
              className="font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              {startItem}
            </span>{" "}
            到{" "}
            <span
              className="font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              {endItem}
            </span>{" "}
            条，共{" "}
            <span
              className="font-medium"
              style={{ color: "var(--text-primary)" }}
            >
              {total}
            </span>{" "}
            条
          </div>
        )}

        {showSizeChanger && (
          <div className="flex items-center space-x-2">
            <span
              className="text-sm"
              style={{ color: "var(--text-secondary)" }}
            >
              每页
            </span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              className="px-2 py-1 text-sm rounded border focus:outline-none transition-wechat"
              style={{
                backgroundColor: "var(--sidebar-bg)",
                borderColor: "var(--border-color)",
                color: "var(--text-primary)",
              }}
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span
              className="text-sm"
              style={{ color: "var(--text-secondary)" }}
            >
              条
            </span>
          </div>
        )}
      </div>

      {/* 右侧：分页控件 */}
      <div className="flex items-center space-x-2">
        {showQuickJumper && (
          <div className="flex items-center space-x-2 mr-4">
            <span
              className="text-sm"
              style={{ color: "var(--text-secondary)" }}
            >
              跳至
            </span>
            <input
              type="number"
              min="1"
              max={totalPages}
              placeholder={current.toString()}
              onKeyPress={handleQuickJump}
              className="w-12 px-2 py-1 text-sm border rounded focus:outline-none transition-wechat"
              style={{
                backgroundColor: "var(--sidebar-bg)",
                borderColor: "var(--border-color)",
                color: "var(--text-primary)",
              }}
            />
            <span
              className="text-sm"
              style={{ color: "var(--text-secondary)" }}
            >
              页
            </span>
          </div>
        )}

        {/* 首页 */}
        <button
          onClick={() => handlePageChange(1)}
          disabled={current === 1}
          className="p-1 rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            color: "var(--text-secondary)",
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
          title="首页"
        >
          <ChevronsLeft size={16} />
        </button>

        {/* 上一页 */}
        <button
          onClick={() => handlePageChange(current - 1)}
          disabled={current === 1}
          className="p-1 rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            color: "var(--text-secondary)",
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
          title="上一页"
        >
          <ChevronLeft size={16} />
        </button>

        {/* 页码 */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((page, index) => (
            <React.Fragment key={index}>
              {page === "..." ? (
                <span
                  className="px-2 py-1"
                  style={{ color: "var(--text-secondary)" }}
                >
                  ...
                </span>
              ) : (
                <button
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-1 rounded text-sm transition-wechat ${
                    page === current ? "" : ""
                  }`}
                  style={{
                    backgroundColor:
                      page === current ? "var(--primary-color)" : "transparent",
                    color: page === current ? "white" : "var(--text-primary)",
                  }}
                  onMouseEnter={(e) => {
                    if (page !== current) {
                      e.currentTarget.style.backgroundColor = "var(--hover-bg)";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (page !== current) {
                      e.currentTarget.style.backgroundColor = "transparent";
                    }
                  }}
                >
                  {page}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* 下一页 */}
        <button
          onClick={() => handlePageChange(current + 1)}
          disabled={current === totalPages}
          className="p-1 rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            color: "var(--text-secondary)",
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
          title="下一页"
        >
          <ChevronRight size={16} />
        </button>

        {/* 末页 */}
        <button
          onClick={() => handlePageChange(totalPages)}
          disabled={current === totalPages}
          className="p-1 rounded transition-wechat disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            color: "var(--text-secondary)",
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = "var(--hover-bg)";
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
          title="末页"
        >
          <ChevronsRight size={16} />
        </button>
      </div>
    </div>
  );
}

export default Pagination;
