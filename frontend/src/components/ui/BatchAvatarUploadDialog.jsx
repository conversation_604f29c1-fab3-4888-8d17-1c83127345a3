import React, { useState, useRef, useCallback } from "react";
import {
  Upload,
  X,
  User,
  Image as ImageIcon,
  Users,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON>, Card } from "./index";
import { toast } from "./Toast";

const BatchAvatarUploadDialog = ({
  visible,
  onClose,
  onConfirm,
  selectedAdvertisers = [],
  title = "批量更改头像",
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadError, setUploadError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const fileInputRef = useRef(null);

  // 处理文件选择
  const handleFileSelect = useCallback((event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error("请选择有效的图片文件（JPG、PNG、GIF、WebP）");
      return;
    }

    // 验证文件大小（最大5MB）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error("图片文件大小不能超过5MB");
      return;
    }

    setSelectedFile(file);

    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  }, []);

  // 处理拖拽上传
  const handleDrop = useCallback((event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error("请选择有效的图片文件（JPG、PNG、GIF、WebP）");
        return;
      }

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error("图片文件大小不能超过5MB");
        return;
      }

      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  }, []);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
  }, []);

  // 处理确认上传
  const handleConfirm = useCallback(async () => {
    if (!selectedFile) {
      toast.error("请先选择图片文件");
      return;
    }

    if (selectedAdvertisers.length === 0) {
      toast.error("请先选择要上传头像的广告主");
      return;
    }

    setUploading(true);
    setUploadProgress({});
    setUploadSuccess(false);
    setUploadError(false);
    setSuccessMessage("");
    setErrorMessage("");

    try {
      // 读取文件为base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target.result.split(",")[1]; // 移除data:image/xxx;base64,前缀

        // 获取图片实际尺寸
        const img = new Image();
        img.onload = async () => {
          try {
            const uploadData = {
              file: selectedFile,
              base64Data,
              fileName: selectedFile.name,
              width: img.width,
              height: img.height,
            };

            // 调用批量上传接口
            await onConfirm(uploadData, selectedAdvertisers, (progress) => {
              setUploadProgress(progress);
            });

            // 计算成功和失败数量
            let successCount = 0;
            let errorCount = 0;
            Object.values(uploadProgress).forEach((item) => {
              if (item.status === "success") {
                successCount++;
              } else if (item.status === "error") {
                errorCount++;
              }
            });

            // 显示成功状态
            if (errorCount === 0) {
              setSuccessMessage(
                `批量更换头像成功！共 ${successCount} 个广告主`
              );
            } else if (successCount > 0) {
              setSuccessMessage(
                `批量更换头像部分成功：成功 ${successCount} 个，失败 ${errorCount} 个`
              );
            } else {
              setErrorMessage(`批量更换头像失败：共 ${errorCount} 个错误`);
              setUploadError(true);
            }

            if (errorCount === 0) {
              setUploadSuccess(true);
            }
            setUploading(false);

            // 延迟关闭弹窗，让用户看到结果提示
            setTimeout(() => {
              setSelectedFile(null);
              setPreviewUrl(null);
              setUploading(false);
              setUploadProgress({});
              setUploadSuccess(false);
              setUploadError(false);
              setSuccessMessage("");
              setErrorMessage("");
              onClose();
            }, 3000);
          } catch (error) {
            console.error("批量上传头像失败:", error);
            // 显示错误状态
            setUploadError(true);
            setErrorMessage("批量上传头像失败: " + error.message);
            setUploading(false);
            setUploadProgress({});
          }
        };
        img.onerror = () => {
          // 如果无法获取图片尺寸，使用默认值
          const uploadData = {
            file: selectedFile,
            base64Data,
            fileName: selectedFile.name,
            width: 300,
            height: 300,
          };

          // 调用批量上传接口
          onConfirm(uploadData, selectedAdvertisers, (progress) => {
            setUploadProgress(progress);
          });

          // 计算成功和失败数量
          let successCount = 0;
          let errorCount = 0;
          Object.values(uploadProgress).forEach((item) => {
            if (item.status === "success") {
              successCount++;
            } else if (item.status === "error") {
              errorCount++;
            }
          });

          // 显示成功状态
          if (errorCount === 0) {
            setSuccessMessage(`批量更换头像成功！共 ${successCount} 个广告主`);
          } else if (successCount > 0) {
            setSuccessMessage(
              `批量更换头像部分成功：成功 ${successCount} 个，失败 ${errorCount} 个`
            );
          } else {
            setErrorMessage(`批量更换头像失败：共 ${errorCount} 个错误`);
            setUploadError(true);
          }

          if (errorCount === 0) {
            setUploadSuccess(true);
          }
          setUploading(false);

          // 延迟关闭弹窗，让用户看到结果提示
          setTimeout(() => {
            setSelectedFile(null);
            setPreviewUrl(null);
            setUploading(false);
            setUploadProgress({});
            setUploadSuccess(false);
            setUploadError(false);
            setSuccessMessage("");
            setErrorMessage("");
            onClose();
          }, 3000);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(selectedFile);
    } catch (error) {
      console.error("批量上传头像失败:", error);
      // 显示错误状态
      setUploadError(true);
      setErrorMessage("批量上传头像失败: " + error.message);
      setUploading(false);
      setUploadProgress({});
    }
  }, [selectedFile, selectedAdvertisers, onConfirm, onClose, uploadProgress]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setUploadSuccess(false);
    setUploadError(false);
    setSuccessMessage("");
    setErrorMessage("");
    onClose();
  }, [onClose]);

  // 清理预览URL
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  if (!visible) return null;

  return (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-lg mx-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={uploading}
          >
            <X size={20} />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* 选中的广告主信息 */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Users size={16} className="text-gray-500" />
              <span className="text-sm text-gray-600">选中的广告主：</span>
              <span className="text-sm font-medium text-gray-900">
                {selectedAdvertisers.length} 个
              </span>
            </div>
            <div className="max-h-32 overflow-y-auto">
              {selectedAdvertisers.map((advertiser, index) => (
                <div
                  key={advertiser.advertiser_id}
                  className="flex items-center gap-2 text-sm"
                >
                  <span className="text-gray-500">{index + 1}.</span>
                  <span className="text-gray-700">
                    {advertiser.advertiser_name}
                  </span>
                  <span className="text-gray-500">
                    (ID: {advertiser.advertiser_id})
                  </span>
                  {uploadProgress[advertiser.advertiser_id] && (
                    <div className="flex items-center gap-1 ml-auto">
                      {uploadProgress[advertiser.advertiser_id].status ===
                        "success" && (
                        <CheckCircle size={14} className="text-green-500" />
                      )}
                      {uploadProgress[advertiser.advertiser_id].status ===
                        "error" && (
                        <AlertCircle size={14} className="text-red-500" />
                      )}
                      {uploadProgress[advertiser.advertiser_id].status ===
                        "uploading" && (
                        <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 成功提示 */}
          {uploadSuccess && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-green-500" />
                <span className="text-sm font-medium text-green-800">
                  {successMessage}
                </span>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {uploadError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} className="text-red-500" />
                <span className="text-sm font-medium text-red-800">
                  {errorMessage}
                </span>
              </div>
            </div>
          )}

          {/* 上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              selectedFile
                ? "border-green-300 bg-green-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            {!selectedFile ? (
              <div>
                <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-2">
                  拖拽图片文件到此处，或{" "}
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-blue-600 hover:text-blue-800 underline"
                    disabled={uploading}
                  >
                    点击选择文件
                  </button>
                </p>
                <p className="text-sm text-gray-500">
                  支持 JPG、PNG、GIF、WebP 格式，最大 5MB
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>
            ) : (
              <div>
                <div className="relative inline-block">
                  <img
                    src={previewUrl}
                    alt="预览"
                    className="max-w-full max-h-48 rounded-lg shadow-sm"
                  />
                  <button
                    onClick={() => {
                      setSelectedFile(null);
                      setPreviewUrl(null);
                    }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    disabled={uploading}
                  >
                    <X size={16} />
                  </button>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            )}
          </div>

          {/* 上传进度 */}
          {uploading && Object.keys(uploadProgress).length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                上传进度
              </h4>
              <div className="space-y-2">
                {selectedAdvertisers.map((advertiser) => {
                  const progress = uploadProgress[advertiser.advertiser_id];
                  if (!progress) return null;

                  return (
                    <div
                      key={advertiser.advertiser_id}
                      className="flex items-center gap-2 text-sm"
                    >
                      <span className="text-gray-700 min-w-[120px]">
                        {advertiser.advertiser_name}
                      </span>
                      {progress.status === "uploading" && (
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                          <span className="text-blue-600">上传中...</span>
                        </div>
                      )}
                      {progress.status === "success" && (
                        <div className="flex items-center gap-2">
                          <CheckCircle size={14} className="text-green-500" />
                          <span className="text-green-600">上传成功</span>
                        </div>
                      )}
                      {progress.status === "error" && (
                        <div className="flex items-center gap-2">
                          <AlertCircle size={14} className="text-red-500" />
                          <span className="text-red-600">
                            {progress.error || "上传失败"}
                          </span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 p-4 border-t border-gray-200">
          <Button variant="outline" onClick={handleCancel} disabled={uploading}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            loading={uploading}
            disabled={!selectedFile || selectedAdvertisers.length === 0}
          >
            {uploading ? "上传中..." : "开始上传"}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default BatchAvatarUploadDialog;
