import React, { useState, useEffect } from "react";
import { Users, CheckCircle, XCircle } from "lucide-react";

/**
 * AudienceSelector 人群包选择组件
 * 支持定向和排除两个类别的人群包选择
 */
const AudienceSelector = ({
  audiences = [],
  selectedTargetAudiences = [],
  selectedExcludeAudiences = [],
  onTargetAudiencesChange,
  onExcludeAudiencesChange,
  loading = false,
  disabled = false,
  className = "",
}) => {
  const [activeTab, setActiveTab] = useState("target"); // 'target' | 'exclude'
  const [searchTerm, setSearchTerm] = useState("");

  // 过滤人群包
  const filteredAudiences = audiences.filter(
    (audience) =>
      audience.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      audience.id?.toString().includes(searchTerm)
  );

  // 处理定向人群包选择
  const handleTargetAudienceToggle = (audienceId) => {
    const newSelected = new Set(selectedTargetAudiences);
    if (newSelected.has(audienceId)) {
      newSelected.delete(audienceId);
    } else {
      newSelected.add(audienceId);
    }
    onTargetAudiencesChange(Array.from(newSelected));
  };

  // 处理排除人群包选择
  const handleExcludeAudienceToggle = (audienceId) => {
    const newSelected = new Set(selectedExcludeAudiences);
    if (newSelected.has(audienceId)) {
      newSelected.delete(audienceId);
    } else {
      newSelected.add(audienceId);
    }
    onExcludeAudiencesChange(Array.from(newSelected));
  };

  // 全选/取消全选定向人群包
  const handleSelectAllTarget = () => {
    const allIds = filteredAudiences.map((audience) => audience.id);
    onTargetAudiencesChange(allIds);
  };

  const handleDeselectAllTarget = () => {
    onTargetAudiencesChange([]);
  };

  // 全选/取消全选排除人群包
  const handleSelectAllExclude = () => {
    const allIds = filteredAudiences.map((audience) => audience.id);
    onExcludeAudiencesChange(allIds);
  };

  const handleDeselectAllExclude = () => {
    onExcludeAudiencesChange([]);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Users size={16} className="text-gray-600" />
          <span className="text-sm font-medium text-gray-900">人群包选择</span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            {audiences.length} 个可用
          </span>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="p-4 border-b border-gray-200">
        <input
          type="text"
          placeholder="搜索人群包名称或ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={disabled}
        />
      </div>

      {/* 标签页 */}
      <div className="flex border-b border-gray-200">
        <button
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === "target"
              ? "bg-blue-50 text-blue-700 border-b-2 border-blue-500"
              : "bg-gray-50 text-gray-600 hover:bg-gray-100"
          }`}
          onClick={() => setActiveTab("target")}
          disabled={disabled}
        >
          定向人群包 ({selectedTargetAudiences.length})
        </button>
        <button
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === "exclude"
              ? "bg-red-50 text-red-700 border-b-2 border-red-500"
              : "bg-gray-50 text-gray-600 hover:bg-gray-100"
          }`}
          onClick={() => setActiveTab("exclude")}
          disabled={disabled}
        >
          排除人群包 ({selectedExcludeAudiences.length})
        </button>
      </div>

      {/* 操作按钮 */}
      <div className="p-4 border-b border-gray-200">
        {activeTab === "target" && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSelectAllTarget}
              className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              disabled={disabled}
            >
              全选
            </button>
            <button
              onClick={handleDeselectAllTarget}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              disabled={disabled}
            >
              取消全选
            </button>
          </div>
        )}
        {activeTab === "exclude" && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSelectAllExclude}
              className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              disabled={disabled}
            >
              全选
            </button>
            <button
              onClick={handleDeselectAllExclude}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              disabled={disabled}
            >
              取消全选
            </button>
          </div>
        )}
      </div>

      {/* 人群包列表 */}
      <div className="max-h-60 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-2 text-sm text-gray-600">加载中...</span>
          </div>
        ) : filteredAudiences.length === 0 ? (
          <div className="text-center py-8 text-gray-500 text-sm">
            {searchTerm ? "没有找到匹配的人群包" : "暂无可用人群包"}
          </div>
        ) : (
          <div className="space-y-2 p-4">
            {filteredAudiences.map((audience) => {
              const isTargetSelected = selectedTargetAudiences.includes(
                audience.id
              );
              const isExcludeSelected = selectedExcludeAudiences.includes(
                audience.id
              );
              const isSelected =
                activeTab === "target" ? isTargetSelected : isExcludeSelected;

              return (
                <div
                  key={audience.id}
                  className={`flex items-center space-x-3 p-3 rounded-lg border-2 transition-all cursor-pointer ${
                    isSelected
                      ? activeTab === "target"
                        ? "border-blue-300 bg-blue-50"
                        : "border-red-300 bg-red-50"
                      : "border-gray-200 bg-white hover:border-gray-300"
                  }`}
                  onClick={() => {
                    if (activeTab === "target") {
                      handleTargetAudienceToggle(audience.id);
                    } else {
                      handleExcludeAudienceToggle(audience.id);
                    }
                  }}
                >
                  {/* 选择状态图标 */}
                  <div className="flex-shrink-0">
                    {isSelected ? (
                      <CheckCircle
                        size={20}
                        className={
                          activeTab === "target"
                            ? "text-blue-600"
                            : "text-red-600"
                        }
                      />
                    ) : (
                      <XCircle size={20} className="text-gray-400" />
                    )}
                  </div>

                  {/* 人群包信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {audience.name || "未命名人群包"}
                      </h4>
                      <span className="text-xs text-gray-500 font-mono">
                        {audience.id}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-600">
                      <span>覆盖人数: {audience.cover_num || "0"}</span>
                      <span>状态: {audience.status_name || "未知"}</span>
                    </div>
                  </div>

                  {/* 类型标签 */}
                  <div className="flex-shrink-0">
                    <span
                      className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                        activeTab === "target"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {activeTab === "target" ? "定向" : "排除"}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 底部统计 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            已选择:{" "}
            {activeTab === "target"
              ? selectedTargetAudiences.length
              : selectedExcludeAudiences.length}{" "}
            个
          </span>
          <span>
            显示: {filteredAudiences.length} / {audiences.length} 个
          </span>
        </div>
      </div>
    </div>
  );
};

export default AudienceSelector;
