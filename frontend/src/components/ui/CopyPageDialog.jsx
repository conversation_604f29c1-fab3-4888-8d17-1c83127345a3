import React, { useState, useEffect } from "react";
import {
  Copy,
  X,
  CheckCircle,
  Globe,
  Calendar,
  Loader2,
} from "lucide-react";
import Button from "./Button";
import { toast } from "./Toast";
import advertiserService from "../../services/advertiserService";
import { useCopyPage } from "../../contexts/CopyPageContext";

/**
 * CopyPageDialog 复制落地页对话框
 */
const CopyPageDialog = ({
  isOpen,
  onClose,
  sourceAccountId,
  sourceAdvertiserId,
  targetAccountId,
  targetAdvertiserId,
  onCopySuccess,
  mode = "paste", // 新增：模式 - "copy"为复制模式，"paste"为粘贴模式
}) => {
  const [pages, setPages] = useState([]);
  const [selectedPages, setSelectedPages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [copying, setCopying] = useState(false);
  
  // 使用复制落地页上下文
  const { copyPageSource } = useCopyPage();

  // 重置表单
  const resetForm = () => {
    setPages([]);
    setSelectedPages([]);
    setLoading(false);
    setCopying(false);
  };

  // 获取落地页列表
  const loadPages = async () => {
    if (!sourceAccountId || !sourceAdvertiserId) {
      return;
    }

    setLoading(true);
    try {
      console.log("获取落地页列表:", { sourceAccountId, sourceAdvertiserId });
      
      // 使用新的GetPageList方法
      const result = await advertiserService.GetPageList({
        account_id: sourceAccountId,
        advertiser_id: sourceAdvertiserId,
        limit: 50,
        page: 1,
        status: 'all',
        search: '',
      });

      console.log("落地页列表返回结果:", result);

      if (result.code === 0) {
        // 解析新的数据结构
        let pagesList = [];
        
        if (result.data && result.data.data && Array.isArray(result.data.data)) {
          // GetPageList返回的数据结构：result.data.data是落地页数组
          pagesList = result.data.data;
          console.log("使用GetPageList数据结构解析，路径: result.data.data");
        } else if (result.data && Array.isArray(result.data)) {
          // 备用结构：result.data 直接是落地页数组
          pagesList = result.data;
          console.log("使用备用结构解析，路径: result.data");
        } else {
          console.warn("无法识别的数据结构:", {
            "result.data": result.data,
            "result.data?.data": result.data?.data,
          });
        }
        
        console.log("处理后的落地页列表:", pagesList);
        console.log("落地页数量:", pagesList.length);
        
        // 验证数据格式
        if (pagesList.length > 0) {
          console.log("第一个落地页数据示例:", pagesList[0]);
          console.log("落地页字段检查:", {
            siteId: pagesList[0].siteId,
            name: pagesList[0].name,
            status: pagesList[0].status,
            thumbnail: pagesList[0].thumbnail
          });
        }
        
        setPages(pagesList);
        // 默认全部选中
        setSelectedPages(pagesList);
      } else {
        throw new Error(result.msg || result.message || "获取落地页列表失败");
      }
    } catch (error) {
      console.error("获取落地页列表失败:", error);
      toast.error(`获取落地页列表失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理页面选择
  const handlePageSelect = (page) => {
    setSelectedPages(prev => {
      const isSelected = prev.some(p => p.siteId === page.siteId);
      if (isSelected) {
        return prev.filter(p => p.siteId !== page.siteId);
      } else {
        return [...prev, page];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (!Array.isArray(pages)) {
      console.warn("pages 不是数组，无法执行全选操作");
      return;
    }
    
    if (selectedPages.length === pages.length) {
      setSelectedPages([]);
    } else {
      setSelectedPages([...pages]);
    }
  };

  // 处理复制操作
  const handleCopy = async () => {
    if (selectedPages.length === 0) {
      toast.warning("请至少选择一个落地页");
      return;
    }

    setCopying(true);
    try {
      if (mode === "copy") {
        // 复制模式：保存选择的落地页到全局状态
        const pages = selectedPages.map(page => ({
          page_id: page.siteId,
          source_id: page.sourceId || page.templateId || ''
        }));
        copyPageSource(sourceAccountId, sourceAdvertiserId, pages);
        
        toast.success(`已复制 ${selectedPages.length} 个落地页，可以到其他广告主进行粘贴`);
        
        resetForm();
        onClose();
        return;
      }

      // 粘贴模式：执行实际的复制操作
      console.log("开始复制落地页:", {
        sourceAccountId,
        sourceAdvertiserId,
        targetAccountId,
        targetAdvertiserId,
        selectedPages: selectedPages.length,
      });

      // 构建复制参数 - 使用pages数组格式
      const copyParams = {
        source_account_id: sourceAccountId,
        source_advertiser_id: sourceAdvertiserId,
        target_account_id: targetAccountId,
        target_advertiser_id: targetAdvertiserId,
        pages: selectedPages.map(page => ({
          page_id: page.siteId,
          source_id: page.sourceId || page.templateId || ''
        })),
      };

      const result = await advertiserService.CopyPage(copyParams);
      console.log("复制落地页结果:", result);

      if (result.code === 0 && result.data) {
        const copyData = result.data;
        const successCount = copyData.success_count || 0;
        const errorCount = copyData.error_count || 0;
        const totalCount = copyData.total_count || selectedPages.length;

        // 根据实际成功数量判断结果
        if (successCount > 0) {
          if (errorCount > 0) {
            // 部分成功
            toast.warning(`复制完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
          } else {
            // 全部成功
            toast.success(`成功复制 ${successCount} 个落地页`);
          }
          onCopySuccess && onCopySuccess(result);
          resetForm();
          onClose();
        } else {
          // 全部失败
          let errorMessage = copyData.summary || "复制落地页失败";
          
          // 尝试从results中获取更详细的错误信息
          if (copyData.results && typeof copyData.results === 'object') {
            const errors = Object.values(copyData.results)
              .filter(item => item && !item.success && item.error)
              .map(item => item.error)
              .slice(0, 3); // 只显示前3个错误
            
            if (errors.length > 0) {
              errorMessage = `复制失败：${errors[0]}`;
              if (errors.length > 1) {
                errorMessage += ` 等${errorCount}个错误`;
              }
            }
          }
          
          throw new Error(errorMessage);
        }
      } else {
        throw new Error(result.msg || result.message || "复制落地页失败");
      }
    } catch (error) {
      console.error("复制落地页失败:", error);
      toast.error(`复制落地页失败: ${error.message}`);
    } finally {
      setCopying(false);
    }
  };

  // 格式化时间
  const formatTime = (timeStr) => {
    if (!timeStr) return "-";
    try {
      // 尝试解析时间字符串或时间戳
      let date;
      if (typeof timeStr === "string") {
        // 如果是字符串，直接解析
        date = new Date(timeStr);
      } else if (typeof timeStr === "number") {
        // 如果是数字，判断是秒还是毫秒
        date = timeStr > 1000000000000 ? new Date(timeStr) : new Date(timeStr * 1000);
      } else {
        return "-";
      }
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return timeStr; // 如果解析失败，返回原始值
      }
      
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return timeStr || "-"; // 如果发生错误，返回原始值或"-"
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadPages();
    } else {
      resetForm();
    }
  }, [isOpen, sourceAccountId, sourceAdvertiserId]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0"
        style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        onClick={onClose}
      />

      {/* 对话框 */}
      <div
        className="relative rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col"
        style={{
          backgroundColor: "#FFFFFF",
          border: "1px solid #E5E5E5",
        }}
      >
        {/* 标题栏 */}
        <div
          className="px-4 py-3 border-b flex items-center justify-between"
          style={{
            borderColor: "#E5E5E5",
            backgroundColor: "#F5F5F5",
          }}
        >
          <div className="flex items-center space-x-2">
            <Globe size={16} style={{ color: "#07C160" }} />
            <h2
              className="text-lg font-medium"
              style={{ color: "rgba(0, 0, 0, 0.85)" }}
            >
              {mode === "copy" ? "选择要复制的落地页" : "确认复制落地页"}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={copying}
          >
            <X size={20} />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* 统计信息 */}
          <div
            className="px-4 py-2 border-b text-sm"
            style={{
              borderColor: "#E5E5E5",
              backgroundColor: "#F5F5F5",
              color: "rgba(0, 0, 0, 0.45)",
            }}
          >
            {mode === "copy" 
              ? `广告主: ${sourceAdvertiserId} | 共 ${Array.isArray(pages) ? pages.length : 0} 个落地页，已选择 ${selectedPages.length} 个`
              : `源广告主: ${sourceAdvertiserId} → 目标广告主: ${targetAdvertiserId} | 共 ${Array.isArray(pages) ? pages.length : 0} 个落地页，已选择 ${selectedPages.length} 个`
            }
          </div>

          {/* 落地页列表 */}
          <div className="flex-1 overflow-auto p-4">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 size={24} className="animate-spin text-blue-500" />
                <span className="ml-2 text-sm text-gray-500">
                  正在加载落地页列表...
                </span>
              </div>
            ) : (!Array.isArray(pages) || pages.length === 0) ? (
              <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                <Globe size={32} className="mb-2 opacity-50" />
                <p>该广告主下暂无落地页</p>
                {/* 调试信息 */}
                <div className="mt-2 text-xs text-red-500 bg-red-50 p-2 rounded max-w-md">
                  <div>调试信息：</div>
                  <div>pages类型: {Array.isArray(pages) ? 'Array' : typeof pages}</div>
                  <div>pages长度: {Array.isArray(pages) ? pages.length : 'N/A'}</div>
                  <div>是否正在加载: {loading ? '是' : '否'}</div>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {/* 全选按钮 */}
                <div className="flex items-center justify-between mb-4">
                  <Button
                    onClick={handleSelectAll}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    {selectedPages.length === (Array.isArray(pages) ? pages.length : 0)
                      ? "取消全选"
                      : "全选"}
                  </Button>
                  <span className="text-xs text-gray-500">
                    点击卡片选择/取消选择
                  </span>
                </div>

                {/* 落地页卡片列表 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Array.isArray(pages) && pages.map((page) => {
                    const isSelected = selectedPages.some(
                      (p) => p.siteId === page.siteId
                    );
                    
                    // 获取状态显示文本和颜色
                    const getStatusInfo = (status) => {
                      switch (status) {
                        case 3:
                          return { text: "待审核", color: "#f59e0b" };
                        case 5:
                          return { text: "已上线", color: "#22c55e" };
                        case 2:
                          return { text: "审核通过", color: "#22c55e" };
                        case 1:
                          return { text: "草稿", color: "#6b7280" };
                        case 4:
                          return { text: "审核拒绝", color: "#ef4444" };
                        default:
                          return { text: `状态${status}`, color: "#6b7280" };
                      }
                    };
                    
                    const statusInfo = getStatusInfo(page.status);
                    
                    return (
                      <div
                        key={page.siteId}
                        className={`border rounded-lg overflow-hidden cursor-pointer transition-all hover:shadow-md ${
                          isSelected
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => handlePageSelect(page)}
                      >
                        <div className="flex items-center p-2">
                          {/* 左侧缩略图 */}
                          <div className="w-16 h-12 overflow-hidden bg-gray-100 rounded flex-shrink-0 mr-3">
                            {page.thumbnail ? (
                              <img
                                src={page.thumbnail}
                                alt={page.name || "落地页封面"}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.target.style.display = 'none';
                                  e.target.parentElement.classList.add('bg-gray-200');
                                }}
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                <Globe size={16} className="text-gray-400" />
                              </div>
                            )}
                          </div>
                          
                          {/* 右侧内容区域 */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                {/* 标题和选中状态 */}
                                <div className="flex items-center space-x-2 mb-1">
                                  <h3
                                    className="font-medium text-sm truncate"
                                    style={{ color: "rgba(0, 0, 0, 0.85)" }}
                                    title={page.name || `落地页 ${page.siteId}`}
                                  >
                                    {page.name || `落地页 ${page.siteId}`}
                                  </h3>
                                  {isSelected && (
                                    <CheckCircle
                                      size={14}
                                      className="text-blue-500 flex-shrink-0"
                                    />
                                  )}
                                </div>
                                
                                {/* 状态和ID信息 */}
                                <div className="flex items-center space-x-2 mb-1">
                                  <span
                                    className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                                    style={{
                                      backgroundColor: `${statusInfo.color}20`,
                                      color: statusInfo.color,
                                      border: `1px solid ${statusInfo.color}40`
                                    }}
                                  >
                                    {statusInfo.text}
                                  </span>
                                </div>
                                
                                {/* ID信息 */}
                                <div className="text-xs text-gray-500">
                                  ID: {page.siteId}
                                </div>
                              </div>
                              
                              {/* 右侧时间信息 */}
                              <div className="text-xs text-gray-500 text-right ml-2 flex-shrink-0">
                                {formatTime(page.createTime)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div
          className="px-4 py-3 border-t"
          style={{
            borderColor: "#E5E5E5",
            backgroundColor: "#F5F5F5",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="text-xs" style={{ color: "rgba(0, 0, 0, 0.45)" }}>
              {selectedPages.length > 0 && (
                mode === "copy" 
                  ? `将复制 ${selectedPages.length} 个落地页到剪贴板` 
                  : `将复制 ${selectedPages.length} 个落地页到广告主 ${targetAdvertiserId}`
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={onClose}
                variant="secondary"
                disabled={copying}
                className="px-4 py-2 text-sm"
              >
                取消
              </Button>
              <Button
                onClick={handleCopy}
                variant="primary"
                disabled={copying || selectedPages.length === 0}
                loading={copying}
                icon={<Copy size={14} />}
                className="px-4 py-2 text-sm"
              >
                {copying
                  ? (mode === "copy" ? "复制中..." : "粘贴中...")
                  : (mode === "copy" 
                      ? `确认复制 ${selectedPages.length} 个` 
                      : `确认粘贴 ${selectedPages.length} 个`
                    )
                }
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CopyPageDialog; 