import React from 'react';

/**
 * 工具栏组件
 * @param {React.ReactNode} leftContent - 左侧内容
 * @param {React.ReactNode} rightContent - 右侧内容
 * @param {React.ReactNode} children - 工具栏内容
 * @param {string} className - 额外的样式类
 * @param {object} style - 内联样式
 * @param {boolean} border - 是否显示底部边框
 * @param {'sm'|'md'|'lg'} size - 工具栏大小
 */
function Toolbar({ 
  leftContent, 
  rightContent, 
  children, 
  className = '', 
  style = {},
  border = true,
  size = 'md'
}) {
  const sizeClasses = {
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6'
  };

  const paddingClass = sizeClasses[size] || sizeClasses.md;

  const toolbarStyle = {
    backgroundColor: 'var(--panel-bg)',
    borderBottom: border ? '1px solid var(--border-color)' : 'none',
    ...style
  };

  // 如果有 children，使用 children 作为内容
  if (children) {
    return (
      <div 
        className={`transition-wechat ${paddingClass} ${className}`}
        style={toolbarStyle}
      >
        {children}
      </div>
    );
  }

  // 否则使用左右布局
  return (
    <div 
      className={`transition-wechat ${paddingClass} ${className}`}
      style={toolbarStyle}
    >
      <div className="flex items-center justify-between">
        {leftContent && (
          <div className="flex items-center space-x-3">
            {leftContent}
          </div>
        )}
        
        {rightContent && (
          <div className="flex items-center space-x-4">
            {rightContent}
          </div>
        )}
      </div>
    </div>
  );
}

// 工具栏组件的子组件
Toolbar.Left = ({ children, className = '' }) => (
  <div className={`flex items-center space-x-3 ${className}`}>
    {children}
  </div>
);

Toolbar.Right = ({ children, className = '' }) => (
  <div className={`flex items-center space-x-4 ${className}`}>
    {children}
  </div>
);

Toolbar.Group = ({ children, className = '' }) => (
  <div className={`flex items-center space-x-2 ${className}`}>
    {children}
  </div>
);

export default Toolbar; 