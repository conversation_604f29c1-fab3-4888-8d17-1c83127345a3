import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, User, Check } from 'lucide-react';
import accountService from '../../services/accountService';

// 账户选择器组件
function AccountSelector({ selectedAccount, onAccountChange, className = '' }) {
  const [isOpen, setIsOpen] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // 使用ref来避免重复调用
  const hasAutoSelected = useRef(false);
  const loadAccountsRef = useRef(null);

  // 加载账户列表
  const loadAccounts = async () => {
    // 防止重复调用
    if (loadAccountsRef.current) {
      console.log('跳过重复的账户列表加载请求');
      return;
    }
    
    loadAccountsRef.current = true;
    setLoading(true);
    
    try {
      console.log('加载账户列表...');
      const result = await accountService.getAccountList({
        page: 1,
        pageSize: 100 // 获取所有账户
      });
      console.log('账户列表加载结果:', result);
      if (result.code === 0) {
        // 只显示已登录的账户（有account_id的）
        const loggedInAccounts = (result.data.list || []).filter(account => 
          account.account_id && account.account_id > 0
        );
        setAccounts(loggedInAccounts);
        setHasInitialized(true);
      }
    } catch (error) {
      console.error('加载账户列表失败:', error);
    } finally {
      setLoading(false);
      loadAccountsRef.current = false;
    }
  };

  // 组件挂载时加载账户列表
  useEffect(() => {
    loadAccounts();
  }, []); // 只在组件挂载时执行一次

  // 自动选择逻辑（移除onAccountChange依赖，添加防重复逻辑）
  useEffect(() => {
    if (hasInitialized && !selectedAccount && accounts.length > 0 && !hasAutoSelected.current) {
      hasAutoSelected.current = true;
      console.log('自动选择第一个账户:', accounts[0]);
      onAccountChange(accounts[0]);
    }
  }, [hasInitialized, selectedAccount, accounts]); // 移除onAccountChange依赖

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.account-selector')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleAccountSelect = (account) => {
    onAccountChange(account);
    setIsOpen(false);
  };

  return (
    <div className={`relative account-selector ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        disabled={loading}
      >
        <div className="flex items-center space-x-2">
          <User size={16} className="text-gray-400" />
          <span className="text-sm">
            {loading ? '加载中...' : selectedAccount ? selectedAccount.account_name : '请选择账户'}
          </span>
        </div>
        <ChevronDown size={16} className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {accounts.length === 0 ? (
            <div className="px-4 py-3 text-sm text-gray-500">
              暂无可用账户
            </div>
          ) : (
            accounts.map((account) => (
              <button
                key={account.id}
                onClick={() => handleAccountSelect(account)}
                className="flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded flex items-center justify-center">
                    <User size={14} className="text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {account.account_name}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: {account.account_id}
                    </div>
                  </div>
                </div>
                {selectedAccount?.id === account.id && (
                  <Check size={16} className="text-blue-600" />
                )}
              </button>
            ))
          )}
        </div>
      )}
    </div>
  );
}

export default AccountSelector; 