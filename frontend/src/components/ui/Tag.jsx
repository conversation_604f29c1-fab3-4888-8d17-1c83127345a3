import React from "react";

const Tag = ({
  children,
  color = "default",
  size = "medium",
  closable = false,
  onClose,
  className = "",
  ...props
}) => {
  const colorClasses = {
    default: "bg-gray-100 text-gray-800 border-gray-200",
    primary: "bg-blue-100 text-blue-800 border-blue-200",
    success: "bg-green-100 text-green-800 border-green-200",
    warning: "bg-yellow-100 text-yellow-800 border-yellow-200",
    error: "bg-red-100 text-red-800 border-red-200",
    info: "bg-blue-100 text-blue-800 border-blue-200",
    green: "bg-green-100 text-green-800 border-green-200",
    red: "bg-red-100 text-red-800 border-red-200",
    blue: "bg-blue-100 text-blue-800 border-blue-200",
    yellow: "bg-yellow-100 text-yellow-800 border-yellow-200",
    purple: "bg-purple-100 text-purple-800 border-purple-200",
    pink: "bg-pink-100 text-pink-800 border-pink-200",
    gray: "bg-gray-100 text-gray-800 border-gray-200",
  };

  const sizeClasses = {
    small: "px-2 py-0.5 text-xs",
    medium: "px-2.5 py-1 text-sm",
    large: "px-3 py-1.5 text-base",
  };

  const handleClose = (e) => {
    e.stopPropagation();
    if (onClose) {
      onClose();
    }
  };

  const baseClasses = `
    inline-flex items-center rounded-md border font-medium
    ${colorClasses[color]}
    ${sizeClasses[size]}
    ${className}
  `;

  return (
    <span className={baseClasses} {...props}>
      {children}
      {closable && (
        <button
          type="button"
          onClick={handleClose}
                        className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-current hover:bg-current/20 focus:outline-none focus:ring-2 focus:ring-offset-2"
          aria-label="关闭标签"
        >
          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      )}
    </span>
  );
};

export default Tag;
