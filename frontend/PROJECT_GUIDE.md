# OceanEngine Manager - 前端项目架构指南

## 📖 项目概述

**OceanEngine Manager** 是一个基于 **Wails v3** 框架开发的桌面应用程序，专门用于巨量引擎（字节跳动广告平台）的综合管理。项目采用现代化的前端技术栈，实现了高性能的桌面应用体验。

### 🎯 核心功能模块

- **📊 账户管理** - 巨量引擎账户的全生命周期管理
- **🏢 广告主管理** - 广告主信息的创建和维护
- **📁 项目管理** - 广告项目的组织和管理
- **🎯 广告管理** - 广告计划的创建、编辑、复制和状态管理
- **🌐 代理管理** - 代理IP的配置和管理（独立窗口）
- **🎬 视频处理** - 视频素材的处理和加工（独立窗口）
- **📝 文本预设** - 文本模板的管理和预设

## 🏗️ 技术架构

### 核心技术栈

```
┌─────────────────────────────────────────────────────────┐
│                    前端技术栈                              │
├─────────────────────────────────────────────────────────┤
│ React 18         │ 现代化UI框架，组件化开发              │
│ Vite 5.0         │ 高性能构建工具，快速热重载            │
│ Tailwind CSS 4.1 │ 原子化CSS框架，快速样式开发          │
│ React Router DOM │ 单页应用路由管理                    │
│ React Window     │ 虚拟滚动优化，大数据量性能保障         │
│ Lucide React     │ 现代化图标库                       │
│ Wails v3 Runtime │ 桌面应用框架，Go后端通信桥梁          │
└─────────────────────────────────────────────────────────┘
```

### 架构特点

#### 🖥️ 桌面优先设计
- **原生体验**: 专为桌面环境优化，非响应式设计
- **Windows风格**: 原生滚动条、按钮、输入框等UI组件
- **多窗口支持**: 主窗口 + 独立功能窗口（代理管理、视频处理）
- **高性能**: 虚拟滚动、组件缓存、状态管理优化

#### 🔧 模块化架构
```
┌─────────────────────────────────────────────────────────┐
│                    模块化架构图                            │
├─────────────────────────────────────────────────────────┤
│  MainPage (主页面)                                      │
│  ├── AccountManager     (账户管理模块)                    │
│  ├── AdvertiserManager  (广告主管理模块)                  │
│  ├── ProjectManager     (项目管理模块)                    │
│  └── PromotionManager   (广告管理模块)                    │
│                                                         │
│  独立窗口                                               │
│  ├── ProxyManager       (代理管理窗口)                    │
│  ├── VideoProcessor     (视频处理窗口)                    │
│  └── TextPresetPage     (文本预设窗口)                    │
└─────────────────────────────────────────────────────────┘
```

## 📁 项目结构详解

```
frontend/
├── 📁 src/
│   ├── 📁 components/           # 组件库
│   │   ├── 📁 ui/              # 基础UI组件
│   │   │   ├── Button.jsx       # 按钮组件（支持多种变体）
│   │   │   ├── Input.jsx        # 输入框组件
│   │   │   ├── VirtualDataTable.jsx  # 虚拟滚动表格
│   │   │   ├── Toast.jsx        # 消息提示组件
│   │   │   ├── ConfirmDialog.jsx # 确认对话框
│   │   │   ├── ContextMenu.jsx  # 右键菜单
│   │   │   ├── SearchInput.jsx  # 搜索输入框
│   │   │   └── index.js         # 组件统一导出
│   │   ├── 📁 project/         # 项目相关组件
│   │   └── StatusBar.jsx       # 状态栏组件
│   │
│   ├── 📁 pages/               # 页面模块
│   │   ├── MainPage.jsx        # 主页面（标签页管理）
│   │   ├── ProxyManager.jsx    # 代理管理页面
│   │   ├── VideoProcessor.jsx  # 视频处理页面
│   │   ├── TextPresetPage.jsx  # 文本预设页面
│   │   │
│   │   ├── 📁 AccountManager/  # 账户管理模块
│   │   │   ├── AccountManager.jsx
│   │   │   ├── 📁 hooks/
│   │   │   ├── 📁 components/
│   │   │   └── 📁 constants/
│   │   │
│   │   ├── 📁 PromotionManager/ # 广告管理模块
│   │   │   ├── PromotionManager.jsx
│   │   │   ├── 📁 hooks/        # 业务逻辑Hook
│   │   │   ├── 📁 components/   # 专用组件
│   │   │   ├── 📁 constants/    # 常量定义
│   │   │   └── 📁 utils/        # 工具函数
│   │   │
│   │   └── [其他管理模块...]
│   │
│   ├── 📁 services/            # 服务层
│   │   ├── api.js              # API服务统一导出
│   │   ├── accountService.js   # 账户相关服务
│   │   ├── promotionService.js # 广告相关服务
│   │   ├── projectService.js   # 项目相关服务
│   │   └── advertiserService.js# 广告主相关服务
│   │
│   ├── 📁 contexts/            # React上下文
│   │   └── AccountContext.jsx  # 账户全局状态管理
│   │
│   ├── 📁 hooks/               # 通用自定义Hook
│   │   ├── useContextMenu.js   # 右键菜单Hook
│   │   ├── useProjectList.js   # 项目列表Hook
│   │   └── useOptimizedDataFilter.js # 数据过滤优化Hook
│   │
│   ├── 📁 utils/               # 工具函数
│   ├── 📁 constants/           # 全局常量
│   │
│   ├── App.jsx                 # 应用根组件
│   ├── main.jsx                # 应用入口
│   └── index.css               # 全局样式
│
├── 📁 bindings/                # Wails后端绑定
│   └── gitlab.e-idear.com/
│       └── enginer/app/oceanEngineManager/backend/
│           ├── 📁 service/     # 后端服务绑定
│           ├── 📁 model/       # 数据模型绑定
│           └── 📁 types/       # 类型定义绑定
│
├── 📁 public/                  # 静态资源
├── package.json                # 依赖配置
├── vite.config.js             # Vite构建配置
└── tailwind.config.js         # Tailwind样式配置
```

## 🎨 设计理念与原则

### 1. 桌面原生体验

```javascript
// 设计原则：固定尺寸，统一标准
const DESIGN_STANDARDS = {
  controlHeight: '28px',      // 标准控件高度
  fontSize: '14px',           // 标准字体大小
  borderRadius: '4px',        // 标准圆角
  spacing: '8px',             // 标准间距单位
  animationDuration: '0ms'    // 禁用动画，即时响应
};
```

### 2. 组件化设计

#### 基础组件示例
```jsx
// Button 组件 - 支持多种变体和状态
<Button 
  variant="primary"    // primary | secondary | danger
  size="default"       // sm | default | lg
  loading={isLoading}  // 加载状态
  disabled={isDisabled}
  onClick={handleClick}
>
  操作按钮
</Button>

// VirtualDataTable - 高性能表格组件
<VirtualDataTable
  columns={columns}
  data={largeDataset}
  loading={loading}
  selectable={true}
  contextMenuItems={contextMenuItems}
  itemHeight={28}
  onRowClick={handleRowClick}
/>
```

### 3. 状态管理策略

```javascript
// 全局状态：使用React Context
const AccountContext = createContext({
  sharedAccount: null,
  setAccount: () => {},
  selectedSubAccount: null,
  setSubAccount: () => {}
});

// 组件级状态：自定义Hook + useState
const usePromotionManager = (selectedProject) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  
  return {
    loading, data, selectedItems,
    loadData, deletePromotion, copyPromotion
  };
};
```

## 🔄 数据流架构

```
┌─────────────────────────────────────────────────────────┐
│                    数据流向图                              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  🎨 UI Components                                       │
│       │                                                 │
│       ├── 📡 Service Layer (API调用)                    │
│       │     │                                           │
│       │     ├── 🔗 Wails Runtime Bridge                 │
│       │     │     │                                     │
│       │     │     └── 🔧 Go Backend Services            │
│       │     │                                           │
│       │     └── 📦 Local State Management               │
│       │                                                 │
│       ├── 🔄 Custom Hooks (业务逻辑)                     │
│       │                                                 │
│       └── 🌐 Global Context (共享状态)                   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 服务层架构

```javascript
// 服务层统一封装
// services/api.js
export const PromotionService = new PromotionServiceClass();
export const AccountService = new AccountServiceClass();
export const ProjectService = ProjectServiceBinding;

// 业务Hook调用服务
const usePromotionManager = () => {
  const copyPromotion = async (params) => {
    const result = await PromotionService.CopyPromotion(params);
    if (result.code === 0) {
      toast.success('复制成功');
      await refreshData();
    }
  };
};
```

## ⚡ 性能优化策略

### 1. 虚拟滚动优化

```javascript
// 大数据量表格性能优化
import { VirtualDataTable } from '@/components/ui';

const PromotionList = () => {
  return (
    <VirtualDataTable
      data={largeDataset}        // 支持10k+数据
      itemHeight={28}            // 固定行高优化
      overscan={10}              // 预渲染行数
      columns={optimizedColumns} // 优化列渲染
    />
  );
};
```

### 2. 组件缓存策略

```javascript
// React.memo 优化组件重渲染
const OptimizedRow = React.memo(({ item, index }) => {
  return <TableRow item={item} index={index} />;
});

// useMemo 优化计算密集操作
const filteredData = useMemo(() => {
  return data.filter(item => !isDeletedPromotion(item));
}, [data, showDeletedItems]);
```

### 3. 状态管理优化

```javascript
// 分层缓存策略
const useDataCache = (cacheKey) => {
  const saveToCache = useCallback((data) => {
    sessionStorage.setItem(cacheKey, JSON.stringify(data));
  }, [cacheKey]);
  
  const restoreFromCache = useCallback(() => {
    const cached = sessionStorage.getItem(cacheKey);
    return cached ? JSON.parse(cached) : null;
  }, [cacheKey]);
};
```

## 🛠️ 开发指南

### 环境配置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 开发构建（未压缩）
npm run build:dev
```

### 新功能开发流程

#### 1. 创建新的管理模块

```bash
# 创建模块目录结构
src/pages/NewManager/
├── NewManager.jsx          # 主组件
├── index.js               # 导出文件
├── hooks/
│   └── useNewManager.js   # 业务逻辑Hook
├── components/
│   ├── NewDialog.jsx      # 专用对话框
│   └── TableColumns.jsx   # 表格列配置
├── constants/
│   └── index.js          # 模块常量
└── utils/
    └── index.js          # 工具函数
```

#### 2. 实现业务Hook

```javascript
// hooks/useNewManager.js
export const useNewManager = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await NewService.getData();
      setData(result.data);
    } finally {
      setLoading(false);
    }
  }, []);
  
  return { data, loading, loadData };
};
```

#### 3. 创建服务层

```javascript
// services/newService.js
class NewServiceClass {
  async getData(params) {
    return await NewServiceBinding.GetData(params);
  }
  
  async createItem(params) {
    return await NewServiceBinding.CreateItem(params);
  }
}

export default new NewServiceClass();
```

### 编码规范

#### 组件命名规范

```javascript
// ✅ 正确命名
const PromotionManager = () => { };
const AccountDialog = () => { };
const usePromotionManager = () => { };

// ❌ 错误命名
const promotionmanager = () => { };
const accountdialog = () => { };
const usepromotionmanager = () => { };
```

#### 样式规范

```jsx
// ✅ 使用Tailwind原子类
<div className="flex items-center space-x-2 p-4 border rounded-lg">
  <Button variant="primary" size="default">
    操作按钮
  </Button>
</div>

// ✅ 使用CSS变量实现主题
<div style={{ 
  backgroundColor: 'var(--panel-bg)',
  color: 'var(--text-primary)',
  borderColor: 'var(--border-color)'
}}>
  内容区域
</div>
```

#### 状态管理规范

```javascript
// ✅ 使用自定义Hook封装业务逻辑
const useDataManager = () => {
  const [state, setState] = useState(initialState);
  
  const actions = useMemo(() => ({
    loadData: async () => { /* 实现 */ },
    deleteItem: async (id) => { /* 实现 */ },
    updateItem: async (item) => { /* 实现 */ }
  }), []);
  
  return { state, ...actions };
};

// ✅ 组件中使用Hook
const DataManager = () => {
  const { state, loadData, deleteItem } = useDataManager();
  
  return (
    <div>
      {/* UI 渲染 */}
    </div>
  );
};
```

## 🧪 测试策略

### 组件测试

```javascript
// 基础组件单元测试
describe('Button Component', () => {
  it('should render with correct variant', () => {
    render(<Button variant="primary">Test</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-blue-500');
  });
});

// Hook测试
describe('usePromotionManager', () => {
  it('should load data correctly', async () => {
    const { result } = renderHook(() => usePromotionManager());
    await act(async () => {
      await result.current.loadData();
    });
    expect(result.current.data).toBeDefined();
  });
});
```

### 集成测试

```javascript
// 页面级集成测试
describe('PromotionManager Integration', () => {
  it('should perform complete workflow', async () => {
    render(<PromotionManager />);
    
    // 测试数据加载
    await waitFor(() => {
      expect(screen.getByText('广告列表')).toBeInTheDocument();
    });
    
    // 测试操作功能
    fireEvent.click(screen.getByText('复制广告'));
    await waitFor(() => {
      expect(screen.getByText('复制成功')).toBeInTheDocument();
    });
  });
});
```

## 🚀 部署与构建

### 构建配置

```javascript
// vite.config.js
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@services': path.resolve(__dirname, './bindings/.../service'),
      '@models': path.resolve(__dirname, './bindings/.../model'),
      '@types': path.resolve(__dirname, './bindings/.../types')
    }
  },
  build: {
    target: 'esnext',
    minify: 'terser',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  }
});
```

### 构建产物

```
dist/
├── assets/           # 静态资源
├── js/              # JavaScript文件
├── css/             # 样式文件
└── index.html       # 入口HTML
```

## 🔧 故障排查

### 常见问题及解决方案

#### 1. 性能问题
```javascript
// 问题：大数据量渲染卡顿
// 解决：使用虚拟滚动
<VirtualDataTable data={largeData} itemHeight={28} />

// 问题：频繁重渲染
// 解决：使用React.memo和useMemo
const OptimizedComponent = React.memo(Component);
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
```

#### 2. 状态管理问题
```javascript
// 问题：状态更新不及时
// 解决：正确使用useCallback和useEffect依赖
const handleUpdate = useCallback(async () => {
  await updateData();
  setRefreshFlag(prev => !prev); // 触发重新加载
}, [updateData]);

useEffect(() => {
  loadData();
}, [refreshFlag, loadData]);
```

#### 3. 样式问题
```css
/* 问题：样式冲突 */
/* 解决：使用CSS变量和作用域 */
.promotion-manager {
  --local-primary-color: #3b82f6;
  --local-bg-color: var(--panel-bg);
}

.promotion-manager .button {
  background-color: var(--local-primary-color);
}
```

## 📚 参考资源

### 技术文档
- [React 18 官方文档](https://react.dev/)
- [Vite 构建工具](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Wails v3 文档](https://wails.io/)

### 项目规范
- [前端编码规范](./docs/coding-standards.md)
- [组件设计指南](./docs/component-guide.md)
- [性能优化指南](./docs/performance-guide.md)

---

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的桌面应用架构
- ✅ 模块化的页面管理系统
- ✅ 高性能虚拟滚动表格
- ✅ 完善的状态管理体系
- ✅ 多窗口支持
- ✅ 批量操作功能
- ✅ 右键菜单系统
- ✅ 视频素材管理
- ✅ 已删除数据管理

---

**维护团队**: 前端开发团队  
**最后更新**: 2025年01月  
**项目状态**: 活跃开发中 🚀 