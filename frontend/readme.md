# 巨量引擎账户管理工具 - 前端项目

## 📋 项目概述

这是一个基于 **Wails v3** 框架开发的桌面应用程序的前端部分，专门用于巨量引擎（字节跳动广告平台）的账户管理。项目采用 **React** + **Vite** 技术栈，并实现了完整的 Windows 原生应用风格界面。

### 🎯 主要功能
- **账户管理** - 巨量引擎账户的增删改查
- **子账户管理** - 子账户的创建和管理
- **项目管理** - 广告项目的管理
- **广告管理** - 广告计划的管理
- **代理管理** - 代理IP的配置和管理（独立窗口）
- **视频加工** - 视频处理工具（独立窗口）

## 🛠️ 技术栈

### 核心框架
- **Vite 5.0.0** - 现代化构建工具
- **Wails v3** - Go + Web 桌面应用框架
- **React 18** - 用户界面框架

### UI组件库
- **Tailwind CSS 4.1** - 样式框架
- **Lucide React** - 图标库
- **React Router DOM** - 路由管理

### 架构特点

#### 1. Windows原生应用风格
- **无响应式设计** - 专为桌面环境优化
- **Windows视觉风格** - 原生滚动条、按钮、输入框等

#### 2. 多窗口架构
- **主窗口** - 集成账户、项目、广告管理功能
- **代理管理窗口** - 独立的代理IP管理界面
- **视频加工窗口** - 独立的视频处理工具

#### 3. 组件化设计
- **自研组件库** - 完整的Windows风格UI组件
- **模块化开发** - 每个功能模块独立开发

### 设计原则
- **固定尺寸** - 标准28px高度，确保一致性
- **即时响应** - 无过渡动画，点击即响应
- **键盘支持** - 完整的键盘操作支持
- **视觉一致** - 统一的Windows视觉风格

## 📁 项目结构

```
frontend/
├── src/
│   ├── components/         # 组件库
│   │   ├── ui/            # 基础UI组件
│   │   │   ├── Button.jsx     # 按钮组件
│   │   │   ├── Input.jsx      # 输入框组件
│   │   │   ├── Table.jsx      # 表格组件
│   │   │   ├── Dialog.jsx     # 对话框组件
│   │   │   └── Tabs.jsx       # 标签页组件
│   │   ├── layout/        # 布局组件
│   │   │   ├── Toolbar.jsx    # 工具栏组件
│   │   │   ├── StatusBar.jsx  # 状态栏组件
│   │   │   └── Panel.jsx      # 面板组件
│   │   └── windows/       # 功能窗口组件
│   │       ├── AccountManager.jsx     # 账户管理
│   │       ├── SubAccountManager.jsx  # 子账户管理
│   │       ├── ProjectManager.jsx     # 项目管理
│   │       └── AdManager.jsx          # 广告管理
│   ├── pages/             # 页面组件
│   │   ├── MainWindow.jsx     # 主窗口
│   │   ├── ProxyManager.jsx   # 代理管理
│   │   └── VideoProcessor.jsx # 视频加工
│   ├── App.jsx            # 主应用组件
│   ├── main.jsx           # 应用入口
│   └── index.css          # 全局样式
├── public/                # 静态资源
├── package.json           # 依赖配置
├── vite.config.js         # Vite配置
└── tailwind.config.js     # Tailwind配置
```

## 🎨 UI组件库

### 基础组件

#### Button 按钮组件
```jsx
<Button variant="primary" size="default" onClick={handleClick}>
  点击按钮
</Button>
```

#### Input 输入框组件
```jsx
<Input 
  type="text" 
  placeholder="请输入内容" 
  value={value} 
  onChange={handleChange} 
/>
```

#### Table 表格组件
```jsx
<Table>
  <Table.Header>
    <Table.Row>
      <Table.HeaderCell>标题</Table.HeaderCell>
    </Table.Row>
  </Table.Header>
  <Table.Body>
    <Table.Row>
      <Table.Cell>内容</Table.Cell>
    </Table.Row>
  </Table.Body>
</Table>
```

### 布局组件

#### Toolbar 工具栏
```jsx
<Toolbar>
  <Toolbar.Group>
    <Button>按钮1</Button>
    <Button>按钮2</Button>
  </Toolbar.Group>
  <Toolbar.Separator />
  <Toolbar.Group>
    <Button>按钮3</Button>
  </Toolbar.Group>
</Toolbar>
```

#### StatusBar 状态栏
```jsx
<StatusBar>
  <StatusBar.Item>状态信息</StatusBar.Item>
  <StatusBar.Separator />
  <StatusBar.Item>版本信息</StatusBar.Item>
</StatusBar>
```

## 🚀 开发指南

### 启动开发服务器
```bash
npm run dev
```

### 构建项目
```bash
# 生产构建
npm run build

# 开发构建
npm run build:dev
```

### 代码检查
```bash
npm run lint
```

### 添加新组件

1. 在相应目录创建组件文件
2. 遵循Windows原生风格设计
3. 使用Tailwind CSS类名
4. 导出组件供其他模块使用

### 样式规范

- 使用 `h-7` (28px) 作为标准控件高度
- 使用 `text-win` 作为标准字体大小
- 使用 `border-win-border` 作为标准边框颜色
- 禁用所有过渡动画

### 注意事项
- 不需要使用API请求架构，直接使用 `@wailsio/runtime`
- 不需要过多路由，一个窗口一个路由
- 专注于桌面应用体验，无需考虑移动端适配