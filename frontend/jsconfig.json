{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@bindings/*": ["./bindings/*"], "@services/*": ["./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service/*"], "@models/*": ["./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model/*"], "@types/*": ["./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/*"], "@reqTypes/*": ["./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType/*"], "@result/*": ["./bindings/gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result/*"]}}, "include": ["src/**/*", "bindings/**/*"], "exclude": ["node_modules", "dist"]}