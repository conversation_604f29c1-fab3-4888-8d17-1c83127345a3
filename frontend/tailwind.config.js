/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Windows 风格颜色
        'win-bg': '#f0f0f0',
        'win-border': '#d4d4d4',
        'win-hover': '#e5f3ff',
        'win-active': '#cce8ff',
        'win-text': '#000000',
        'win-text-secondary': '#666666',
        'win-blue': '#0078d4',
        'win-blue-hover': '#106ebe',
        'win-blue-active': '#005a9e',
        'win-red': '#d13438',
        'win-green': '#107c10',
        'win-orange': '#ff8c00',
      },
      fontFamily: {
        'win': ['Segoe UI', 'Microsoft YaHei', 'sans-serif'],
      },
      height: {
        '7': '28px', // 标准控件高度
      },
      minHeight: {
        '7': '28px',
      },
      fontSize: {
        'win': ['14px', '20px'],
        'win-sm': ['12px', '16px'],
      },
    },
  },
  plugins: [],
} 