package main

import (
	"embed"
	"log"
	"path/filepath"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/event"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/handler"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"

	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/wailsapp/wails/v3/pkg/events"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// 初始化日志系统
	cfg := config.GetConfig()
	if cfg == nil {
		log.Fatal("无法获取配置")
	}

	logConfig := &utils.LogConfig{
		Level:      cfg.Log.Level,
		Format:     cfg.Log.Format,
		Output:     cfg.Log.Output,
		MaxSize:    cfg.Log.MaxSize,
		MaxBackups: cfg.Log.MaxBackups,
		MaxAge:     cfg.Log.MaxAge,
		Compress:   cfg.Log.Compress,
		Directory:  cfg.Log.Directory,
	}

	if err := utils.InitLogger(logConfig); err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}

	// 初始化策略日志系统
	strategyLogConfig := &utils.StrategyLogConfig{
		Directory: filepath.Join(cfg.Log.Directory, "strategy"),
	}
	if err := utils.InitStrategyLogger(strategyLogConfig); err != nil {
		log.Fatalf("初始化策略日志系统失败: %v", err)
	}

	defer utils.Sync()

	utils.LogInfo("应用程序启动", "version", cfg.App.Version, "environment", cfg.App.Environment)

	newApp := NewApp()
	app := application.New(application.Options{
		Name:        "OceanEngineManager",
		Description: "巨量引擎账户管理工具",
		Services: []application.Service{
			application.NewService(newApp.Account),
			application.NewService(newApp.Advertiser),
			application.NewService(newApp.Project),
			application.NewService(newApp.Promotion),
			application.NewService(newApp.Proxy),
			application.NewService(newApp.TextInfo),
			application.NewService(newApp.Ffmpeg),
			application.NewService(newApp.System),
			application.NewService(newApp.Window),
			application.NewService(newApp.Strategy),
			application.NewService(newApp.StrategyBinding),
			application.NewService(newApp.StrategyLog),
			application.NewService(newApp.Log),
			application.NewService(newApp.ClipTask),
			application.NewService(newApp.Config),
			application.NewService(newApp.SensitiveWord),
			application.NewService(newApp.SensitiveWordMonitor),
			application.NewService(newApp.RetargetingTag),
			application.NewService(newApp.DataSyncService),
		},
		Assets: application.AssetOptions{
			Handler: handler.NewFileHandler(assets),
		},
		Mac: application.MacOptions{
			ApplicationShouldTerminateAfterLastWindowClosed: true,
		},
	})

	// 创建窗口
	mainWindow := app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:            "巨量引擎账户管理工具",
		Width:            1600,
		Height:           1100,
		Mac:              application.MacWindow{},
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/",
	})

	proxyWindow := app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:            "代理管理",
		Width:            1080,
		Height:           780,
		Mac:              application.MacWindow{},
		Windows:          application.WindowsWindow{},
		Hidden:           true,
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/#/proxy",
	})

	textWindow := app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:            "添加文字预设",
		Width:            1060,
		Height:           1095,
		Mac:              application.MacWindow{},
		Windows:          application.WindowsWindow{},
		Hidden:           true,
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/#/text",
	})

	clipWindow := app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:            "视频混剪",
		Width:            1400,
		Height:           900,
		Mac:              application.MacWindow{},
		Windows:          application.WindowsWindow{},
		Hidden:           true,
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/#/clip",
	})

	toolsWindow := app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:  "视频加工工具",
		Width:  1080,
		Height: 900,
		Mac:    application.MacWindow{},
		Windows: application.WindowsWindow{
			Menu: nil,
		},
		Hidden:            true,
		BackgroundColour:  application.NewRGB(27, 38, 54),
		URL:               "/#/video",
		EnableDragAndDrop: true,
	})

	// 设置窗口映射
	newApp.Window.SetWindowMap(map[string]*application.WebviewWindow{
		"/":      mainWindow,
		"/proxy": proxyWindow,
		"/video": toolsWindow,
		"/text":  textWindow,
		"/clip":  clipWindow,
	})

	// 注册窗口事件
	mainWindow.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
		app.Quit() // 关闭主窗口时退出应用
	})

	textWindow.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
		e.Cancel()
		textWindow.Hide()
		app.EmitEvent("text_window_close", nil)
	})

	clipWindow.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
		e.Cancel()
		clipWindow.Hide()
		app.EmitEvent("clip_window_close", nil)
	})

	toolsWindow.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
		e.Cancel()
		toolsWindow.Hide()
	})

	proxyWindow.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
		e.Cancel()
		proxyWindow.Hide()
	})

	// 创建菜单
	menu := app.NewMenu()
	toolsMenu := menu.AddSubmenu("工具")

	toolsMenu.Add("代理IP管理").OnClick(func(ctx *application.Context) {
		proxyWindow.Show()
		proxyWindow.Focus()
	})

	toolsMenu.AddSeparator()

	toolsMenu.Add("自动删评管理").OnClick(func(ctx *application.Context) {
		mainWindow.Show()
		mainWindow.Focus()
		// 发送事件通知前端切换到自动删评页面
		app.EmitEvent("navigate_to_sensitive_word_monitor", nil)
	})

	toolsMenu.AddSeparator()

	toolsMenu.Add("视频加工").OnClick(func(ctx *application.Context) {
		toolsWindow.Show()
		toolsWindow.Focus()
	})

	app.SetMenu(menu)

	// 注册文件拖拽事件
	toolsWindow.OnWindowEvent(events.Common.WindowFilesDropped, func(event *application.WindowEvent) {
		files := event.Context().DroppedFiles()
		app.EmitEvent(constant.Runtime_Event_UploadVideo, files)
	})
	// 初始化事件
	event.InitEvents(app)
	newApp.SetApp(app)

	if err := app.Run(); err != nil {
		log.Fatal(err)
	}
}
