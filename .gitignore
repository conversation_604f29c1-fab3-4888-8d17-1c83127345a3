node_modules
.DS_Store

# build files
es/
lib/
dist/
typings/

_site
tmp*
coverage
test-report.html
.idea/
yarn-error.log
*.zip
.history
.stylelintcache

.env.local
.env.*.local

data.db
# lock文件 请根据自身项目或团队需求选择具体的包管理工具 并移除具体的ignore的lock文件
yarn.lock
pnpm-lock.yaml

.env.development
auto-import.d.ts
.eslintrc-auto-import.json
src/components.d.ts
package-lock.json


frontend/node_modules
frontend/dist
frontend/wailsjs
frontend/package.json.md5

#go
**/*_test.go
bin
frontend/bindings
.task
temp

build/windows/nsis/MicrosoftEdgeWebview2Setup.exe

tools
logs
wails_windows_amd64.syso
GEMINI.md