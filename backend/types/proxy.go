package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// ProxyListReq 代理列表请求参数
type ProxyListReq struct {
	Page         int               `json:"page"`
	PageSize     int               `json:"page_size"`
	IpAddress    string            `json:"ip_address"`
	Type         string            `json:"type"`
	Status       int               `json:"status"`
	Remark       string            `json:"remark"`
	OrderBy      string            `json:"order_by"`
	OrderDesc    bool              `json:"order_desc"`
	ExtraFilters map[string]string `json:"extra_filters"`
}

// ProxyListResp 代理列表响应
type ProxyListResp struct {
	Total    int64          `json:"total"`
	List     []*model.Proxy `json:"list"`
	Page     int            `json:"page"`
	PageSize int            `json:"page_size"`
}

// ProxyDetailReq 代理详情请求参数
type ProxyDetailReq struct {
	Id int `json:"id"`
}

// ProxyCreateReq 创建代理请求参数
type ProxyCreateReq struct {
	IpAddress string `json:"ip_address"` // IP地址
	Port      string `json:"port"`       // 端口
	Username  string `json:"username"`   // 用户名
	Password  string `json:"password"`   // 密码
	Type      string `json:"type"`       // 代理类型（http/socks5）
	Status    int    `json:"status"`     // 状态
	Remark    string `json:"remark"`     // 备注
}

// ProxyUpdateReq 更新代理请求参数
type ProxyUpdateReq struct {
	Id        int    `json:"id"`         // 代理ID
	IpAddress string `json:"ip_address"` // IP地址
	Port      string `json:"port"`       // 端口
	Username  string `json:"username"`   // 用户名
	Password  string `json:"password"`   // 密码
	Type      string `json:"type"`       // 代理类型（http/socks5）
	Status    int    `json:"status"`     // 状态
	Remark    string `json:"remark"`     // 备注
}

// ProxyImportReq 导入代理请求参数
type ProxyImportReq struct {
	Content string `json:"content"` // 代理内容（支持多种格式）
	Type    string `json:"type"`    // 代理类型（http/socks5）
}

// ProxyTestReq 测试代理请求参数
type ProxyTestReq struct {
	Id int `json:"id"` // 代理ID
}

// ProxyTestResp 测试代理响应
type ProxyTestResp struct {
	Success   bool   `json:"success"`   // 是否成功
	Message   string `json:"message"`   // 消息
	IP        string `json:"ip"`        // 测试后的IP
	Location  string `json:"location"`  // IP所在位置
	Speed     int    `json:"speed"`     // 速度(ms)
	Anonymous bool   `json:"anonymous"` // 是否匿名
}
