package types

import (
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// StrategyBindingListReq 策略绑定列表请求
type StrategyBindingListReq struct {
	Page         int                    `json:"page"`
	PageSize     int                    `json:"page_size"`
	StrategyID   int64                  `json:"strategy_id"`
	BindingType  string                 `json:"binding_type"`
	BindingID    string                 `json:"binding_id"`
	BindingName  string                 `json:"binding_name"`
	Enabled      *bool                  `json:"enabled"`
	OrderBy      string                 `json:"order_by"`
	OrderDesc    bool                   `json:"order_desc"`
	ExtraFilters map[string]interface{} `json:"extra_filters"`
}

// StrategyBindingListResp 策略绑定列表响应
type StrategyBindingListResp struct {
	Total    int64                    `json:"total"`
	List     []*model.StrategyBinding `json:"list"`
	Page     int                      `json:"page"`
	PageSize int                      `json:"page_size"`
}

// CreateStrategyBindingReq 创建策略绑定请求
type CreateStrategyBindingReq struct {
	StrategyID  int64  `json:"strategy_id"`
	BindingType string `json:"binding_type"` // advertiser/project/promotion
	BindingID   string `json:"binding_id"`
	BindingName string `json:"binding_name"`
	Enabled     bool   `json:"enabled"`
	Priority    int    `json:"priority"`
	Description string `json:"description"`
}

// UpdateStrategyBindingReq 更新策略绑定请求
type UpdateStrategyBindingReq struct {
	ID          int64  `json:"id"`
	StrategyID  int64  `json:"strategy_id"`
	BindingType string `json:"binding_type"`
	BindingID   string `json:"binding_id"`
	BindingName string `json:"binding_name"`
	Enabled     bool   `json:"enabled"`
	Priority    int    `json:"priority"`
	Description string `json:"description"`
}

// ToggleStrategyBindingStatusReq 切换策略绑定状态请求
type ToggleStrategyBindingStatusReq struct {
	ID int64 `json:"id"`
}

// DeleteStrategyBindingReq 删除策略绑定请求
type DeleteStrategyBindingReq struct {
	ID int64 `json:"id"`
}

// BatchDeleteStrategyBindingsReq 批量删除策略绑定请求
type BatchDeleteStrategyBindingsReq struct {
	IDs []int64 `json:"ids"`
}

// StrategyBindingDetailReq 策略绑定详情请求
type StrategyBindingDetailReq struct {
	ID int64 `json:"id"`
}

// StrategyBindingDetailResp 策略绑定详情响应
type StrategyBindingDetailResp struct {
	*model.StrategyBinding
	StrategyName string `json:"strategy_name"`
}

// GetStrategiesByBindingReq 根据绑定实体获取策略列表请求
type GetStrategiesByBindingReq struct {
	BindingType string `json:"binding_type"`
	BindingID   string `json:"binding_id"`
}

// GetStrategiesByBindingResp 根据绑定实体获取策略列表响应
type GetStrategiesByBindingResp struct {
	Strategies []*model.Strategy `json:"strategies"`
}

// BindStrategyToEntityReq 绑定策略到实体请求
type BindStrategyToEntityReq struct {
	StrategyIDs []int64 `json:"strategy_ids"`
	BindingType string  `json:"binding_type"`
	BindingID   string  `json:"binding_id"`
	BindingName string  `json:"binding_name"`
	Priority    int     `json:"priority"`
	Description string  `json:"description"`
}

// BatchBindStrategyToEntitiesReq 批量绑定策略到多个实体请求
type BatchBindStrategyToEntitiesReq struct {
	StrategyIDs []int64           `json:"strategy_ids"`
	BindingType string            `json:"binding_type"`
	Entities    []BatchBindEntity `json:"entities"`
	Priority    int               `json:"priority"`
	Description string            `json:"description"`
}

// BatchBindEntity 批量绑定实体信息
type BatchBindEntity struct {
	BindingID   string `json:"binding_id"`
	BindingName string `json:"binding_name"`
}

// UnbindStrategyFromEntityReq 从实体解绑策略请求
type UnbindStrategyFromEntityReq struct {
	StrategyIDs []int64 `json:"strategy_ids"`
	BindingType string  `json:"binding_type"`
	BindingID   string  `json:"binding_id"`
}

// StrategyWithSource 带来源信息的策略
type StrategyWithSource struct {
	Strategy   *model.Strategy `json:"strategy"`    // 策略信息
	Source     string          `json:"source"`      // 来源类型：direct-直接绑定，inherited-继承
	SourceType string          `json:"source_type"` // 来源实体类型：advertiser/project/promotion
	SourceID   string          `json:"source_id"`   // 来源实体ID
	SourceName string          `json:"source_name"` // 来源实体名称
}

// GetInheritedStrategiesReq 获取继承策略请求
type GetInheritedStrategiesReq struct {
	BindingType  string `json:"binding_type"`  // 绑定类型
	BindingID    string `json:"binding_id"`    // 绑定实体ID
	BindingName  string `json:"binding_name"`  // 绑定实体名称
	AdvertiserID string `json:"advertiser_id"` // 广告主ID（用于查找继承策略）
	ProjectID    string `json:"project_id"`    // 项目ID（用于查找继承策略）
}

// InheritedStrategiesResp 继承策略响应
type InheritedStrategiesResp struct {
	DirectStrategies    []*StrategyWithSource `json:"direct_strategies"`    // 直接绑定的策略
	InheritedStrategies []*StrategyWithSource `json:"inherited_strategies"` // 继承的策略
	AllStrategies       []*StrategyWithSource `json:"all_strategies"`       // 所有策略（直接+继承）
}

// ToModel 转换为数据库模型
func (req *CreateStrategyBindingReq) ToModel() *model.StrategyBinding {
	return &model.StrategyBinding{
		StrategyID:  req.StrategyID,
		BindingType: req.BindingType,
		BindingID:   req.BindingID,
		BindingName: req.BindingName,
		Enabled:     req.Enabled,
		Priority:    req.Priority,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// ToModel 转换为数据库模型
func (req *UpdateStrategyBindingReq) ToModel() *model.StrategyBinding {
	return &model.StrategyBinding{
		ID:          req.ID,
		StrategyID:  req.StrategyID,
		BindingType: req.BindingType,
		BindingID:   req.BindingID,
		BindingName: req.BindingName,
		Enabled:     req.Enabled,
		Priority:    req.Priority,
		Description: req.Description,
		UpdatedAt:   time.Now(),
	}
}
