package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// PromotionListReq 广告推广列表请求参数
type PromotionListReq struct {
	Page          int64             `json:"page"`
	PageSize      int64             `json:"page_size"`
	AccountId     int64             `json:"account_id"`
	PromotionName string            `json:"promotion_name"`
	AdvertiserId  int64             `json:"advertiser_id"`
	ProjectId     string            `json:"project_id"`
	Status        string            `json:"status"`
	Remark        string            `json:"remark"`
	OrderBy       string            `json:"order_by"`
	OrderDesc     bool              `json:"order_desc"`
	ExtraFilters  map[string]string `json:"extra_filters"`
}

// PromotionListResp 广告推广列表响应
type PromotionListResp struct {
	Total    int64              `json:"total"`
	List     []*model.Promotion `json:"list"`
	Page     int64              `json:"page"`
	PageSize int64              `json:"page_size"`
}

// PromotionDetailReq 广告推广详情请求参数
type PromotionDetailReq struct {
	PromotionId int64 `json:"promotion_id"`
}

// PromotionStatResp 广告推广统计响应
type PromotionStatResp struct {
	TotalCount          int64   `json:"total_count"`            // 总数量
	ActiveCount         int64   `json:"active_count"`           // 活跃数量
	TotalBudget         float64 `json:"total_budget"`           // 总预算
	TotalCost           float64 `json:"total_cost"`             // 总消耗
	TotalConversions    int     `json:"total_conversions"`      // 总转化数
	TotalClicks         int     `json:"total_clicks"`           // 总点击数
	TotalImpressions    int     `json:"total_impressions"`      // 总展示数
	AverageCtr          float64 `json:"average_ctr"`            // 平均点击率
	AverageConversion   float64 `json:"average_conversion"`     // 平均转化率
	AverageCostPerClick float64 `json:"average_cost_per_click"` // 平均点击成本
}

// PromotionCreateReq 创建广告推广请求参数
type PromotionCreateReq struct {
	PromotionName     string  `json:"promotion_name"`               // 推广名称
	Status            string  `json:"status"`                       // 状态
	Budget            float64 `json:"budget"`                       // 预算
	AdvertiserId      int64   `json:"advertiser_id"`                // 广告主ID
	AccountId         int64   `json:"account_id"`                   // 账户ID
	ProjectId         *string `json:"project_id,omitempty"`         // 项目ID
	Remark            *string `json:"remark,omitempty"`             // 备注
	AdType            string  `json:"ad_type"`                      // 广告类型
	TargetingSettings *string `json:"targeting_settings,omitempty"` // 定向设置（JSON格式）
	CreativeSettings  *string `json:"creative_settings,omitempty"`  // 创意设置（JSON格式）
}

// DeletePromotionAsyncReq 异步删除广告请求参数
type DeletePromotionAsyncReq struct {
	AccountId    int64    `json:"account_id"`    // 账户ID
	AdvertiserId string   `json:"advertiser_id"` // 广告主ID
	PromotionIds []string `json:"promotion_ids"` // 广告ID列表
}

// BatchDeletePromotionAsyncReq 异步批量删除广告请求参数
type BatchDeletePromotionAsyncReq struct {
	AccountId         int64                                   `json:"account_id"`          // 账户ID
	AccountDetailList []bot.BatchDeletePromotionAccountDetail `json:"account_detail_list"` // 广告详情列表
}

// UpdatePromotionStatusReq 更新广告状态请求参数
type UpdatePromotionStatusReq struct {
	AccountId    int64          `json:"account_id"`    // 账户ID
	AdvertiserId int64          `json:"advertiser_id"` // 广告主ID
	StatusMap    map[string]int `json:"status_map"`    // 状态映射 广告ID -> 状态
}

// UpdatePromotionNameReq 修改广告名称请求参数
type UpdatePromotionNameReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	PromotionId  string `json:"promotion_id"`  // 广告ID
	Name         string `json:"name"`          // 新名称
}

// CopyPromotionReq 复制广告请求参数
type CopyPromotionReq struct {
	AccountId         int64                   `json:"account_id"`          // 账户ID
	AdvertiserId      int64                   `json:"advertiser_id"`       // 广告主ID
	SourcePromotionId string                  `json:"source_promotion_id"` // 源广告ID
	NewPromotionName  string                  `json:"new_promotion_name"`  // 新广告名称
	NewProjectId      string                  `json:"new_project_id"`      // 新项目ID
	VideoMaterialInfo []bot.VideoMaterialInfo `json:"video_material_info"` // 视频素材信息
}

// BatchCopyPromotionReq 批量复制广告请求参数
type BatchCopyPromotionReq struct {
	AccountId          int64    `json:"account_id"`           // 账户ID
	AdvertiserId       int64    `json:"advertiser_id"`        // 广告主ID
	SourcePromotionIds []string `json:"source_promotion_ids"` // 源广告ID列表
	NamePrefix         string   `json:"name_prefix"`          // 新广告名称前缀
	TargetProjectId    string   `json:"target_project_id"`    // 目标项目ID
}

// GetPromotionInfoReq 获取广告详情请求参数
type GetPromotionInfoReq struct {
	AccountId             int64    `json:"account_id"`              // 账户ID
	AdvertiserId          int64    `json:"advertiser_id"`           // 广告主ID
	PromotionIds          []string `json:"promotion_ids"`           // 广告ID列表
	NeedInvisibleMaterial bool     `json:"need_invisible_material"` // 是否需要不可见素材
}

// UpdatePromotionBidReq 修改广告出价请求参数
type UpdatePromotionBidReq struct {
	AccountId    int64             `json:"account_id"`    // 账户ID
	AdvertiserId int64             `json:"advertiser_id"` // 广告主ID
	Bids         map[string]string `json:"bids"`          // 出价映射 广告ID -> 出价金额
}
