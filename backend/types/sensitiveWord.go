package types

import "gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"

// DeletedCommentListReq 被删除评论列表请求
type DeletedCommentListReq struct {
	Page          int    `json:"page"`           // 页码
	PageSize      int    `json:"page_size"`      // 每页大小
	CommentText   string `json:"comment_text"`   // 评论内容搜索
	SensitiveWord string `json:"sensitive_word"` // 敏感词搜索
	AccountName   string `json:"account_name"`   // 账户名称搜索
	ProjectName   string `json:"project_name"`   // 项目名称搜索
	PromotionName string `json:"promotion_name"` // 推广计划名称搜索
}

// DeletedCommentListResp 被删除评论列表响应
type DeletedCommentListResp struct {
	Total    int64                   `json:"total"`     // 总数
	List     []*model.DeletedComment `json:"list"`      // 列表
	Page     int                     `json:"page"`      // 当前页
	PageSize int                     `json:"page_size"` // 每页大小
}

// CheckSensitiveWordResp 检查敏感词响应
type CheckSensitiveWordResp struct {
	ContainsSensitive bool     `json:"contains_sensitive"` // 是否包含敏感词
	SensitiveWords    []string `json:"sensitive_words"`    // 发现的敏感词列表
	Text              string   `json:"text"`               // 检查的文本
}
