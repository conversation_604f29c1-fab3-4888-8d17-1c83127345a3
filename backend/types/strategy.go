package types

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// StrategyCondition 策略条件
type StrategyCondition struct {
	ID        int64       `json:"id"`
	Type      string      `json:"type"`       // cost, ctr, conversion_rate, impressions, clicks, cpc, cpm, ad_status, diagnosis_status, material_label, ad_time_special
	Field     string      `json:"field"`      // 字段名，通常与type相同
	FieldName string      `json:"field_name"` // 字段显示名称，用于日志和界面显示
	Operator  string      `json:"operator"`   // >, <, >=, <=, ==, !=, between, in
	Value     interface{} `json:"value"`      // 支持字符串和数组类型，用于非between操作符
	Values    []string    `json:"values"`     // 用于in操作符的多值
	Min       float64     `json:"min"`        // 用于between操作符的最小值
	Max       float64     `json:"max"`        // 用于between操作符的最大值
	Unit      string      `json:"unit"`       // yuan, percent, count, minutes, enum
}

// StrategyActionConfig 策略动作配置
type StrategyActionConfig struct {
	Recipients      []string `json:"recipients"`        // 接收人列表
	Message         string   `json:"message"`           // 自定义消息
	WebhookURL      string   `json:"webhook_url"`       // 企业微信机器人URL
	CopyCount       int      `json:"copy_count"`        // 复制数量
	BidAdjustment   int      `json:"bid_adjustment"`    // 出价调整幅度(%)
	TargetProjectID string   `json:"target_project_id"` // 目标项目ID
}

// StrategyAction 策略动作
type StrategyAction struct {
	ID      int64                `json:"id"`
	Type    string               `json:"type"`    // sms, email, wechat_work, pause_ad, close_ad, copy_ad, adjust_bid
	Enabled bool                 `json:"enabled"` // 是否启用
	Config  StrategyActionConfig `json:"config"`
}

// StrategyListReq 策略列表请求
type StrategyListReq struct {
	Page         int                    `json:"page"`
	PageSize     int                    `json:"page_size"`
	Name         string                 `json:"name"`
	Enabled      *bool                  `json:"enabled"`
	LogicType    string                 `json:"logic_type"`
	OrderBy      string                 `json:"order_by"`
	OrderDesc    bool                   `json:"order_desc"`
	ExtraFilters map[string]interface{} `json:"extra_filters"`
}

// StrategyListResp 策略列表响应
type StrategyListResp struct {
	Total    int64             `json:"total"`
	List     []*model.Strategy `json:"list"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
}

// CreateStrategyReq 创建策略请求
type CreateStrategyReq struct {
	Name             string              `json:"name"`
	Description      string              `json:"description"`
	Type             string              `json:"type"`
	Enabled          bool                `json:"enabled"`
	LogicType        string              `json:"logic_type"`
	MonitorFrequency int                 `json:"monitor_frequency"`
	Conditions       []StrategyCondition `json:"conditions"`
	Actions          []StrategyAction    `json:"actions"`
}

// UpdateStrategyReq 更新策略请求
type UpdateStrategyReq struct {
	ID               int64               `json:"id"`
	Name             string              `json:"name"`
	Description      string              `json:"description"`
	Type             string              `json:"type"`
	Enabled          bool                `json:"enabled"`
	LogicType        string              `json:"logic_type"`
	MonitorFrequency int                 `json:"monitor_frequency"`
	Conditions       []StrategyCondition `json:"conditions"`
	Actions          []StrategyAction    `json:"actions"`
}

// ToggleStrategyStatusReq 切换策略状态请求
type ToggleStrategyStatusReq struct {
	ID int64 `json:"id"`
}

// DeleteStrategyReq 删除策略请求
type DeleteStrategyReq struct {
	ID int64 `json:"id"`
}

// BatchDeleteStrategiesReq 批量删除策略请求
type BatchDeleteStrategiesReq struct {
	IDs []int64 `json:"ids"`
}

// StrategyDetailReq 策略详情请求
type StrategyDetailReq struct {
	ID int64 `json:"id"`
}

// StrategyDetailResp 策略详情响应
type StrategyDetailResp struct {
	*model.Strategy
	ParsedConditions []StrategyCondition `json:"parsed_conditions"`
	ParsedActions    []StrategyAction    `json:"parsed_actions"`
}

// ToModel 转换为数据库模型
func (req *CreateStrategyReq) ToModel() (*model.Strategy, error) {
	// 使用json.Encoder避免Unicode转义
	var conditionsBuffer, actionsBuffer strings.Builder

	// 序列化条件
	conditionsEncoder := json.NewEncoder(&conditionsBuffer)
	conditionsEncoder.SetEscapeHTML(false)
	conditionsEncoder.SetIndent("", "")
	if err := conditionsEncoder.Encode(req.Conditions); err != nil {
		return nil, err
	}
	conditionsJSON := strings.TrimSpace(conditionsBuffer.String())

	// 序列化动作
	actionsEncoder := json.NewEncoder(&actionsBuffer)
	actionsEncoder.SetEscapeHTML(false)
	actionsEncoder.SetIndent("", "")
	if err := actionsEncoder.Encode(req.Actions); err != nil {
		return nil, err
	}
	actionsJSON := strings.TrimSpace(actionsBuffer.String())

	// 计算启用的动作数量
	enabledActionsCount := 0
	for _, action := range req.Actions {
		if action.Enabled {
			enabledActionsCount++
		}
	}

	return &model.Strategy{
		Name:             req.Name,
		Description:      req.Description,
		Type:             req.Type,
		Enabled:          req.Enabled,
		LogicType:        req.LogicType,
		MonitorFrequency: req.MonitorFrequency,
		ConditionsCount:  len(req.Conditions),
		ActionsCount:     enabledActionsCount,
		Conditions:       conditionsJSON,
		Actions:          actionsJSON,
		TriggerCount:     0,
		LastTriggered:    nil,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}, nil
}

// ToModel 转换为数据库模型
func (req *UpdateStrategyReq) ToModel() (*model.Strategy, error) {
	// 使用json.Encoder避免Unicode转义
	var conditionsBuffer, actionsBuffer strings.Builder

	// 序列化条件
	conditionsEncoder := json.NewEncoder(&conditionsBuffer)
	conditionsEncoder.SetEscapeHTML(false)
	conditionsEncoder.SetIndent("", "")
	if err := conditionsEncoder.Encode(req.Conditions); err != nil {
		return nil, err
	}
	conditionsJSON := strings.TrimSpace(conditionsBuffer.String())

	// 序列化动作
	actionsEncoder := json.NewEncoder(&actionsBuffer)
	actionsEncoder.SetEscapeHTML(false)
	actionsEncoder.SetIndent("", "")
	if err := actionsEncoder.Encode(req.Actions); err != nil {
		return nil, err
	}
	actionsJSON := strings.TrimSpace(actionsBuffer.String())

	// 计算启用的动作数量
	enabledActionsCount := 0
	for _, action := range req.Actions {
		if action.Enabled {
			enabledActionsCount++
		}
	}

	return &model.Strategy{
		ID:               req.ID,
		Name:             req.Name,
		Description:      req.Description,
		Type:             req.Type,
		Enabled:          req.Enabled,
		LogicType:        req.LogicType,
		MonitorFrequency: req.MonitorFrequency,
		ConditionsCount:  len(req.Conditions),
		ActionsCount:     enabledActionsCount,
		Conditions:       conditionsJSON,
		Actions:          actionsJSON,
		UpdatedAt:        time.Now(),
	}, nil
}

// StrategyToDetailResp 将策略模型转换为详情响应
func StrategyToDetailResp(s *model.Strategy) (*StrategyDetailResp, error) {
	var conditions []StrategyCondition
	var actions []StrategyAction

	// 解析条件JSON
	if len(s.Conditions) > 0 {
		if err := json.Unmarshal([]byte(s.Conditions), &conditions); err != nil {
			return nil, err
		}

		// between 操作符已经使用 min 和 max 字段，无需转换
	}

	// 解析动作JSON
	if len(s.Actions) > 0 {
		if err := json.Unmarshal([]byte(s.Actions), &actions); err != nil {
			return nil, err
		}
	}

	return &StrategyDetailResp{
		Strategy:         s,
		ParsedConditions: conditions,
		ParsedActions:    actions,
	}, nil
}
