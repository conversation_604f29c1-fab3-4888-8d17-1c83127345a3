package types

import "gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"

// AccountListReq 账户列表请求参数
type AccountListReq struct {
	Page         int               `json:"page"`
	PageSize     int               `json:"page_size"`
	AccountName  string            `json:"account_name"`
	LoginType    string            `json:"login_type"`
	Remark       string            `json:"remark"`
	OrderBy      string            `json:"order_by"`
	OrderDesc    bool              `json:"order_desc"`
	ExtraFilters map[string]string `json:"extra_filters"`
}

// AccountListResp 账户列表响应
type AccountListResp struct {
	Total    int64            `json:"total"`
	List     []*model.Account `json:"list"`
	Page     int              `json:"page"`
	PageSize int              `json:"page_size"`
}

// AdvertiserReq 广告主列表请求参数
type AdvertiserReq struct {
	AccountId   int64  `json:"account_id"`   // 主账户ID
	Keyword     string `json:"keyword"`      // 子账户Id
	ProjectId   string `json:"project_id"`   // 项目ID
	PromotionId string `json:"promotion_id"` // 广告ID
	Page        int64  `json:"page"`         // 页码
	PageSize    int64  `json:"page_size"`    // 每页大小
}

// ConcernAddSubAccountReq 收藏子账户请求参数
type ConcernAddSubAccountReq struct {
	AccountId    int64 `json:"account_id"`    // 主账户ID
	AdvertiserId int64 `json:"advertiser_id"` // 子账户ID
}

// ConcernRemoveSubAccountReq 取消收藏子账户请求参数
type ConcernRemoveSubAccountReq struct {
	AccountId    int64 `json:"account_id"`    // 主账户ID
	AdvertiserId int64 `json:"advertiser_id"` // 子账户ID
}
