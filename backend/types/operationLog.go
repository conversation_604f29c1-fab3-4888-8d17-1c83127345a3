package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// OperationLogListResp 操作日志列表响应
type OperationLogListResp struct {
	Total    int64                 `json:"total"`
	List     []*model.OperationLog `json:"list"`
	Page     int                   `json:"page"`
	PageSize int                   `json:"page_size"`
}

// OperationLogCreateReq 创建操作日志请求参数
type OperationLogCreateReq struct {
	AccountName   string `json:"account_name"`   // 账户名称
	AccountId     int64  `json:"account_id"`     // 账户ID（巨量引擎平台ID）
	Module        string `json:"module"`         // 模块名
	OperationType string `json:"operation_type"` // 操作类型
	Operation     string `json:"operation"`      // 操作名称
	Description   string `json:"description"`    // 操作描述
}

// OperationLogStatsReq 操作日志统计请求参数
type OperationLogStatsReq struct {
	StartTime string `json:"start_time"` // 开始时间
	EndTime   string `json:"end_time"`   // 结束时间
}
