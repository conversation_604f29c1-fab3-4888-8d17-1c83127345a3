package types

import "time"

// ApiRecordListReq API记录列表请求
type ApiRecordListReq struct {
	Page       int    `json:"page" form:"page"`             // 页码
	PageSize   int    `json:"pageSize" form:"pageSize"`     // 每页大小
	Url        string `json:"url" form:"url"`               // URL筛选
	Method     string `json:"method" form:"method"`         // 请求方法筛选
	Source     string `json:"source" form:"source"`         // 来源筛选
	Success    *bool  `json:"success" form:"success"`       // 成功状态筛选
	StatusCode *int   `json:"statusCode" form:"statusCode"` // 状态码筛选
	StartTime  string `json:"startTime" form:"startTime"`   // 开始时间
	EndTime    string `json:"endTime" form:"endTime"`       // 结束时间
}

// ApiRecordListResp API记录列表响应
type ApiRecordListResp struct {
	List     []ApiRecordItem `json:"list"`     // 记录列表
	Total    int64           `json:"total"`    // 总数
	Page     int             `json:"page"`     // 当前页
	PageSize int             `json:"pageSize"` // 每页大小
}

// ApiRecordItem API记录项
type ApiRecordItem struct {
	Id           int64     `json:"id"`           // 记录ID
	Url          string    `json:"url"`          // 请求URL
	Query        string    `json:"query"`        // 查询参数
	Method       string    `json:"method"`       // 请求方法
	ReqBody      string    `json:"reqBody"`      // 请求体
	ReqHeader    string    `json:"reqHeader"`    // 请求头
	RespHeader   string    `json:"respHeader"`   // 响应头
	RespBody     string    `json:"respBody"`     // 响应体
	StatusCode   int       `json:"statusCode"`   // HTTP状态码
	ResponseTime int64     `json:"responseTime"` // 响应时间（毫秒）
	Success      bool      `json:"success"`      // 请求是否成功
	ErrorMsg     string    `json:"errorMsg"`     // 错误信息
	Source       string    `json:"source"`       // 来源标识
	CreateTime   time.Time `json:"createTime"`   // 创建时间
	UpdateTime   time.Time `json:"updateTime"`   // 更新时间
}

// ApiRecordCreateReq 创建API记录请求
type ApiRecordCreateReq struct {
	Url          string `json:"url" validate:"required"`    // 请求URL（必需）
	Query        string `json:"query"`                      // 查询参数
	Method       string `json:"method" validate:"required"` // 请求方法（必需）
	ReqBody      string `json:"reqBody"`                    // 请求体
	ReqHeader    string `json:"reqHeader"`                  // 请求头
	RespHeader   string `json:"respHeader"`                 // 响应头
	RespBody     string `json:"respBody"`                   // 响应体
	StatusCode   int    `json:"statusCode"`                 // HTTP状态码
	ResponseTime int64  `json:"responseTime"`               // 响应时间（毫秒）
	Success      bool   `json:"success"`                    // 请求是否成功
	ErrorMsg     string `json:"errorMsg"`                   // 错误信息
	Source       string `json:"source"`                     // 来源标识
}

// ApiRecordBatchCreateReq 批量创建API记录请求
type ApiRecordBatchCreateReq struct {
	Records []ApiRecordCreateReq `json:"records" validate:"required,dive"`
}

// ApiRecordStatResp API记录统计响应
type ApiRecordStatResp struct {
	TotalCount   int64   `json:"totalCount"`   // 总记录数
	SuccessCount int64   `json:"successCount"` // 成功记录数
	FailedCount  int64   `json:"failedCount"`  // 失败记录数
	SuccessRate  float64 `json:"successRate"`  // 成功率
}

// ApiRecordStatBySourceResp 按来源统计API记录响应
type ApiRecordStatBySourceResp struct {
	TotalCount      int64   `json:"totalCount"`      // 总记录数
	SuccessCount    int64   `json:"successCount"`    // 成功记录数
	FailedCount     int64   `json:"failedCount"`     // 失败记录数
	SuccessRate     float64 `json:"successRate"`     // 成功率
	AvgResponseTime float64 `json:"avgResponseTime"` // 平均响应时间
}

// ApiRecordStatByUrlResp 按URL统计API记录响应
type ApiRecordStatByUrlResp struct {
	TotalCount      int64   `json:"totalCount"`      // 总记录数
	SuccessCount    int64   `json:"successCount"`    // 成功记录数
	FailedCount     int64   `json:"failedCount"`     // 失败记录数
	SuccessRate     float64 `json:"successRate"`     // 成功率
	AvgResponseTime float64 `json:"avgResponseTime"` // 平均响应时间
}

// ApiRecordTopUrlItem 热门URL项
type ApiRecordTopUrlItem struct {
	Url             string  `json:"url"`             // URL
	Count           int64   `json:"count"`           // 调用次数
	SuccessCount    int64   `json:"successCount"`    // 成功次数
	AvgResponseTime float64 `json:"avgResponseTime"` // 平均响应时间
}

// ApiRecordTopUrlsResp 热门URL列表响应
type ApiRecordTopUrlsResp struct {
	List []ApiRecordTopUrlItem `json:"list"`
}

// ApiRecordErrorItem 错误统计项
type ApiRecordErrorItem struct {
	ErrorMsg string `json:"errorMsg"` // 错误信息
	Count    int64  `json:"count"`    // 错误次数
}

// ApiRecordErrorStatsResp 错误统计响应
type ApiRecordErrorStatsResp struct {
	List []ApiRecordErrorItem `json:"list"`
}

// ApiRecordTimeRangeReq 时间范围查询请求
type ApiRecordTimeRangeReq struct {
	StartTime string                 `json:"startTime" validate:"required"` // 开始时间
	EndTime   string                 `json:"endTime" validate:"required"`   // 结束时间
	Source    string                 `json:"source"`                        // 来源筛选
	Method    string                 `json:"method"`                        // 方法筛选
	Success   *bool                  `json:"success"`                       // 成功状态筛选
	Extra     map[string]interface{} `json:"extra"`                         // 额外筛选条件
}

// ApiRecordDeleteReq 删除API记录请求
type ApiRecordDeleteReq struct {
	Ids []int64 `json:"ids" validate:"required,min=1"` // 要删除的记录ID列表
}

// ApiRecordCleanupReq 清理API记录请求
type ApiRecordCleanupReq struct {
	BeforeTime string `json:"beforeTime" validate:"required"` // 删除此时间之前的记录
}

// CommonResp 通用响应
type CommonResp struct {
	Code int         `json:"code"` // 响应码
	Msg  string      `json:"msg"`  // 响应信息
	Data interface{} `json:"data"` // 响应数据
}

// ApiRecordAnalyticsReq API记录分析请求
type ApiRecordAnalyticsReq struct {
	TimeRange string `json:"timeRange" form:"timeRange"` // 时间范围: today, week, month, custom
	StartTime string `json:"startTime" form:"startTime"` // 自定义开始时间
	EndTime   string `json:"endTime" form:"endTime"`     // 自定义结束时间
	GroupBy   string `json:"groupBy" form:"groupBy"`     // 分组维度: hour, day, week, month
	Source    string `json:"source" form:"source"`       // 来源筛选
	Url       string `json:"url" form:"url"`             // URL筛选
}

// ApiRecordAnalyticsItem 分析数据项
type ApiRecordAnalyticsItem struct {
	Time            string  `json:"time"`            // 时间点
	TotalCount      int64   `json:"totalCount"`      // 总请求数
	SuccessCount    int64   `json:"successCount"`    // 成功请求数
	FailedCount     int64   `json:"failedCount"`     // 失败请求数
	SuccessRate     float64 `json:"successRate"`     // 成功率
	AvgResponseTime float64 `json:"avgResponseTime"` // 平均响应时间
}

// ApiRecordAnalyticsResp API记录分析响应
type ApiRecordAnalyticsResp struct {
	Summary ApiRecordStatResp        `json:"summary"` // 总览统计
	Charts  []ApiRecordAnalyticsItem `json:"charts"`  // 图表数据
}
