package types

import (
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// ActionExecutionDetail 动作执行详情
type ActionExecutionDetail struct {
	Index        int       `json:"index"`         // 动作索引
	Type         string    `json:"type"`          // 动作类型
	Description  string    `json:"description"`   // 动作描述
	Success      bool      `json:"success"`       // 是否成功
	ErrorMessage string    `json:"error_message"` // 错误信息
	StartTime    time.Time `json:"start_time"`    // 开始时间
	EndTime      time.Time `json:"end_time"`      // 结束时间
	Duration     int64     `json:"duration"`      // 执行耗时(毫秒)
}

// StrategyLogListReq 策略日志列表请求
type StrategyLogListReq struct {
	Page            int       `json:"page"`             // 页码
	PageSize        int       `json:"page_size"`        // 每页大小
	StrategyName    string    `json:"strategy_name"`    // 策略名称
	TargetType      string    `json:"target_type"`      // 目标类型
	TargetID        string    `json:"target_id"`        // 目标ID
	ConditionMet    *bool     `json:"condition_met"`    // 条件是否满足
	ActionCompleted *bool     `json:"action_completed"` // 动作是否完成
	StartTime       time.Time `json:"start_time"`       // 开始时间
	EndTime         time.Time `json:"end_time"`         // 结束时间
}

// StrategyLogListResp 策略日志列表响应
type StrategyLogListResp struct {
	List  []*model.StrategyLog `json:"list"`  // 日志列表
	Total int64                `json:"total"` // 总数
}

// StrategyLogDetailReq 策略日志详情请求
type StrategyLogDetailReq struct {
	ID int64 `json:"id"` // 日志ID
}

// StrategyLogDetailResp 策略日志详情响应
type StrategyLogDetailResp struct {
	StrategyLog *model.StrategyLog `json:"strategy_log"` // 策略日志
}

// StrategyLogStatsReq 策略日志统计请求
type StrategyLogStatsReq struct {
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
}

// StrategyLogStatsResp 策略日志统计响应
type StrategyLogStatsResp struct {
	TotalCount           int64     `json:"total_count"`             // 总执行次数
	SuccessCount         int64     `json:"success_count"`           // 成功次数
	ErrorCount           int64     `json:"error_count"`             // 失败次数
	ConditionMetCount    int64     `json:"condition_met_count"`     // 条件满足次数
	ConditionNotMetCount int64     `json:"condition_not_met_count"` // 条件不满足次数
	StartTime            time.Time `json:"start_time"`              // 开始时间
	EndTime              time.Time `json:"end_time"`                // 结束时间
}

// StrategyLogCreateReq 创建策略日志请求
type StrategyLogCreateReq struct {
	StrategyID      int64     `json:"strategy_id"`      // 策略ID
	StrategyName    string    `json:"strategy_name"`    // 策略名称
	TargetType      string    `json:"target_type"`      // 目标类型
	TargetID        string    `json:"target_id"`        // 目标ID
	TargetName      string    `json:"target_name"`      // 目标名称
	Conditions      string    `json:"conditions"`       // 执行条件
	ConditionMet    bool      `json:"condition_met"`    // 条件是否满足
	Actions         string    `json:"actions"`          // 执行动作
	ActionDetails   string    `json:"action_details"`   // 动作执行详情
	ActionCompleted bool      `json:"action_completed"` // 动作是否完成
	ErrorMessage    string    `json:"error_message"`    // 错误信息
	ExecutionTime   time.Time `json:"execution_time"`   // 执行时间
	Duration        int64     `json:"duration"`         // 执行耗时
}

// ToModel 转换为数据库模型
func (req *StrategyLogCreateReq) ToModel() *model.StrategyLog {
	return &model.StrategyLog{
		StrategyID:      req.StrategyID,
		StrategyName:    req.StrategyName,
		TargetType:      req.TargetType,
		TargetID:        req.TargetID,
		TargetName:      req.TargetName,
		Conditions:      req.Conditions,
		ConditionMet:    req.ConditionMet,
		Actions:         req.Actions,
		ActionDetails:   req.ActionDetails,
		ActionCompleted: req.ActionCompleted,
		ErrorMessage:    req.ErrorMessage,
		ExecutionTime:   req.ExecutionTime,
		Duration:        req.Duration,
	}
}
