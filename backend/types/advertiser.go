package types

// GetMaterialVideoInfoReq 获取视频素材信息请求参数
type GetMaterialVideoInfoReq struct {
	AccountId    int64    `json:"account_id"`
	AdvertiserId int64    `json:"advertiser_id"`
	Vids         []string `json:"vids"`
}

// DownloadResourceReq 下载资源请求参数
type DownloadResourceReq struct {
	AccountId int64  `json:"account_id"` // 账户ID
	Url       string `json:"url"`        // 资源URL
}

// GetMaterialTitleReq 获取素材标题请求参数
type GetMaterialTitleReq struct {
	AccountId           int64                    `json:"account_id"`            // 账户ID
	AdvertiserId        int64                    `json:"advertiser_id"`         // 广告主ID
	VideoMaterials      []VideoMaterialInfo      `json:"video_materials"`       // 视频素材列表
	ImageMaterials      []ImageMaterialInfo      `json:"image_materials"`       // 图片素材列表
	AwemePhotoMaterials []AwemePhotoMaterialInfo `json:"aweme_photo_materials"` // 抖音图片素材列表
}

// VideoMaterialInfo 视频素材信息
type VideoMaterialInfo struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}

// ImageMaterialInfo 图片素材信息
type ImageMaterialInfo struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}

// AwemePhotoMaterialInfo 抖音图片素材信息
type AwemePhotoMaterialInfo struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}
