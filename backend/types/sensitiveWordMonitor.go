package types

// SensitiveWordMonitorStatus 敏感词监控服务状态
type SensitiveWordMonitorStatus struct {
	Running        bool                   `json:"running"`         // 是否运行中
	Interval       string                 `json:"interval"`        // 监控间隔
	DeleteStats    map[string]interface{} `json:"delete_stats"`    // 删除统计信息
	ExecutionState map[string]interface{} `json:"execution_state"` // 执行状态信息
	SensitiveWords map[string]interface{} `json:"sensitive_words"` // 敏感词配置状态
}

// SensitiveWordMonitorConfig 敏感词监控配置
type SensitiveWordMonitorConfig struct {
	Interval int64 `json:"interval"` // 监控间隔（秒）
}

// SensitiveWordMonitorResult 敏感词监控结果
type SensitiveWordMonitorResult struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 结果消息
}

// SensitiveWordCheckResult 敏感词检查结果
type SensitiveWordCheckResult struct {
	CommentId      string   `json:"comment_id"`      // 评论ID
	CommentText    string   `json:"comment_text"`    // 评论内容
	SensitiveWords []string `json:"sensitive_words"` // 发现的敏感词
	ProjectName    string   `json:"project_name"`    // 项目名称
	PromotionName  string   `json:"promotion_name"`  // 推广计划名称
	AccountName    string   `json:"account_name"`    // 账户名称
	CheckTime      string   `json:"check_time"`      // 检查时间
	Action         string   `json:"action"`          // 执行动作
}
