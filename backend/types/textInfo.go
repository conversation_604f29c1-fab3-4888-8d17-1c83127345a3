package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// TextInfoListReq 文本信息列表请求参数
type TextInfoListReq struct {
	Page         int               `json:"page"`
	PageSize     int               `json:"page_size"`
	Text         string            `json:"text"`
	FontFile     string            `json:"font_file"`
	FontSize     int64             `json:"font_size"`
	FontColor    string            `json:"font_color"`
	Box          int64             `json:"box"`
	Remark       string            `json:"remark"`
	OrderBy      string            `json:"order_by"`
	OrderDesc    bool              `json:"order_desc"`
	ExtraFilters map[string]string `json:"extra_filters"`
}

// TextInfoListResp 文本信息列表响应
type TextInfoListResp struct {
	Total    int64             `json:"total"`
	List     []*model.TextInfo `json:"list"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
}

// TextInfoDetailReq 文本信息详情请求参数
type TextInfoDetailReq struct {
	Id int64 `json:"id"`
}

// TextInfoCreateReq 创建文本信息请求参数
type TextInfoCreateReq struct {
	Text        string  `json:"text"`             // 文本内容
	FontFile    string  `json:"font_file"`        // 字体文件
	FontSize    int64   `json:"font_size"`        // 字体大小
	FontColor   string  `json:"font_color"`       // 字体颜色
	PositionX   float64 `json:"position_x"`       // X轴位置
	PositionY   float64 `json:"position_y"`       // Y轴位置
	Box         int64   `json:"box"`              // 是否启用背景框（0、禁用 1、启用）
	BoxColor    string  `json:"box_color"`        // 背景框颜色
	BoxBorderRw int64   `json:"box_border_rw"`    // 背景框宽度
	ShadowWx    int64   `json:"shadow_wx"`        // 文本阴影的水平偏移
	ShadowWy    int64   `json:"shadow_wy"`        // 文本阴影的垂直偏移
	ShadowColor string  `json:"shadow_color"`     // 文本阴影颜色
	BorderRw    int64   `json:"border_rw"`        // 文本框边框宽度
	BorderColor string  `json:"border_color"`     // 文本框边框颜色
	Remark      *string `json:"remark,omitempty"` // 备注
}

// TextInfoStatResp 文本信息统计响应
type TextInfoStatResp struct {
	TotalCount int64 `json:"total_count"` // 总数量
}
