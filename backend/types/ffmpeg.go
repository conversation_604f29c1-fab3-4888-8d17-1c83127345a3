package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// SetVideoCoverReq 设置视频封面请求参数
type SetVideoCoverReq struct {
	VideoFile string `json:"video_file"` // 视频文件路径
	CoverFile string `json:"cover_file"` // 封面文件路径
}

// SetTextInfoReq 设置视频文本信息请求参数
type SetTextInfoReq struct {
	VideoFile     string            `json:"video_file"`      // 视频文件路径
	TextInfoList  []*model.TextInfo `json:"text_info"`       // 文本信息
	IsOriginSound bool              `json:"is_origin_sound"` // 是否保留原声音
	Width         int               `json:"width"`           // 视频宽度
	Height        int               `json:"height"`          // 视频高度
	ScaleMode     string            `json:"scale_mode"`      // 缩放模式
}

// VideoAspectRatio 视频比例选项
type VideoAspectRatio string

const (
	AspectRatioOriginal VideoAspectRatio = "original" // 保持原比例
	AspectRatio9_16     VideoAspectRatio = "9:16"     // 9:16比例
	AspectRatio16_9     VideoAspectRatio = "16:9"     // 16:9比例
)

// VideoResolution 视频分辨率选项
type VideoResolution struct {
	Width  int    `json:"width"`  // 宽度
	Height int    `json:"height"` // 高度
	Name   string `json:"name"`   // 分辨率名称
}

// VideoEffect 视频效果选项
type VideoEffect string

const (
	EffectCropScale  VideoEffect = "crop_scale" // 微小的裁剪或缩放
	EffectRotation   VideoEffect = "rotation"   // 轻微的旋转
	EffectBrightness VideoEffect = "brightness" // 细微的亮度或对比度调整
	EffectColor      VideoEffect = "color"      // 轻度的色彩变化
	EffectSpeed      VideoEffect = "speed"      // 微小的速度变化
	EffectSharpen    VideoEffect = "sharpen"    // 轻微的锐化或模糊
	EffectVolume     VideoEffect = "volume"     // 轻微的音量变化
	EffectBitrate    VideoEffect = "bitrate"    // 轻微的码率变化
)

// VideoVariationReq 视频裂变请求参数
type VideoVariationReq struct {
	VideoFile     string           `json:"video_file"`      // 视频文件路径
	Count         int              `json:"count"`           // 生成视频数量
	AspectRatio   VideoAspectRatio `json:"aspect_ratio"`    // 视频比例
	Resolution    VideoResolution  `json:"resolution"`      // 视频分辨率
	Effects       []VideoEffect    `json:"effects"`         // 视频效果列表
	OutputDir     string           `json:"output_dir"`      // 输出目录
	IsOriginSound bool             `json:"is_origin_sound"` // 是否保留原声音
}

// VideoVariationResp 视频裂变响应
type VideoVariationResp struct {
	Success     bool     `json:"success"`      // 是否成功
	Message     string   `json:"message"`      // 提示信息
	VideoPaths  []string `json:"video_paths"`  // 生成的视频路径列表
	FailedPaths []string `json:"failed_paths"` // 生成失败的视频路径列表
}

// 预定义的分辨率列表
var PredefinedResolutions = []VideoResolution{
	{Width: 0, Height: 0, Name: "原始分辨率"},
	{Width: 1080, Height: 1920, Name: "1080x1920 (9:16)"},
	{Width: 1920, Height: 1080, Name: "1920x1080 (16:9)"},
	{Width: 720, Height: 1280, Name: "720x1280 (9:16)"},
	{Width: 1280, Height: 720, Name: "1280x720 (16:9)"},
	{Width: 540, Height: 960, Name: "540x960 (9:16)"},
	{Width: 960, Height: 540, Name: "960x540 (16:9)"},
	{Width: 480, Height: 854, Name: "480x854 (9:16)"},
	{Width: 854, Height: 480, Name: "854x480 (16:9)"},
}

type BatchGetVideoInfoReq struct {
	VideoFileList []string `json:"video_file_list"` // 视频文件路径列表
}

type VideoVariationProgress struct {
	VideoFile string  `json:"video_file"` // 视频文件路径
	Progress  float64 `json:"progress"`   // 进度百分比
	Count     int     `json:"count"`      // 总共生成的视频数量
	GenCount  int     `json:"gen_count"`  // 已生成的视频数量
	Message   string  `json:"message"`    // 提示信息
	VideoPath string  `json:"video_path"` // 生成的视频路径
}
