package types

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
)

// ProjectListReq 项目列表请求参数
type ProjectListReq struct {
	Page         int64             `json:"page"`
	PageSize     int64             `json:"page_size"`
	AccountId    int64             `json:"account_id"`
	ProjectName  string            `json:"project_name"`
	Status       string            `json:"status"`
	Remark       string            `json:"remark"`
	OrderBy      string            `json:"order_by"`
	OrderDesc    bool              `json:"order_desc"`
	ExtraFilters map[string]string `json:"extra_filters"`
}

// ProjectListResp 项目列表响应
type ProjectListResp struct {
	Total    int64            `json:"total"`
	List     []*model.Project `json:"list"`
	Page     int64            `json:"page"`
	PageSize int64            `json:"page_size"`
}

// ProjectDetailReq 项目详情请求参数
type ProjectDetailReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserID int64  `json:"advertiser_id"` // 广告主ID
	ProjectID    string `json:"project_id"`    // 项目ID
}

// DeleteProjectAsyncReq 异步删除项目请求参数
type DeleteProjectAsyncReq struct {
	AccountId    int64    `json:"account_id"`    // 账户ID
	AdvertiserId string   `json:"advertiser_id"` // 广告主ID
	ProjectIds   []string `json:"project_ids"`   // 项目ID列表
}

// BatchDeleteProjectAsyncReq 异步批量删除项目请求参数
type BatchDeleteProjectAsyncReq struct {
	AccountId         int64                                 `json:"account_id"`          // 账户ID
	AccountDetailList []bot.BatchDeleteProjectAccountDetail `json:"account_detail_list"` // 项目详情列表
}

// UpdateProjectStatusReq 更新项目状态请求参数
type UpdateProjectStatusReq struct {
	AccountId    int64          `json:"account_id"`    // 账户ID
	AdvertiserId int64          `json:"advertiser_id"` // 广告主ID
	StatusMap    map[string]int `json:"status_map"`    // 状态映射 项目ID -> 状态
}

// UpdateProjectNameReq 修改项目名称请求参数
type UpdateProjectNameReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	ProjectId    string `json:"project_id"`    // 项目ID
	Name         string `json:"name"`          // 新名称
}

// CopyProjectReq 复制项目请求参数
type CopyProjectReq struct {
	AccountId           int64                  `json:"account_id"`            // 账户ID
	SourceProjectID     string                 `json:"source_project_id"`     // 源项目ID
	SourceAdvertiserID  int64                  `json:"source_advertiser_id"`  // 源项目广告主ID（用于获取项目详情）
	AdvertiserID        int64                  `json:"advertiser_id"`         // 目标广告主ID（新项目所属的广告主）
	NewProjectName      string                 `json:"new_project_name"`      // 新项目名称（可选，不传则自动生成）
	CopyCount           int                    `json:"copy_count"`            // 复制数量（默认为1）
	OverrideParams      map[string]interface{} `json:"override_params"`       // 需要覆盖的参数
	SourceProjectDetail *bot.ProjectInfoData   `json:"source_project_detail"` // 源项目详情数据（可选，前端传递可避免后端重复获取）
}

// UpdateProjectStrategyReq 更新项目策略请求参数
type UpdateProjectStrategyReq struct {
	Id          int64  `json:"id"`           // 项目数据库ID
	AccountId   int64  `json:"account_id"`   // 账户ID
	ProjectId   string `json:"project_id"`   // 项目ID
	StrategyIds string `json:"strategy_ids"` // 策略ID（逗号分隔）
}

// BatchUpdateProjectBidReq 批量修改项目出价请求参数
type BatchUpdateProjectBidReq struct {
	AccountId       int64             `json:"account_id"`        // 账户ID
	AdvertiserId    int64             `json:"advertiser_id"`     // 广告主ID
	PromotionBidMap map[string]string `json:"promotion_bid_map"` // 出价映射 项目ID -> 出价金额
	IsAsync         bool              `json:"is_async"`          // 是否异步执行
}
