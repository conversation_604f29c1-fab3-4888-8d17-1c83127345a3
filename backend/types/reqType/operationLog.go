package reqType

import "time"

// OperationLogListReq 操作日志列表请求参数
type OperationLogListReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`

	Id                 int64     `json:"id"`                                                      // 日志ID
	AccountName        string    `json:"account_name"`                                            // 账户名称
	AccountId          int64     `gorm:"column:account_id" json:"account_id"`                     // 账户ID（巨量引擎平台ID）
	Module             string    `gorm:"column:module" json:"module"`                             // 模块名
	OperationType      string    `gorm:"column:operation_type" json:"operation_type"`             // 操作类型
	Operation          string    `gorm:"column:operation" json:"operation"`                       // 操作名称
	Description        string    `gorm:"column:description" json:"description"`                   // 操作描述
	StartOperationTime time.Time `gorm:"column:start_operation_time" json:"start_operation_time"` // 操作时间
	EndOperationTime   time.Time `gorm:"column:end_operation_time" json:"end_operation_time"`     // 操作时间
	OrderBy            string    `json:"order_by"`
	OrderDesc          bool      `json:"order_desc"`
}
