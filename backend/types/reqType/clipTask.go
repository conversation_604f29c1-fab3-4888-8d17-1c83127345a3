package reqType

// VideoGroupReq 视频分组请求结构体
type VideoGroupReq struct {
	VideoUrl      []string `json:"video_url"`       // 视频URL列表
	IsOriginSound bool     `json:"is_origin_sound"` // 是否保留原声
}

// CreateClipTaskReq 创建混剪任务请求
type CreateClipTaskReq struct {
	CompanyId    int64           `json:"company_id"`     // 公司id
	UserId       int64           `json:"user_id"`        // 用户id
	ClipTaskName string          `json:"clip_task_name"` // 混剪任务名称
	AspectRatio  string          `json:"aspect_ratio"`   // 画面比例
	ScaleMode    string          `json:"scale_mode"`     // 缩放模式(WidthFix、HeightFix、ScaleToFill)
	MusicMixMode int64           `json:"music_mix_mode"` // 音乐混合模式（1、bgm与视频原声混合，2、只要bgm）
	GenTotal     int64           `json:"gen_total"`      // 可生成视频的总数
	MusicUrl     []string        `json:"music_url"`      // 音乐URL列表
	HeadCover    []string        `json:"head_cover"`     // 头部封面列表
	TailCover    []string        `json:"tail_cover"`     // 尾部封面列表
	GroupList    []VideoGroupReq `json:"group_list"`     // 视频分组列表
}

// GetClipTaskReq 获取混剪任务请求
type GetClipTaskReq struct {
	Id         int64  `json:"id"`           // 任务ID
	ClipTaskId string `json:"clip_task_id"` // 混剪任务ID
}

// UpdateClipTaskReq 更新混剪任务请求
type UpdateClipTaskReq struct {
	Id                int64  `json:"id"`                  // 任务ID
	ClipTaskName      string `json:"clip_task_name"`      // 混剪任务名称
	AspectRatio       string `json:"aspect_ratio"`        // 画面比例
	ScaleMode         string `json:"scale_mode"`          // 缩放模式
	MusicMixMode      int64  `json:"music_mix_mode"`      // 音乐混合模式
	GenTotal          int64  `json:"gen_total"`           // 可生成视频的总数
	RemainingGenCount int64  `json:"remaining_gen_count"` // 剩余可生成视频的数量
	GenStatus         int64  `json:"gen_status"`          // 生成状态
}

// DeleteClipTaskReq 删除混剪任务请求
type DeleteClipTaskReq struct {
	Id int64 `json:"id"` // 任务ID
}

// ListClipTasksReq 获取混剪任务列表请求
type ListClipTasksReq struct {
	Page         int    `json:"page"`           // 页码
	PageSize     int    `json:"page_size"`      // 每页数量
	ClipTaskName string `json:"clip_task_name"` // 任务名称（模糊搜索）
	GenStatus    int64  `json:"gen_status"`     // 生成状态
}

// ListClipTasksResp 获取混剪任务列表响应
type ListClipTasksResp struct {
	List     interface{} `json:"list"`      // 任务列表 (实际类型为 []*model.ClipTask)
	Total    int64       `json:"total"`     // 总数
	Page     int         `json:"page"`      // 当前页码
	PageSize int         `json:"page_size"` // 每页数量
}

// BatchDeleteClipTasksReq 批量删除混剪任务请求
type BatchDeleteClipTasksReq struct {
	Ids []int64 `json:"ids"` // 任务ID列表
}

// UpdateClipTaskStatusReq 更新混剪任务状态请求
type UpdateClipTaskStatusReq struct {
	Id        int64 `json:"id"`         // 任务ID
	GenStatus int64 `json:"gen_status"` // 生成状态
}

// UpdateClipTaskRemainingCountReq 更新剩余生成数量请求
type UpdateClipTaskRemainingCountReq struct {
	Id                int64 `json:"id"`                  // 任务ID
	RemainingGenCount int64 `json:"remaining_gen_count"` // 剩余可生成视频的数量
}

type StartClipTaskReq struct {
	Id    int64 `json:"id"`    // 任务ID
	Count int64 `json:"count"` // 生成数量
}

// StartClipTaskResp 启动混剪任务响应
type StartClipTaskResp struct {
	Success     bool     `json:"success"`      // 是否成功
	Message     string   `json:"message"`      // 提示信息
	VideoPaths  []string `json:"video_paths"`  // 生成的视频路径列表
	FailedPaths []string `json:"failed_paths"` // 生成失败的视频路径列表
}
