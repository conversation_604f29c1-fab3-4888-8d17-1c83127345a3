package reqType

// CreateConfigReq 创建配置请求
type CreateConfigReq struct {
	Key         string `json:"key"`
	Value       string `json:"value"`
	Description string `json:"description"`
	Category    string `json:"category"`
	IsSystem    bool   `json:"is_system"`
}

// UpdateConfigReq 更新配置请求
type UpdateConfigReq struct {
	ID          int64  `json:"id"`
	Key         string `json:"key"`
	Value       string `json:"value"`
	Description string `json:"description"`
	Category    string `json:"category"`
	IsSystem    bool   `json:"is_system"`
}

// ListConfigsReq 获取配置列表请求
type ListConfigsReq struct {
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	Conditions map[string]interface{} `json:"conditions"`
}
