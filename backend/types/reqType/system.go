package reqType

type OpenFileReq struct {
	Title       string `json:"title"`        // 窗口标题
	DisplayName string `json:"display_name"` // 筛选器显示名称
	Pattern     string `json:"pattern"`      // 筛选器匹配模式
	IsMultiple  bool   `json:"is_multiple"`  // 是否多选
}

type ReadFileReq struct {
	FilePathList []string `json:"file_path_list"` // 文件路径列表
}

type DeleteFileReq struct {
	FilePathList []string `json:"file_path_list"` // 文件路径列表
}

type DownFileReq struct {
	Url       string `json:"url"`        // 下载链接
	AccountId int64  `json:"account_id"` // 账户ID，用于获取Bot实例
	Format    string `json:"format"`     // 文件格式
}
