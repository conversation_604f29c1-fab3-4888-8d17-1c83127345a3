package interfaces

import (
	"context"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
)

type StrategyEngineLogger interface {
	GetLogs(level string, limit int) ([]interface{}, error)
	SetLogLevel(level string)
	GetLogFileContent() (string, error)
}

// SchedulerManager 调度器管理器接口
type SchedulerManager interface {
	// Start 启动调度器
	Start(ctx context.Context, app *application.App) error

	// Stop 停止调度器
	Stop() error

	// ExecuteStrategyNow 立即执行指定策略
	ExecuteStrategyNow(strategyID int64) error

	// GetEngineStatus 获取引擎状态
	GetEngineStatus() map[string]interface{}

	// ForceUpdateMetrics 强制更新监控指标
	ForceUpdateMetrics()

	// ForceCleanupStrategies 强制执行策略清理
	ForceCleanupStrategies() error

	// SetCleanupInterval 设置清理间隔
	SetCleanupInterval(interval time.Duration) error

	// RefreshStrategies 刷新策略（重新从数据库加载）
	RefreshStrategies() error
}
