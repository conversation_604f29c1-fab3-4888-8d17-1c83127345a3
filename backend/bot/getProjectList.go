package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetProjectList 查询项目列表
func (b *Bot) GetProjectList(page, pageSize int64, searchType int, keyword, projectId, cookie string) (*GetProjectResp, error) {
	projectReq := GetProjectProject{
		ProjectStatusFirst: []int{-1},
		CampaignType:       []int{1},
	}
	if projectId != "" {
		projectReq.ProjectIds = []string{projectId}
	}

	req := GetProjectReq{
		StartTime: fmt.Sprintf("%d", utils.GetTodayStartTimeUnix()),
		EndTime:   fmt.Sprintf("%d", utils.GetTomorrowStartTimeUnix()),
		CascadeMetrics: []string{
			"project_name",
			"project_id",
			"project_status_first",
			"project_status_second",
			"advertiser_name",
			"advertiser_id",
			"project_status_first_name",
			"project_status_second",
			"project_status_second_name",
			"project_status_first_name",
			"project_status_second",
			"project_status_second_name",
			"project_status_first_name",
			"project_status_second",
			"project_status_second_name",
			"project_status_first_name",
			"project_status_second",
			"project_status_second_name",
		},
		Fields: []string{
			"show_cnt",
			"cpm_platform",
			"stat_cost",
			"click_cnt",
			"ctr",
			"conversion_cost",
			"convert_cnt",
			"conversion_rate",
		},
		OrderField:  "create_time",
		OrderType:   1,
		Offset:      page,
		Limit:       pageSize,
		AccountType: 0,
		Filter: GetProjectFilter{
			Advertiser: GetProjectAdvertiser{},
			Group:      GetProjectGroup{},
			Project:    projectReq,
			Promotion:  GetProjectPromotion{},
			Search: GetProjectSearch{
				Keyword:    keyword,
				SearchType: 0,
				QueryType:  "phrase",
			},
		},
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	if r, err := NewPostApi[GetProjectResp]("https://business.oceanengine.com/nbs/api/bm/promotion/ad/get_bidding_project_list").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("origin", "https://business.oceanengine.com").
		AddHeader("referer", "https://business.oceanengine.com").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	GetProjectReq struct {
		StartTime      string           `json:"start_time"`
		EndTime        string           `json:"end_time"`
		CascadeMetrics []string         `json:"cascade_metrics"`
		Fields         []string         `json:"fields"`
		OrderField     string           `json:"order_field"`
		OrderType      int              `json:"order_type"`
		Offset         int64            `json:"offset"`
		Limit          int64            `json:"limit"`
		AccountType    int              `json:"account_type"`
		Filter         GetProjectFilter `json:"filter"`
	}

	GetProjectFilter struct {
		Advertiser GetProjectAdvertiser `json:"advertiser"`
		Group      GetProjectGroup      `json:"group"`
		Project    GetProjectProject    `json:"project"`
		Promotion  GetProjectPromotion  `json:"promotion"`
		Search     GetProjectSearch     `json:"search,omitempty"`
	}

	GetProjectAdvertiser struct {
	}

	GetProjectGroup struct {
	}

	GetProjectProject struct {
		ProjectStatusFirst []int    `json:"projectStatusFirst"`
		CampaignType       []int    `json:"campaignType"`
		ProjectIds         []string `json:"projectIds,omitempty"`
	}

	GetProjectPromotion struct {
	}
	GetProjectSearch struct {
		Keyword    string `json:"keyword"`
		SearchType int    `json:"searchType"`
		QueryType  string `json:"queryType"`
	}

	// GetProjectResp 响应体
	GetProjectResp struct {
		Code      int             `json:"code"`
		Data      GetProjectData  `json:"data"`
		Extra     GetProjectExtra `json:"extra"`
		Msg       string          `json:"msg"`
		RequestId string          `json:"request_id"`
	}

	GetProjectData struct {
		DataList        []GetProjectDataList `json:"data_list"`
		TotalMetrics    GetProjectMetrics    `json:"total_metrics"`
		Pagination      GetProjectPagination `json:"pagination"`
		DownloadId      string               `json:"download_id"`
		DownloadChannel int                  `json:"download_channel"`
	}

	GetProjectDataList struct {
		ProjectId                  string                      `json:"project_id"`
		ProjectName                string                      `json:"project_name"`
		LandingType                int                         `json:"landing_type"`
		LandingTypeName            string                      `json:"landing_type_name"`
		ProjectStatus              int                         `json:"project_status"`
		ProjectStatusName          string                      `json:"project_status_name"`
		DeliveryMode               int                         `json:"delivery_mode"`
		CampaignBudget             string                      `json:"campaign_budget"`
		StartTime                  string                      `json:"start_time"`
		EndTime                    string                      `json:"end_time"`
		DownloadTypeName           string                      `json:"download_type_name"`
		ExternalAction             int                         `json:"external_action"`
		ExternalActionName         string                      `json:"external_action_name"`
		ActionTrackUrl             string                      `json:"action_track_url"`
		CampaignId                 int64                       `json:"campaign_id"`
		CampaignOptStatus          int                         `json:"campaign_opt_status"`
		DeepExternalActionName     string                      `json:"deep_external_action_name"`
		DeepBidType                int                         `json:"deep_bid_type"`
		DeepBidTypeName            string                      `json:"deep_bid_type_name,omitempty"`
		AdvertiserId               int64                       `json:"advertiser_id"`
		CreateTime                 string                      `json:"create_time"`
		ModifyTime                 string                      `json:"modify_time"`
		MarketingInfo              GetProjectMarketingInfo     `json:"marketing_info"`
		PromotionStrategy          GetProjectPromotionStrategy `json:"promotion_strategy"`
		AssetName                  string                      `json:"asset_name,omitempty"`
		AssetType                  int                         `json:"asset_type"`
		AssetTypeName              string                      `json:"asset_type_name"`
		DeliverySceneName          string                      `json:"delivery_scene_name"`
		ProjectBid                 string                      `json:"project_bid"`
		ProjectDeepCpaBid          string                      `json:"project_deep_cpa_bid"`
		ProjectRoiGoal             float64                     `json:"project_roi_goal,omitempty"`
		AdPricing                  int                         `json:"ad_pricing"`
		AdPricingName              string                      `json:"ad_pricing_name"`
		FeedDeliverySearch         int                         `json:"feed_delivery_search,omitempty"`
		ProjectAggregateModifyTime string                      `json:"project_aggregate_modify_time"`
		ProjectStatusFirst         int                         `json:"project_status_first"`
		ProjectStatusSecond        []int                       `json:"project_status_second"`
		ProjectStatusFirstName     string                      `json:"project_status_first_name"`
		ProjectStatusSecondName    []string                    `json:"project_status_second_name"`
		DeliveryPackage            int                         `json:"delivery_package"`
		DeliveryModeInternal       int                         `json:"delivery_mode_internal"`
		ProjectFirstRoiGoal        int                         `json:"project_first_roi_goal,omitempty"`
		AdvertiserName             string                      `json:"advertiser_name"`
		AccountRole                int                         `json:"account_role"`
		SmartBidType               int                         `json:"smart_bid_type"`
		ShopMultiRoiGoals          GetProjectShopMultiRoiGoals `json:"shop_multi_roi_goals"`
		DeliveryProduct            int                         `json:"delivery_product"`
		DeliveryMedium             int                         `json:"delivery_medium"`
		DeliveryMediumName         string                      `json:"delivery_medium_name"`
		ClickCnt                   string                      `json:"click_cnt"`
		Ctr                        string                      `json:"ctr"`
		ShowCnt                    string                      `json:"show_cnt"`
		CpmPlatform                string                      `json:"cpm_platform"`
		StatCost                   string                      `json:"stat_cost"`
		AutoExtend                 GetProjectAutoExtend        `json:"auto_extend,omitempty"`
		DeliveryProductName        string                      `json:"delivery_product_name,omitempty"`
		PromotionObject            GetProjectPromotionObject   `json:"promotion_object,omitempty"`
		ConversionCost             string                      `json:"conversion_cost"`
		ConversionRate             string                      `json:"conversion_rate"`
		ConvertCnt                 string                      `json:"convert_cnt"`
	}

	GetProjectMarketingInfo struct {
		MarketingGoal      int `json:"marketingGoal"`
		DeliveryRelatedNum int `json:"deliveryRelatedNum"`
		AutoAdType         int `json:"autoAdType,omitempty"`
	}

	GetProjectPromotionStrategy struct {
	}

	GetProjectShopMultiRoiGoals struct {
	}

	GetProjectAutoExtend struct {
	}

	GetProjectPromotionObject struct {
		//ProductPlatformId any    `json:"productPlatformId,omitempty"` //string and int64
		ProductId       string `json:"productId,omitempty"`
		PromotionType   int    `json:"promotionType,omitempty"`
		UniqueProductId string `json:"uniqueProductId,omitempty"`
		QuickAppId      string `json:"quickAppId,omitempty"`
		AppName         string `json:"appName"`
		PackageName     string `json:"packageName"`
		DpaAdtype       int    `json:"dpaAdtype"`
		OpenUrlType     int    `json:"openUrlType"`
	}

	GetProjectMetrics struct {
		CpmPlatform string `json:"cpm_platform"`
		StatCost    string `json:"stat_cost"`
		ClickCnt    string `json:"click_cnt"`
		Ctr         string `json:"ctr"`
		ShowCnt     string `json:"show_cnt"`
	}

	GetProjectPagination struct {
		Page    int  `json:"page"`
		Limit   int  `json:"limit"`
		Total   int  `json:"total"`
		HasMore bool `json:"hasMore"`
	}

	GetProjectExtra struct {
	}
)
