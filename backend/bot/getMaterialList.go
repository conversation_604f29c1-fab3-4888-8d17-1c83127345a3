package bot

import (
	"encoding/json"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetMaterialList 获取素材列表
// advertiserID: 广告主ID
// signature: 签名
// promotionIds: 推广计划ID列表
// page: 页码
// limit: 每页限制
// startTime: 开始时间 (格式: 2025-07-05)
// endTime: 结束时间 (格式: 2025-07-05)
// fields: 统计字段
// cookie: Cookie信息
func (b *Bot) GetMaterialList(advertiserID int64, signature string, promotionIds []string, page, limit int, startTime, endTime string, fields []string, cookie string) (*GetMaterialListResp, error) {
	// 构建请求体
	reqBody := GetMaterialListReq{
		PromotionIds:         promotionIds,
		Page:                 page,
		Limit:                limit,
		St:                   startTime,
		Et:                   endTime,
		StarNew:              1,
		Fields:               fields,
		SortStat:             "create_time",
		SortOrder:            1,
		DeliveryPackage:      []int{},
		DeliveryMode:         []int{1},
		DeliveryModeInternal: []int{1},
		QuickDelivery:        []int{},
		IsAigc:               false,
		IsAutoStar:           false,
	}

	// 序列化请求体
	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	// 使用NewPostApi发送请求
	if resp, err := NewPostApi[GetMaterialListResp]("https://ad.oceanengine.com/ad/api/promotion/materials/list").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("_signature", signature).
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(bodyBytes).
		Do(b); err != nil {
		return nil, err
	} else {
		return resp, nil
	}
}

// GetMaterialListReq 获取素材列表请求结构体
type GetMaterialListReq struct {
	PromotionIds         []string `json:"promotion_ids"`          // 推广计划ID列表
	Page                 int      `json:"page"`                   // 页码
	Limit                int      `json:"limit"`                  // 每页限制
	St                   string   `json:"st"`                     // 开始时间
	Et                   string   `json:"et"`                     // 结束时间
	StarNew              int      `json:"starNew"`                // 星标新素材
	Fields               []string `json:"fields"`                 // 统计字段
	SortStat             string   `json:"sort_stat"`              // 排序字段
	SortOrder            int      `json:"sort_order"`             // 排序方式 1:升序 -1:降序
	DeliveryPackage      []int    `json:"delivery_package"`       // 投放包
	DeliveryMode         []int    `json:"delivery_mode"`          // 投放模式
	DeliveryModeInternal []int    `json:"delivery_mode_internal"` // 内部投放模式
	QuickDelivery        []int    `json:"quick_delivery"`         // 快速投放
	IsAigc               bool     `json:"isAigc"`                 // 是否AIGC
	IsAutoStar           bool     `json:"isAutoStar"`             // 是否自动打星
}

// GetMaterialListResp 获取素材列表响应结构体
type GetMaterialListResp struct {
	Code      int                  `json:"code"`       // 响应码，0表示成功
	Data      GetMaterialListData  `json:"data"`       // 响应数据
	Extra     GetMaterialListExtra `json:"extra"`      // 额外信息
	Msg       string               `json:"msg"`        // 响应消息
	RequestId string               `json:"request_id"` // 请求ID
	Env       GetMaterialListEnv   `json:"env"`
}

// GetMaterialListData 素材列表数据
type GetMaterialListData struct {
	Materials    []GetMaterialListItem       `json:"materials"`     // 素材列表
	Pagination   GetMaterialListPagination   `json:"pagination"`    // 分页信息
	TotalMetrics GetMaterialListTotalMetrics `json:"total_metrics"` // 总计数据
}

// GetMaterialListItem 素材列表项
type GetMaterialListItem struct {
	Image                    GetMaterialListImage   `json:"image"`
	AdOptStatus              int                    `json:"ad_opt_status"`
	Metrics                  GetMaterialListMetrics `json:"metrics"`
	AdId                     string                 `json:"ad_id"`
	MaterialType             int                    `json:"material_type"`
	Video                    GetMaterialListVideo   `json:"video"`
	IsPreviewAble            bool                   `json:"is_preview_able"`
	MaterialStatusName       string                 `json:"material_status_name"`
	PromotionId              string                 `json:"promotion_id"`
	MaterialStatusFirstName  string                 `json:"material_status_first_name"`
	MaterialName             string                 `json:"material_name"`
	IsCarryMaterial          bool                   `json:"is_carry_material"`
	MaterialId               string                 `json:"material_id"`
	MaterialRejectReasonType int                    `json:"material_reject_reason_type"`
	MaterialDraftId          string                 `json:"material_draft_id"`
	ProjectId                string                 `json:"project_id"`
	AdvertiserId             string                 `json:"advertiser_id"`
	ShouldForcePackage       bool                   `json:"should_force_package,omitempty"`
	MaterialStatusSecond     []int                  `json:"material_status_second,omitempty"`
	ShouldForceAdv           bool                   `json:"should_force_adv,omitempty"`
	MaterialStatusSecondName []string               `json:"material_status_second_name,omitempty"`
	ShouldForceEa            bool                   `json:"should_force_ea,omitempty"`
	MaterialLabel            []interface{}          `json:"material_label,omitempty"`
	MaterialStatus           int                    `json:"material_status,omitempty"`
	MaterialLabelText        string                 `json:"material_label_text,omitempty"`
	MaterialStatusFirst      int                    `json:"material_status_first,omitempty"`
}

// GetMaterialListPagination 分页信息
type GetMaterialListPagination struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	TotalPage  int `json:"total_page"`
	TotalCount int `json:"total_count"`
}

// GetMaterialListTotalMetrics 总计数据
type GetMaterialListTotalMetrics struct {
	StatCost       string `json:"stat_cost"`       // 总消耗
	ClickCnt       string `json:"click_cnt"`       // 总点击数
	CpcPlatform    string `json:"cpc_platform"`    // 平均点击单价
	ConvertCnt     string `json:"convert_cnt"`     // 总转化数
	ConversionCost string `json:"conversion_cost"` // 平均转化成本
}

// GetMaterialListExtra 额外信息
type GetMaterialListExtra struct {
	// 预留字段，根据实际API响应调整
}

// GetMaterialListEnv 环境信息
type GetMaterialListEnv struct {
	Ppe     bool   `json:"ppe"`      // PPE环境标识
	PpeName string `json:"ppe_name"` // PPE环境名称
}

// GetMaterialListImage 素材图片信息
type GetMaterialListImage struct {
	WebUri    string `json:"web_uri"`    // 图片Web URI
	ImageMode int    `json:"image_mode"` // 图片模式
	SignUrl   string `json:"sign_url"`   // 签名URL
}

// GetMaterialListMetrics 素材指标数据
type GetMaterialListMetrics struct {
	StatCost       string `json:"stat_cost"`       // 消耗
	ClickCnt       string `json:"click_cnt"`       // 点击数
	CpcPlatform    string `json:"cpc_platform"`    // 点击单价
	ConvertCnt     string `json:"convert_cnt"`     // 转化数
	ConversionCost string `json:"conversion_cost"` // 转化成本
}

// GetMaterialListVideo 素材视频信息
type GetMaterialListVideo struct {
	VideoId   string `json:"video_id"`   // 视频ID
	CoverUri  string `json:"cover_uri"`  // 封面URI
	ImageMode int    `json:"image_mode"` // 图片模式
}
