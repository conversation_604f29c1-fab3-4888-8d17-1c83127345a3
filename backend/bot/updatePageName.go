package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// UpdatePageName 更新页面名称
func (b *Bot) UpdatePageName(advertiserId int64, siteId string, name string, cookie string) (*UpdatePageNameResp, error) {
	// 构建请求体
	reqBody := UpdatePageNameReq{
		Name: name,
	}

	// 将请求体转换为JSON
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 发送PUT请求
	if r, err := NewPutApi[UpdatePageNameResp](fmt.Sprintf("https://h5.oceanengine.com/api/v2/sites/%s/name", siteId)).
		AddHeader("Accept", "application/json").
		AddHeader("Content-Type", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://h5.oceanengine.com").
		AddHeader("Referer", "https://h5.oceanengine.com").
		AddParam("aadvid", fmt.Sprintf("%d", advertiserId)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// UpdatePageNameReq 更新页面名称请求参数结构体
type UpdatePageNameReq struct {
	Name string `json:"name"`
}

// UpdatePageNameResp 更新页面名称响应结构体
type UpdatePageNameResp struct {
	StatusCode int    `json:"statusCode"`
	Error      string `json:"error"`
	Message    string `json:"message"`
}
