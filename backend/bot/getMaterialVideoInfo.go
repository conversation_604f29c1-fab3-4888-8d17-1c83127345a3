package bot

import (
	"encoding/json"
	"fmt"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetMaterialVideoInfo 获取视频素材信息
// advertiserID: 广告主ID
// cookie: Cookie信息
// vids: 视频ID列表
func (b *Bot) GetMaterialVideoInfo(advertiserID int64, cookie string, vids []string) (*GetMaterialVideoInfoResp, error) {
	// 构建请求体
	reqBody := GetMaterialVideoInfoReq{
		Vids: vids,
	}

	// 序列化请求体
	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 使用NewPostApi发送请求
	if resp, err := NewPostApi[GetMaterialVideoInfoResp]("https://ad.oceanengine.com/nbs/api/ads/material/video_info").
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("accept", "application/json").
		AddHeader("content-type", "application/json").
		AddHeader("cookie", cookie).
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(bodyBytes).
		Do(b); err != nil {
		return nil, fmt.Errorf("获取视频素材信息失败: %v", err)
	} else {
		return resp, nil
	}
}

// GetMaterialVideoInfoReq 获取视频素材信息请求结构体
type GetMaterialVideoInfoReq struct {
	Vids []string `json:"vids"` // 视频ID列表
}

// GetMaterialVideoInfoResp 获取视频素材信息响应结构体
type GetMaterialVideoInfoResp struct {
	Code      int                          `json:"code"`       // 响应码
	Data      map[string]MaterialVideoInfo `json:"data"`       // 视频信息数据，key为视频ID
	Extra     map[string]interface{}       `json:"extra"`      // 额外信息
	Msg       string                       `json:"msg"`        // 响应消息
	RequestId string                       `json:"request_id"` // 请求ID
}

// MaterialVideoInfo 视频素材信息
type MaterialVideoInfo struct {
	VideoUrl         string  `json:"video_url"`          // 视频播放地址
	VWidth           int     `json:"vwidth"`             // 视频宽度
	VHeight          int     `json:"vheight"`            // 视频高度
	VideoId          string  `json:"video_id"`           // 视频ID
	CoverUrl         string  `json:"cover_url"`          // 封面地址
	Source           string  `json:"source"`             // 来源
	AvatarUrl        string  `json:"avatar_url"`         // 头像地址
	VideoSize        int64   `json:"video_size"`         // 视频大小（字节）
	VideoDuration    float64 `json:"video_duration"`     // 视频时长（秒）
	Status           int     `json:"status"`             // 状态
	CoverUri         string  `json:"cover_uri"`          // 封面URI
	Bitrate          int     `json:"bitrate"`            // 比特率
	OriginalVideoUrl string  `json:"original_video_url"` // 原视频地址
	UserTag          string  `json:"user_tag"`           // 用户标签
	Format           string  `json:"format"`             // 视频格式
	FileHash         string  `json:"file_hash"`          // 文件哈希值
}
