package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// ConcernRemoveSubAccount 取消收藏项目
func (b *Bot) ConcernRemoveSubAccount(advertiserId int64, cookie string) (*ConcernRemoveSubAccountResp, error) {
	req := ConcernRemoveSubAccountReq{
		AccountId: fmt.Sprintf("%d", advertiserId),
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[ConcernRemoveSubAccountResp]("https://business.oceanengine.com/nbs/api/bm/promotion/concern_remove").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	ConcernRemoveSubAccountReq struct {
		AccountId string `json:"account_id"`
	}

	// ConcernRemoveSubAccountResp 响应体
	ConcernRemoveSubAccountResp struct {
		Code int `json:"code"`
		Data struct {
		} `json:"data"`
		Extra struct {
		} `json:"extra"`
		Msg       string `json:"msg"`
		RequestId string `json:"request_id"`
	}
)
