package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
	"strconv"
)

// UpdateProjectStatus 修改项目状态 0启用 1禁用
func (b *Bot) UpdateProjectStatus(advertiserId int64, statusMap map[string]int, cookie string) (*UpdateProjectStatusResp, error) {
	statusMapInt64 := make(map[int64]int)
	for k, v := range statusMap {
		id, err := strconv.ParseInt(k, 10, 64)
		if err != nil {
			return nil, err
		}
		statusMapInt64[id] = v
	}
	req := UpdateProjectStatusReq{
		IsAsync:   false,
		StatusMap: statusMapInt64,
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[UpdateProjectStatusResp]("https://ad.oceanengine.com/ad/api/promotion/projects/update_status").
		AddHeader("Accept", "application/json").
		AddHeader("Content-Type", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("aadvid", fmt.Sprintf("%d", advertiserId)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	UpdateProjectStatusReq struct {
		StatusMap map[int64]int `json:"status_map"`
		IsAsync   bool          `json:"is_async"`
	}

	// UpdateProjectStatusResp 响应体
	UpdateProjectStatusResp struct {
		Code  int      `json:"code"`
		Data  []string `json:"data"`
		Extra struct {
		} `json:"extra"`
		Msg       string `json:"msg"`
		RequestId string `json:"request_id"`
		Env       struct {
			Ppe     bool   `json:"ppe"`
			PpeName string `json:"ppe_name"`
		} `json:"env"`
	}
)
