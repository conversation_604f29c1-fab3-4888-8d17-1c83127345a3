package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// XureTemplated 调用xure_templated接口
func (b *Bot) XureTemplated(AdvertiserId int64, templateId string, cookie string) (*XureTemplatedResp, error) {
	// 构建请求体
	reqBody := XureTemplatedReq{
		TemplateId: templateId,
	}

	// 将请求体转换为JSON
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	// 发送POST请求
	if r, err := NewPostApi[XureTemplatedResp]("https://h5.oceanengine.com/api/v2/sites/xure_templated/").
		AddHeader("Accept", "application/json").
		AddHeader("Content-Type", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("aadvid", fmt.Sprintf("%d", AdvertiserId)).
		SetBody(jsonBody).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// XureTemplatedReq 请求参数结构体
type XureTemplatedReq struct {
	TemplateId string `json:"templateId"`
}

// XureTemplatedResp 响应结构体
type XureTemplatedResp struct {
	SiteId   string `json:"siteId"`
	PageType int    `json:"pageType"`
}
