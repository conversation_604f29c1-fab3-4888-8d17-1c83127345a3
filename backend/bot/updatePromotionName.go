package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// UpdatePromotionName 修改广告状态
func (b *Bot) UpdatePromotionName(advertiserId int64, projectId, name string, cookie string) (*UpdatePromotionNameResp, error) {
	req := UpdatePromotionNameReq{
		Id:   projectId,
		Name: name,
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[UpdatePromotionNameResp]("https://ad.oceanengine.com/ad/api/agw/promotion/ads/rename").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("aadvid", fmt.Sprintf("%d", advertiserId)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	UpdatePromotionNameReq struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	}

	// UpdatePromotionNameResp 响应体
	UpdatePromotionNameResp struct {
		Code int `json:"code"`
		Data struct {
		} `json:"data"`
		Extra struct {
		} `json:"extra"`
		Msg       string `json:"msg"`
		RequestId string `json:"request_id"`
	}
)
