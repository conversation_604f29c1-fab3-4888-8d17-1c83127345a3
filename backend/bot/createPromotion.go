package bot

import (
	"encoding/json"
	"fmt"
	"strconv"
)

// CreatePromotion 创建广告
func (b *<PERSON>t) CreatePromotion(advertiserID int64, promotionData *CreatePromotionReq, cookie string) (*CreatePromotionResp, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(promotionData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	if r, err := NewPostApi[CreatePromotionResp]("https://ad.oceanengine.com/superior/api/ad/promotion/create_promotion").
		AddHeader("Content-Type", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// CreatePromotionReq 创建广告请求结构体
type CreatePromotionReq struct {
	// 广告数据
	PromotionData PromotionData `json:"promotion_data"`
	// 素材组
	MaterialGroup MaterialGroup `json:"material_group"`
	// 广告名称
	Name string `json:"name"`
	// 项目ID
	ProjectID string `json:"project_id"`
	// 广告ID
	PromotionID string `json:"promotion_id"`
	// 校验哈希
	CheckHash string `json:"check_hash"`
	// 是否自动投放模式
	IsAutoDeliveryMode bool `json:"is_auto_delivery_mode"`
}

// PromotionData 广告数据结构体
type PromotionData struct {
	// 客户端设置
	ClientSettings map[string]interface{} `json:"client_settings"`
	// 原广告ID
	OriginPromotionID string `json:"origin_promotion_id"`
	// 是否启用个性化行为
	EnablePersonalAction bool `json:"enable_personal_action"`
	// 预算
	Budget string `json:"budget,omitempty"`
	// 出价
	Bid *string `json:"bid,omitempty"`
}

// MaterialGroup 素材组结构体
type MaterialGroup struct {
	// 可播放素材信息
	PlayableMaterialInfo []interface{} `json:"playable_material_info"`
	// 视频素材信息
	VideoMaterialInfo []VideoMaterialInfo `json:"video_material_info"`
	// 图片素材信息
	ImageMaterialInfo []interface{} `json:"image_material_info"`
	// 组件素材信息
	ComponentMaterialInfo []interface{} `json:"component_material_info"`
	// 行动号召素材信息
	CallToActionMaterialInfo []CallToActionMaterialInfo `json:"call_to_action_material_info"`
	// 产品信息
	ProductInfo ProductInfo `json:"product_info"`
	// 标题素材信息
	TitleMaterialInfo []TitleMaterialInfo `json:"title_material_info"`
}

// VideoMaterialInfo 视频素材信息结构体
type VideoMaterialInfo struct {
	// 图片模式
	ImageMode int `json:"image_mode"`
	// 图片信息
	ImageInfo []ImageInfo `json:"image_info"`
	// 视频信息
	VideoInfo VideoInfo `json:"video_info"`
	// 复制模式
	CopyMode int `json:"copy_mode"`
}

// ImageInfo 图片信息结构体
type ImageInfo struct {
	// 高度
	Height int `json:"height"`
	// 宽度
	Width int `json:"width"`
	// Web URI
	WebURI string `json:"web_uri"`
	// 签名URL
	SignURL string `json:"sign_url"`
}

// VideoInfo 视频信息结构体
type VideoInfo struct {
	// 视频ID
	VideoID string `json:"video_id"`
	// 状态
	Status int `json:"status"`
	// 视频时长
	VideoDuration int `json:"video_duration"`
	// 初始大小
	InitialSize int64 `json:"initial_size"`
	// 视频唯一标识
	VideoUnique string `json:"video_unique"`
	// 文件MD5
	FileMD5 string `json:"file_md5"`
	// 缩略图高度
	ThumbHeight int `json:"thumb_height"`
	// 缩略图宽度
	ThumbWidth int `json:"thumb_width"`
	// 缩略图URI
	ThumbURI string `json:"thumb_uri"`
	// 原始文件URI
	OriginalFileURI string `json:"original_file_uri"`
	// 时长
	Duration int `json:"duration"`
	// 错误描述
	ErrorDesc string `json:"error_desc"`
	// 用户引用
	UserReference string `json:"user_reference"`
	// 宽度
	Width int `json:"width"`
	// 高度
	Height int `json:"height"`
	// 视频标识
	VID string `json:"vid"`
	// 上传ID
	UploadID string `json:"upload_id"`
	// 封面URI
	CoverURI string `json:"cover_uri"`
	// 编码格式
	Codec string `json:"codec"`
	// 比特率
	Bitrate int64 `json:"bitrate"`
}

// CallToActionMaterialInfo 行动号召素材信息结构体
type CallToActionMaterialInfo struct {
	// CDP素材ID
	CDPMaterialID string `json:"cdp_material_id"`
	// 行动号召文本
	CallToAction string `json:"call_to_action"`
}

// ProductInfo 产品信息结构体
type ProductInfo struct {
	// 产品名称
	ProductName ProductName `json:"product_name"`
	// 产品图片
	ProductImages []ProductImage `json:"product_images"`
	// 产品卖点
	ProductSellingPoints []ProductSellingPoint `json:"product_selling_points"`
}

// ProductName 产品名称结构体
type ProductName struct {
	// CDP素材ID
	CDPMaterialID string `json:"cdp_material_id"`
	// 名称
	Name string `json:"name"`
}

// ProductImage 产品图片结构体
type ProductImage struct {
	// 图片URI
	ImageURI string `json:"image_uri"`
	// 宽度
	Width int `json:"width"`
	// 高度
	Height int `json:"height"`
}

// ProductSellingPoint 产品卖点结构体
type ProductSellingPoint struct {
	// CDP素材ID
	CDPMaterialID string `json:"cdp_material_id"`
	// 卖点
	SellingPoint string `json:"selling_point"`
}

// TitleMaterialInfo 标题素材信息结构体
type TitleMaterialInfo struct {
	// 标题
	Title string `json:"title"`
	// 是否动态
	IsDynamic int `json:"is_dynamic"`
}

// CreatePromotionResp 创建广告响应结构体
type CreatePromotionResp struct {
	// 响应码
	Code int `json:"code"`
	// 响应数据
	Data CreatePromotionData `json:"data"`
	// 额外信息
	Extra map[string]interface{} `json:"extra"`
	// 消息
	Msg string `json:"msg"`
	// 请求ID
	RequestID string `json:"request_id"`
}

// CreatePromotionData 创建广告响应数据结构体
type CreatePromotionData struct {
	// 广告ID
	PromotionID string `json:"promotion_id"`
	// 关键词错误
	KeywordsError []string `json:"keywordsError"`
}
