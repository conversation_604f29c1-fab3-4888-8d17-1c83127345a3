package bot

import (
	"strconv"
)

// GetPromotionInfo 获取广告详情
func (b *Bot) GetPromotionInfo(advertiserID int64, promotionIDs []string, needInvisibleMaterial bool, cookie string) (*GetPromotionInfoResp, error) {

	if r, err := NewGetApi[GetPromotionInfoResp]("https://ad.oceanengine.com/superior/api/ad/promotion/detail").
		AddHeader("Accept", "application/json").
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		AddHeader("Cookie", cookie).
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		AddParam("need_invisible_material", strconv.FormatBool(needInvisibleMaterial)).
		AddParamArray("promotion_ids[]", promotionIDs).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetPromotionInfoResp 获取广告详情响应结构体
type GetPromotionInfoResp struct {
	Code      int                          `json:"code"`       // 响应码
	Data      map[string]PromotionInfoData `json:"data"`       // 广告详情数据，key为广告ID
	Extra     map[string]interface{}       `json:"extra"`      // 额外信息
	Msg       string                       `json:"msg"`        // 响应消息
	RequestID string                       `json:"request_id"` // 请求ID
}

// PromotionInfoData 广告详情数据结构
type PromotionInfoData struct {
	ID                  string                 `json:"id"`                    // 广告ID
	AdvertiserID        string                 `json:"advertiser_id"`         // 广告主ID
	ProjectID           string                 `json:"project_id"`            // 项目ID
	CampaignID          string                 `json:"campaign_id"`           // 推广计划ID
	AdID                string                 `json:"ad_id"`                 // 广告单元ID
	Name                string                 `json:"name"`                  // 广告名称
	Data                PromotionDataInfo      `json:"data"`                  // 广告数据
	Status              int                    `json:"status"`                // 广告状态
	IsDel               int                    `json:"is_del"`                // 是否删除
	EditVersion         string                 `json:"edit_version"`          // 编辑版本
	ProjectIsDel        int                    `json:"project_is_del"`        // 项目是否删除
	CreateTime          string                 `json:"create_time"`           // 创建时间
	ModifyTime          string                 `json:"modify_time"`           // 修改时间
	OptStatus           int                    `json:"opt_status"`            // 操作状态
	AuditStatus         int                    `json:"audit_status"`          // 审核状态
	AggregateModifyTime string                 `json:"aggregate_modify_time"` // 聚合修改时间
	MaterialGroup       PromotionMaterialGroup `json:"material_group"`        // 素材组
	Project             PromotionProjectInfo   `json:"project"`               // 项目信息
	CanCopy             bool                   `json:"can_copy"`              // 是否可复制
}

// PromotionDataInfo 广告数据信息
type PromotionDataInfo struct {
	Budget               string                 `json:"budget"`                 // 预算
	Bid                  string                 `json:"bid"`                    // 出价
	Source               string                 `json:"source"`                 // 来源
	BudgetMode           int                    `json:"budget_mode"`            // 预算模式
	CreateChannel        int                    `json:"create_channel"`         // 创建渠道
	SystemOrigin         int                    `json:"system_origin"`          // 系统来源
	EnablePersonalAction bool                   `json:"enable_personal_action"` // 启用个性化行为
	ShopMultiRoiGoals    map[string]interface{} `json:"shop_multi_roi_goals"`   // 商店多ROI目标
	BindProductEnable    bool                   `json:"bind_product_enable"`    // 绑定产品启用
}

// PromotionMaterialGroup 广告素材组
type PromotionMaterialGroup struct {
	VideoMaterialInfo        []PromotionVideoMaterialInfo        `json:"video_material_info"`          // 视频素材信息
	TitleMaterialInfo        []PromotionTitleMaterialInfo        `json:"title_material_info"`          // 标题素材信息
	ProductInfo              PromotionProductInfo                `json:"product_info"`                 // 产品信息
	CallToActionMaterialInfo []PromotionCallToActionMaterialInfo `json:"call_to_action_material_info"` // 行动号召素材信息
	OpenUrlMaterialInfo      map[string]interface{}              `json:"open_url_material_info"`       // 打开链接素材信息
}

// PromotionVideoMaterialInfo 广告视频素材信息
type PromotionVideoMaterialInfo struct {
	MaterialID        string               `json:"material_id"`         // 素材ID
	ImageMode         int                  `json:"image_mode"`          // 图片模式
	ImageInfo         []PromotionImageInfo `json:"image_info"`          // 图片信息
	VideoInfo         PromotionVideoInfo   `json:"video_info"`          // 视频信息
	AuditResult       PromotionAuditResult `json:"audit_result"`        // 审核结果
	MaterialOptStatus int                  `json:"material_opt_status"` // 素材操作状态
	LegoMid           string               `json:"lego_mid"`            // Lego素材ID
	CopyMode          int                  `json:"copy_mode"`           // 复制模式
}

// PromotionImageInfo 广告图片信息
type PromotionImageInfo struct {
	Height  int    `json:"height"`   // 高度
	Width   int    `json:"width"`    // 宽度
	WebURI  string `json:"web_uri"`  // Web URI
	SignURL string `json:"sign_url"` // 签名URL
}

// PromotionVideoInfo 广告视频信息
type PromotionVideoInfo struct {
	VideoID         string `json:"video_id"`          // 视频ID
	Status          int    `json:"status"`            // 状态
	VideoDuration   int    `json:"video_duration"`    // 视频时长
	InitialSize     int64  `json:"initial_size"`      // 初始大小
	VideoUnique     string `json:"video_unique"`      // 视频唯一标识
	FileMD5         string `json:"file_md5"`          // 文件MD5
	ThumbHeight     int    `json:"thumb_height"`      // 缩略图高度
	ThumbWidth      int    `json:"thumb_width"`       // 缩略图宽度
	ThumbURI        string `json:"thumb_uri"`         // 缩略图URI
	OriginalFileURI string `json:"original_file_uri"` // 原始文件URI
	Duration        int    `json:"duration"`          // 时长
	ErrorDesc       string `json:"error_desc"`        // 错误描述
	UserReference   string `json:"user_reference"`    // 用户引用
	Width           int    `json:"width"`             // 宽度
	Height          int    `json:"height"`            // 高度
	VID             string `json:"vid"`               // 视频标识
	UploadID        string `json:"upload_id"`         // 上传ID
	CoverURI        string `json:"cover_uri"`         // 封面URI
	Codec           string `json:"codec"`             // 编码格式
	Bitrate         int64  `json:"bitrate"`           // 比特率
}

// PromotionAuditResult 审核结果
type PromotionAuditResult struct {
	AuditStatus  string `json:"audit_status"`  // 审核状态
	RejectReason string `json:"reject_reason"` // 拒绝原因
}

// PromotionTitleMaterialInfo 广告标题素材信息
type PromotionTitleMaterialInfo struct {
	MaterialID        string               `json:"material_id"`         // 素材ID
	Title             string               `json:"title"`               // 标题
	IsDynamic         int                  `json:"is_dynamic"`          // 是否动态
	AuditResult       PromotionAuditResult `json:"audit_result"`        // 审核结果
	IsRecommended     int                  `json:"is_recommended"`      // 是否推荐
	MaterialOptStatus int                  `json:"material_opt_status"` // 素材操作状态
	LegoMid           string               `json:"lego_mid"`            // Lego素材ID
}

// PromotionProductInfo 广告产品信息
type PromotionProductInfo struct {
	ProductName          PromotionProductName           `json:"product_name"`           // 产品名称
	ProductImages        []PromotionProductImage        `json:"product_images"`         // 产品图片
	ProductSellingPoints []PromotionProductSellingPoint `json:"product_selling_points"` // 产品卖点
	ProductNameList      []PromotionProductNameList     `json:"product_name_list"`      // 产品名称列表
}

// PromotionProductName 广告产品名称
type PromotionProductName struct {
	CDPMaterialID string `json:"cdp_material_id"` // CDP素材ID
	Name          string `json:"name"`            // 名称
}

// PromotionProductImage 广告产品图片
type PromotionProductImage struct {
	CDPMaterialID string `json:"cdp_material_id"` // CDP素材ID
	ImageURI      string `json:"image_uri"`       // 图片URI
	Height        int    `json:"height"`          // 高度
	Width         int    `json:"width"`           // 宽度
	SignURL       string `json:"sign_url"`        // 签名URL
}

// PromotionProductSellingPoint 广告产品卖点
type PromotionProductSellingPoint struct {
	CDPMaterialID string `json:"cdp_material_id"` // CDP素材ID
	SellingPoint  string `json:"selling_point"`   // 卖点
	IsRecommended int    `json:"is_recommended"`  // 是否推荐
}

// PromotionProductNameList 广告产品名称列表
type PromotionProductNameList struct {
	CDPMaterialID string                      `json:"cdp_material_id"` // CDP素材ID
	Name          string                      `json:"name"`            // 名称
	Product       PromotionProductNameProduct `json:"product"`         // 产品信息
}

// PromotionProductNameProduct 广告产品名称中的产品信息
type PromotionProductNameProduct struct {
	ProductPlatformID string `json:"product_platform_id"` // 产品平台ID
	ProductID         string `json:"product_id"`          // 产品ID
	UniqueProductID   string `json:"unique_product_id"`   // 唯一产品ID
	ProductObjectID   string `json:"product_object_id"`   // 产品对象ID
}

// PromotionCallToActionMaterialInfo 广告行动号召素材信息
type PromotionCallToActionMaterialInfo struct {
	CDPMaterialID string `json:"cdp_material_id"` // CDP素材ID
	CallToAction  string `json:"call_to_action"`  // 行动号召
	IsRecommended int    `json:"is_recommended"`  // 是否推荐
}

// PromotionProjectInfo 广告项目信息
type PromotionProjectInfo struct {
	MarketingInfo     PromotionMarketingInfo     `json:"marketing_info"`     // 营销信息
	PromotionObject   PromotionPromotionObject   `json:"promotion_object"`   // 推广对象
	Inventory         PromotionInventory         `json:"inventory"`          // 库存
	Audience          PromotionAudience          `json:"audience"`           // 受众
	PromotionStrategy PromotionPromotionStrategy `json:"promotion_strategy"` // 推广策略
	TrackUrls         PromotionTrackUrls         `json:"track_urls"`         // 监测链接
	ClassifyInfo      PromotionClassifyInfo      `json:"classify_info"`      // 分类信息
	SystemOrigin      string                     `json:"system_origin"`      // 系统来源
	CreateChannel     string                     `json:"create_channel"`     // 创建渠道
	ProjectName       string                     `json:"project_name"`       // 项目名称
}

// PromotionMarketingInfo 广告营销信息
type PromotionMarketingInfo struct {
	LandingType          int `json:"landing_type"`           // 落地页类型
	CDPMarketingGoal     int `json:"cdp_marketing_goal"`     // CDP营销目标
	CampaignType         int `json:"campaign_type"`          // 推广类型
	AppPromotionType     int `json:"app_promotion_type"`     // 应用推广类型
	DeliveryRelatedNum   int `json:"delivery_related_num"`   // 投放相关数量
	DeliveryMode         int `json:"delivery_mode"`          // 投放模式
	DeliveryPackage      int `json:"delivery_package"`       // 投放包
	DeliveryModeInternal int `json:"delivery_mode_internal"` // 内部投放模式
}

// PromotionPromotionObject 广告推广对象
type PromotionPromotionObject struct {
	ProductPlatformID  string   `json:"product_platform_id"`  // 产品平台ID
	ProductID          string   `json:"product_id"`           // 产品ID
	AppType            int      `json:"app_type"`             // 应用类型
	DownloadType       int      `json:"download_type"`        // 下载类型
	DownloadURL        string   `json:"download_url"`         // 下载链接
	PackageName        string   `json:"package_name"`         // 包名
	AppName            string   `json:"app_name"`             // 应用名称
	UniqueProductID    string   `json:"unique_product_id"`    // 唯一产品ID
	AppPkgID           string   `json:"app_pkg_id"`           // 应用包ID
	ProductObjectID    string   `json:"product_object_id"`    // 产品对象ID
	DeliveryProduct    int      `json:"delivery_product"`     // 投放产品
	DeliveryMedium     int      `json:"delivery_medium"`      // 投放媒体
	DeliveryProductIDs []string `json:"delivery_product_ids"` // 投放产品ID列表
	DeliveryMediumIDs  []string `json:"delivery_medium_ids"`  // 投放媒体ID列表
}

// PromotionInventory 广告库存
type PromotionInventory struct {
	InventoryType    []int `json:"inventory_type"`    // 库存类型
	SmartInventory   int   `json:"smart_inventory"`   // 智能库存
	InventoryCatalog int   `json:"inventory_catalog"` // 库存目录
	UnionVideoType   int   `json:"union_video_type"`  // 联盟视频类型
	DeliveryRange    int   `json:"delivery_range"`    // 投放范围
}

// PromotionAudience 广告受众
type PromotionAudience struct {
	District          string   `json:"district"`            // 地域
	Platform          []string `json:"platform"`            // 平台
	AutoExtendEnabled int      `json:"auto_extend_enabled"` // 自动扩展启用
	HideIfConverted   string   `json:"hide_if_converted"`   // 隐藏已转化用户
	HideIfExists      string   `json:"hide_if_exists"`      // 隐藏已安装用户
}

// PromotionPromotionStrategy 广告推广策略
type PromotionPromotionStrategy struct {
	AssetIDs        []string `json:"asset_ids"`          // 素材ID列表
	ExternalAction  string   `json:"external_action"`    // 外部转化目标
	SmartBidType    int      `json:"smart_bid_type"`     // 智能出价类型
	FlowControlMode int      `json:"flow_control_mode"`  // 流量控制模式
	ScheduleType    int      `json:"schedule_type"`      // 投放时间类型
	StartTime       string   `json:"start_time"`         // 开始时间
	EndTime         string   `json:"end_time"`           // 结束时间
	BudgetMode      int      `json:"budget_mode"`        // 预算模式
	PricingType     int      `json:"pricing_type"`       // 出价类型
	TrackURLType    int      `json:"track_url_type"`     // 监测链接类型
	TrackURLGroupID string   `json:"track_url_group_id"` // 监测链接组ID
	ConvertSource   int      `json:"convert_source"`     // 转化来源
	Bid             string   `json:"bid"`                // 出价
	DeepCPABid      string   `json:"deep_cpa_bid"`       // 深度CPA出价
	ROIGoal         string   `json:"roi_goal"`           // ROI目标
	FirstROIGoal    string   `json:"first_roi_goal"`     // 首次ROI目标
	SevenROIGoal    string   `json:"seven_roi_goal"`     // 七日ROI目标
}

// PromotionTrackUrls 广告监测链接
type PromotionTrackUrls struct {
	ActionTrackURL   []string `json:"action_track_url"`    // 点击监测链接
	TrackURLSendType string   `json:"track_url_send_type"` // 监测链接发送类型
}

// PromotionClassifyInfo 广告分类信息
type PromotionClassifyInfo struct {
	Classify int `json:"classify"` // 分类
}
