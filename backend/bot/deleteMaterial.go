package bot

import (
	"encoding/json"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// DeleteMaterial 删除素材
// advertiserID: 广告主ID
// promotionID: 推广计划ID
// ids: 素材ID列表
// cookie: Cookie信息
func (b *Bot) DeleteMaterial(advertiserID int64, promotionID string, ids []string, cookie string) (*DeleteMaterialResp, error) {
	// 构建请求体
	reqBody := DeleteMaterialReq{
		IDs:         ids,
		PromotionID: promotionID,
	}

	// 序列化请求体
	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	// 使用NewPostApi发送请求
	if resp, err := NewPostApi[DeleteMaterialResp]("https://ad.oceanengine.com/superior/api/promote/materials/del").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(bodyBytes).
		Do(b); err != nil {
		return nil, err
	} else {
		return resp, nil
	}
}

// DeleteMaterialReq 删除素材请求结构体
type DeleteMaterialReq struct {
	IDs         []string `json:"ids"`          // 素材ID列表
	PromotionID string   `json:"promotion_id"` // 推广计划ID
}

// DeleteMaterialResp 删除素材响应结构体
type DeleteMaterialResp struct {
	Code      int                    `json:"code"`       // 响应码，0表示成功
	Data      map[string]interface{} `json:"data"`       // 响应数据
	Extra     map[string]interface{} `json:"extra"`      // 额外信息
	Msg       string                 `json:"msg"`        // 响应消息
	RequestID string                 `json:"request_id"` // 请求ID
} 