package bot

import (
	"fmt"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetRetargetingTags 获取项目可用人群包列表
func (b *Bot) GetRetargetingTags(advertiserId string, req *GetRetargetingTagsReq, cookie string) (*GetRetargetingTagsResp, error) {
	// 构建URL，包含签名参数
	url := fmt.Sprintf("https://ad.oceanengine.com/superior/api/audience/retargeting_tags?_signature=2gR-DwAAAADXzU8YoecGYdoEfhAALJl&aadvid=%s", advertiserId)

	// 发送GET请求
	if r, err := NewGetApi[GetRetargetingTagsResp](url).
		AddHeader("Accept", "application/json, text/plain, */*").
		AddHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,vi;q=0.7").
		AddHeader("Cache-Control", "no-cache").
		AddHeader("Cookie", cookie).
		AddHeader("Pragma", "no-cache").
		AddHeader("Priority", "u=1, i").
		AddHeader("Referer", fmt.Sprintf("https://ad.oceanengine.com/superior/create-project?aadvid=%s", advertiserId)).
		AddHeader("Sec-Ch-Ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"").
		AddHeader("Sec-Ch-Ua-Mobile", "?0").
		AddHeader("Sec-Ch-Ua-Platform", "\"Windows\"").
		AddHeader("Sec-Fetch-Dest", "empty").
		AddHeader("Sec-Fetch-Mode", "cors").
		AddHeader("Sec-Fetch-Site", "same-origin").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36").
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("X-Sessionid", "90a8ce2f-7238-4c60-81d6-cd07b1943fd7").
		Do(b); err != nil {
		return nil, err
	} else {

		return r, nil
	}
}

// GetRetargetingTagsReq 获取项目可用人群包列表请求参数
type GetRetargetingTagsReq struct {
	// 可以根据实际需要添加查询参数
	// 目前这个API看起来是GET请求，主要参数在URL中
}

// GetRetargetingTagsResp 获取项目可用人群包列表响应
type GetRetargetingTagsResp struct {
	Code      int    `json:"code"`
	Message   string `json:"msg"`
	RequestId string `json:"request_id"`
	Data      struct {
		Status          string           `json:"status"`           // 状态
		CustomAudiences []CustomAudience `json:"custom_audiences"` // 人群包列表
	} `json:"data"`
	Extra interface{} `json:"extra"`
}

// CustomAudience 人群包
type CustomAudience struct {
	ID                 string `json:"id"`                   // 人群包ID
	AdvertiserID       string `json:"advertiser_id"`        // 广告主ID
	CoreUserID         string `json:"core_user_id"`         // 核心用户ID
	Name               string `json:"name"`                 // 人群包名称
	Tag                string `json:"tag"`                  // 标签
	CreateTime         string `json:"create_time"`          // 创建时间
	ModifyTime         string `json:"modify_time"`          // 修改时间
	FinishTime         string `json:"finish_time"`          // 完成时间
	Status             int    `json:"status"`               // 状态
	PortraitStatus     int    `json:"portrait_status"`      // 画像状态
	PushStatus         int    `json:"push_status"`          // 推送状态
	ShowStatus         int    `json:"show_status"`          // 显示状态
	CoverNum           string `json:"cover_num"`            // 覆盖人数
	Isdel              int    `json:"isdel"`                // 是否删除
	CalculateType      int    `json:"calculate_type"`       // 计算类型
	Source             int    `json:"source"`               // 来源
	ReportSecondLevel  int    `json:"report_second_level"`  // 报表二级分类
	FirstType          int    `json:"first_type"`           // 一级类型
	SecondType         int    `json:"second_type"`          // 二级类型
	Category           int    `json:"category"`             // 分类
	Data               string `json:"data"`                 // 数据详情（JSON字符串）
	Party              int    `json:"party"`                // 第三方
	ConvertFilePath    string `json:"convert_file_path"`    // 转换文件路径
	CustomTypeName     string `json:"custom_type_name"`     // 自定义类型名称
	StatusName         string `json:"status_name"`          // 状态名称
	CustomClassifyName string `json:"custom_classify_name"` // 自定义分类名称
	PullOffTagStatus   int    `json:"pull_off_tag_status"`  // 下架标签状态
	NonProfileTagOff   int    `json:"non_profile_tag_off"`  // 非画像标签关闭
}
