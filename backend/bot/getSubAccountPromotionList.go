package bot

import (
	"encoding/json"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetSubAccountPromotionList 获取子账户下的广告列表
func (b *Bot) GetSubAccountPromotionList(aadvid string, page, pageSize int, cookie string) (*SubAccountPromotionResp, error) {
	req := SubAccountPromotionReq{
		CampaignType:    []int{1},
		CascadeFields:   []string{"automatic_rule_count"},
		Fields:          []string{},
		IsSophonx:       1,
		Limit:           pageSize,
		Page:            page,
		ProjectIds:      []string{},
		ProjectStatus:   []int{-1},
		PromotionStatus: []int{0},
		SortOrder:       1,
		SortStat:        "create_time",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/ads/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountPromotionResp](url).
		AddHeader("Content-Type", "application/json; charset=UTF-8").
		AddHeader("Accept", "application/json, text/plain, */*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetSubAccountPromotionListByStatus 根据广告状态获取广告列表
func (b *Bot) GetSubAccountPromotionListByStatus(aadvid string, page, pageSize int, cookie string, promotionStatus []int) (*SubAccountPromotionResp, error) {
	req := SubAccountPromotionReq{
		CampaignType:    []int{1},
		CascadeFields:   []string{"automatic_rule_count"},
		Fields:          []string{},
		IsSophonx:       1,
		Limit:           pageSize,
		Page:            page,
		ProjectIds:      []string{},
		ProjectStatus:   []int{-1},
		PromotionStatus: promotionStatus,
		SortOrder:       1,
		SortStat:        "create_time",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/ads/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountPromotionResp](url).
		AddHeader("Content-Type", "application/json; charset=UTF-8").
		AddHeader("Accept", "application/json, text/plain, */*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetSubAccountPromotionListByProject 根据项目ID获取广告列表
func (b *Bot) GetSubAccountPromotionListByProject(aadvid string, page, pageSize int, cookie string, projectIds []string) (*SubAccountPromotionResp, error) {
	req := SubAccountPromotionReq{
		CampaignType:    []int{1},
		CascadeFields:   []string{"automatic_rule_count"},
		Fields:          []string{},
		IsSophonx:       1,
		Limit:           pageSize,
		Page:            page,
		ProjectIds:      projectIds,
		ProjectStatus:   []int{-1},
		PromotionStatus: []int{0},
		SortOrder:       1,
		SortStat:        "create_time",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/ads/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountPromotionResp](url).
		AddHeader("Content-Type", "application/json; charset=UTF-8").
		AddHeader("Accept", "application/json, text/plain, */*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	// SubAccountPromotionReq 子账户广告列表请求
	SubAccountPromotionReq struct {
		CampaignType    []int    `json:"campaign_type"`    // 广告类型
		CascadeFields   []string `json:"cascade_fields"`   // 级联字段
		Fields          []string `json:"fields"`           // 返回字段
		IsSophonx       int      `json:"isSophonx"`        // 是否Sophonx
		Limit           int      `json:"limit"`            // 每页数量
		Page            int      `json:"page"`             // 页码
		ProjectIds      []string `json:"project_ids"`      // 项目ID列表
		ProjectStatus   []int    `json:"project_status"`   // 项目状态
		PromotionStatus []int    `json:"promotion_status"` // 广告状态
		SortOrder       int      `json:"sort_order"`       // 排序方向 1-降序 0-升序
		SortStat        string   `json:"sort_stat"`        // 排序字段
	}

	// SubAccountPromotionResp 子账户广告列表响应
	SubAccountPromotionResp struct {
		Code      int                     `json:"code"`
		Data      SubAccountPromotionData `json:"data"`
		Msg       string                  `json:"msg"`
		RequestId string                  `json:"request_id"`
		Extra     map[string]interface{}  `json:"extra"`
		Env       SubAccountPromotionEnv  `json:"env"`
	}

	// SubAccountPromotionData 响应数据
	SubAccountPromotionData struct {
		Ads          []SubAccountPromotionItem       `json:"ads"`
		TotalMetrics SubAccountPromotionTotalMetrics `json:"total_metrics"`
		Pagination   SubAccountPromotionPagination   `json:"pagination"`
	}

	// SubAccountPromotionTotalMetrics 总指标
	SubAccountPromotionTotalMetrics struct {
		ShowCnt                     string `json:"show_cnt"`                        // 展示数
		ClickCnt                    string `json:"click_cnt"`                       // 点击数
		Ctr                         string `json:"ctr"`                             // 点击率
		CpmPlatform                 string `json:"cpm_platform"`                    // 千次展示成本
		CpcPlatform                 string `json:"cpc_platform"`                    // 点击成本
		StatCost                    string `json:"stat_cost"`                       // 消耗
		ConvertCnt                  string `json:"convert_cnt"`                     // 转化数
		ConversionCost              string `json:"conversion_cost"`                 // 转化成本
		ConversionRate              string `json:"conversion_rate"`                 // 转化率
		PreConvertCount             string `json:"pre_convert_count"`               // 预转化数
		PreConvertCost              string `json:"pre_convert_cost"`                // 预转化成本
		SearchFirstPositionStatCost string `json:"search_first_position_stat_cost"` // 搜索首位消耗
	}

	// SubAccountPromotionPagination 分页信息
	SubAccountPromotionPagination struct {
		Page       int `json:"page"`
		PageSize   int `json:"page_size"`
		TotalPage  int `json:"total_page"`
		TotalCount int `json:"total_count"`
	}

	// SubAccountPromotionEnv 环境信息
	SubAccountPromotionEnv struct {
		Ppe     bool   `json:"ppe"`
		PpeName string `json:"ppe_name"`
	}

	// SubAccountPromotionItem 广告信息
	SubAccountPromotionItem struct {
		// 基础信息
		PromotionId                  string `json:"promotion_id"`                    // 广告ID
		AdId                         string `json:"ad_id"`                           // 广告ID
		PromotionName                string `json:"promotion_name"`                  // 广告名称
		ProjectId                    string `json:"project_id"`                      // 项目ID
		ProjectName                  string `json:"project_name"`                    // 项目名称
		AdvertiserId                 string `json:"advertiser_id"`                   // 广告主ID
		CampaignId                   string `json:"campaign_id"`                     // 广告组ID
		CreateTime                   string `json:"create_time"`                     // 创建时间
		ModifyTime                   string `json:"modify_time"`                     // 修改时间
		StartTime                    string `json:"start_time"`                      // 开始时间
		EndTime                      string `json:"end_time"`                        // 结束时间
		PromotionCreateTime          string `json:"promotion_create_time"`           // 广告创建时间
		PromotionModifyTime          string `json:"promotion_modify_time"`           // 广告修改时间
		PromotionAggregateModifyTime string `json:"promotion_aggregate_modify_time"` // 广告聚合修改时间

		// 状态信息
		PromotionStatus           int      `json:"promotion_status"`             // 广告状态
		PromotionStatusName       string   `json:"promotion_status_name"`        // 广告状态名称
		PromotionStatusFirst      int      `json:"promotion_status_first"`       // 广告一级状态
		PromotionStatusSecond     []int    `json:"promotion_status_second"`      // 广告二级状态
		PromotionStatusFirstName  string   `json:"promotion_status_first_name"`  // 广告一级状态名称
		PromotionStatusSecondName []string `json:"promotion_status_second_name"` // 广告二级状态名称
		ProjectStatus             int      `json:"project_status"`               // 项目状态
		ProjectStatusName         string   `json:"project_status_name"`          // 项目状态名称
		ProjectStatusFirst        int      `json:"project_status_first"`         // 项目一级状态
		ProjectStatusSecond       []int    `json:"project_status_second"`        // 项目二级状态
		ProjectStatusFirstName    string   `json:"project_status_first_name"`    // 项目一级状态名称
		ProjectStatusSecondName   []string `json:"project_status_second_name"`   // 项目二级状态名称

		// 预算和出价
		AdBudget               string `json:"ad_budget"`                 // 广告预算
		AdBudgetMode           int    `json:"ad_budget_mode"`            // 广告预算模式
		AdBudgetModeName       string `json:"ad_budget_mode_name"`       // 广告预算模式名称
		AdBid                  string `json:"ad_bid"`                    // 广告出价
		ProjectBid             string `json:"project_bid"`               // 项目出价
		CampaignBudget         string `json:"campaign_budget"`           // 广告组预算
		CampaignBudgetMode     int    `json:"campaign_budget_mode"`      // 广告组预算模式
		CampaignBudgetModeName string `json:"campaign_budget_mode_name"` // 广告组预算模式名称

		// 投放相关
		LandingType          int    `json:"landing_type"`           // 落地页类型
		LandingTypeName      string `json:"landing_type_name"`      // 落地页类型名称
		DeliveryMode         int    `json:"delivery_mode"`          // 投放模式
		DeliveryModeInternal int    `json:"delivery_mode_internal"` // 内部投放模式
		DeliveryProduct      int    `json:"delivery_product"`       // 投放产品
		DeliveryProductName  string `json:"delivery_product_name"`  // 投放产品名称
		DeliveryMedium       int    `json:"delivery_medium"`        // 投放媒体
		DeliveryMediumName   string `json:"delivery_medium_name"`   // 投放媒体名称
		DeliverySceneName    string `json:"delivery_scene_name"`    // 投放场景名称
		DeliveryPackage      int    `json:"delivery_package"`       // 投放包

		// 广告类型和状态
		CampaignType     int    `json:"campaign_type"`      // 广告类型
		CampaignTypeName string `json:"campaign_type_name"` // 广告类型名称
		AdOptStatus      int    `json:"ad_opt_status"`      // 广告优化状态
		AdPricing        string `json:"ad_pricing"`         // 广告定价
		AdPricingName    string `json:"ad_pricing_name"`    // 广告定价名称
		AdSmartBidType   int    `json:"ad_smart_bid_type"`  // 广告智能出价类型

		// 外部行为和出价
		ExternalAction         string `json:"external_action"`           // 外部行为
		ExternalActionName     string `json:"external_action_name"`      // 外部行为名称
		DeepExternalActionName string `json:"deep_external_action_name"` // 深度外部行为名称
		DeepBidType            int    `json:"deep_bid_type"`             // 深度出价类型
		DeepBidTypeName        string `json:"deep_bid_type_name"`        // 深度出价类型名称
		DeepCpaBid             string `json:"deep_cpa_bid"`              // 深度CPA出价

		// 下载相关
		DownloadType     int    `json:"download_type"`      // 下载类型
		DownloadTypeName string `json:"download_type_name"` // 下载类型名称
		DownloadUrl      string `json:"download_url"`       // 下载链接

		// 其他设置
		ActionTrackUrl     string `json:"action_track_url"`      // 行为追踪URL
		IsSetPrivative     bool   `json:"is_set_privative"`      // 是否设置私有
		IsPreviewAble      bool   `json:"is_preview_able"`       // 是否可预览
		IsHintRejectReason int    `json:"is_hint_reject_reason"` // 是否提示拒绝原因
		HasAigcMaterials   bool   `json:"has_aigc_materials"`    // 是否有AIGC素材
		HasAigcRepair      string `json:"has_aigc_repair"`       // 是否有AIGC修复
		LearningStatus     int    `json:"learning_status"`       // 学习状态
		LearningConvertCnt string `json:"learning_convert_cnt"`  // 学习转化数

		// 应用代码
		AppCode     []string `json:"app_code"`      // 应用代码
		AppCodeName []string `json:"app_code_name"` // 应用代码名称

		// 统计数据
		Metrics SubAccountPromotionMetrics `json:"metrics"` // 统计数据

		// 营销信息
		MarketingInfo SubAccountPromotionMarketingInfo `json:"marketing_info"`

		// 推广信息
		Promotion SubAccountPromotionInfo `json:"promotion"`

		// 店铺多ROI目标
		ShopMultiRoiGoals SubAccountPromotionShopMultiRoiGoals `json:"shop_multi_roi_goals"`
	}

	// SubAccountPromotionMetrics 广告统计数据
	SubAccountPromotionMetrics struct {
		ShowCnt                     string `json:"show_cnt"`                        // 展示数
		ClickCnt                    string `json:"click_cnt"`                       // 点击数
		Ctr                         string `json:"ctr"`                             // 点击率
		CpmPlatform                 string `json:"cpm_platform"`                    // 千次展示成本
		CpcPlatform                 string `json:"cpc_platform"`                    // 点击成本
		StatCost                    string `json:"stat_cost"`                       // 消耗
		ConvertCnt                  string `json:"convert_cnt"`                     // 转化数
		ConversionCost              string `json:"conversion_cost"`                 // 转化成本
		ConversionRate              string `json:"conversion_rate"`                 // 转化率
		PreConvertCount             string `json:"pre_convert_count"`               // 预转化数
		PreConvertCost              string `json:"pre_convert_cost"`                // 预转化成本
		SearchFirstPositionStatCost string `json:"search_first_position_stat_cost"` // 搜索首位消耗
	}

	// SubAccountPromotionMarketingInfo 营销信息
	SubAccountPromotionMarketingInfo struct {
		MarketingGoal      int    `json:"marketing_goal"`       // 营销目标
		DeliveryRelatedNum int    `json:"delivery_related_num"` // 投放相关数量
		AppPromotionType   string `json:"app_promotion_type"`   // 应用推广类型
	}

	// SubAccountPromotionInfo 推广信息
	SubAccountPromotionInfo struct {
		Budget               string `json:"budget"`                 // 预算
		Source               string `json:"source"`                 // 来源
		AdDownloadStatus     string `json:"ad_download_status"`     // 广告下载状态
		IsCommentDisable     string `json:"is_comment_disable"`     // 是否禁用评论
		EnablePersonalAction bool   `json:"enable_personal_action"` // 启用个人行为
		Bid                  string `json:"bid"`                    // 出价
	}

	// SubAccountPromotionShopMultiRoiGoals 店铺多ROI目标
	SubAccountPromotionShopMultiRoiGoals struct {
		// 根据实际返回数据补充字段
	}
)
