package bot

import (
	"strconv"
)

// GetMaterialVideoAuth 获取素材视频授权
func (b *Bot) GetMaterialVideoAuth(advertiserID int64, cookie string) (*GetMaterialVideoAuthResp, error) {
	if r, err := NewGetApi[GetMaterialVideoAuthResp]("https://ad.oceanengine.com/nbs/api/common/material/video/auth").
		AddHeader("Cookie", cookie).
		AddHeader("Accept", "application/json").
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetMaterialVideoAuthResp 获取素材视频授权响应结构体
type GetMaterialVideoAuthResp struct {
	Code      int                    `json:"code"`       // 响应码
	Data      MaterialVideoAuthData  `json:"data"`       // 授权数据
	Extra     map[string]interface{} `json:"extra"`      // 额外信息
	Msg       string                 `json:"msg"`        // 响应消息
	RequestID string                 `json:"request_id"` // 请求ID
}

// MaterialVideoAuthData 素材视频授权数据结构
type MaterialVideoAuthData struct {
	Token VideoAuthToken `json:"token"` // 授权令牌
}

// VideoAuthToken 视频授权令牌结构
type VideoAuthToken struct {
	AccessKeyID     string `json:"AccessKeyId"`     // 访问密钥ID
	SecretAccessKey string `json:"SecretAccessKey"` // 访问密钥
	SessionToken    string `json:"SessionToken"`    // 会话令牌
	ExpiredTime     string `json:"ExpiredTime"`     // 过期时间
	CurrentTime     string `json:"CurrentTime"`     // 当前时间
}
