package bot

import (
	"encoding/json"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// DeletePromotion 删除广告(可批量删除同一广告主下的多个广告)
func (b *Bot) DeletePromotion(advertiserId string, promotionIds []string, cookie string) (*DeletePromotionResp, error) {
	req := DeletePromotionReq{
		AdvertiserId: advertiserId,
		PromotionIds: promotionIds,
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[DeletePromotionResp]("https://business.oceanengine.com/nbs/api/bm/promotion/ad/delete_superior_ad").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type DeletePromotionReq struct {
	AdvertiserId string   `json:"advertiser_id"`
	PromotionIds []string `json:"promotion_ids"`
}
type DeletePromotionResp struct {
	Code  int      `json:"code"`
	Data  []string `json:"data"` //删除成功的广告ID
	Extra struct {
	} `json:"extra"`
	Msg       string `json:"msg"`
	RequestId string `json:"request_id"`
}
