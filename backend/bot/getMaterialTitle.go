package bot

import (
	"encoding/json"
	"fmt"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetMaterialTitle 获取素材标题
// advertiserID: 广告主ID
// cookie: Cookie信息
// req: 获取素材标题请求参数
func (b *Bot) GetMaterialTitle(advertiserID int64, cookie string, req *GetMaterialTitleReq) (*GetMaterialTitleResp, error) {
	// 序列化请求体
	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 使用NewPostApi发送请求
	if resp, err := NewPostApi[GetMaterialTitleResp]("https://ad.oceanengine.com/superior/api/v2/ad/get_material_title").
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("accept", "application/json").
		AddHeader("content-type", "application/json").
		AddHeader("cookie", cookie).
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(bodyBytes).
		Do(b); err != nil {
		return nil, fmt.Errorf("获取素材标题失败: %v", err)
	} else {
		return resp, nil
	}
}

// GetMaterialTitleReq 获取素材标题请求结构体
type GetMaterialTitleReq struct {
	VideoMaterials      []VideoMaterial      `json:"video_materials"`       // 视频素材列表
	ImageMaterials      []ImageMaterial      `json:"image_materials"`       // 图片素材列表
	AwemePhotoMaterials []AwemePhotoMaterial `json:"aweme_photo_materials"` // 抖音图片素材列表
}

// VideoMaterial 视频素材
type VideoMaterial struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}

// ImageMaterial 图片素材
type ImageMaterial struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}

// AwemePhotoMaterial 抖音图片素材
type AwemePhotoMaterial struct {
	MaterialID string `json:"material_id"` // 素材ID
	LegoMid    string `json:"lego_mid"`    // Lego中台ID
}

// GetMaterialTitleResp 获取素材标题响应结构体
type GetMaterialTitleResp struct {
	Data      GetMaterialTitleData `json:"data"`       // 响应数据
	Code      int                  `json:"code"`       // 状态码，0表示成功
	Msg       string               `json:"msg"`        // 响应消息
	RequestId string               `json:"request_id"` // 请求ID
	Env       EnvInfo              `json:"env"`        // 环境信息
}

// GetMaterialTitleData 素材标题数据
type GetMaterialTitleData struct {
	VideoMaterials      []VideoMaterialWithName      `json:"video_materials"`       // 视频素材列表（带名称）
	ImageMaterials      []ImageMaterialWithName      `json:"image_materials"`       // 图片素材列表（带名称）
	AwemePhotoMaterials []AwemePhotoMaterialWithName `json:"aweme_photo_materials"` // 抖音图片素材列表（带名称）
}

// VideoMaterialWithName 带名称的视频素材
type VideoMaterialWithName struct {
	LegoMid      string `json:"lego_mid"`      // Lego中台ID
	MaterialID   string `json:"material_id"`   // 素材ID
	MaterialName string `json:"material_name"` // 素材名称
}

// ImageMaterialWithName 带名称的图片素材
type ImageMaterialWithName struct {
	LegoMid      string `json:"lego_mid"`      // Lego中台ID
	MaterialID   string `json:"material_id"`   // 素材ID
	MaterialName string `json:"material_name"` // 素材名称
}

// AwemePhotoMaterialWithName 带名称的抖音图片素材
type AwemePhotoMaterialWithName struct {
	LegoMid      string `json:"lego_mid"`      // Lego中台ID
	MaterialID   string `json:"material_id"`   // 素材ID
	MaterialName string `json:"material_name"` // 素材名称
}

// EnvInfo 环境信息
type EnvInfo struct {
	Ppe bool `json:"ppe"` // 是否为PPE環境
}
