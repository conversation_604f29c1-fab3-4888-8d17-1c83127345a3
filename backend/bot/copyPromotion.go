package bot

import (
	"fmt"
	"strconv"
	"time"
)

// CopyPromotion 复制广告
func (b *Bot) CopyPromotion(advertiserID int64, sourcePromotionID string, newPromotionName string, newProjectID string, cookie string, videoMaterialInfo []VideoMaterialInfo) (*CreatePromotionResp, error) {
	// 1. 先获取源广告的详情
	promotionInfo, err := b.GetPromotionInfo(advertiserID, []string{sourcePromotionID}, false, cookie)
	if err != nil {
		return nil, fmt.Errorf("获取源广告详情失败: %v", err)
	}

	// 检查是否获取到广告数据
	sourceData, exists := promotionInfo.Data[sourcePromotionID]
	if !exists {
		return nil, fmt.Errorf("未找到广告ID为 %s 的广告详情", sourcePromotionID)
	}

	// 检查广告是否可以复制
	if !sourceData.CanCopy {
		return nil, fmt.Errorf("广告ID为 %s 的广告不允许复制", sourcePromotionID)
	}

	// 检查是否为自动投放模式 (delivery_mode = 3 表示自动投放)
	isAutoDelivery := sourceData.Project.MarketingInfo.DeliveryMode == 3

	// 2. 构建创建广告的请求数据
	createReq := &CreatePromotionReq{
		Name:               newPromotionName,
		ProjectID:          newProjectID,
		PromotionID:        "",             // 新广告ID为空，由系统生成
		CheckHash:          "",             // 校验哈希为空
		IsAutoDeliveryMode: isAutoDelivery, // 根据源广告的投放模式设置
		PromotionData:      mapPromotionData(sourceData, isAutoDelivery),
		MaterialGroup:      mapMaterialGroup(sourceData.MaterialGroup, videoMaterialInfo),
	}

	// 3. 调用创建广告接口
	result, err := b.CreatePromotion(advertiserID, createReq, cookie)
	if err != nil {
		return nil, fmt.Errorf("创建新广告失败: %v", err)
	}

	return result, nil
}

// mapPromotionData 将获取到的广告数据映射到创建请求的广告数据结构
func mapPromotionData(sourceData PromotionInfoData, isAutoDelivery bool) PromotionData {
	promotionData := PromotionData{
		ClientSettings:       PromotionClientSettings{}, // 客户端设置为空
		OriginPromotionID:    sourceData.ID,             // 原广告ID
		EnablePersonalAction: sourceData.Data.EnablePersonalAction,
		Budget:               sourceData.Data.Budget,
	}

	// 如果不是自动投放模式，才设置出价参数
	if !isAutoDelivery {
		promotionData.Bid = &sourceData.Data.Bid
	}
	// 自动投放模式下Bid字段保持为nil，不会被序列化到JSON中，避免"自动投放广告不能包含出价参数"错误

	return promotionData
}

// mapMaterialGroup 将获取到的素材组映射到创建请求的素材组结构
func mapMaterialGroup(sourceMaterial PromotionMaterialGroup, additionalVideoMaterialInfo []VideoMaterialInfo) MaterialGroup {
	// 如果提供了新的视频素材，则只使用新素材；否则使用源广告的视频素材
	var videoMaterials []VideoMaterialInfo
	if additionalVideoMaterialInfo != nil && len(additionalVideoMaterialInfo) > 0 {
		videoMaterials = additionalVideoMaterialInfo
	} else {
		videoMaterials = mapVideoMaterialInfo(sourceMaterial.VideoMaterialInfo)
	}

	return MaterialGroup{
		PlayableMaterialInfo:     []interface{}{}, // 可播放素材信息为空
		VideoMaterialInfo:        videoMaterials,
		ImageMaterialInfo:        []interface{}{}, // 图片素材信息为空
		ComponentMaterialInfo:    []interface{}{}, // 组件素材信息为空
		CallToActionMaterialInfo: mapCallToActionMaterialInfo(sourceMaterial.CallToActionMaterialInfo),
		ProductInfo:              mapProductInfo(sourceMaterial.ProductInfo),
		TitleMaterialInfo:        mapTitleMaterialInfo(sourceMaterial.TitleMaterialInfo),
	}
}

// mapVideoMaterialInfo 映射视频素材信息
func mapVideoMaterialInfo(sourceVideos []PromotionVideoMaterialInfo) []VideoMaterialInfo {
	var videos []VideoMaterialInfo
	for _, sourceVideo := range sourceVideos {
		video := VideoMaterialInfo{
			ImageMode: sourceVideo.ImageMode,
			ImageInfo: mapImageInfo(sourceVideo.ImageInfo),
			VideoInfo: mapVideoInfo(sourceVideo.VideoInfo),
			CopyMode:  sourceVideo.CopyMode,
		}
		videos = append(videos, video)
	}
	return videos
}

// mapImageInfo 映射图片信息
func mapImageInfo(sourceImages []PromotionImageInfo) []ImageInfo {
	var images []ImageInfo
	for _, sourceImage := range sourceImages {
		image := ImageInfo{
			Height:  sourceImage.Height,
			Width:   sourceImage.Width,
			WebURI:  sourceImage.WebURI,
			SignURL: sourceImage.SignURL,
		}
		images = append(images, image)
	}
	return images
}

// mapVideoInfo 映射视频信息
func mapVideoInfo(sourceVideo PromotionVideoInfo) VideoInfo {
	return VideoInfo{
		VideoID:         sourceVideo.VideoID,
		Status:          sourceVideo.Status,
		VideoDuration:   sourceVideo.VideoDuration,
		InitialSize:     sourceVideo.InitialSize,
		VideoUnique:     sourceVideo.VideoUnique,
		FileMD5:         sourceVideo.FileMD5,
		ThumbHeight:     sourceVideo.ThumbHeight,
		ThumbWidth:      sourceVideo.ThumbWidth,
		ThumbURI:        sourceVideo.ThumbURI,
		OriginalFileURI: sourceVideo.OriginalFileURI,
		Duration:        sourceVideo.Duration,
		ErrorDesc:       sourceVideo.ErrorDesc,
		UserReference:   sourceVideo.UserReference,
		Width:           sourceVideo.Width,
		Height:          sourceVideo.Height,
		VID:             sourceVideo.VID,
		UploadID:        sourceVideo.UploadID,
		CoverURI:        sourceVideo.CoverURI,
		Codec:           sourceVideo.Codec,
		Bitrate:         sourceVideo.Bitrate,
	}
}

// mapCallToActionMaterialInfo 映射行动号召素材信息
func mapCallToActionMaterialInfo(sourceCTAs []PromotionCallToActionMaterialInfo) []CallToActionMaterialInfo {
	var ctas []CallToActionMaterialInfo
	for _, sourceCTA := range sourceCTAs {
		cta := CallToActionMaterialInfo{
			CDPMaterialID: sourceCTA.CDPMaterialID,
			CallToAction:  sourceCTA.CallToAction,
		}
		ctas = append(ctas, cta)
	}
	return ctas
}

// mapProductInfo 映射产品信息
func mapProductInfo(sourceProduct PromotionProductInfo) ProductInfo {
	return ProductInfo{
		ProductName:          mapProductName(sourceProduct.ProductName),
		ProductImages:        mapProductImages(sourceProduct.ProductImages),
		ProductSellingPoints: mapProductSellingPoints(sourceProduct.ProductSellingPoints),
	}
}

// mapProductName 映射产品名称
func mapProductName(sourceName PromotionProductName) ProductName {
	return ProductName{
		CDPMaterialID: sourceName.CDPMaterialID,
		Name:          sourceName.Name,
	}
}

// mapProductImages 映射产品图片
func mapProductImages(sourceImages []PromotionProductImage) []ProductImage {
	var images []ProductImage
	for _, sourceImage := range sourceImages {
		if sourceImage.Product != nil { // Product 字段有值代表是项目下的产品图，而非广告下的
			continue
		}
		image := ProductImage{
			ImageURI: sourceImage.ImageURI,
			Width:    sourceImage.Width,
			Height:   sourceImage.Height,
		}
		images = append(images, image)
	}
	return images
}

// mapProductSellingPoints 映射产品卖点
func mapProductSellingPoints(sourcePoints []PromotionProductSellingPoint) []ProductSellingPoint {
	var points []ProductSellingPoint
	for _, sourcePoint := range sourcePoints {
		point := ProductSellingPoint{
			CDPMaterialID: sourcePoint.CDPMaterialID,
			SellingPoint:  sourcePoint.SellingPoint,
		}
		points = append(points, point)
	}
	return points
}

// mapTitleMaterialInfo 映射标题素材信息
func mapTitleMaterialInfo(sourceTitles []PromotionTitleMaterialInfo) []TitleMaterialInfo {
	var titles []TitleMaterialInfo
	for _, sourceTitle := range sourceTitles {
		title := TitleMaterialInfo{
			Title:     sourceTitle.Title,
			IsDynamic: sourceTitle.IsDynamic,
		}
		titles = append(titles, title)
	}
	return titles
}

// TestCopyPromotion 测试广告复制功能
func TestCopyPromotion() {
	// 创建Bot实例
	bot := &Bot{}

	// 测试参数
	advertiserID := int64(1806067471523945)                                 // 广告主ID
	sourcePromotionID := "7514563811636396043"                              // 源广告ID
	newPromotionName := "复制的广告_" + strconv.FormatInt(time.Now().Unix(), 10) // 新广告名称，添加时间戳避免重复
	newProjectID := "7507184522831003684"                                   // 新项目ID（可以是同一个项目或不同项目）
	cookie := "your_cookie_here"                                            // 需要替换为实际的Cookie

	fmt.Println("开始测试广告复制功能...")
	fmt.Printf("源广告ID: %s\n", sourcePromotionID)
	fmt.Printf("新广告名称: %s\n", newPromotionName)
	fmt.Printf("目标项目ID: %s\n", newProjectID)

	// 执行复制操作
	result, err := bot.CopyPromotion(advertiserID, sourcePromotionID, newPromotionName, newProjectID, cookie, nil)
	if err != nil {
		fmt.Printf("复制广告失败: %v\n", err)
		return
	}

	// 打印结果
	fmt.Printf("复制广告成功！\n")
	fmt.Printf("响应码: %d\n", result.Code)
	fmt.Printf("响应消息: %s\n", result.Msg)
	fmt.Printf("新广告ID: %s\n", result.Data.PromotionID)
	fmt.Printf("请求ID: %s\n", result.RequestID)

	// 如果有关键词错误，也打印出来
	if len(result.Data.KeywordsError) > 0 {
		fmt.Printf("关键词错误: %v\n", result.Data.KeywordsError)
	}
}

// BatchCopyPromotion 批量复制广告
func (b *Bot) BatchCopyPromotion(advertiserID int64, sourcePromotionIDs []string, namePrefix string, targetProjectID string, cookie string) ([]CreatePromotionResp, []error) {
	var results []CreatePromotionResp
	var errors []error

	for i, sourceID := range sourcePromotionIDs {
		// 生成新广告名称，添加序号和时间戳
		newName := fmt.Sprintf("%s_%d_%d", namePrefix, i+1, time.Now().Unix())

		fmt.Printf("正在复制广告 %d/%d: %s -> %s\n", i+1, len(sourcePromotionIDs), sourceID, newName)

		result, err := b.CopyPromotion(advertiserID, sourceID, newName, targetProjectID, cookie, nil)
		if err != nil {
			fmt.Printf("复制广告 %s 失败: %v\n", sourceID, err)
			errors = append(errors, fmt.Errorf("复制广告 %s 失败: %v", sourceID, err))
			continue
		}

		results = append(results, *result)
		fmt.Printf("复制广告 %s 成功，新广告ID: %s\n", sourceID, result.Data.PromotionID)

		// 添加延迟避免请求过快
		time.Sleep(1 * time.Second)
	}

	return results, errors
}

// TestBatchCopyPromotion 测试批量复制广告功能
func TestBatchCopyPromotion() {
	// 创建Bot实例
	bot := &Bot{}

	// 测试参数
	advertiserID := int64(1806067471523945)                          // 广告主ID
	sourcePromotionIDs := []string{"7514563811636396043", "另一个广告ID"} // 源广告ID列表
	namePrefix := "批量复制广告"                                           // 新广告名称前缀
	targetProjectID := "7507184522831003684"                         // 目标项目ID
	cookie := "your_cookie_here"                                     // 需要替换为实际的Cookie

	fmt.Println("开始测试批量复制广告功能...")
	fmt.Printf("源广告ID列表: %v\n", sourcePromotionIDs)
	fmt.Printf("广告名称前缀: %s\n", namePrefix)
	fmt.Printf("目标项目ID: %s\n", targetProjectID)

	// 执行批量复制操作
	results, errors := bot.BatchCopyPromotion(advertiserID, sourcePromotionIDs, namePrefix, targetProjectID, cookie)

	// 打印结果
	fmt.Printf("\n批量复制完成！\n")
	fmt.Printf("成功复制: %d 个广告\n", len(results))
	fmt.Printf("失败: %d 个广告\n", len(errors))

	if len(results) > 0 {
		fmt.Println("\n成功复制的广告:")
		for i, result := range results {
			fmt.Printf("  %d. 新广告ID: %s\n", i+1, result.Data.PromotionID)
		}
	}

	if len(errors) > 0 {
		fmt.Println("\n复制失败的错误:")
		for i, err := range errors {
			fmt.Printf("  %d. %v\n", i+1, err)
		}
	}
}
