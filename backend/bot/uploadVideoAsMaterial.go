package bot

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"hash/crc32"
	"os"
	"path/filepath"
)

func (b *Bot) UploadVideoAsMaterial(advertiserID int64, cookie string, filePath string) (*UploadVideoAsMaterialResp, error) {
	auth, err := b.GetMaterialVideoAuth(advertiserID, cookie)
	if err != nil {
		return nil, fmt.Errorf("获取素材视频授权失败: %v", err)
	}
	accessKey := auth.Data.Token.AccessKeyID
	SecretAccessKey := auth.Data.Token.SecretAccessKey
	SessionToken := auth.Data.Token.SessionToken

	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	uploadInfo, err := b.GetApplyUploadInner(len(fileData), accessKey, SecretAccessKey, SessionToken)
	if err != nil {
		return nil, fmt.Errorf("获取上传信息失败: %v", err)
	}
	// 计算文件MD5值
	hash := md5.Sum(fileData)
	md5Hash := hex.EncodeToString(hash[:])
	fmt.Printf("计算的文件MD5值: %s\n", md5Hash)

	fmt.Println("开始上传文件...")
	//十进制转十六进制
	crc32Hex := fmt.Sprintf("%08x", crc32.ChecksumIEEE(fileData))
	// 调用UploadFile方法
	resp, err := b.UploadFile(fileData, uploadInfo, crc32Hex)
	if err != nil {
		fmt.Printf("上传文件失败: %v\n", err)
		return nil, fmt.Errorf("上传文件失败: %v", err)
	}
	fmt.Printf("文件上传成功: Code=%d, Message=%s, CRC32=%s\n",
		resp.Code, resp.Message, resp.Data.CRC32)

	// 接下来调用CommitUploadInner方法提交上传
	fmt.Println("开始提交上传...")

	// 从上传信息中获取SessionKey（从第一个上传节点获取）
	if len(uploadInfo.Result.InnerUploadAddress.UploadNodes) == 0 {
		fmt.Println("没有可用的上传节点，无法获取SessionKey")
		return nil, fmt.Errorf("没有可用的上传节点，无法获取SessionKey")
	}
	sessionKey := uploadInfo.Result.InnerUploadAddress.UploadNodes[0].SessionKey

	// 调用CommitUploadInner方法
	commitResp, err := b.CommitUploadInner(sessionKey, accessKey, SecretAccessKey, SessionToken)
	if err != nil {
		fmt.Printf("提交上传失败: %v\n", err)
		return nil, fmt.Errorf("提交上传失败: %v", err)
	}

	fmt.Printf("提交上传成功: RequestId=%s\n", commitResp.ResponseMetadata.RequestId)

	// 打印视频元数据
	if len(commitResp.Result.Results) > 0 {
		result := commitResp.Result.Results[0]
		fmt.Printf("视频信息: Vid=%s, 时长=%.2f秒, 分辨率=%dx%d, 格式=%s, 大小=%d字节\n",
			result.Vid, result.VideoMeta.Duration, result.VideoMeta.Width, result.VideoMeta.Height,
			result.VideoMeta.Format, result.VideoMeta.Size)
		fmt.Printf("视频MD5: %s\n", result.VideoMeta.Md5)
		fmt.Printf("封面URI: %s\n", result.PosterUri)
	}

	// 接下来调用CreateMaterial方法创建素材
	fmt.Println("开始创建素材...")

	// 调用CreateMaterial方法，使用计算出的MD5值
	// 获取Vid
	if len(uploadInfo.Result.InnerUploadAddress.UploadNodes) == 0 {
		fmt.Println("没有可用的上传节点")
		return nil, fmt.Errorf("没有可用的上传节点")
	}
	vid := uploadInfo.Result.InnerUploadAddress.UploadNodes[0].Vid

	materialResp, err := b.CreateMaterial(advertiserID, cookie, vid, md5Hash)
	if err != nil {
		fmt.Printf("创建素材失败: %v\n", err)
		return nil, fmt.Errorf("创建素材失败: %v", err)
	}

	fmt.Printf("创建素材成功: Code=%d, Message=%s, RequestID=%s\n",
		materialResp.Code, materialResp.Msg, materialResp.RequestID)

	// 打印素材ID映射
	for vid, materialID := range materialResp.Data {
		fmt.Printf("Vid: %s -> MaterialID: %s\n", vid, materialID)
	}

	// 接下来调用ConfirmVideo方法确认视频
	fmt.Println("开始确认视频...")

	videoMeta := commitResp.Result.Results[0]
	// 构建视频确认信息
	videos := []VideoConfirmItem{
		{
			ID:   vid,
			Name: filepath.Base(filePath), // 视频文件名
			VideoInfo: VideoConfirmInfo{
				VideoID:      vid,
				Bitrate:      videoMeta.VideoMeta.Bitrate,  // 比特率（示例值，实际需要从视频分析中获取）
				Duration:     videoMeta.VideoMeta.Duration, // 时长（示例值，实际需要从视频分析中获取）
				FileType:     videoMeta.VideoMeta.FileType,
				Format:       videoMeta.VideoMeta.Format,
				Height:       videoMeta.VideoMeta.Height,       // 高度（示例值，实际需要从视频分析中获取）
				MD5:          videoMeta.VideoMeta.Md5,          // 使用计算的MD5值
				OriginHeight: videoMeta.VideoMeta.OriginHeight, // 原始高度
				OriginWidth:  videoMeta.VideoMeta.OriginWidth,  // 原始宽度
				Size:         int64(videoMeta.VideoMeta.Size),  // 文件大小
				CoverURI:     videoMeta.PosterUri,              // 封面URI（示例值）
				Width:        videoMeta.VideoMeta.Width,        // 宽度（示例值）
				URI:          videoMeta.VideoMeta.Uri,          // 视频URI（示例值）
			},
		},
	}

	// 调用ConfirmVideo方法
	confirmResp, err := b.ConfirmVideo(advertiserID, cookie, videos)
	if err != nil {
		fmt.Printf("确认视频失败: %v\n", err)
		return nil, fmt.Errorf("确认视频失败: %v", err)
	}

	fmt.Printf("确认视频成功: Code=%d, Message=%s\n", confirmResp.Code, confirmResp.Msg)

	// 打印确认结果
	for _, result := range confirmResp.Data.Results {
		fmt.Printf("确认结果: MaterialID=%s, VideoID=%s, StatusCode=%d\n",
			result.MaterialID, result.RequestVideoID, result.StatusCode)

		// 打印所有者更新结果
		for _, owner := range result.UpdateOwnerResults {
			fmt.Printf("所有者更新: OwnerID=%s, OwnerType=%d, StatusCode=%d\n",
				owner.OwnerID, owner.OwnerType, owner.StatusCode)
		}
	}

	fmt.Println("完整的视频素材上传和确认流程执行成功!")

	// 构建返回结果
	result := &UploadVideoAsMaterialResp{
		ImageInfo: []UploadVideoImageInfo{
			{
				Width:   videoMeta.VideoMeta.Width,
				Height:  videoMeta.VideoMeta.Height,
				WebURI:  videoMeta.PosterUri,
				SignURL: "", // 这里可能需要根据实际情况生成签名URL
			},
		},
		VideoInfo: UploadVideoInfo{
			Status:      10, // 假设状态为10表示成功
			ThumbHeight: videoMeta.VideoMeta.Height,
			ThumbWidth:  videoMeta.VideoMeta.Width,
			InitialSize: int64(videoMeta.VideoMeta.Size),
			Height:      videoMeta.VideoMeta.Height,
			Width:       videoMeta.VideoMeta.Width,
			Duration:    int(videoMeta.VideoMeta.Duration),
			FileMD5:     videoMeta.VideoMeta.Md5,
			Bitrate:     int64(videoMeta.VideoMeta.Bitrate),
			VideoID:     videoMeta.Vid,
			CoverURI:    videoMeta.PosterUri,
			VID:         videoMeta.Vid,
		},
		ImageMode: 15, // 根据您的示例数据
		CoverType: 1,  // 根据您的示例数据
	}

	return result, nil
}

// UploadVideoAsMaterialResp 上传视频作为素材的响应结构体
type UploadVideoAsMaterialResp struct {
	ImageInfo []UploadVideoImageInfo `json:"image_info"` // 图片信息
	VideoInfo UploadVideoInfo        `json:"video_info"` // 视频信息
	ImageMode int                    `json:"image_mode"` // 图片模式
	CoverType int                    `json:"cover_type"` // 封面类型
}

// UploadVideoImageInfo 上传视频图片信息结构体
type UploadVideoImageInfo struct {
	Width   int    `json:"width"`    // 宽度
	Height  int    `json:"height"`   // 高度
	WebURI  string `json:"web_uri"`  // Web URI
	SignURL string `json:"sign_url"` // 签名URL
}

// UploadVideoInfo 上传视频信息结构体
type UploadVideoInfo struct {
	Status      int    `json:"status"`       // 状态
	ThumbHeight int    `json:"thumb_height"` // 缩略图高度
	ThumbWidth  int    `json:"thumb_width"`  // 缩略图宽度
	InitialSize int64  `json:"initial_size"` // 初始大小
	Height      int    `json:"height"`       // 高度
	Width       int    `json:"width"`        // 宽度
	Duration    int    `json:"duration"`     // 时长
	FileMD5     string `json:"file_md5"`     // 文件MD5
	Bitrate     int64  `json:"bitrate"`      // 比特率
	VideoID     string `json:"video_id"`     // 视频ID
	CoverURI    string `json:"cover_uri"`    // 封面URI
	VID         string `json:"vid"`          // 视频标识
}
