package bot

import (
	"encoding/json"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// ConcernAddSubAccount 收藏项目
func (b *Bot) ConcernAddSubAccount(advertiserId int64, cookie string) (*ConcernAddSubAccountResp, error) {
	req := ConcernAddSubAccountReq{
		AccountId: fmt.Sprintf("%d", advertiserId),
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[ConcernAddSubAccountResp]("https://business.oceanengine.com/nbs/api/bm/promotion/concern_add").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	ConcernAddSubAccountReq struct {
		AccountId string `json:"account_id"`
	}

	// ConcernAddSubAccountResp 响应体
	ConcernAddSubAccountResp struct {
		Code int `json:"code"`
		Data struct {
		} `json:"data"`
		Extra struct {
		} `json:"extra"`
		Msg       string `json:"msg"`
		RequestId string `json:"request_id"`
	}
)
