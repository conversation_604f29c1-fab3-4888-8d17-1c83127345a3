package bot

import (
	"encoding/json"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// GetSubAccountProjectList 获取子账户下的项目列表
func (b *Bot) GetSubAccountProjectList(aadvid string, page, pageSize int, cookie string, startDate, endDate string) (*SubAccountProjectResp, error) {
	// 如果没有提供日期，使用今天
	if startDate == "" {
		startDate = "2025-07-04"
	}
	if endDate == "" {
		endDate = "2025-07-04"
	}

	req := SubAccountProjectReq{
		St:              startDate,
		Et:              endDate,
		SortStat:        "create_time",
		ProjectStatus:   []int{-1},
		PromotionStatus: []int{-1},
		Limit:           pageSize,
		Page:            page,
		SortOrder:       1,
		CampaignType:    []int{1},
		Fields: []string{
			"stat_cost",
			"convert_cnt",
			"conversion_cost",
			"show_cnt",
			"click_cnt",
			"conversion_rate",
			"cpm_platform",
			"cpc_platform",
			"pre_convert_count",
			"pre_convert_cost",
		},
		IsSophonx:     1,
		SearchType:    "8",
		CascadeFields: []string{"support_cost_rate_7d", "budget_optimize_switch"},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/projects/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountProjectResp](url).
		AddHeader("Content-Type", "application/json;charset=UTF-8").
		AddHeader("Accept", "*/*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetSubAccountProjectListSimple 简化版本，使用默认日期
func (b *Bot) GetSubAccountProjectListSimple(aadvid string, page, pageSize int, cookie string) (*SubAccountProjectResp, error) {
	return b.GetSubAccountProjectList(aadvid, page, pageSize, cookie, "", "")
}

type (
	// SubAccountProjectReq 子账户项目列表请求
	SubAccountProjectReq struct {
		St              string   `json:"st"`                // 开始时间
		Et              string   `json:"et"`                // 结束时间
		SortStat        string   `json:"sort_stat"`         // 排序字段
		ProjectStatus   []int    `json:"project_status"`    // 项目状态
		PromotionStatus []int    `json:"promotion_status"`  // 广告状态
		Limit           int      `json:"limit"`             // 每页数量
		Page            int      `json:"page"`              // 页码
		SortOrder       int      `json:"sort_order"`        // 排序方向 1-降序 0-升序
		CampaignType    []int    `json:"campaign_type"`     // 广告类型
		Fields          []string `json:"fields"`            // 返回字段
		IsSophonx       int      `json:"isSophonx"`         // 是否Sophonx
		SearchType      string   `json:"search_type"`       // 搜索类型
		CascadeFields   []string `json:"cascade_fields"`    // 级联字段
		Keyword         string   `json:"keyword,omitempty"` // 搜索关键词
	}

	// SubAccountProjectResp 子账户项目列表响应
	SubAccountProjectResp struct {
		Code      int                    `json:"code"`
		Data      SubAccountProjectData  `json:"data"`
		Msg       string                 `json:"msg"`
		RequestId string                 `json:"request_id"`
		Extra     map[string]interface{} `json:"extra"`
		Env       SubAccountEnv          `json:"env"`
	}

	// SubAccountProjectData 响应数据
	SubAccountProjectData struct {
		Projects     []SubAccountProjectItem `json:"projects"`
		TotalMetrics SubAccountTotalMetrics  `json:"total_metrics"`
		Pagination   SubAccountPagination    `json:"pagination"`
	}

	// SubAccountTotalMetrics 总指标
	SubAccountTotalMetrics struct {
		ShowCnt         string `json:"show_cnt"`
		ClickCnt        string `json:"click_cnt"`
		Ctr             string `json:"ctr"`
		CpmPlatform     string `json:"cpm_platform"`
		CpcPlatform     string `json:"cpc_platform"`
		StatCost        string `json:"stat_cost"`
		ConvertCnt      string `json:"convert_cnt"`
		ConversionCost  string `json:"conversion_cost"`
		ConversionRate  string `json:"conversion_rate"`
		PreConvertCount string `json:"pre_convert_count"`
		PreConvertCost  string `json:"pre_convert_cost"`
	}

	// SubAccountPagination 分页信息
	SubAccountPagination struct {
		Page       int `json:"page"`
		PageSize   int `json:"page_size"`
		TotalPage  int `json:"total_page"`
		TotalCount int `json:"total_count"`
	}

	// SubAccountEnv 环境信息
	SubAccountEnv struct {
		Ppe     bool   `json:"ppe"`
		PpeName string `json:"ppe_name"`
	}

	// SubAccountProjectItem 项目信息
	SubAccountProjectItem struct {
		// 基础信息
		ProjectId         string `json:"project_id"`          // 项目ID
		ProjectName       string `json:"project_name"`        // 项目名称
		ProjectStatus     int    `json:"project_status"`      // 项目状态
		ProjectStatusName string `json:"project_status_name"` // 项目状态名称
		AdvertiserId      string `json:"advertiser_id"`       // 广告主ID
		CampaignId        string `json:"campaign_id"`         // 广告组ID
		CreateTime        string `json:"create_time"`         // 创建时间
		ModifyTime        string `json:"modify_time"`         // 修改时间
		StartTime         string `json:"start_time"`          // 开始时间
		EndTime           string `json:"end_time"`            // 结束时间

		// 预算和出价
		CampaignBudget      string  `json:"campaign_budget"`        // 项目预算
		ProjectBid          string  `json:"project_bid"`            // 项目出价
		ProjectDeepCpaBid   string  `json:"project_deep_cpa_bid"`   // 项目深度出价
		ProjectRoiGoal      float64 `json:"project_roi_goal"`       // 项目ROI目标
		ProjectFirstRoiGoal int     `json:"project_first_roi_goal"` // 项目一级ROI目标

		// 投放相关
		LandingType          int    `json:"landing_type"`           // 落地页类型
		LandingTypeName      string `json:"landing_type_name"`      // 落地页类型名称
		DeliveryMode         int    `json:"delivery_mode"`          // 投放模式
		DeliveryModeInternal int    `json:"delivery_mode_internal"` // 内部投放模式
		DeliveryProduct      int    `json:"delivery_product"`       // 投放产品
		DeliveryProductName  string `json:"delivery_product_name"`  // 投放产品名称
		DeliveryMedium       int    `json:"delivery_medium"`        // 投放媒体
		DeliveryMediumName   string `json:"delivery_medium_name"`   // 投放媒体名称
		DeliverySceneName    string `json:"delivery_scene_name"`    // 投放场景名称
		DeliveryPackage      int    `json:"delivery_package"`       // 投放包

		// 广告类型和状态
		CampaignType           int    `json:"campaign_type"`             // 广告类型
		CampaignTypeName       string `json:"campaign_type_name"`        // 广告类型名称
		CampaignStatus         int    `json:"campaign_status"`           // 广告组状态
		CampaignOptStatus      int    `json:"campaign_opt_status"`       // 广告组优化状态
		CampaignBudgetMode     int    `json:"campaign_budget_mode"`      // 预算模式
		CampaignBudgetModeName string `json:"campaign_budget_mode_name"` // 预算模式名称

		// 外部行为和出价
		ExternalAction         string `json:"external_action"`           // 外部行为
		ExternalActionName     string `json:"external_action_name"`      // 外部行为名称
		DeepExternalActionName string `json:"deep_external_action_name"` // 深度外部行为名称
		DeepBidType            int    `json:"deep_bid_type"`             // 深度出价类型
		DeepBidTypeName        string `json:"deep_bid_type_name"`        // 深度出价类型名称
		AdPricing              string `json:"ad_pricing"`                // 广告定价
		AdPricingName          string `json:"ad_pricing_name"`           // 广告定价名称
		SmartBidType           string `json:"smart_bid_type"`            // 智能出价类型

		// 项目状态
		ProjectStatusFirst      int      `json:"project_status_first"`       // 项目一级状态
		ProjectStatusSecond     []int    `json:"project_status_second"`      // 项目二级状态
		ProjectStatusFirstName  string   `json:"project_status_first_name"`  // 项目一级状态名称
		ProjectStatusSecondName []string `json:"project_status_second_name"` // 项目二级状态名称

		// 其他设置
		FeedDeliverySearch         int    `json:"feed_delivery_search"`          // 信息流投放搜索
		ProjectAggregateModifyTime string `json:"project_aggregate_modify_time"` // 项目聚合修改时间
		DownloadType               int    `json:"download_type"`                 // 下载类型
		DownloadTypeName           string `json:"download_type_name"`            // 下载类型名称
		DownloadUrl                string `json:"download_url"`                  // 下载链接
		ActionTrackUrl             string `json:"action_track_url"`              // 行为追踪URL
		AssetTypeName              string `json:"asset_type_name"`               // 资产类型名称

		// 补偿相关
		CompensateAmount         string `json:"compensate_amount"`          // 补偿金额
		CompensateStatus         int    `json:"compensate_status"`          // 补偿状态
		CompensateUrl            string `json:"compensate_url"`             // 补偿链接
		CompensateEndReasons     string `json:"compensate_end_reasons"`     // 补偿结束原因
		CompensateInvalidReasons string `json:"compensate_invalid_reasons"` // 补偿无效原因

		// 分类和权限
		Classify        string `json:"classify"`          // 分类
		CanSetPrivative bool   `json:"can_set_privative"` // 可设置私有
		IsSetPrivative  bool   `json:"is_set_privative"`  // 是否设置私有

		// 应用代码
		AppCode     []string `json:"app_code"`      // 应用代码
		AppCodeName []string `json:"app_code_name"` // 应用代码名称

		// 支持成本率
		SupportCostRate7d int `json:"support_cost_rate7d"` // 7天支持成本率

		// 统计数据
		Metrics SubAccountProjectMetrics `json:"metrics"` // 统计数据

		// 营销信息
		MarketingInfo SubAccountMarketingInfo `json:"marketing_info"`

		// 推广策略
		PromotionStrategy SubAccountPromotionStrategy `json:"promotion_strategy"`

		// 推广对象
		PromotionObject SubAccountPromotionObject `json:"promotion_object"`

		// 店铺多ROI目标
		ShopMultiRoiGoals SubAccountShopMultiRoiGoals `json:"shop_multi_roi_goals"`

		// 受众和库存
		Audience  map[string]interface{} `json:"audience"`  // 受众
		Inventory map[string]interface{} `json:"inventory"` // 库存
	}

	// SubAccountProjectMetrics 项目统计数据
	SubAccountProjectMetrics struct {
		ShowCnt         string `json:"show_cnt"`          // 展示数
		ClickCnt        string `json:"click_cnt"`         // 点击数
		Ctr             string `json:"ctr"`               // 点击率
		CpmPlatform     string `json:"cpm_platform"`      // 千次展示成本
		CpcPlatform     string `json:"cpc_platform"`      // 点击成本
		StatCost        string `json:"stat_cost"`         // 消耗
		ConvertCnt      string `json:"convert_cnt"`       // 转化数
		ConversionCost  string `json:"conversion_cost"`   // 转化成本
		ConversionRate  string `json:"conversion_rate"`   // 转化率
		PreConvertCount string `json:"pre_convert_count"` // 预转化数
		PreConvertCost  string `json:"pre_convert_cost"`  // 预转化成本
	}

	// SubAccountMarketingInfo 营销信息
	SubAccountMarketingInfo struct {
		MarketingGoal      int    `json:"marketing_goal"`       // 营销目标
		DeliveryRelatedNum int    `json:"delivery_related_num"` // 投放相关数量
		AppPromotionType   string `json:"app_promotion_type"`   // 应用推广类型
	}

	// SubAccountPromotionStrategy 推广策略
	SubAccountPromotionStrategy struct {
		// 根据实际返回数据补充字段
	}

	// SubAccountPromotionObject 推广对象
	SubAccountPromotionObject struct {
		ProductId       string `json:"productId"`       // 产品ID
		PromotionType   int    `json:"promotionType"`   // 推广类型
		UniqueProductId string `json:"uniqueProductId"` // 唯一产品ID
		QuickAppId      string `json:"quickAppId"`      // 快应用ID
		AppName         string `json:"appName"`         // 应用名称
		PackageName     string `json:"packageName"`     // 包名
		DpaAdtype       int    `json:"dpaAdtype"`       // DPA广告类型
		OpenUrlType     int    `json:"openUrlType"`     // 打开URL类型
	}

	// SubAccountAutoExtend 自动扩展
	SubAccountAutoExtend struct {
		// 根据实际返回数据补充字段
	}

	// SubAccountShopMultiRoiGoals 店铺多ROI目标
	SubAccountShopMultiRoiGoals struct {
		// 根据实际返回数据补充字段
	}
)

// GetSubAccountProjectListByStatus 根据项目状态获取项目列表
func (b *Bot) GetSubAccountProjectListByStatus(aadvid string, page, pageSize int, cookie string, projectStatus []int, startDate, endDate string) (*SubAccountProjectResp, error) {
	// 如果没有提供日期，使用今天
	if startDate == "" {
		startDate = "2025-07-04"
	}
	if endDate == "" {
		endDate = "2025-07-04"
	}

	req := SubAccountProjectReq{
		St:              startDate,
		Et:              endDate,
		SortStat:        "create_time",
		ProjectStatus:   projectStatus,
		PromotionStatus: []int{-1},
		Limit:           pageSize,
		Page:            page,
		SortOrder:       1,
		CampaignType:    []int{1},
		Fields: []string{
			"stat_cost",
			"convert_cnt",
			"conversion_cost",
			"show_cnt",
			"click_cnt",
			"conversion_rate",
			"cpm_platform",
			"cpc_platform",
			"pre_convert_count",
			"pre_convert_cost",
		},
		IsSophonx:     1,
		SearchType:    "8",
		CascadeFields: []string{"support_cost_rate_7d", "budget_optimize_switch"},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/projects/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountProjectResp](url).
		AddHeader("Content-Type", "application/json;charset=UTF-8").
		AddHeader("Accept", "*/*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// GetSubAccountProjectListByKeyword 根据关键词搜索项目列表
func (b *Bot) GetSubAccountProjectListByKeyword(aadvid string, page, pageSize int, cookie string, keyword string, startDate, endDate string) (*SubAccountProjectResp, error) {
	// 如果没有提供日期，使用今天
	if startDate == "" {
		startDate = "2025-07-04"
	}
	if endDate == "" {
		endDate = "2025-07-04"
	}

	req := SubAccountProjectReq{
		St:              startDate,
		Et:              endDate,
		SortStat:        "create_time",
		ProjectStatus:   []int{-1},
		PromotionStatus: []int{-1},
		Limit:           pageSize,
		Page:            page,
		SortOrder:       1,
		CampaignType:    []int{1},
		Fields: []string{
			"stat_cost",
			"convert_cnt",
			"conversion_cost",
			"show_cnt",
			"click_cnt",
			"conversion_rate",
			"cpm_platform",
			"cpc_platform",
			"pre_convert_count",
			"pre_convert_cost",
		},
		IsSophonx:     1,
		SearchType:    "8",
		CascadeFields: []string{"support_cost_rate_7d", "budget_optimize_switch"},
		Keyword:       keyword,
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	url := "https://ad.oceanengine.com/ad/api/promotion/projects/list?aadvid=" + aadvid

	if r, err := NewPostApi[SubAccountProjectResp](url).
		AddHeader("Content-Type", "application/json;charset=UTF-8").
		AddHeader("Accept", "*/*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com/promotion/promote-manage/project?aadvid="+aadvid).
		AddHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}
