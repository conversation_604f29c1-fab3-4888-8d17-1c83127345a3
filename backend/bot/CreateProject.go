package bot

import (
	"encoding/json"
	"fmt"
	"strconv"
)

// CreateProject 创建项目
func (b *Bot) CreateProject(advertiserID int64, projectData *CreateProjectReq, cookie string) (*CreateProjectResp, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(projectData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}
	fmt.Printf("创建项目请求参数: %s\n", string(jsonData))

	if r, err := NewPostApi[CreateProjectResp]("https://ad.oceanengine.com/superior/api/project").
		AddHeader("Content-Type", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		AddParam("aadvid", strconv.FormatInt(advertiserID, 10)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

// CreateProjectReq 创建项目请求结构体
type CreateProjectReq struct {
	AppPromotionType       int                `json:"app_promotion_type,omitempty"`      // 应用推广类型
	TrackURLType           int                `json:"track_url_type,omitempty"`          // 检测链接类型
	TrackURLGroupID        string             `json:"track_url_group_id"`                // 已有链接组ID
	TrackURLGroupInfo      *TrackURLGroupInfo `json:"track_url_group_info,omitempty"`    // 已有链接组信息
	TrackURLSendType       string             `json:"track_url_send_type"`               // 发送类型
	SmartBidType           int                `json:"smart_bid_type"`                    // 智能出价类型
	IsSearchSpeedPhaseFour bool               `json:"is_search_speed_phase_four"`        // 搜索加速第四阶段
	DownloadURL            string             `json:"download_url,omitempty"`            // 下载链接
	Budget                 int                `json:"budget"`                            // 日预算
	Bid                    float64            `json:"bid"`                               // 出价
	InventoryCatalog       int                `json:"inventory_catalog"`                 // 库存目录
	FlowControlMode        int                `json:"flow_control_mode"`                 // 竞价策略
	DownloadType           int                `json:"download_type,omitempty"`           // 下载类型
	ConvertSource          int                `json:"convert_source,omitempty"`          // 转化来源
	DeliveryMode           int                `json:"delivery_mode"`                     // 投放模式
	DeliveryPackage        int                `json:"delivery_package"`                  // AB 实验包 ID
	LandingType            int                `json:"landing_type"`                      // 落地页类型
	DeliveryRelatedNum     int                `json:"delivery_related_num"`              // 投放相关数量
	AppName                string             `json:"app_name,omitempty"`                // 应用名称
	PackageName            string             `json:"package_name,omitempty"`            // 包名
	Name                   string             `json:"name"`                              // 项目名称
	ScheduleType           int                `json:"schedule_type"`                     // 投放时间类型
	WeekScheduleType       int                `json:"week_schedule_type"`                // 投放时段
	PricingType            int                `json:"pricing_type"`                      // 出价类型
	AppType                int                `json:"app_type,omitempty"`                // 应用类型
	District               string             `json:"district,omitempty"`                // 地域
	Gender                 string             `json:"gender"`                            // 性别
	Age                    [][]string         `json:"age"`                               // 年龄
	RetargetingTags        []string           `json:"retargeting_tags"`                  // 重定向标签
	HideIfExists           string             `json:"hide_if_exists"`                    // 已安装用户
	HideIfConverted        string             `json:"hide_if_converted,omitempty"`       // 已转化用户
	ConvertedTimeDuration  string             `json:"converted_time_duration,omitempty"` // 转化时间周期
	IOSOSVSelect           string             `json:"ios_osv_select"`                    // iOS版本选择
	CDPMarketingGoal       int                `json:"cdp_marketing_goal"`                // CDP 营销目标
	AssetIDs               []string           `json:"asset_ids"`                         // 素材ID列表
	ExternalAction         string             `json:"external_action"`                   // 外部转化目标
	BudgetMode             int                `json:"budget_mode"`                       // 日预算模式
	CampaignType           int                `json:"campaign_type"`                     // 推广类型
	DeliveryProduct        int                `json:"delivery_product,omitempty"`        // 投放产品线
	SmartInventory         int                `json:"smart_inventory"`                   // 智能库存
	AutoAdType             int                `json:"auto_ad_type,omitempty"`            // 自动广告类型（可选字段，只有值为1时才包含）
	AutoAdScene            int                `json:"auto_ad_scene,omitempty"`           // 自动广告场景（可选字段，只有开启自动投放时才出现）
	Products               []string           `json:"products,omitempty"`                // 商品列表
	OriginProjectID        string             `json:"origin_project_id"`                 // 原项目ID
	TrackURL               []string           `json:"track_url"`                         // 展现链路
	ActionTrackURL         []string           `json:"action_track_url"`                  // 点击链路
	FirstFrame             []string           `json:"first_frame"`                       // 视频播放链路
	LastFrame              []string           `json:"last_frame"`                        // 视频完播链路
	EffectiveFrame         []string           `json:"effective_frame"`                   // 视频有效播放链路
	IsSearch3Online        bool               `json:"is_search_3_online"`                // 搜索3.0在线

	// 可选字段（根据正常参数补充）
	StartTime                 string   `json:"start_time,omitempty"`                   // 开始时间
	EndTime                   string   `json:"end_time,omitempty"`                     // 结束时间
	WeekSchedule              []string `json:"week_schedule,omitempty"`                // 投放时段
	RetargetingTagsExclude    []string `json:"retargeting_tags_exclude,omitempty"`     // 重定向标签排除
	Keywords                  []string `json:"keywords,omitempty"`                     // 关键词
	AudienceExtend            int      `json:"audience_extend,omitempty"`              // 定向拓展
	AutoExtendEnabled         int      `json:"auto_extend_enabled,omitempty"`          // 智能放量
	AutoExtendTargets         []string `json:"auto_extend_targets,omitempty"`          // 智能放量目标
	InventoryType             []int    `json:"inventory_type,omitempty"`               // 库存类型
	FeedDeliverySearch        int      `json:"feed_delivery_search,omitempty"`         // Feed投放搜索
	AC                        []string `json:"ac,omitempty"`                           // 网络类型
	DeviceType                []string `json:"device_type,omitempty"`                  // 设备类型
	IOSOSV                    string   `json:"ios_osv,omitempty"`                      // iOS版本
	LaunchPriceSelect         string   `json:"launch_price_select,omitempty"`          // 启动价格选择
	LaunchPrice               []string `json:"launch_price,omitempty"`                 // 启动价格
	AssetName                 string   `json:"asset_name,omitempty"`                   // 素材名称
	AssetType                 int      `json:"asset_type,omitempty"`                   // 素材类型
	ProductPlatformID         string   `json:"product_platform_id,omitempty"`          // 商品平台ID
	ProductID                 string   `json:"product_id,omitempty"`                   // 商品ID
	UniqueProductID           string   `json:"unique_product_id,omitempty"`            // 唯一商品ID
	Platform                  []string `json:"platform,omitempty"`                     // 平台
	DeepExternalAction        string   `json:"deep_external_action,omitempty"`         // 深度转化目标
	DeepBidType               int      `json:"deep_bid_type,omitempty"`                // 深度出价类型
	DeliveryProductIds        []string `json:"delivery_product_ids,omitempty"`         // 投放产品ID列表
	DeliveryMediumIds         []string `json:"delivery_medium_ids,omitempty"`          // 投放媒体ID列表
	FilterAwemeAbnormalActive string   `json:"filter_aweme_abnormal_active,omitempty"` // 过滤抖音异常活跃用户
	FilterAwemeFansCount      string   `json:"filter_aweme_fans_count,omitempty"`      // 过滤抖音粉丝数
	FilterOwnAwemeFans        string   `json:"filter_own_aweme_fans,omitempty"`        // 过滤自己的抖音粉丝
	ClueAcquisitionMethod     int      `json:"clue_acquisition_method,omitempty"`      // 线索获取方式
	MultiAssetSwitch          int      `json:"multi_asset_switch,omitempty"`           // 多素材开关
	AudiencePackageID         string   `json:"audience_package_id,omitempty"`          // 受众包ID
	AudienceSupportKeys       []string `json:"audience_support_keys,omitempty"`        // 受众支持键
	SearchBidRatio            string   `json:"search_bid_ratio,omitempty"`             // 搜索出价比例
	ActionInterestSelect      string   `json:"action_interest_select,omitempty"`       // 行为兴趣选择
	AwemeFansSelect           string   `json:"aweme_fans_select,omitempty"`            // 抖音粉丝选择
	SuperiorPopularityType    string   `json:"superior_popularity_type,omitempty"`     // 优质人群类型
	DeliveryScene             int      `json:"delivery_scene,omitempty"`               // 投放场景

	// 销售线索项目专用字段
	LeadProducts []Product `json:"-"` // 销售线索项目的商品列表（不直接序列化）
}

// MarshalJSON 自定义JSON序列化方法
func (c *CreateProjectReq) MarshalJSON() ([]byte, error) {
	type Alias CreateProjectReq

	// 创建别名结构体，避免递归调用
	alias := &struct {
		*Alias
		Products interface{} `json:"products,omitempty"`
	}{
		Alias: (*Alias)(c),
	}

	// 根据项目类型决定products字段的类型
	if c.LandingType == 1 { // 销售线索项目
		if len(c.LeadProducts) > 0 {
			alias.Products = c.LeadProducts
		}
	} else { // 其他类型项目
		if len(c.Products) > 0 {
			alias.Products = c.Products
		}
	}

	return json.Marshal(alias)
}

// TrackURLGroupInfo 监测链接组信息
type TrackURLGroupInfo struct {
	ActionTrackURL    string `json:"action_track_url"`       // 点击监测链接
	DisplayTrackURL   string `json:"display_track_url"`      // 展现监测链接
	FirstFrame        string `json:"first_frame"`            // 视频播放监测链接
	LastFrame         string `json:"last_frame"`             // 视频完播监测链接
	EffectiveFrame    string `json:"effective_frame"`        // 视频有效播放监测链接
	TrackURLGroupID   string `json:"track_url_group_id"`     // 监测链接组ID
	TrackURLGroupName string `json:"track_url_group_name"`   // 监测链接组名称
	DownloadURL       string `json:"download_url,omitempty"` // 下载链接
	ActiveTrackURL    string `json:"active_track_url"`       // 激活监测链接
}

// CreateProjectResp 创建项目响应结构体
type CreateProjectResp struct {
	Code  int                `json:"code"` // 响应码
	Data  *CreateProjectData `json:"data"` // 响应数据
	Extra struct {
	} `json:"extra"`
	Msg       string `json:"msg"`
	RequestId string `json:"request_id"`
}

// CreateProjectData 创建项目响应数据
type CreateProjectData struct {
	ProjectId     string   `json:"id"`            // 创建的项目ID
	KeywordsError []string `json:"keywordsError"` // 错误关键字
}
