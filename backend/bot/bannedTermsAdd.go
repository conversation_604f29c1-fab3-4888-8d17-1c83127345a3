package bot

import (
	"encoding/json"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// BannedTermsAdd 添加屏蔽词
func (b *Bot) BannedTermsAdd(appId int64, userIds []int64, terms []string, advertiserID string, cookie string) (*BannedTermsAddResp, error) {
	req := BannedTermsAddReq{
		AppID:          appId,
		IesCoreUserIds: userIds,
		Terms:          terms,
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	if r, err := NewPostApi[BannedTermsAddResp]("https://ad.oceanengine.com/nbs/api/ad/comment/banned_terms/add").
		AddHeader("Content-Type", "application/json; charset=UTF-8").
		AddHeader("Accept", "application/json, text/plain, */*").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		AddParam("aadvid", advertiserID).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	BannedTermsAddReq struct {
		AppID          int64    `json:"app_id"`
		IesCoreUserIds []int64  `json:"ies_core_user_ids"`
		Terms          []string `json:"terms"`
	}
)

type (
	BannedTermsAddResp struct {
		Code      int                    `json:"code"`
		Data      BannedTermsAddData     `json:"data"`
		Extra     map[string]interface{} `json:"extra"`
		Msg       string                 `json:"msg"`
		RequestId string                 `json:"request_id"`
	}

	BannedTermsAddData struct {
		Result map[string]bool   `json:"result"`
		Reason map[string]string `json:"reason"`
	}
)

// POST https://ad.oceanengine.com/nbs/api/ad/comment/banned_terms/add?aadvid=1834463983048010 HTTP/1.1
// Host: ad.oceanengine.com
// Connection: keep-alive
// Content-Length: 73
// sec-ch-ua-platform: "Windows"
// X-CSRFToken: 0f6MduSXy-mazSw6hKxcO6cf
// User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
// Accept: application/json, text/plain, */*
// sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
// Content-Type: application/json; charset=UTF-8
// sec-ch-ua-mobile: ?0
// Origin: https://ad.oceanengine.com
// Sec-Fetch-Site: same-origin
// Sec-Fetch-Mode: cors
// Sec-Fetch-Dest: empty
// Referer: https://ad.oceanengine.com/statistics_pages/tool_apps/comment/block_rule/words?aadvid=1834463983048010
// Accept-Encoding: gzip, deflate, br, zstd
// Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
// Cookie: _tea_utm_cache_1192=undefined; csrftoken=0f6MduSXy-mazSw6hKxcO6cf; sessionid_ss=ced7b2703f35533a7441d2f500f686e1; grwng_uid=10a463ff-ee96-49f9-81e1-f5f32c33803e; passport_csrf_token_default=1c6467d9006d7200f5eb1321a2172422; sid_tt=ced7b2703f35533a7441d2f500f686e1; gr_user_id=e2d5d29e-64a2-41d5-bb65-468af11cef4f; uid_tt=4f8611a8d42a4efd2efa89b4f7b2a33c; sso_uid_tt=d4cc769b6084cb695f6c7de73fe88b21; passport_mfa_token=CjGFgQbYBTLNa79jeS14VirM%2B6hPyuZcHz5rarhYTFuQEvPMFrq7ZI4cDb03l7FR1SfkGkoKPAAAAAAAAAAAAABPJu364ktaFzjAkld93SRd0Cw5wnEfFKtpA6yDJsqE5TTpL1aGCUIpN7Uphdfwd8IxlhDt7PQNGPax0WwgAiIBA0WFNyw%3D; toutiao_sso_user_ss=4ee0edc34b39e1094f9de5b608f59c99; is_staff_user=false; uid_tt_ss=4f8611a8d42a4efd2efa89b4f7b2a33c; passport_csrf_token=1c6467d9006d7200f5eb1321a2172422; sid_ucp_sso_v1=1.0.0-KDkwZjk0MDFkNjEzY2VhN2EyNDcxMGZiYmYxYWVmZTdmMDI4ZTIzNTIKHwiaosDaxqzYBxDii-PCBhj6CiAMMJrrmsIGOAFA6wcaAmxxIiA0ZWUwZWRjMzRiMzllMTA5NGY5ZGU1YjYwOGY1OWM5OQ; n_mh=5-27CdOaOta8x969ReWYEtIW3Pkt3en1KmM2Tz6m8V8; d_ticket=3019b820d836abf05ce20d036d9d5ac649439; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary_ss=true; sessionid=ced7b2703f35533a7441d2f500f686e1; ssid_ucp_sso_v1=1.0.0-KDkwZjk0MDFkNjEzY2VhN2EyNDcxMGZiYmYxYWVmZTdmMDI4ZTIzNTIKHwiaosDaxqzYBxDii-PCBhj6CiAMMJrrmsIGOAFA6wcaAmxxIiA0ZWUwZWRjMzRiMzllMTA5NGY5ZGU1YjYwOGY1OWM5OQ; sso_uid_tt_ss=d4cc769b6084cb695f6c7de73fe88b21; toutiao_sso_user=4ee0edc34b39e1094f9de5b608f59c99; ttwid=1%7ChdXiX6tXoi8QOne9vYmkphkIsL1p_DapVqdpKHdoSvc%7C1750927034%7Cb94360b5cd46f16eec6d62b7c377838c86479483fbd4ca7e104da7038ba7cb2c; odin_tt=dc7b2c293e954cc882a199a000cde748c530967bb29c8187f2a8c4fc899de28603f5b6ae20373b6d6863034b77bd4b1124566c9243fbfd44ebc8e192c927b1c3; sid_guard=ced7b2703f35533a7441d2f500f686e1%7C1750927034%7C5184000%7CMon%2C+25-Aug-2025+08%3A37%3A14+GMT; uid_tt_ss=4f8611a8d42a4efd2efa89b4f7b2a33c; sessionid_ss=ced7b2703f35533a7441d2f500f686e1; sid_ucp_v1=1.0.0-KGFmOGU2YmU2ZjYxN2M3YjQyMzhlNDUyYWJkMjcxYTliOTlmZGJjZDIKGQiaosDaxqzYBxC6jfTCBhj6CiAMOAFA6wcaAmxmIiBjZWQ3YjI3MDNmMzU1MzNhNzQ0MWQyZjUwMGY2ODZlMQ; sid_ucp_v1=1.0.0-KGFmOGU2YmU2ZjYxN2M3YjQyMzhlNDUyYWJkMjcxYTliOTlmZGJjZDIKGQiaosDaxqzYBxC6jfTCBhj6CiAMOAFA6wcaAmxmIiBjZWQ3YjI3MDNmMzU1MzNhNzQ0MWQyZjUwMGY2ODZlMQ; ssid_ucp_v1=1.0.0-KGFmOGU2YmU2ZjYxN2M3YjQyMzhlNDUyYWJkMjcxYTliOTlmZGJjZDIKGQiaosDaxqzYBxC6jfTCBhj6CiAMOAFA6wcaAmxmIiBjZWQ3YjI3MDNmMzU1MzNhNzQ0MWQyZjUwMGY2ODZlMQ; ssid_ucp_v1=1.0.0-KGFmOGU2YmU2ZjYxN2M3YjQyMzhlNDUyYWJkMjcxYTliOTlmZGJjZDIKGQiaosDaxqzYBxC6jfTCBhj6CiAMOAFA6wcaAmxmIiBjZWQ3YjI3MDNmMzU1MzNhNzQ0MWQyZjUwMGY2ODZlMQ; is_force_toggle_superior_1834463762188361=1; x-web-secsdk-uid=93d002b5-47e3-47b5-8a71-58c55a0d43dd; csrf_session_id=c64650cfe6b914915a2891f80f6b760e; ttcid=7cf6fd13d0f44250a5fa373bbc57264916; s_v_web_id=verify_mcd5t9ri_UTtN95nm_8dzL_4RJ5_B4JC_wnJDALo7I2B2; is_force_toggle_superior_1834461529054281=1; is_force_toggle_superior_1834464099993739=1; is_force_toggle_superior_1834463983048010=1; MONITOR_WEB_ID=a027e776-0cad-404f-8237-f43a0084cf2f; trident_csrf_token=oojwu5vJaw3vxQnHvXAQ9YZ8; get_new_msg_timer_cycle=Mon Jun 30 2025 18:26:02 GMT+0800 (中国标准时间); aefa4e5d2593305f_gr_last_sent_sid_with_cs1=180cc40e-64b4-41c4-995b-be5575689f79; aefa4e5d2593305f_gr_last_sent_cs1=1834463983048010; aefa4e5d2593305f_gr_cs1=1834463983048010; aefa4e5d2593305f_gr_session_id=180cc40e-64b4-41c4-995b-be5575689f79; aefa4e5d2593305f_gr_session_id_180cc40e-64b4-41c4-995b-be5575689f79=true; tt_scid=HeOlogECepmKgAPKRiFA63-4rHURej748vHLSjXJKh1R0zitZAOkvyBMDHMgAMSu23ae; is_force_toggle_superior_1834449343267465=1; is_force_toggle_superior_****************=1; trace_log_adv_id=****************; trace_log_user_id=****************

// {"app_id":1128,"ies_core_user_ids":[****************],"terms":["垃圾"]}

// HTTP/1.1 200 OK
// Server: Tengine
// Content-Type: application/json; charset=utf-8
// Content-Length: 136
// Connection: keep-alive
// Date: Mon, 30 Jun 2025 10:17:14 GMT
// X-Tt-Logid: 20250630181714371632F8B379A7812D04
// X-Ad-Account-Proxy-Forward: v-*********
// X-Download-Options: noopen
// X-Xss-Protection: 1; mode=block
// X-Frame-Options: SAMEORIGIN
// Access-Control-Allow-Credentials: true
// X_tt_logid: 20250630181714371632F8B379A7812D04
// X-Content-Type-Options: nosniff
// Vary: Origin
// Access-Control-Allow-Origin: https://ad.oceanengine.com
// Strict-Transport-Security: max-age=********; includeSubDomains
// server-timing: inner; dur=350
// x-tt-trace-host: 01d8d9099105fdf66f9e0ffd5e45afefa20c1e886f55bcb56da436ba7756eaaafd999931af553c98891ee59863015102d47e124579fad129baad45c8e29d19d102f9c4dd5234ac7841e22718e3e868b32683bc8f688721e6e530a84f68c8a29d2948737ff320a63fda8fb4460df50faecc
// x-tt-trace-tag: id=03;cdn-cache=miss;type=dyn
// x-tt-trace-id: 00-250630181714371632F8B379A7812D04-094AE25D0953E9BD-00
// Content-Security-Policy-Report-Only: default-src 'self' blob: data: 'unsafe-inline' 'unsafe-eval' dn-growing.qbox.me *.baidu.com *.bdstatic.com *.byteimg.com *.google-analytics.com *.growingio.com *.douyin.com *.snssdk.com *.ctobsnssdk.com *.bytecdn.cn *.bytedance.net *.bytedance.com *.toutiaopage.com *.chengzijianzhan.com *.byted.org *.365yg.com *.toutiao.com *.pstatp.com *.ixigua.com *.ixiguavideo.com *.ribaoapi.com *.bdurl.net *.openlanguage.com *.jinritemai.com *.oceanengine.com;report-uri https://csp.snssdk.com/v17
// server-timing: cdn-cache;desc=MISS,edge;dur=0,origin;dur=410
// Via: ens-dynamic4.cn8639[410,0]
// Timing-Allow-Origin: *
// EagleId: 01c1da3417512786344049000e

// {"code":0,"data":{"result":{"****************":true},"reason":{}},"extra":{},"msg":"","request_id":"20250630181714371632F8B379A7812D04"}
