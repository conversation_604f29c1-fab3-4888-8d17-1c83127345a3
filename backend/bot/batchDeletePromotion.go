package bot

import (
	"encoding/json"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// BatchDeletePromotion 删除广告(可跨广告主批量删除多个广告)
func (b *Bot) BatchDeletePromotion(accountDetailList []BatchDeletePromotionAccountDetail, cookie string) (*BatchDeletePromotionResp, error) {
	req := BatchDeletePromotionReq{
		AccountDetail: accountDetailList,
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	if r, err := NewPostApi[BatchDeletePromotionResp]("https://business.oceanengine.com/nbs/api/bm/promotion/ad/batch_delete_promotion").
		AddHeader("Content-Type", "application/json").
		AddHeader("Accept", "application/json").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		SetBody(jsonData).
		Do(b); err != nil {
		return nil, err
	} else {
		return r, nil
	}
}

type (
	BatchDeletePromotionReq struct {
		AccountDetail []BatchDeletePromotionAccountDetail `json:"account_detail"`
	}
	BatchDeletePromotionAccountDetail struct {
		AdvertiserId string `json:"advertiser_id"` //广告主ID
		Id           string `json:"id"`            //广告ID
		Name         string `json:"name"`          //广告名称,可不传
	}
)

type (
	BatchDeletePromotionResp struct {
		Code      int                       `json:"code"`
		Data      BatchDeletePromotionData  `json:"data"`
		Extra     BatchDeletePromotionExtra `json:"extra"`
		Msg       string                    `json:"msg"`
		RequestId string                    `json:"request_id"`
	}

	BatchDeletePromotionData struct {
		List      []BatchDeletePromotionListItem `json:"list"`
		HasFailed bool                           `json:"has_failed"`
	}

	BatchDeletePromotionListItem struct {
		Id     string `json:"id"`
		Name   string `json:"name"`
		Reason string `json:"reason"`
	}

	BatchDeletePromotionExtra struct {
	}
)
