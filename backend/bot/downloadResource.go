package bot

import (
	"fmt"
	"io"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// DownloadResourceReq 下载资源请求结构体
type DownloadResourceReq struct {
	Url string `json:"url"` // 资源URL
}

// DownloadResourceResp 下载资源响应结构体
type DownloadResourceResp struct {
	Success       bool   `json:"success"`         // 是否成功
	Message       string `json:"message"`         // 响应消息
	Url           string `json:"url"`             // 原始URL
	ContentType   string `json:"content_type"`    // 文件类型
	FileSize      int64  `json:"file_size"`       // 文件大小（字节）
	FileName      string `json:"file_name"`       // 文件名
	Data          []byte `json:"data"`            // 文件字节数据
	LocalFileName string `json:"local_file_name"` // 本地保存的文件名
}

// DownloadResource 下载资源文件
// url: 要下载的资源URL
func (b *Bot) DownloadResource(url, format, cookie string) (*DownloadResourceResp, error) {
	if url == "" {
		return &DownloadResourceResp{
			Success: false,
			Message: "URL不能为空",
			Url:     url,
		}, fmt.Errorf("URL不能为空")
	}

	// 使用链式调用方式发送GET请求
	if resp, err := NewGetApi[any](url).
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36").
		AddHeader("Accept", "*/*").
		AddHeader("Accept-Encoding", "gzip, deflate, br").
		AddHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8").
		AddHeader("Connection", "keep-alive").
		AddHeader("Cookie", cookie).
		AddHeader("X-CSRFToken", utils.GetCsrfTokenFromCookie(cookie)).
		DoRaw(b); err != nil {
		return &DownloadResourceResp{
			Success: false,
			Message: fmt.Sprintf("发送请求失败: %v", err),
			Url:     url,
		}, fmt.Errorf("发送请求失败: %v", err)
	} else {
		defer resp.Body.Close()

		// 检查HTTP状态码
		if resp.StatusCode != 200 {
			return &DownloadResourceResp{
				Success: false,
				Message: fmt.Sprintf("HTTP请求失败，状态码: %d", resp.StatusCode),
				Url:     url,
			}, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
		}

		// 读取响应体数据
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return &DownloadResourceResp{
				Success: false,
				Message: fmt.Sprintf("读取响应数据失败: %v", err),
				Url:     url,
			}, fmt.Errorf("读取响应数据失败: %v", err)
		}

		// 获取文件信息
		contentType := resp.Header.Get("Content-Type")
		fileSize := resp.ContentLength
		if fileSize <= 0 {
			fileSize = int64(len(data))
		}

		// 从URL中提取文件名
		fileName := getFileNameFromUrl(url) + "." + format

		// 构建成功响应
		return &DownloadResourceResp{
			Success:     true,
			Message:     "下载成功",
			Url:         url,
			ContentType: contentType,
			FileSize:    fileSize,
			FileName:    fileName,
			Data:        data,
		}, nil
	}
}

// getFileNameFromUrl 从URL中提取文件名
func getFileNameFromUrl(url string) string {
	// 找到最后一个斜杠的位置
	lastSlashIndex := -1
	for i := len(url) - 1; i >= 0; i-- {
		if url[i] == '/' {
			lastSlashIndex = i
			break
		}
	}

	if lastSlashIndex >= 0 && lastSlashIndex < len(url)-1 {
		fileName := url[lastSlashIndex+1:]

		// 如果包含查询参数，去掉查询参数部分
		questionMarkIndex := -1
		for i := 0; i < len(fileName); i++ {
			if fileName[i] == '?' {
				questionMarkIndex = i
				break
			}
		}

		if questionMarkIndex >= 0 {
			fileName = fileName[:questionMarkIndex]
		}

		if fileName != "" {
			return fileName
		}
	}

	// 如果无法提取文件名，返回默认名称
	return "downloaded_file"
}
