package bot

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
)

// FlexibleString 可以同时接受字符串和数字的自定义类型
type FlexibleString string

// UnmarshalJSON 自定义JSON解析方法，支持字符串和数字类型
func (fs *FlexibleString) UnmarshalJSON(data []byte) error {
	// 尝试解析为字符串
	var s string
	if err := json.Unmarshal(data, &s); err == nil {
		*fs = FlexibleString(s)
		return nil
	}

	// 尝试解析为数字
	var n float64
	if err := json.Unmarshal(data, &n); err == nil {
		*fs = FlexibleString(strconv.FormatFloat(n, 'f', -1, 64))
		return nil
	}

	// 尝试解析为整数
	var i int64
	if err := json.Unmarshal(data, &i); err == nil {
		*fs = FlexibleString(strconv.FormatInt(i, 10))
		return nil
	}

	return fmt.Errorf("无法解析字段为字符串或数字: %s", string(data))
}

// MarshalJSON 自定义JSON序列化方法
func (fs FlexibleString) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(fs))
}

// String 返回字符串表示
func (fs FlexibleString) String() string {
	return string(fs)
}

// Float64 尝试转换为float64
func (fs FlexibleString) Float64() (float64, error) {
	return strconv.ParseFloat(string(fs), 64)
}

// Int64 尝试转换为int64
func (fs FlexibleString) Int64() (int64, error) {
	return strconv.ParseInt(string(fs), 10, 64)
}

// GetProjectInfo 获取项目详情
func (b *Bot) GetProjectInfo(advertiserID int64, projectIDs []string, cookie string) (*GetProjectInfoResp, error) {

	// 构建请求URL和参数
	baseURL := "https://ad.oceanengine.com/superior/api/project"
	params := map[string]string{
		"aadvid":                               strconv.FormatInt(advertiserID, 10),
		"project_ids":                          strings.Join(projectIDs, ","),
		"need_raw_campaign":                    "true",
		"need_keywords":                        "true",
		"need_product_regulation_opt_status":   "true",
		"need_blue_flow_package_active":        "true",
		"need_fill_history_blue_keywords_info": "true",
	}

	// 构建完整URL
	var queryParams []string
	for k, v := range params {
		queryParams = append(queryParams, fmt.Sprintf("%s=%s", k, v))
	}
	fullURL := fmt.Sprintf("%s?%s", baseURL, strings.Join(queryParams, "&"))

	fmt.Println("url", fullURL)

	// 使用NewGetApi发送请求
	if r, err := NewGetApi[GetProjectInfoResp](fullURL).
		AddHeader("Accept", "application/json").
		AddHeader("Origin", "https://ad.oceanengine.com").
		AddHeader("Referer", "https://ad.oceanengine.com").
		AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36").
		AddHeader("Cookie", cookie).
		Do(b); err != nil {
		return nil, err
	} else {
		j, _ := json.Marshal(r)
		fmt.Println("getProjectInfo", string(j))
		return r, nil
	}
}

// GetProjectInfoResp 获取项目详情响应结构体
type GetProjectInfoResp struct {
	Code      int                    `json:"code"`       // 响应码
	Data      []ProjectInfoData      `json:"data"`       // 项目详情数据列表
	Extra     map[string]interface{} `json:"extra"`      // 额外信息
	Msg       string                 `json:"msg"`        // 响应消息
	RequestID string                 `json:"request_id"` // 请求ID
}

// ProjectInfoData 项目详情数据结构
type ProjectInfoData struct {
	ID                string            `json:"Id"`                // 项目ID
	AdvertiserID      string            `json:"AdvertiserId"`      // 广告主ID
	CampaignID        string            `json:"CampaignId"`        // 推广计划ID
	Name              string            `json:"Name"`              // 项目名称
	Data              ProjectDataDetail `json:"Data"`              // 项目详细数据
	IsDel             int               `json:"IsDel"`             // 是否删除
	EditVersion       string            `json:"EditVersion"`       // 编辑版本
	CreateTime        string            `json:"CreateTime"`        // 创建时间
	ModifyTime        string            `json:"ModifyTime"`        // 修改时间
	DeliverySceneName string            `json:"DeliverySceneName"` // 投放场景名称
	NebulaCampaign    NebulaCampaign    `json:"NebulaCampaign"`    // 推广计划详情
	CanCopy           bool              `json:"CanCopy"`           // 是否可复制
	AC                []string          `json:"ac"`                // 网络类型
	ActivateType      []string          `json:"activateType"`      // 激活类型
	Carrier           []string          `json:"carrier"`           // 运营商
	DeviceType        []string          `json:"deviceType"`        // 设备类型
	Platform          []string          `json:"platform"`          // 平台
}

// ProjectDataDetail 项目详细数据 - 使用接口类型支持不同落地页类型
type ProjectDataDetail struct {
	MarketingInfo     MarketingInfo     `json:"MarketingInfo"`        // 营销信息
	PromotionObject   PromotionObject   `json:"PromotionObject"`      // 推广对象
	Inventory         Inventory         `json:"Inventory"`            // 库存
	Audience          Audience          `json:"Audience"`             // 受众
	PromotionStrategy PromotionStrategy `json:"PromotionStrategy"`    // 推广策略
	TrackUrls         TrackUrls         `json:"TrackUrls"`            // 监测链接
	ClassifyInfo      ClassifyInfo      `json:"ClassifyInfo"`         // 分类信息
	SystemOrigin      string            `json:"SystemOrigin"`         // 系统来源
	CreateChannel     string            `json:"CreateChannel"`        // 创建渠道
	DpaLibrary        []interface{}     `json:"DpaLibrary"`           // DPA库
	AutoExtend        *AutoExtend       `json:"AutoExtend,omitempty"` // 自动扩展信息（销售线索项目）
}

// GetLandingType 获取落地页类型
func (p *ProjectDataDetail) GetLandingType() int {
	return p.MarketingInfo.LandingType
}

// IsAppPromotion 是否为应用推广项目
func (p *ProjectDataDetail) IsAppPromotion() bool {
	return p.GetLandingType() == 3
}

// IsLeadGeneration 是否为销售线索推广项目
func (p *ProjectDataDetail) IsLeadGeneration() bool {
	return p.GetLandingType() == 1
}

// IsNativeInteraction 是否为原生互动项目
func (p *ProjectDataDetail) IsNativeInteraction() bool {
	return p.GetLandingType() == 7
}

// GetAppPromotionData 获取应用推广项目专用数据
func (p *ProjectDataDetail) GetAppPromotionData() *AppPromotionData {
	if !p.IsAppPromotion() {
		return nil
	}
	return &AppPromotionData{
		MarketingInfo: p.MarketingInfo,
		AppPromotionObject: AppPromotionObject{
			AppType:            p.PromotionObject.AppType,
			DownloadType:       p.PromotionObject.DownloadType,
			DownloadURL:        p.PromotionObject.DownloadURL,
			PackageName:        p.PromotionObject.PackageName,
			AppName:            p.PromotionObject.AppName,
			ContentType:        p.PromotionObject.ContentType,
			AppPkgID:           p.PromotionObject.AppPkgID,
			DeliveryProduct:    p.PromotionObject.DeliveryProduct,
			DeliveryMedium:     p.PromotionObject.DeliveryMedium,
			DeliveryProductIDs: p.PromotionObject.DeliveryProductIDs,
			DeliveryMediumIDs:  p.PromotionObject.DeliveryMediumIDs,
			AssetName:          p.PromotionObject.AssetName,
			AssetType:          p.PromotionObject.AssetType,
		},
		Inventory:         p.Inventory,
		Audience:          p.Audience,
		PromotionStrategy: p.PromotionStrategy,
		TrackUrls:         p.TrackUrls,
		ClassifyInfo:      p.ClassifyInfo,
		SystemOrigin:      p.SystemOrigin,
		CreateChannel:     p.CreateChannel,
		DpaLibrary:        p.DpaLibrary,
	}
}

// GetLeadGenerationData 获取销售线索推广项目专用数据
func (p *ProjectDataDetail) GetLeadGenerationData() *LeadGenerationData {
	if !p.IsLeadGeneration() {
		return nil
	}
	return &LeadGenerationData{
		MarketingInfo: p.MarketingInfo,
		LeadPromotionObject: LeadPromotionObject{
			ProductPlatformID:     p.PromotionObject.ProductPlatformID,
			ProductID:             p.PromotionObject.ProductID,
			UniqueProductID:       p.PromotionObject.UniqueProductID,
			ProductObjectID:       p.PromotionObject.ProductObjectID,
			ContentType:           p.PromotionObject.ContentType,
			DeliveryProduct:       p.PromotionObject.DeliveryProduct,
			DeliveryMedium:        p.PromotionObject.DeliveryMedium,
			DeliveryProductIDs:    p.PromotionObject.DeliveryProductIDs,
			DeliveryMediumIDs:     p.PromotionObject.DeliveryMediumIDs,
			DpaTargetType:         p.PromotionObject.DpaTargetType,
			CategoryType:          p.PromotionObject.CategoryType,
			AssetName:             p.PromotionObject.AssetName,
			AssetType:             p.PromotionObject.AssetType,
			ClueAcquisitionMethod: p.PromotionObject.ClueAcquisitionMethod,
		},
		Inventory:         p.Inventory,
		Audience:          p.Audience,
		PromotionStrategy: p.PromotionStrategy,
		TrackUrls:         p.TrackUrls,
		ClassifyInfo:      p.ClassifyInfo,
		SystemOrigin:      p.SystemOrigin,
		CreateChannel:     p.CreateChannel,
		DpaLibrary:        p.DpaLibrary,
		AutoExtend:        p.AutoExtend,
	}
}

// GetNativeInteractionData 获取原生互动项目专用数据
func (p *ProjectDataDetail) GetNativeInteractionData() *NativeInteractionData {
	if !p.IsNativeInteraction() {
		return nil
	}
	return &NativeInteractionData{
		MarketingInfo: p.MarketingInfo,
		NativeInteractionObject: NativeInteractionObject{
			ContentType:        p.PromotionObject.ContentType,
			AssetName:          p.PromotionObject.AssetName,
			AssetType:          p.PromotionObject.AssetType,
			DeliveryProduct:    p.PromotionObject.DeliveryProduct,
			DeliveryMedium:     p.PromotionObject.DeliveryMedium,
			DeliveryProductIDs: p.PromotionObject.DeliveryProductIDs,
			DeliveryMediumIDs:  p.PromotionObject.DeliveryMediumIDs,
		},
		Inventory:         p.Inventory,
		Audience:          p.Audience,
		PromotionStrategy: p.PromotionStrategy,
		TrackUrls:         p.TrackUrls,
		ClassifyInfo:      p.ClassifyInfo,
		SystemOrigin:      p.SystemOrigin,
		CreateChannel:     p.CreateChannel,
		DpaLibrary:        p.DpaLibrary,
		AutoExtend:        p.AutoExtend,
	}
}

// AppPromotionData 应用推广项目专用数据结构
type AppPromotionData struct {
	MarketingInfo      MarketingInfo      `json:"MarketingInfo"`     // 营销信息
	AppPromotionObject AppPromotionObject `json:"PromotionObject"`   // 应用推广对象
	Inventory          Inventory          `json:"Inventory"`         // 库存
	Audience           Audience           `json:"Audience"`          // 受众
	PromotionStrategy  PromotionStrategy  `json:"PromotionStrategy"` // 推广策略
	TrackUrls          TrackUrls          `json:"TrackUrls"`         // 监测链接
	ClassifyInfo       ClassifyInfo       `json:"ClassifyInfo"`      // 分类信息
	SystemOrigin       string             `json:"SystemOrigin"`      // 系统来源
	CreateChannel      string             `json:"CreateChannel"`     // 创建渠道
	DpaLibrary         []interface{}      `json:"DpaLibrary"`        // DPA库
}

// LeadGenerationData 销售线索推广项目专用数据结构
type LeadGenerationData struct {
	MarketingInfo       MarketingInfo       `json:"MarketingInfo"`        // 营销信息
	LeadPromotionObject LeadPromotionObject `json:"PromotionObject"`      // 销售线索推广对象
	Inventory           Inventory           `json:"Inventory"`            // 库存
	Audience            Audience            `json:"Audience"`             // 受众
	PromotionStrategy   PromotionStrategy   `json:"PromotionStrategy"`    // 推广策略
	TrackUrls           TrackUrls           `json:"TrackUrls"`            // 监测链接
	ClassifyInfo        ClassifyInfo        `json:"ClassifyInfo"`         // 分类信息
	SystemOrigin        string              `json:"SystemOrigin"`         // 系统来源
	CreateChannel       string              `json:"CreateChannel"`        // 创建渠道
	DpaLibrary          []interface{}       `json:"DpaLibrary"`           // DPA库
	AutoExtend          *AutoExtend         `json:"AutoExtend,omitempty"` // 自动扩展信息
}

// NativeInteractionData 原生互动项目专用数据结构
type NativeInteractionData struct {
	MarketingInfo           MarketingInfo           `json:"MarketingInfo"`        // 营销信息
	NativeInteractionObject NativeInteractionObject `json:"PromotionObject"`      // 原生互动推广对象
	Inventory               Inventory               `json:"Inventory"`            // 库存
	Audience                Audience                `json:"Audience"`             // 受众
	PromotionStrategy       PromotionStrategy       `json:"PromotionStrategy"`    // 推广策略
	TrackUrls               TrackUrls               `json:"TrackUrls"`            // 监测链接
	ClassifyInfo            ClassifyInfo            `json:"ClassifyInfo"`         // 分类信息
	SystemOrigin            string                  `json:"SystemOrigin"`         // 系统来源
	CreateChannel           string                  `json:"CreateChannel"`        // 创建渠道
	DpaLibrary              []interface{}           `json:"DpaLibrary"`           // DPA库
	AutoExtend              *AutoExtend             `json:"AutoExtend,omitempty"` // 自动扩展信息
}

// AppPromotionObject 应用推广对象
type AppPromotionObject struct {
	AppType            int      `json:"AppType"`            // 应用类型
	DownloadType       int      `json:"DownloadType"`       // 下载类型
	DownloadURL        string   `json:"DownloadUrl"`        // 下载链接
	PackageName        string   `json:"PackageName"`        // 包名
	AppName            string   `json:"AppName"`            // 应用名称
	ContentType        int      `json:"ContentType"`        // 内容类型
	AppPkgID           string   `json:"AppPkgId"`           // 应用包ID
	DeliveryProduct    int      `json:"DeliveryProduct"`    // 投放产品
	DeliveryMedium     int      `json:"DeliveryMedium"`     // 投放媒体
	DeliveryProductIDs []string `json:"DeliveryProductIds"` // 投放产品ID列表
	DeliveryMediumIDs  []string `json:"DeliveryMediumIds"`  // 投放媒体ID列表
	AssetName          string   `json:"AssetName"`          // 素材名称
	AssetType          int      `json:"AssetType"`          // 素材类型
}

// LeadPromotionObject 销售线索推广对象
type LeadPromotionObject struct {
	ProductPlatformID     string   `json:"ProductPlatformId"`     // 商品平台ID
	ProductID             string   `json:"ProductId"`             // 商品ID
	UniqueProductID       string   `json:"UniqueProductId"`       // 唯一商品ID
	ProductObjectID       string   `json:"ProductObjectId"`       // 商品对象ID
	ContentType           int      `json:"ContentType"`           // 内容类型
	DeliveryProduct       int      `json:"DeliveryProduct"`       // 投放产品
	DeliveryMedium        int      `json:"DeliveryMedium"`        // 投放媒体
	DeliveryProductIDs    []string `json:"DeliveryProductIds"`    // 投放产品ID列表
	DeliveryMediumIDs     []string `json:"DeliveryMediumIds"`     // 投放媒体ID列表
	DpaTargetType         int      `json:"DpaTargetType"`         // DPA目标类型
	CategoryType          int      `json:"CategoryType"`          // 分类类型
	AssetName             string   `json:"AssetName"`             // 素材名称
	AssetType             int      `json:"AssetType"`             // 素材类型
	ClueAcquisitionMethod int      `json:"ClueAcquisitionMethod"` // 线索获取方式
}

// NativeInteractionObject 原生互动推广对象
type NativeInteractionObject struct {
	ContentType        int      `json:"ContentType"`        // 内容类型
	AssetName          string   `json:"AssetName"`          // 素材名称
	AssetType          int      `json:"AssetType"`          // 素材类型
	DeliveryProduct    int      `json:"DeliveryProduct"`    // 投放产品
	DeliveryMedium     int      `json:"DeliveryMedium"`     // 投放媒体
	DeliveryProductIDs []string `json:"DeliveryProductIds"` // 投放产品ID列表
	DeliveryMediumIDs  []string `json:"DeliveryMediumIds"`  // 投放媒体ID列表
}

// AutoExtend 自动扩展信息
type AutoExtend struct {
	WeekScheduleType int       `json:"WeekScheduleType"` // 周计划类型
	DeliveryScene    int       `json:"DeliveryScene"`    // 投放场景
	Products         []Product `json:"Products"`         // 商品列表
}

// Product 商品信息
type Product struct {
	ProductPlatformID string `json:"ProductPlatformId"` // 商品平台ID
	ProductID         string `json:"ProductId"`         // 商品ID
	UniqueProductID   string `json:"UniqueProductId"`   // 唯一商品ID
	ProductObjectID   string `json:"ProductObjectId"`   // 商品对象ID
}

// MarketingInfo 营销信息
type MarketingInfo struct {
	LandingType          int `json:"LandingType"`           // 落地页类型
	CdpMarketingGoal     int `json:"CdpMarketingGoal"`      // CDP营销目标
	CampaignType         int `json:"CampaignType"`          // 推广类型
	AppPromotionType     int `json:"AppPromotionType"`      // 应用推广类型
	DeliveryRelatedNum   int `json:"DeliveryRelatedNum"`    // 投放相关数量
	DeliveryMode         int `json:"DeliveryMode"`          // 投放模式
	DeliveryPackage      int `json:"DeliveryPackage"`       // 投放包
	DeliveryModeInternal int `json:"DeliveryModeInternal"`  // 内部投放模式
	AutoAdType           int `json:"autoAdType,omitempty"`  // 自动广告类型（可选字段，只有开启自动投放时才出现）
	AutoAdScene          int `json:"autoAdScene,omitempty"` // 自动广告场景（可选字段，只有开启自动投放时才出现）
}

// PromotionObject 推广对象（保留原有结构体以兼容现有代码）
type PromotionObject struct {
	ProductPlatformID     string   `json:"ProductPlatformId"`     // 商品平台ID
	ProductID             string   `json:"ProductId"`             // 商品ID
	AppType               int      `json:"AppType"`               // 应用类型
	DownloadType          int      `json:"DownloadType"`          // 下载类型
	DownloadURL           string   `json:"DownloadUrl"`           // 下载链接
	PackageName           string   `json:"PackageName"`           // 包名
	AppName               string   `json:"AppName"`               // 应用名称
	ContentType           int      `json:"ContentType"`           // 内容类型
	UniqueProductID       string   `json:"UniqueProductId"`       // 唯一商品ID
	AppPkgID              string   `json:"AppPkgId"`              // 应用包ID
	ProductObjectID       string   `json:"ProductObjectId"`       // 商品对象ID
	DeliveryProduct       int      `json:"DeliveryProduct"`       // 投放产品
	DeliveryMedium        int      `json:"DeliveryMedium"`        // 投放媒体
	DeliveryProductIDs    []string `json:"DeliveryProductIds"`    // 投放产品ID列表
	DeliveryMediumIDs     []string `json:"DeliveryMediumIds"`     // 投放媒体ID列表
	DpaTargetType         int      `json:"DpaTargetType"`         // DPA目标类型
	CategoryType          int      `json:"CategoryType"`          // 分类类型
	AssetName             string   `json:"AssetName"`             // 素材名称
	AssetType             int      `json:"AssetType"`             // 素材类型
	ClueAcquisitionMethod int      `json:"ClueAcquisitionMethod"` // 线索获取方式
}

// Inventory 库存
type Inventory struct {
	InventoryType    []int `json:"InventoryType"`    // 库存类型
	SmartInventory   int   `json:"SmartInventory"`   // 智能库存
	InventoryCatalog int   `json:"InventoryCatalog"` // 库存目录
	DeliveryRange    int   `json:"DeliveryRange"`    // 投放范围
}

// Audience 受众
type Audience struct {
	District                  string                 `json:"District"`                  // 地域
	Platform                  []string               `json:"Platform"`                  // 平台
	Gender                    string                 `json:"Gender"`                    // 性别
	Age                       [][]string             `json:"Age"`                       // 年龄段
	RetargetingTags           []string               `json:"RetargetingTags"`           // 重定向标签（定向人群包）
	RetargetingTagsExclude    []string               `json:"RetargetingTagsExclude"`    // 重定向标签排除（排除人群包）
	ActionScene               []int                  `json:"ActionScene"`               // 行为场景
	AutoExtendEnabled         int                    `json:"AutoExtendEnabled"`         // 自动扩展启用
	HideIfConverted           string                 `json:"HideIfConverted"`           // 隐藏已转化用户
	HideIfExists              string                 `json:"HideIfExists"`              // 隐藏已安装用户
	AudiencePackageDetail     map[string]interface{} `json:"AudiencePackageDetail"`     // 受众包详情
	FilterAwemeAbnormalActive string                 `json:"FilterAwemeAbnormalActive"` // 过滤抖音异常活跃用户
	FilterAwemeFansCount      string                 `json:"FilterAwemeFansCount"`      // 过滤抖音粉丝数
	FilterOwnAwemeFans        string                 `json:"FilterOwnAwemeFans"`        // 过滤自己的抖音粉丝
}

// PromotionStrategy 推广策略
type PromotionStrategy struct {
	AssetIDs           []string       `json:"AssetIds"`           // 素材ID列表
	ExternalAction     string         `json:"ExternalAction"`     // 外部转化目标
	SmartBidType       int            `json:"SmartBidType"`       // 智能出价类型
	FlowControlMode    int            `json:"FlowControlMode"`    // 流量控制模式
	ScheduleType       int            `json:"ScheduleType"`       // 投放时间类型
	StartTime          string         `json:"StartTime"`          // 开始时间
	EndTime            string         `json:"EndTime"`            // 结束时间
	BudgetMode         int            `json:"BudgetMode"`         // 预算模式
	Budget             FlexibleString `json:"Budget"`             // 预算（支持字符串和数字）
	PricingType        int            `json:"PricingType"`        // 出价类型
	TrackURLType       int            `json:"TrackUrlType"`       // 监测链接类型
	TrackURLGroupID    string         `json:"TrackUrlGroupId"`    // 监测链接组ID
	DeepBidType        int            `json:"DeepBidType"`        // 深度出价类型
	DeepExternalAction string         `json:"DeepExternalAction"` // 深度外部转化目标
	ConvertSource      int            `json:"ConvertSource"`      // 转化来源
	Bid                FlexibleString `json:"Bid"`                // 出价（支持字符串和数字）
	FeedDeliverySearch int            `json:"FeedDeliverySearch"` // Feed投放搜索
	SearchBidRatio     FlexibleString `json:"SearchBidRatio"`     // 搜索出价比例（支持字符串和数字）
	AudienceExtend     int            `json:"AudienceExtend"`     // 受众扩展
	IsNewKeywordStore  bool           `json:"IsNewKeywordStore"`  // 是否新关键词库
	BudgetFloat        float64        `json:"budget"`             // 预算（浮点数）
}

// TrackUrls 监测链接
type TrackUrls struct {
	TrackURL         []string `json:"TrackUrl"`         // 展现监测链接
	ActionTrackURL   []string `json:"ActionTrackUrl"`   // 点击监测链接
	FirstFrame       []string `json:"FirstFrame"`       // 视频播放监测链接
	LastFrame        []string `json:"LastFrame"`        // 视频完播监测链接
	EffectiveFrame   []string `json:"EffectiveFrame"`   // 视频有效播放监测链接
	TrackURLSendType string   `json:"TrackUrlSendType"` // 监测链接发送类型
}

// ClassifyInfo 分类信息
type ClassifyInfo struct {
	Classify int `json:"Classify"` // 分类
}

// NebulaCampaign 推广计划详情
type NebulaCampaign struct {
	ID               string           `json:"id"`                // 推广计划ID
	AdvertiserID     string           `json:"advertiser_id"`     // 广告主ID
	FrequencyID      string           `json:"frequency_id"`      // 频次ID
	Name             string           `json:"name"`              // 推广计划名称
	CreateTime       string           `json:"create_time"`       // 创建时间
	ModifyTime       string           `json:"modify_time"`       // 修改时间
	Stat1            int              `json:"stat1"`             // 状态1
	Stat2            int              `json:"stat2"`             // 状态2
	IsDel            int              `json:"isdel"`             // 是否删除
	OptTime          string           `json:"opt_time"`          // 操作时间
	Version          int              `json:"version"`           // 版本
	LandingType      int              `json:"landing_type"`      // 落地页类型
	OperatorType     int              `json:"operator_type"`     // 操作者类型
	EditVersion      string           `json:"edit_version"`      // 编辑版本
	PricingCategory  int              `json:"pricing_category"`  // 出价分类
	SystemOrigin     int              `json:"system_origin"`     // 系统来源
	CreateChannel    int              `json:"create_channel"`    // 创建渠道
	CampaignType     int              `json:"campaign_type"`     // 推广类型
	ObjectiveType    int              `json:"objective_type"`    // 目标类型
	CampaignCategory int              `json:"campaign_category"` // 推广分类
	DataDict         CampaignDataDict `json:"data_dict"`         // 数据字典
}

// CampaignDataDict 推广计划数据字典
type CampaignDataDict struct {
	BudgetMode              int    `json:"budget_mode"`                // 预算模式
	Budget                  string `json:"budget"`                     // 预算
	CreateChannelUserID     string `json:"create_channel_user_id"`     // 创建渠道用户ID
	BudgetOptimizeSwitch    int    `json:"budget_optimize_switch"`     // 预算优化开关
	DeliveryRelatedNum      int    `json:"delivery_related_num"`       // 投放相关数量
	MarketingScene          int    `json:"marketing_scene"`            // 营销场景
	DeliveryMode            int    `json:"delivery_mode"`              // 投放模式
	MarketingPurpose        int    `json:"marketing_purpose"`          // 营销目的
	CampaignRecordCreatorID string `json:"campaign_record_creator_id"` // 推广计划记录创建者ID
	CdpMarketingGoal        int    `json:"cdp_marketing_goal"`         // CDP营销目标
	AppPromotionType        int    `json:"app_promotion_type"`         // 应用推广类型
	CdpProjectID            string `json:"cdp_project_id"`             // CDP项目ID
	PlatformVersion         int    `json:"platform_version"`           // 平台版本
	CdpProjectName          string `json:"cdp_project_name"`           // CDP项目名称
	DeliveryPackage         int    `json:"delivery_package"`           // 投放包
	PayType                 int    `json:"pay_type"`                   // 付费类型
}
