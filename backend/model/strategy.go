package model

import (
	"time"

	"gorm.io/gorm"
)

// Strategy 策略模型
type Strategy struct {
	ID               int64      `json:"id" gorm:"primaryKey;autoIncrement;comment:策略ID"`
	Name             string     `json:"name" gorm:"type:varchar(255);not null;comment:策略名称"`
	Description      string     `json:"description" gorm:"type:text;comment:策略描述"`
	Type             string     `json:"type" gorm:"type:varchar(20);default:'global';comment:策略类型(advertiser/project/promotion/global)"`
	Enabled          bool       `json:"enabled" gorm:"default:true;comment:是否启用"`
	LogicType        string     `json:"logic_type" gorm:"type:varchar(10);default:'AND';comment:逻辑关系(AND/OR)"`
	MonitorFrequency int        `json:"monitor_frequency" gorm:"default:300;comment:监测频率(秒)"`
	ConditionsCount  int        `json:"conditions_count" gorm:"default:0;comment:条件数量"`
	ActionsCount     int        `json:"actions_count" gorm:"default:0;comment:动作数量"`
	TriggerCount     int        `json:"trigger_count" gorm:"default:0;comment:触发次数"`
	LastTriggered    *time.Time `json:"last_triggered" gorm:"comment:最后触发时间"`
	Conditions       string     `json:"conditions" gorm:"type:text;comment:触发条件JSON字符串"`
	Actions          string     `json:"actions" gorm:"type:text;comment:触发动作JSON字符串"`
	CreatedAt        time.Time  `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time  `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 表名
func (Strategy) TableName() string {
	return "strategies"
}

// StrategyModel 策略模型操作
type StrategyModel struct {
	DB *gorm.DB
}

// NewStrategyModel 创建策略模型
func NewStrategyModel(db *gorm.DB) *StrategyModel {
	return &StrategyModel{DB: db}
}

// Create 创建策略
func (m *StrategyModel) Create(strategy *Strategy) error {
	return m.DB.Create(strategy).Error
}

// GetById 根据ID获取策略
func (m *StrategyModel) GetById(id int64) (*Strategy, error) {
	var strategy Strategy
	err := m.DB.First(&strategy, id).Error
	if err != nil {
		return nil, err
	}
	return &strategy, nil
}

// Update 更新策略
func (m *StrategyModel) Update(tx *gorm.DB, strategy *Strategy) error {
	db := m.DB
	if tx != nil {
		db = tx
	}
	return db.Save(strategy).Error
}

// Delete 删除策略
func (m *StrategyModel) Delete(strategy *Strategy) error {
	return m.DB.Delete(strategy).Error
}

// BatchDelete 批量删除策略
func (m *StrategyModel) BatchDelete(ids []int64) error {
	return m.DB.Where("id IN ?", ids).Delete(&Strategy{}).Error
}

// List 获取策略列表
func (m *StrategyModel) List(page, pageSize int, conditions map[string]interface{}) ([]*Strategy, int64, error) {
	var strategies []*Strategy
	var total int64

	query := m.DB.Model(&Strategy{})

	// 应用查询条件
	for key, value := range conditions {
		switch key {
		case "name":
			if value != nil {
				if name, ok := value.(string); ok {
					query = query.Where("name LIKE ?", "%"+name+"%")
				}
			}
		case "enabled":
			query = query.Where("enabled = ?", value)
		case "logic_type":
			query = query.Where("logic_type = ?", value)
		default:
			query = query.Where(key+" = ?", value)
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&strategies).Error; err != nil {
		return nil, 0, err
	}

	return strategies, total, nil
}

// MapUpdate 使用Map更新策略
func (m *StrategyModel) MapUpdate(tx *gorm.DB, id int64, data map[string]interface{}) error {
	db := m.DB
	if tx != nil {
		db = tx
	}
	return db.Model(&Strategy{}).Where("id = ?", id).Updates(data).Error
}

// ToggleEnabled 切换启用状态
func (m *StrategyModel) ToggleEnabled(id int64) error {
	return m.DB.Model(&Strategy{}).Where("id = ?", id).Update("enabled", gorm.Expr("NOT enabled")).Error
}

// UpdateTriggerInfo 更新触发信息
func (m *StrategyModel) UpdateTriggerInfo(id int64) error {
	return m.DB.Model(&Strategy{}).Where("id = ?", id).Updates(map[string]interface{}{
		"trigger_count":  gorm.Expr("trigger_count + 1"),
		"last_triggered": time.Now(),
	}).Error
}

// GetEnabledStrategies 获取所有启用的策略
func (m *StrategyModel) GetEnabledStrategies() ([]*Strategy, error) {
	var strategies []*Strategy
	err := m.DB.Where("enabled = ?", true).Find(&strategies).Error
	return strategies, err
}

// GetAllStrategies 获取所有存在的策略（不管是否启用）
func (m *StrategyModel) GetAllStrategies() ([]*Strategy, error) {
	var strategies []*Strategy
	err := m.DB.Find(&strategies).Error
	return strategies, err
}

// CountAll 统计策略总数
func (m *StrategyModel) CountAll() (int, error) {
	var count int64
	err := m.DB.Model(&Strategy{}).Count(&count).Error
	return int(count), err
}

// CountEnabled 统计启用的策略数量
func (m *StrategyModel) CountEnabled() (int, error) {
	var count int64
	err := m.DB.Model(&Strategy{}).Where("enabled = ?", true).Count(&count).Error
	return int(count), err
}
