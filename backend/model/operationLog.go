package model

import (
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"

	"gorm.io/gorm"
)

// OperationLog 操作日志模型
type OperationLog struct {
	Id            int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"` // 数据库主键ID
	AccountName   string    `gorm:"column:account_name;comment:账户名称" json:"account_name"`         // 账户名称
	AccountId     int64     `gorm:"column:account_id;comment:账户ID" json:"account_id"`             // 账户ID（巨量引擎平台ID）
	Module        string    `gorm:"column:module;comment:模块名" json:"module"`                      // 模块名
	OperationType string    `gorm:"column:operation_type;comment:操作类型" json:"operation_type"`     // 操作类型
	Operation     string    `gorm:"column:operation;comment:操作名称" json:"operation"`               // 操作名称
	Description   string    `gorm:"column:description;comment:操作描述" json:"description"`           // 操作描述
	OperationTime time.Time `gorm:"column:operation_time;comment:操作时间" json:"operation_time"`     // 操作时间
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_log"
}

// OperationLogModel 操作日志模型
type OperationLogModel struct {
	db *gorm.DB
}

// NewOperationLogModel 创建操作日志模型
func NewOperationLogModel(db *gorm.DB) *OperationLogModel {
	return &OperationLogModel{
		db: db,
	}
}

// Create 创建操作日志
func (m *OperationLogModel) Create(log *OperationLog) error {
	return m.db.Create(log).Error
}

// BatchCreate 批量创建操作日志
func (m *OperationLogModel) BatchCreate(logs []*OperationLog) error {
	return m.db.Create(logs).Error
}

// GetById 根据ID获取操作日志
func (m *OperationLogModel) GetById(id int64) (*OperationLog, error) {
	var log OperationLog
	if err := m.db.First(&log, id).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

// List 获取操作日志列表
func (m *OperationLogModel) List(in reqType.OperationLogListReq) ([]*OperationLog, int64, error) {
	var logs []*OperationLog
	var total int64

	db := m.db.Model(&OperationLog{})

	// 添加查询条件
	if in.AccountId != 0 {
		db = db.Where("account_id = ?", in.AccountId)
	}
	if in.Module != "" {
		db = db.Where("module = ?", in.Module)
	}
	if in.OperationType != "" {
		db = db.Where("operation_type = ?", in.OperationType)
	}
	if in.Operation != "" {
		db = db.Where("operation = ?", in.Operation)
	}
	if in.Description != "" {
		db = db.Where("description LIKE ?", "%"+in.Description+"%")
	}
	if !in.StartOperationTime.IsZero() {
		db = db.Where("operation_time >= ?", in.StartOperationTime)
	}
	if !in.EndOperationTime.IsZero() {
		db = db.Where("operation_time < ?", in.EndOperationTime)
	}
	// 分页查询
	if in.PageSize > 0 && in.Page > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((in.Page - 1) * in.PageSize).Limit(in.PageSize)
	}
	if err := db.Order("operation_time DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteByTime 根据时间删除操作日志
func (m *OperationLogModel) DeleteByTime(beforeTime time.Time) error {
	return m.db.Where("operation_time < ?", beforeTime).Delete(&OperationLog{}).Error
}

// DeleteBatch 批量删除操作日志
func (m *OperationLogModel) DeleteBatch(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&OperationLog{}).Error
}

// GetStatsByTimeRange 获取指定时间范围内的操作统计
func (m *OperationLogModel) GetStatsByTimeRange(startTime, endTime time.Time) (map[string]int64, error) {
	var results []struct {
		OperationType string
		Count         int64
	}

	err := m.db.Model(&OperationLog{}).
		Select("operation_type, COUNT(*) as count").
		Where("operation_time BETWEEN ? AND ?", startTime, endTime).
		Group("operation_type").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int64)
	for _, result := range results {
		stats[result.OperationType] = result.Count
	}

	return stats, nil
}
