package model

import (
	"time"

	"gorm.io/gorm"
)

// TextInfo 文本信息模型
type TextInfo struct {
	Id          int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`                 // 数据库主键ID
	Text        string    `gorm:"column:text;comment:文本内容" json:"text,omitempty"`                               // 文本内容
	FontFile    string    `gorm:"column:font_file;comment:字体文件" json:"font_file,omitempty"`                     // 字体文件
	FontSize    int64     `gorm:"column:font_size;comment:字体大小" json:"font_size,omitempty"`                     // 字体大小
	FontColor   string    `gorm:"column:font_color;comment:字体颜色" json:"font_color,omitempty"`                   // 字体颜色
	PositionX   float64   `gorm:"column:position_x;comment:X轴位置" json:"position_x,omitempty"`                   // X轴位置
	PositionY   float64   `gorm:"column:position_y;comment:Y轴位置" json:"position_y,omitempty"`                   // Y轴位置
	Box         int64     `gorm:"column:box;comment:是否启用背景框" json:"box,omitempty"`                              // 是否启用背景框（1、禁用 2、启用）
	BoxColor    string    `gorm:"column:box_color;comment:背景框颜色" json:"box_color,omitempty"`                    // 背景框颜色
	BoxBorderRw int64     `gorm:"column:box_border_rw;comment:背景框宽度" json:"box_border_rw,omitempty"`            // 背景框宽度
	ShadowWx    int64     `gorm:"column:shadow_wx;comment:文本阴影水平偏移" json:"shadow_wx,omitempty"`                 // 文本阴影的水平偏移
	ShadowWy    int64     `gorm:"column:shadow_wy;comment:文本阴影垂直偏移" json:"shadow_wy,omitempty"`                 // 文本阴影的垂直偏移
	ShadowColor string    `gorm:"column:shadow_color;comment:文本阴影颜色" json:"shadow_color,omitempty"`             // 文本阴影颜色
	BorderRw    int64     `gorm:"column:border_rw;comment:文本框边框宽度" json:"border_rw,omitempty"`                  // 文本框边框宽度
	BorderColor string    `gorm:"column:border_color;comment:文本框边框颜色" json:"border_color,omitempty"`            // 文本框边框颜色
	CreateTime  time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	Remark      *string   `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
}

// TableName 指定表名
func (TextInfo) TableName() string {
	return "text_info"
}

// TextInfoModel 文本信息表模型
type TextInfoModel struct {
	db *gorm.DB
}

// NewTextInfoModel 创建文本信息模型
func NewTextInfoModel(db *gorm.DB) *TextInfoModel {
	return &TextInfoModel{
		db: db,
	}
}

// Create 创建新文本信息
func (m *TextInfoModel) Create(textInfo *TextInfo) error {
	return m.db.Create(textInfo).Error
}

// Update 更新文本信息
func (m *TextInfoModel) Update(tx *gorm.DB, textInfo *TextInfo) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Updates(textInfo).Error
}

// MapUpdate 更新文本信息
func (m *TextInfoModel) MapUpdate(tx *gorm.DB, id int64, textInfo map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&TextInfo{}).Where("id = ?", id).Updates(textInfo).Error
}

// Save 保存文本信息
func (m *TextInfoModel) Save(tx *gorm.DB, textInfo *TextInfo) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(textInfo).Error
}

// Delete 删除文本信息
func (m *TextInfoModel) Delete(textInfo *TextInfo) error {
	return m.db.Delete(textInfo).Error
}

// GetById 根据ID获取文本信息
func (m *TextInfoModel) GetById(id int64) (*TextInfo, error) {
	var textInfo TextInfo
	if err := m.db.First(&textInfo, id).Error; err != nil {
		return nil, err
	}
	return &textInfo, nil
}

// List 获取文本信息列表
func (m *TextInfoModel) List(page, pageSize int, conditions map[string]interface{}) ([]*TextInfo, int64, error) {
	var textInfos []*TextInfo
	var total int64

	db := m.db.Model(&TextInfo{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "text" || key == "remark" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if err := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&textInfos).Error; err != nil {
		return nil, 0, err
	}

	return textInfos, total, nil
}

// ListAll 获取所有文本信息
func (m *TextInfoModel) ListAll() ([]*TextInfo, error) {
	var textInfos []*TextInfo
	if err := m.db.Find(&textInfos).Error; err != nil {
		return nil, err
	}
	return textInfos, nil
}

// GetStat 获取文本信息统计信息
func (m *TextInfoModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64

	if err := m.db.Model(&TextInfo{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count": totalCount,
	}, nil
}

// BatchDelete 批量删除文本信息
func (m *TextInfoModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&TextInfo{}).Error
}

// GetByText 根据文本内容获取文本信息
func (m *TextInfoModel) GetByText(text string) (*TextInfo, error) {
	var textInfo TextInfo
	if err := m.db.Where("text = ?", text).First(&textInfo).Error; err != nil {
		return nil, err
	}
	return &textInfo, nil
}
