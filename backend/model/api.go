package model

import (
	"time"

	"gorm.io/gorm"
)

// ApiRecord 第三方接口记录模型
type ApiRecord struct {
	Id           int64               `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`                 // 数据库主键ID
	Url          string              `gorm:"column:url;not null;comment:请求URL" json:"url"`                                 // 请求URL（不包括query参数）
	Query        string              `gorm:"column:query;comment:查询参数" json:"query"`                                       // 查询参数
	Method       string              `gorm:"column:method;not null;comment:请求方法" json:"method"`                            // 请求方法（GET、POST等）
	ReqBody      string              `gorm:"column:req_body;type:longtext;comment:请求体" json:"req_body"`                    // 请求体
	ReqHeader    map[string][]string `gorm:"column:req_header;type:json;serializer:json;comment:请求头" json:"req_header"`    // 请求头
	RespHeader   map[string][]string `gorm:"column:resp_header;type:json;serializer:json;comment:响应头" json:"resp_header"`  // 响应头
	RespBody     string              `gorm:"column:resp_body;type:longtext;comment:响应体" json:"resp_body"`                  // 响应体
	StatusCode   int                 `gorm:"column:status_code;comment:HTTP状态码" json:"status_code"`                        // HTTP状态码
	ResponseTime int64               `gorm:"column:response_time;comment:响应时间" json:"response_time"`                       // 响应时间（微秒）
	Success      bool                `gorm:"column:success;default:false;comment:请求是否成功" json:"success"`                   // 请求是否成功
	ErrorMsg     string              `gorm:"column:error_msg;comment:错误信息" json:"error_msg"`                               // 错误信息
	Source       string              `gorm:"column:source;comment:来源标识" json:"source"`                                     // 来源标识（用于区分不同的调用方）
	CreateTime   time.Time           `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime   time.Time           `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName 指定表名
func (ApiRecord) TableName() string {
	return "api_record"
}

// ApiRecordModel API记录表模型
type ApiRecordModel struct {
	db *gorm.DB
}

// NewApiRecordModel 创建API记录模型
func NewApiRecordModel(db *gorm.DB) *ApiRecordModel {
	return &ApiRecordModel{
		db: db,
	}
}

// Create 创建新的API记录
func (m *ApiRecordModel) Create(apiRecord *ApiRecord) error {
	return m.db.Create(apiRecord).Error
}

// BatchCreate 批量创建API记录
func (m *ApiRecordModel) BatchCreate(tx *gorm.DB, apiRecords []*ApiRecord) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.CreateInBatches(apiRecords, 500).Error
}

// Update 更新API记录信息
func (m *ApiRecordModel) Update(tx *gorm.DB, apiRecord *ApiRecord) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Updates(apiRecord).Error
}

// MapUpdate 使用Map更新API记录信息
func (m *ApiRecordModel) MapUpdate(tx *gorm.DB, id int64, updates map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&ApiRecord{}).Where("id = ?", id).Updates(updates).Error
}

// Save 保存API记录信息
func (m *ApiRecordModel) Save(tx *gorm.DB, apiRecord *ApiRecord) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(apiRecord).Error
}

// Delete 删除API记录
func (m *ApiRecordModel) Delete(apiRecord *ApiRecord) error {
	return m.db.Delete(apiRecord).Error
}

// GetById 根据ID获取API记录
func (m *ApiRecordModel) GetById(id int64) (*ApiRecord, error) {
	var apiRecord ApiRecord
	if err := m.db.First(&apiRecord, id).Error; err != nil {
		return nil, err
	}
	return &apiRecord, nil
}

// List 获取API记录列表
func (m *ApiRecordModel) List(page, pageSize int, conditions map[string]interface{}) ([]*ApiRecord, int64, error) {
	var apiRecords []*ApiRecord
	var total int64

	db := m.db.Model(&ApiRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "url" || key == "method" || key == "source" || key == "error_msg" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询，按创建时间倒序
	if err := db.Order("create_time DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&apiRecords).Error; err != nil {
		return nil, 0, err
	}

	return apiRecords, total, nil
}

// ListByTimeRange 根据时间范围获取API记录
func (m *ApiRecordModel) ListByTimeRange(startTime, endTime time.Time, conditions map[string]interface{}) ([]*ApiRecord, error) {
	var apiRecords []*ApiRecord

	db := m.db.Model(&ApiRecord{}).Where("create_time BETWEEN ? AND ?", startTime, endTime)

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "url" || key == "method" || key == "source" || key == "error_msg" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	if err := db.Order("create_time DESC").Find(&apiRecords).Error; err != nil {
		return nil, err
	}

	return apiRecords, nil
}

// GetStat 获取API记录统计信息
func (m *ApiRecordModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var successCount int64
	var failedCount int64

	// 总记录数
	if err := m.db.Model(&ApiRecord{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 成功记录数
	if err := m.db.Model(&ApiRecord{}).Where("success = ?", true).Count(&successCount).Error; err != nil {
		return nil, err
	}

	// 失败记录数
	if err := m.db.Model(&ApiRecord{}).Where("success = ?", false).Count(&failedCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":   totalCount,
		"success_count": successCount,
		"failed_count":  failedCount,
		"success_rate":  float64(successCount) / float64(totalCount) * 100,
	}, nil
}

// GetStatBySource 根据来源获取统计信息
func (m *ApiRecordModel) GetStatBySource(source string) (map[string]interface{}, error) {
	var totalCount int64
	var successCount int64
	var avgResponseTime float64

	db := m.db.Model(&ApiRecord{}).Where("source = ?", source)

	// 总记录数
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 成功记录数
	if err := db.Where("success = ?", true).Count(&successCount).Error; err != nil {
		return nil, err
	}

	// 平均响应时间
	if err := db.Select("AVG(response_time)").Scan(&avgResponseTime).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":       totalCount,
		"success_count":     successCount,
		"failed_count":      totalCount - successCount,
		"success_rate":      float64(successCount) / float64(totalCount) * 100,
		"avg_response_time": avgResponseTime,
	}, nil
}

// GetStatByUrl 根据URL获取统计信息
func (m *ApiRecordModel) GetStatByUrl(url string) (map[string]interface{}, error) {
	var totalCount int64
	var successCount int64
	var avgResponseTime float64

	db := m.db.Model(&ApiRecord{}).Where("url = ?", url)

	// 总记录数
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 成功记录数
	if err := db.Where("success = ?", true).Count(&successCount).Error; err != nil {
		return nil, err
	}

	// 平均响应时间
	if err := db.Select("AVG(response_time)").Scan(&avgResponseTime).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":       totalCount,
		"success_count":     successCount,
		"failed_count":      totalCount - successCount,
		"success_rate":      float64(successCount) / float64(totalCount) * 100,
		"avg_response_time": avgResponseTime,
	}, nil
}

// BatchDelete 批量删除API记录
func (m *ApiRecordModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&ApiRecord{}).Error
}

// DeleteByTimeRange 删除指定时间范围内的记录
func (m *ApiRecordModel) DeleteByTimeRange(beforeTime time.Time) error {
	return m.db.Where("create_time < ?", beforeTime).Delete(&ApiRecord{}).Error
}

// GetTopUrls 获取调用次数最多的URL
func (m *ApiRecordModel) GetTopUrls(limit int) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	err := m.db.Model(&ApiRecord{}).
		Select("url, COUNT(*) as count, AVG(response_time) as avg_response_time, SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as success_count").
		Group("url").
		Order("count DESC").
		Limit(limit).
		Scan(&results).Error

	return results, err
}

// GetErrorStats 获取错误统计
func (m *ApiRecordModel) GetErrorStats(limit int) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	err := m.db.Model(&ApiRecord{}).
		Select("error_msg, COUNT(*) as count").
		Where("success = ? AND error_msg != ''", false).
		Group("error_msg").
		Order("count DESC").
		Limit(limit).
		Scan(&results).Error

	return results, err
}
