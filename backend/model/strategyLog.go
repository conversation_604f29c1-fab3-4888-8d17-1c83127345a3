package model

import (
	"time"

	"gorm.io/gorm"
)

// StrategyLog 策略执行日志模型
type StrategyLog struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:日志ID"`
	StrategyID      int64     `json:"strategy_id" gorm:"not null;comment:策略ID"`
	StrategyName    string    `json:"strategy_name" gorm:"type:varchar(255);not null;comment:策略名称"`
	TargetType      string    `json:"target_type" gorm:"type:varchar(20);not null;comment:执行目标类型(advertiser/project/promotion)"`
	TargetID        string    `json:"target_id" gorm:"type:varchar(50);not null;comment:执行目标ID"`
	TargetName      string    `json:"target_name" gorm:"type:varchar(255);comment:执行目标名称"`
	AdvertiserName  string    `json:"advertiser_name" gorm:"type:varchar(255);comment:广告主名称"`
	ProjectName     string    `json:"project_name" gorm:"type:varchar(255);comment:项目名称"`
	PromotionName   string    `json:"promotion_name" gorm:"type:varchar(255);comment:广告名称"`
	Conditions      string    `json:"conditions" gorm:"type:text;comment:执行的条件JSON"`
	ConditionMet    bool      `json:"condition_met" gorm:"default:false;comment:条件是否满足"`
	Actions         string    `json:"actions" gorm:"type:text;comment:执行的动作JSON"`
	ActionDetails   string    `json:"action_details" gorm:"type:text;comment:动作执行详情JSON"`
	ActionCompleted bool      `json:"action_completed" gorm:"default:false;comment:动作是否完成"`
	ErrorMessage    string    `json:"error_message" gorm:"type:text;comment:错误信息"`
	ExecutionTime   time.Time `json:"execution_time" gorm:"not null;comment:执行时间"`
	Duration        int64     `json:"duration" gorm:"default:0;comment:执行耗时(毫秒)"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
}

// TableName 表名
func (StrategyLog) TableName() string {
	return "strategy_logs"
}

// StrategyLogModel 策略日志模型操作
type StrategyLogModel struct {
	DB *gorm.DB
}

// NewStrategyLogModel 创建策略日志模型
func NewStrategyLogModel(db *gorm.DB) *StrategyLogModel {
	return &StrategyLogModel{DB: db}
}

// Create 创建策略日志
func (m *StrategyLogModel) Create(log *StrategyLog) error {
	return m.DB.Create(log).Error
}

// GetById 根据ID获取策略日志
func (m *StrategyLogModel) GetById(id int64) (*StrategyLog, error) {
	var log StrategyLog
	err := m.DB.First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// List 获取策略日志列表
func (m *StrategyLogModel) List(page, pageSize int, conditions map[string]interface{}) ([]*StrategyLog, int64, error) {
	var logs []*StrategyLog
	var total int64

	query := m.DB.Model(&StrategyLog{})

	// 应用查询条件
	for key, value := range conditions {
		switch key {
		case "strategy_id":
			query = query.Where("strategy_id = ?", value)
		case "strategy_name":
			if value != nil {
				if name, ok := value.(string); ok {
					query = query.Where("strategy_name LIKE ?", "%"+name+"%")
				}
			}
		case "target_type":
			query = query.Where("target_type = ?", value)
		case "target_id":
			query = query.Where("target_id = ?", value)
		case "condition_met":
			query = query.Where("condition_met = ?", value)
		case "action_completed":
			query = query.Where("action_completed = ?", value)
		case "start_time":
			query = query.Where("execution_time >= ?", value)
		case "end_time":
			query = query.Where("execution_time <= ?", value)
		default:
			query = query.Where(key+" = ?", value)
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("execution_time DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteByTime 根据时间删除策略日志
func (m *StrategyLogModel) DeleteByTime(beforeTime time.Time) error {
	return m.DB.Where("execution_time < ?", beforeTime).Delete(&StrategyLog{}).Error
}

// CountAll 统计策略日志总数
func (m *StrategyLogModel) CountAll() (int64, error) {
	var count int64
	err := m.DB.Model(&StrategyLog{}).Count(&count).Error
	return count, err
}

// CountByConditions 根据条件统计策略日志数量
func (m *StrategyLogModel) CountByConditions(conditions map[string]interface{}) (int64, error) {
	var count int64
	query := m.DB.Model(&StrategyLog{})

	// 应用查询条件
	for key, value := range conditions {
		switch key {
		case "strategy_id":
			query = query.Where("strategy_id = ?", value)
		case "strategy_name":
			if value != nil {
				if name, ok := value.(string); ok {
					query = query.Where("strategy_name LIKE ?", "%"+name+"%")
				}
			}
		case "target_type":
			query = query.Where("target_type = ?", value)
		case "target_id":
			query = query.Where("target_id = ?", value)
		case "condition_met":
			query = query.Where("condition_met = ?", value)
		case "action_completed":
			query = query.Where("action_completed = ?", value)
		case "start_time":
			query = query.Where("execution_time >= ?", value)
		case "end_time":
			query = query.Where("execution_time <= ?", value)
		default:
			query = query.Where(key+" = ?", value)
		}
	}

	err := query.Count(&count).Error
	return count, err
}
