package model

import (
	"time"

	"gorm.io/gorm"
)

// StrategyBinding 策略绑定模型
type StrategyBinding struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:绑定ID"`
	StrategyID  int64     `json:"strategy_id" gorm:"not null;index;comment:策略ID"`
	BindingType string    `json:"binding_type" gorm:"type:varchar(20);not null;index;comment:绑定类型(advertiser/project/promotion)"`
	BindingID   string    `json:"binding_id" gorm:"type:varchar(100);not null;index;comment:绑定实体ID"`
	BindingName string    `json:"binding_name" gorm:"type:varchar(255);comment:绑定实体名称"`
	Enabled     bool      `json:"enabled" gorm:"default:true;comment:是否启用"`
	Priority    int       `json:"priority" gorm:"default:0;comment:优先级(数字越大优先级越高)"`
	Description string    `json:"description" gorm:"type:text;comment:绑定描述"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 表名
func (StrategyBinding) TableName() string {
	return "strategy_bindings"
}

// StrategyBindingModel 策略绑定模型操作
type StrategyBindingModel struct {
	DB *gorm.DB
}

// NewStrategyBindingModel 创建策略绑定模型
func NewStrategyBindingModel(db *gorm.DB) *StrategyBindingModel {
	return &StrategyBindingModel{DB: db}
}

// Create 创建策略绑定
func (m *StrategyBindingModel) Create(binding *StrategyBinding) error {
	return m.DB.Create(binding).Error
}

// GetById 根据ID获取策略绑定
func (m *StrategyBindingModel) GetById(id int64) (*StrategyBinding, error) {
	var binding StrategyBinding
	err := m.DB.First(&binding, id).Error
	if err != nil {
		return nil, err
	}
	return &binding, nil
}

// Update 更新策略绑定
func (m *StrategyBindingModel) Update(tx *gorm.DB, binding *StrategyBinding) error {
	db := m.DB
	if tx != nil {
		db = tx
	}
	return db.Save(binding).Error
}

// Delete 删除策略绑定
func (m *StrategyBindingModel) Delete(binding *StrategyBinding) error {
	return m.DB.Delete(binding).Error
}

// BatchDelete 批量删除策略绑定
func (m *StrategyBindingModel) BatchDelete(ids []int64) error {
	return m.DB.Where("id IN ?", ids).Delete(&StrategyBinding{}).Error
}

// List 获取策略绑定列表
func (m *StrategyBindingModel) List(page, pageSize int, conditions map[string]interface{}) ([]*StrategyBinding, int64, error) {
	var bindings []*StrategyBinding
	var total int64

	query := m.DB.Model(&StrategyBinding{})

	// 应用查询条件
	for key, value := range conditions {
		switch key {
		case "strategy_id":
			query = query.Where("strategy_id = ?", value)
		case "binding_type":
			query = query.Where("binding_type = ?", value)
		case "binding_id":
			query = query.Where("binding_id = ?", value)
		case "enabled":
			query = query.Where("enabled = ?", value)
		case "binding_name":
			if value != nil {
				if name, ok := value.(string); ok {
					query = query.Where("binding_name LIKE ?", "%"+name+"%")
				}
			}
		default:
			query = query.Where(key+" = ?", value)
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("priority DESC, created_at DESC").Find(&bindings).Error; err != nil {
		return nil, 0, err
	}

	return bindings, total, nil
}

// GetByStrategyID 根据策略ID获取绑定列表
func (m *StrategyBindingModel) GetByStrategyID(strategyID int64) ([]*StrategyBinding, error) {
	var bindings []*StrategyBinding
	err := m.DB.Where("strategy_id = ? AND enabled = ?", strategyID, true).Order("priority DESC").Find(&bindings).Error
	return bindings, err
}

// GetByBindingTypeAndID 根据绑定类型和ID获取绑定列表
func (m *StrategyBindingModel) GetByBindingTypeAndID(bindingType, bindingID string) ([]*StrategyBinding, error) {
	var bindings []*StrategyBinding
	err := m.DB.Where("binding_type = ? AND binding_id = ? AND enabled = ?", bindingType, bindingID, true).Order("priority DESC").Find(&bindings).Error
	return bindings, err
}

// GetStrategiesByBinding 根据绑定实体获取策略列表
func (m *StrategyBindingModel) GetStrategiesByBinding(bindingType, bindingID string) ([]*Strategy, error) {
	var strategies []*Strategy
	err := m.DB.Joins("JOIN strategy_bindings ON strategies.id = strategy_bindings.strategy_id").
		Where("strategy_bindings.binding_type = ? AND strategy_bindings.binding_id = ? AND strategy_bindings.enabled = ?",
			bindingType, bindingID, true).
		Order("strategy_bindings.priority DESC").
		Find(&strategies).Error
	return strategies, err
}

// GetAdvertiserStrategies 获取广告主的策略列表
func (m *StrategyBindingModel) GetAdvertiserStrategies(advertiserID string) ([]*Strategy, error) {
	return m.GetStrategiesByBinding("advertiser", advertiserID)
}

// GetProjectStrategies 获取项目的策略列表
func (m *StrategyBindingModel) GetProjectStrategies(projectID string) ([]*Strategy, error) {
	return m.GetStrategiesByBinding("project", projectID)
}

// GetPromotionStrategies 获取广告的策略列表
func (m *StrategyBindingModel) GetPromotionStrategies(promotionID string) ([]*Strategy, error) {
	return m.GetStrategiesByBinding("promotion", promotionID)
}

// MapUpdate 使用Map更新策略绑定
func (m *StrategyBindingModel) MapUpdate(tx *gorm.DB, id int64, data map[string]interface{}) error {
	db := m.DB
	if tx != nil {
		db = tx
	}
	return db.Model(&StrategyBinding{}).Where("id = ?", id).Updates(data).Error
}

// ToggleEnabled 切换启用状态
func (m *StrategyBindingModel) ToggleEnabled(id int64) error {
	return m.DB.Model(&StrategyBinding{}).Where("id = ?", id).Update("enabled", gorm.Expr("NOT enabled")).Error
}

// DeleteByStrategyID 删除策略的所有绑定
func (m *StrategyBindingModel) DeleteByStrategyID(strategyID int64) error {
	return m.DB.Where("strategy_id = ?", strategyID).Delete(&StrategyBinding{}).Error
}

// DeleteByBinding 删除绑定实体的所有策略绑定
func (m *StrategyBindingModel) DeleteByBinding(bindingType, bindingID string) error {
	return m.DB.Where("binding_type = ? AND binding_id = ?", bindingType, bindingID).Delete(&StrategyBinding{}).Error
}

// GetInheritedStrategies 获取继承的策略列表（包括上级实体的策略）
func (m *StrategyBindingModel) GetInheritedStrategies(bindingType, bindingID string) ([]*Strategy, error) {
	var allStrategies []*Strategy
	strategyMap := make(map[int64]*Strategy) // 用于去重

	// 1. 获取直接绑定的策略
	directStrategies, err := m.GetStrategiesByBinding(bindingType, bindingID)
	if err != nil {
		return nil, err
	}
	for _, strategy := range directStrategies {
		strategyMap[strategy.ID] = strategy
	}

	// 2. 根据绑定类型获取继承的策略
	switch bindingType {
	case "promotion":
		// 广告继承项目的策略 - 这里需要外部传入项目ID
		// 由于循环导入的限制，这部分逻辑移到服务层处理
		break
	case "project":
		// 项目继承广告主的策略 - 这里需要外部传入广告主ID
		// 由于循环导入的限制，这部分逻辑移到服务层处理
		break
	}

	// 转换为切片
	for _, strategy := range strategyMap {
		allStrategies = append(allStrategies, strategy)
	}

	return allStrategies, nil
}

// CountAll 统计策略绑定总数
func (m *StrategyBindingModel) CountAll() (int, error) {
	var count int64
	err := m.DB.Model(&StrategyBinding{}).Count(&count).Error
	return int(count), err
}

// CountEnabled 统计启用的策略绑定数量
func (m *StrategyBindingModel) CountEnabled() (int, error) {
	var count int64
	err := m.DB.Model(&StrategyBinding{}).Where("enabled = ?", true).Count(&count).Error
	return int(count), err
}

// CountByStrategyID 统计指定策略的绑定数量
func (m *StrategyBindingModel) CountByStrategyID(strategyID int64) (int, error) {
	var count int64
	err := m.DB.Model(&StrategyBinding{}).Where("strategy_id = ? AND enabled = ?", strategyID, true).Count(&count).Error
	return int(count), err
}
