package model

import (
	"strings"
	"time"

	"gorm.io/gorm"
)

// DeletedComment 被删除的评论记录
type DeletedComment struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	CommentId     string    `json:"comment_id" gorm:"type:varchar(100);not null;comment:评论ID"`
	CommentText   string    `json:"comment_text" gorm:"type:text;not null;comment:评论内容"`
	SensitiveWord string    `json:"sensitive_word" gorm:"type:varchar(100);not null;comment:触发的敏感词"`
	AccountName   string    `json:"account_name" gorm:"type:varchar(100);comment:账户名称"`
	ProjectName   string    `json:"project_name" gorm:"type:varchar(100);comment:项目名称"`
	PromotionName string    `json:"promotion_name" gorm:"type:varchar(100);comment:推广计划名称"`
	DeleteTime    time.Time `json:"delete_time" gorm:"autoCreateTime;comment:删除时间"`
}

// TableName 表名
func (DeletedComment) TableName() string {
	return "deleted_comments"
}

// DeletedCommentModel 被删除评论模型操作
type DeletedCommentModel struct {
	DB *gorm.DB
}

// NewDeletedCommentModel 创建被删除评论模型
func NewDeletedCommentModel(db *gorm.DB) *DeletedCommentModel {
	return &DeletedCommentModel{DB: db}
}

// Create 创建删除记录
func (m *DeletedCommentModel) Create(comment *DeletedComment) error {
	return m.DB.Create(comment).Error
}

// List 获取删除记录列表
func (m *DeletedCommentModel) List(page, pageSize int, conditions map[string]interface{}) ([]*DeletedComment, int64, error) {
	var comments []*DeletedComment
	var total int64

	db := m.DB.Model(&DeletedComment{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "comment_text" || key == "account_name" || key == "project_name" || key == "promotion_name" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询，按删除时间倒序
	if err := db.Order("delete_time DESC").Offset((page - 1) * pageSize).Limit(pageSize).Find(&comments).Error; err != nil {
		return nil, 0, err
	}

	return comments, total, nil
}

// GetStat 获取删除记录统计信息
func (m *DeletedCommentModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var todayCount int64

	if err := m.DB.Model(&DeletedComment{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 获取今天的删除数量
	today := time.Now().Format("2006-01-02")
	if err := m.DB.Model(&DeletedComment{}).Where("DATE(delete_time) = ?", today).Count(&todayCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count": totalCount,
		"today_count": todayCount,
	}, nil
}

// CheckSensitiveWords 检查文本是否包含敏感词
func CheckSensitiveWords(text string, sensitiveWords string) []string {
	if sensitiveWords == "" {
		return nil
	}

	words := strings.Split(sensitiveWords, ",")
	var foundWords []string

	for _, word := range words {
		word = strings.TrimSpace(word)
		if word != "" && strings.Contains(text, word) {
			foundWords = append(foundWords, word)
		}
	}

	return foundWords
}
