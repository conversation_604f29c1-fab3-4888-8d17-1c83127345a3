package model

import (
	"time"

	"gorm.io/gorm"
)

// ClipTaskVideoGroup 视频分组结构体
type ClipTaskVideoGroup struct {
	VideoUrl      []string `json:"video_url"`       // 视频URL列表
	IsOriginSound bool     `json:"is_origin_sound"` // 是否保留原声
}

// ClipTask 混剪任务模型
type ClipTask struct {
	Id                int64                `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`                           // 数据库主键ID
	ClipTaskName      string               `gorm:"column:clip_task_name;comment:混剪任务名称" json:"clip_task_name"`                             // 混剪任务名称
	AspectRatio       string               `gorm:"column:aspect_ratio;comment:画面比例" json:"aspect_ratio"`                                   // 画面比例
	ScaleMode         string               `gorm:"column:scale_mode;comment:缩放模式" json:"scale_mode"`                                       // 缩放模式(WidthFix、HeightFix、ScaleToFill)
	MusicMixMode      int64                `gorm:"column:music_mix_mode;comment:音乐混合模式" json:"music_mix_mode"`                             // 音乐混合模式（1、bgm与视频原声混合，2、只要bgm）
	GenTotal          int64                `gorm:"column:gen_total;comment:可生成视频总数" json:"gen_total"`                                      // 可生成视频的总数
	RemainingGenCount int64                `gorm:"column:remaining_gen_count;comment:剩余可生成视频数量" json:"remaining_gen_count"`                // 剩余可生成视频的数量
	GenStatus         int64                `gorm:"column:gen_status;comment:生成状态" json:"gen_status"`                                       // 生成状态(1、暂未生成视频 2、已开始生成视频)
	MusicUrl          []string             `gorm:"column:music_url;type:json;serializer:json;comment:音乐URL列表" json:"music_url,omitempty"`  // 音乐URL列表
	HeadCover         []string             `gorm:"column:head_cover;type:json;serializer:json;comment:头部封面列表" json:"head_cover,omitempty"` // 头部封面列表
	TailCover         []string             `gorm:"column:tail_cover;type:json;serializer:json;comment:尾部封面列表" json:"tail_cover,omitempty"` // 尾部封面列表
	GroupList         []ClipTaskVideoGroup `gorm:"column:group_list;type:json;serializer:json;comment:视频分组列表" json:"group_list,omitempty"` // 视频分组列表
	CreatedAt         time.Time            `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`             // 创建时间
	UpdatedAt         time.Time            `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`             // 更新时间
}

// TableName 指定表名
func (ClipTask) TableName() string {
	return "clip_task"
}

// ClipTaskModel 混剪任务表模型
type ClipTaskModel struct {
	db *gorm.DB
}

// NewClipTaskModel 创建混剪任务模型
func NewClipTaskModel(db *gorm.DB) *ClipTaskModel {
	return &ClipTaskModel{
		db: db,
	}
}

// Create 创建新混剪任务
func (m *ClipTaskModel) Create(clipTask *ClipTask) error {
	return m.db.Create(clipTask).Error
}

// Update 更新混剪任务信息
func (m *ClipTaskModel) Update(tx *gorm.DB, clipTask *ClipTask) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Updates(clipTask).Error
}

// MapUpdate 更新混剪任务信息
func (m *ClipTaskModel) MapUpdate(tx *gorm.DB, id int64, clipTask map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&ClipTask{}).Where("id = ?", id).Updates(clipTask).Error
}

// Save 保存混剪任务信息
func (m *ClipTaskModel) Save(tx *gorm.DB, clipTask *ClipTask) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(clipTask).Error
}

// Delete 删除混剪任务
func (m *ClipTaskModel) Delete(clipTask *ClipTask) error {
	return m.db.Delete(clipTask).Error
}

// GetById 根据ID获取混剪任务
func (m *ClipTaskModel) GetById(id int64) (*ClipTask, error) {
	var clipTask ClipTask
	if err := m.db.First(&clipTask, id).Error; err != nil {
		return nil, err
	}
	return &clipTask, nil
}

// GetByClipTaskId 根据混剪任务ID获取混剪任务
func (m *ClipTaskModel) GetByClipTaskId(clipTaskId string) (*ClipTask, error) {
	var clipTask ClipTask
	if err := m.db.Where("clip_task_id = ?", clipTaskId).First(&clipTask).Error; err != nil {
		return nil, err
	}
	return &clipTask, nil
}

// List 获取混剪任务列表
func (m *ClipTaskModel) List(page, pageSize int, conditions map[string]interface{}) ([]*ClipTask, int64, error) {
	var clipTasks []*ClipTask
	var total int64

	db := m.db.Model(&ClipTask{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "clip_task_name" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}
	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 按创建时间倒序排列
	if err := db.Order("created_at DESC").Find(&clipTasks).Error; err != nil {
		return nil, 0, err
	}
	if page <= 0 || pageSize <= 0 {
		total = int64(len(clipTasks))
	}
	return clipTasks, total, nil
}

// GetStat 获取混剪任务统计信息
func (m *ClipTaskModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var readyCount int64
	var generatingCount int64

	if err := m.db.Model(&ClipTask{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&ClipTask{}).Where("gen_status = ?", 1).Count(&readyCount).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&ClipTask{}).Where("gen_status = ?", 2).Count(&generatingCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":      totalCount,
		"ready_count":      readyCount,
		"generating_count": generatingCount,
	}, nil
}

// BatchDelete 批量删除混剪任务
func (m *ClipTaskModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&ClipTask{}).Error
}

// BatchCreate 批量创建混剪任务
func (m *ClipTaskModel) BatchCreate(tx *gorm.DB, clipTasks []*ClipTask) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.CreateInBatches(clipTasks, 500).Error
}

// GetByUserId 根据用户ID获取混剪任务列表
func (m *ClipTaskModel) GetByUserId(userId int64) ([]*ClipTask, error) {
	var clipTasks []*ClipTask
	if err := m.db.Where("user_id = ?", userId).Order("created_at DESC").Find(&clipTasks).Error; err != nil {
		return nil, err
	}
	return clipTasks, nil
}

// GetByCompanyId 根据公司ID获取混剪任务列表
func (m *ClipTaskModel) GetByCompanyId(companyId int64) ([]*ClipTask, error) {
	var clipTasks []*ClipTask
	if err := m.db.Where("company_id = ?", companyId).Order("created_at DESC").Find(&clipTasks).Error; err != nil {
		return nil, err
	}
	return clipTasks, nil
}

// UpdateGenStatus 更新生成状态
func (m *ClipTaskModel) UpdateGenStatus(id int64, status int64) error {
	return m.db.Model(&ClipTask{}).Where("id = ?", id).Update("gen_status", status).Error
}

// UpdateRemainingGenCount 更新剩余生成数量
func (m *ClipTaskModel) UpdateRemainingGenCount(id int64, count int64) error {
	return m.db.Model(&ClipTask{}).Where("id = ?", id).Update("remaining_gen_count", count).Error
}
