package model

import (
	"time"

	"gorm.io/gorm"
)

// Project 项目模型
type Project struct {
	Id        int64 `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"` // 数据库主键ID
	AccountId int64 `gorm:"column:account_id;comment:关联账户ID" json:"account_id"`           // 关联账户ID

	ProjectId                  string                      `gorm:"column:project_id;comment:项目ID" json:"project_id"`                                                                // 项目ID
	ProjectName                string                      `gorm:"column:project_name;comment:项目名称" json:"project_name"`                                                            // 项目名称
	LandingType                int                         `gorm:"column:landing_type;comment:项目落地页类型" json:"landing_type"`                                                         // 项目落地页类型
	LandingTypeName            string                      `gorm:"column:landing_type_name;comment:项目落地页类型名称" json:"landing_type_name"`                                             // 项目落地页类型名称
	ProjectStatus              int                         `gorm:"column:project_status;comment:项目状态" json:"project_status"`                                                        // 项目状态
	ProjectStatusName          string                      `gorm:"column:project_status_name;comment:项目状态名称" json:"project_status_name"`                                            // 项目状态名称
	DeliveryMode               int                         `gorm:"column:delivery_mode;comment:项目投放模式" json:"delivery_mode"`                                                        // 项目投放模式  1 手动投放  3 自动投放
	CampaignBudget             string                      `gorm:"column:campaign_budget;comment:项目推广活动预算" json:"campaign_budget"`                                                  // 项目推广活动预算
	StartTime                  string                      `gorm:"column:start_time;comment:项目开始时间" json:"start_time"`                                                              // 项目开始时间
	EndTime                    string                      `gorm:"column:end_time;comment:项目结束时间" json:"end_time"`                                                                  // 项目结束时间
	DownloadTypeName           string                      `gorm:"column:download_type_name;comment:项目下载类型名称" json:"download_type_name"`                                            // 项目下载类型名称
	ExternalAction             int                         `gorm:"column:external_action;comment:项目外部行为" json:"external_action"`                                                    // 项目外部行为
	ExternalActionName         string                      `gorm:"column:external_action_name;comment:项目外部行为名称" json:"external_action_name"`                                        // 项目外部行为名称
	ActionTrackUrl             string                      `gorm:"column:action_track_url;comment:项目行为跟踪URL" json:"action_track_url"`                                               // 项目行为跟踪URL
	CampaignId                 int64                       `gorm:"column:campaign_id;comment:项目推广活动ID" json:"campaign_id"`                                                          // 项目推广活动ID
	CampaignOptStatus          int                         `gorm:"column:campaign_opt_status;comment:项目推广活动优化状态" json:"campaign_opt_status"`                                        // 项目推广活动优化状态
	DeepExternalActionName     string                      `gorm:"column:deep_external_action_name;comment:项目深度外部行为名称" json:"deep_external_action_name"`                            // 项目深度外部行为名称
	DeepBidType                int                         `gorm:"column:deep_bid_type;comment:项目深度出价类型" json:"deep_bid_type"`                                                      // 项目深度出价类型
	DeepBidTypeName            string                      `gorm:"column:deep_bid_type_name;comment:项目深度出价类型名称" json:"deep_bid_type_name,omitempty"`                                // 项目深度出价类型名称
	AdvertiserId               int64                       `gorm:"column:advertiser_id;comment:广告主ID" json:"advertiser_id"`                                                         // 广告主ID
	ModifyTime                 string                      `gorm:"column:modify_time;comment:项目修改时间" json:"modify_time"`                                                            // 项目修改时间
	MarketingInfo              GetProjectMarketingInfo     `gorm:"column:marketing_info;type:json;serializer:json;comment:项目营销信息" json:"marketing_info"`                            // 项目营销信息
	PromotionStrategy          GetProjectPromotionStrategy `gorm:"column:promotion_strategy;type:json;serializer:json;comment:项目推广策略" json:"promotion_strategy"`                    // 项目推广策略
	AssetName                  string                      `gorm:"column:asset_name;comment:项目资产名称" json:"asset_name,omitempty"`                                                    // 项目资产名称
	AssetType                  int                         `gorm:"column:asset_type;comment:项目资产类型" json:"asset_type"`                                                              // 项目资产类型
	AssetTypeName              string                      `gorm:"column:asset_type_name;comment:项目资产类型名称" json:"asset_type_name"`                                                  // 项目资产类型名称
	DeliverySceneName          string                      `gorm:"column:delivery_scene_name;comment:项目投放场景名称" json:"delivery_scene_name"`                                          // 项目投放场景名称
	ProjectBid                 string                      `gorm:"column:project_bid;comment:项目出价" json:"project_bid"`                                                              // 项目出价
	ProjectDeepCpaBid          string                      `gorm:"column:project_deep_cpa_bid;comment:项目深度CPA出价" json:"project_deep_cpa_bid"`                                       // 项目深度CPA出价
	ProjectRoiGoal             float64                     `gorm:"column:project_roi_goal;comment:项目ROI目标" json:"project_roi_goal,omitempty"`                                       // 项目ROI目标
	AdPricing                  int                         `gorm:"column:ad_pricing;comment:项目广告定价方式" json:"ad_pricing"`                                                            // 项目广告定价方式
	AdPricingName              string                      `gorm:"column:ad_pricing_name;comment:项目广告定价方式名称" json:"ad_pricing_name"`                                                // 项目广告定价方式名称
	FeedDeliverySearch         int                         `gorm:"column:feed_delivery_search;comment:项目信息流投放搜索" json:"feed_delivery_search,omitempty"`                             // 项目信息流投放搜索
	ProjectAggregateModifyTime string                      `gorm:"column:project_aggregate_modify_time;comment:项目聚合修改时间" json:"project_aggregate_modify_time"`                      // 项目聚合修改时间
	ProjectStatusFirst         int                         `gorm:"column:project_status_first;comment:项目状态第一级" json:"project_status_first"`                                         // 项目状态第一级
	ProjectStatusSecond        []int                       `gorm:"column:project_status_second;type:json;serializer:json;comment:项目状态第二级" json:"project_status_second"`             // 项目状态第二级
	ProjectStatusFirstName     string                      `gorm:"column:project_status_first_name;comment:项目状态第一级名称" json:"project_status_first_name"`                             // 项目状态第一级名称
	ProjectStatusSecondName    []string                    `gorm:"column:project_status_second_name;type:json;serializer:json;comment:项目状态第二级名称" json:"project_status_second_name"` // 项目状态第二级名称
	DeliveryPackage            int                         `gorm:"column:delivery_package;comment:项目投放包" json:"delivery_package"`                                                   // 项目投放包
	DeliveryModeInternal       int                         `gorm:"column:delivery_mode_internal;comment:项目内部投放模式" json:"delivery_mode_internal"`                                    // 项目内部投放模式   1 手动投放  3 自动投放
	ProjectFirstRoiGoal        int                         `gorm:"column:project_first_roi_goal;comment:项目第一ROI目标" json:"project_first_roi_goal,omitempty"`                         // 项目第一ROI目标
	AdvertiserName             string                      `gorm:"column:advertiser_name;comment:广告主名称" json:"advertiser_name"`                                                     // 广告主名称
	AccountRole                int                         `gorm:"column:account_role;comment:账户角色" json:"account_role"`                                                            // 账户角色
	SmartBidType               int                         `gorm:"column:smart_bid_type;comment:项目智能出价类型" json:"smart_bid_type"`                                                    // 项目智能出价类型
	ShopMultiRoiGoals          GetProjectShopMultiRoiGoals `gorm:"column:shop_multi_roi_goals;type:json;serializer:json;comment:项目店铺多ROI目标" json:"shop_multi_roi_goals"`            // 项目店铺多ROI目标
	DeliveryProduct            int                         `gorm:"column:delivery_product;comment:项目投放产品" json:"delivery_product"`                                                  // 项目投放产品
	DeliveryMedium             int                         `gorm:"column:delivery_medium;comment:项目投放媒介" json:"delivery_medium"`                                                    // 项目投放媒介
	DeliveryMediumName         string                      `gorm:"column:delivery_medium_name;comment:项目投放媒介名称" json:"delivery_medium_name"`                                        // 项目投放媒介名称
	ClickCnt                   string                      `gorm:"column:click_cnt;comment:项目点击次数" json:"click_cnt"`                                                                // 项目点击次数
	Ctr                        string                      `gorm:"column:ctr;comment:项目点击率" json:"ctr"`                                                                             // 项目点击率
	ShowCnt                    string                      `gorm:"column:show_cnt;comment:项目展示次数" json:"show_cnt"`                                                                  // 项目展示次数
	CpmPlatform                string                      `gorm:"column:cpm_platform;comment:项目千次展示成本" json:"cpm_platform"`                                                        // 项目千次展示成本
	StatCost                   string                      `gorm:"column:stat_cost;comment:项目统计消耗" json:"stat_cost"`                                                                // 项目统计消耗
	AutoExtend                 GetProjectAutoExtend        `gorm:"column:auto_extend;type:json;serializer:json;comment:项目自动延期" json:"auto_extend,omitempty"`                        // 项目自动延期
	DeliveryProductName        string                      `gorm:"column:delivery_product_name;comment:项目投放产品名称" json:"delivery_product_name,omitempty"`                            // 项目投放产品名称
	PromotionObject            GetProjectPromotionObject   `gorm:"column:promotion_object;type:json;serializer:json;comment:项目推广对象" json:"promotion_object,omitempty"`              // 项目推广对象
	ConversionCost             string                      `gorm:"column:conversion_cost;comment:项目转化成本" json:"conversion_cost"`                                                    // 项目转化成本
	ConversionRate             string                      `gorm:"column:conversion_rate;comment:项目转化率" json:"conversion_rate"`                                                     // 项目转化率
	ConvertCnt                 string                      `gorm:"column:convert_cnt;comment:项目转化次数" json:"convert_cnt"`                                                            // 项目转化次数
	CreateTime                 time.Time                   `gorm:"column:create_time;comment:创建时间" json:"create_time"`                                                              // 创建时间
	UpdateTime                 time.Time                   `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                    // 更新时间
	DeleteTime                 int64                       `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                                                              // 删除时间（Unix时间戳）
	StrategyIds                []int64                     `gorm:"-" json:"strategy_ids"`                                                                                           // 策略ID列表（非数据库字段）
	StrategiesId               string                      `gorm:"column:strategies_id;comment:关联的策略ID" json:"strategies_id"`                                                       // 关联的策略ID（逗号分隔）
}

// TableName 指定表名
func (Project) TableName() string {
	return "project"
}

// ProjectModel 项目表模型
type ProjectModel struct {
	db *gorm.DB
}

// NewProjectModel 创建项目模型
func NewProjectModel(db *gorm.DB) *ProjectModel {
	return &ProjectModel{
		db: db,
	}
}

// Create 创建新项目
func (m *ProjectModel) Create(project *Project) error {
	return m.db.Create(project).Error
}

// Update 更新项目信息
func (m *ProjectModel) Update(tx *gorm.DB, project *Project) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Select("*").Updates(project).Error
}

// MapUpdate 更新项目信息
func (m *ProjectModel) MapUpdate(tx *gorm.DB, id int64, project map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&Project{}).Where("id = ?", id).Updates(project).Error
}

// Save 保存项目信息
func (m *ProjectModel) Save(tx *gorm.DB, project *Project) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(project).Error
}

// Delete 删除项目
func (m *ProjectModel) Delete(project *Project) error {
	return m.db.Delete(project).Error
}

// GetById 根据ID获取项目
func (m *ProjectModel) GetById(id int64) (*Project, error) {
	var project Project
	if err := m.db.First(&project, id).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

// GetByProjectId 根据项目ID获取项目
func (m *ProjectModel) GetByProjectId(projectId string) (*Project, error) {
	var project Project
	if err := m.db.Where("project_id = ?", projectId).First(&project).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

// List 获取项目列表
func (m *ProjectModel) List(page, pageSize int, conditions map[string]interface{}) ([]*Project, int64, error) {
	var projects []*Project
	var total int64

	db := m.db.Model(&Project{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "project_name" || key == "remark" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}
	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 分页查询
	if err := db.Find(&projects).Error; err != nil {
		return nil, 0, err
	}
	if page > 0 && pageSize > 0 {
		total = int64(len(projects))
	}
	return projects, total, nil
}

// GetStat 获取项目统计信息
func (m *ProjectModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var activeCount int64

	if err := m.db.Model(&Project{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&Project{}).Where("status = ?", "active").Count(&activeCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":  totalCount,
		"active_count": activeCount,
	}, nil
}

// BatchDelete 批量删除项目
func (m *ProjectModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&Project{}).Error
}

// BatchCreate 批量创建项目
func (m *ProjectModel) BatchCreate(tx *gorm.DB, projects []*Project) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.CreateInBatches(projects, 500).Error
}

// BatchMarkDelete 批量标记删除项目
func (m *ProjectModel) BatchMarkDelete(projectIds []string) error {
	return m.db.Model(&Project{}).Where("project_id IN ?", projectIds).Update("delete_time", time.Now().Unix()).Error
}

// GetProjectIdByAccountId 根据账户ID获取项目ID列表
func (m *ProjectModel) GetProjectIdByAccountId(accountId int64) ([]*Project, error) {
	var projects []*Project
	if err := m.db.Select("id, project_id, create_time, delete_time").Where("account_id = ?", accountId).Find(&projects).Error; err != nil {
		return nil, err
	}
	return projects, nil
}
