package model

import "time"

type ProjectTemplate struct {
	Id                        int         `json:"id"`                           // id
	Name                      string      `json:"name"`                         // 模板名称
	LandingType               string      `json:"landing_type"`                 // 落地页类型 LINK 销售线索
	AdType                    string      `json:"ad_type"`                      // 广告类型
	DeliveryMode              string      `json:"delivery_mode"`                // 投放模式 MANUAL 手动投放
	DeliveryType              string      `json:"delivery_type"`                // 投放类型 NORMAL 通投广告
	MarketingScenarios        string      `json:"marketing_scenarios"`          // 营销场景
	PromotionType             string      `json:"promotion_type"`               // 推广类型
	AssetType                 string      `json:"asset_type"`                   // 资产类型
	OptimizationObjectives    string      `json:"optimization_objectives"`      // 优化目标
	ExternalUrls              []string    `json:"external_urls"`                // 外部链接
	DirectLink                string      `json:"direct_link"`                  // 直接链接
	ActiveContactLink         string      `json:"active_contact_link"`          // 主动联系链接
	IntelligentPayback        interface{} `json:"intelligent_payback"`          // 智能回款
	Gender                    string      `json:"gender"`                       // 性别
	FilterFans                string      `json:"filter_fans"`                  // 粉丝过滤
	FilterAwemeAbnormalActive string      `json:"filter_aweme_abnormal_active"` // 抖音异常活跃
	FilterAwemeFansCount      int         `json:"filter_aweme_fans_count"`      // 抖音粉丝数
	District                  string      `json:"district"`                     // 地区
	City                      interface{} `json:"city"`                         // 城市
	LocationType              string      `json:"location_type"`                // 定位类型
	Age                       interface{} `json:"age"`                          // 年龄
	RetargetingTagsInclude    interface{} `json:"retargeting_tags_include"`     // 受众标签包含
	RetargetingTagsExclude    interface{} `json:"retargeting_tags_exclude"`     // 受众标签排除
	HideIfConverted           string      `json:"hide_if_converted"`            // 转化后隐藏
	ConvertedTimeDuration     string      `json:"converted_time_duration"`      // 转化时间
	BiddingStrategy           string      `json:"bidding_strategy"`             // 竞价策略
	BidCoefficient            float64     `json:"bid_coefficient"`              // 竞价系数
	ProductSetting            string      `json:"product_setting"`              // 产品设置
	UniqueProductId           string      `json:"unique_product_id"`            // 唯一产品ID
	AudienceExtend            string      `json:"audience_extend"`              // 受众扩展
	AnchorRelatedType         string      `json:"anchor_related_type"`          // 主播相关类型
	StopMethod                int         `json:"stop_method"`                  // 停止方式
	ProductBuffer             int         `json:"product_buffer"`               // 产品缓冲
	Brand                     string      `json:"brand"`                        // 品牌
	ProductName               string      `json:"product_name"`                 // 产品名称
	ProductImages             []struct {
		URL string `json:"URL"` // 图片URL
		MD5 string `json:"MD5"` // 图片MD5
	} `json:"product_images"` // 产品图片
	ProductSellingPoints []string  `json:"product_selling_points"` // 产品卖点
	CallToActionButtons  []string  `json:"call_to_action_buttons"` // 行动号召按钮
	Budget               int       `json:"budget"`                 // 预算
	BidMode              int       `json:"bid_mode"`               // 竞价模式
	CpaBid               float64   `json:"cpa_bid"`                // CPA出价
	MaxCpaBid            int       `json:"max_cpa_bid"`            // 最高Cpa出价
	Increase             int       `json:"increase"`               // 增量
	ProjectBudget        int       `json:"project_budget"`         // 项目预算
	ProCpaBid            int       `json:"pro_cpa_bid"`            // 	// 项目Cpa出价
	Source               string    `json:"source"`                 // 来源
	Title                []string  `json:"title"`                  // 标题
	FirstPause           int       `json:"first_pause"`            // 首次暂停
	SecondPause          int       `json:"second_pause"`           // 二次暂停
	ThirdPause           int       `json:"third_pause"`            // 三次暂停
	CompanyId            int       `json:"company_id"`             // 公司ID
	CreatedAt            time.Time `json:"created_at"`             // 创建时间
	UpdatedAt            time.Time `json:"updated_at"`             // 更新时间
	Username             string    `json:"username"`               // 用户名
	VideoHpVisibility    string    `json:"video_hp_visibility"`    // 视频隐私设置
}

type T struct {
	TrackUrlGroupInfo struct {
	} `json:"track_url_group_info"`
	TrackUrl                  []interface{} `json:"track_url"`
	ActionTrackUrl            []interface{} `json:"action_track_url"`
	FirstFrame                []interface{} `json:"first_frame"`
	LastFrame                 []interface{} `json:"last_frame"`
	EffectiveFrame            []interface{} `json:"effective_frame"`
	TrackUrlSendType          string        `json:"track_url_send_type"` //
	SmartBidType              int           `json:"smart_bid_type"`
	IsSearchSpeedPhaseFour    bool          `json:"is_search_speed_phase_four"`
	DeliveryScene             int           `json:"delivery_scene"`
	Budget                    int           `json:"budget"`            //总预算
	Bid                       float64       `json:"bid"`               //出价 0.01
	InventoryCatalog          int           `json:"inventory_catalog"` //t 投放目录 3
	FlowControlMode           int           `json:"flow_control_mode"` //t 流量控制模式 0
	DeliveryMode              int           `json:"delivery_mode"`
	DeliveryPackage           int           `json:"delivery_package"`
	LandingType               int           `json:"landing_type"`
	DeliveryRelatedNum        int           `json:"delivery_related_num"`
	Name                      string        `json:"name"` //项目名称
	ScheduleType              int           `json:"schedule_type"`
	WeekScheduleType          int           `json:"week_schedule_type"`
	PricingType               int           `json:"pricing_type"`
	District                  string        `json:"district"`
	Gender                    string        `json:"gender"`
	Age                       [][]string    `json:"age"`
	Ac                        []string      `json:"ac"`
	HideIfConverted           string        `json:"hide_if_converted"`
	LaunchPriceSelect         string        `json:"launch_price_select"`
	LaunchPrice               []interface{} `json:"launch_price"`
	CdpMarketingGoal          int           `json:"cdp_marketing_goal"`
	StartTime                 string        `json:"start_time"` //投放时间 1747584000
	ExternalAction            string        `json:"external_action"`
	BudgetMode                int           `json:"budget_mode"`
	CampaignType              int           `json:"campaign_type"`
	AssetType                 int           `json:"asset_type"`
	DeliveryProduct           int           `json:"delivery_product"`
	MultiAssetSwitch          int           `json:"multi_asset_switch"`
	ClueAcquisitionMethod     int           `json:"clue_acquisition_method"`
	MultiAssetTypes           []int         `json:"multi_asset_types"`
	AutoAdType                int           `json:"auto_ad_type"`
	FilterAwemeAbnormalActive string        `json:"filter_aweme_abnormal_active"`
	FilterOwnAwemeFans        string        `json:"filter_own_aweme_fans"`
	FilterAwemeFansCount      string        `json:"filter_aweme_fans_count"`
	Products                  []struct {
		ProductPlatformId string `json:"ProductPlatformId"`
		ProductId         string `json:"ProductId"`
		UniqueProductId   string `json:"UniqueProductId"`
	} `json:"products"`
	AudienceSupportKeys []string `json:"audience_support_keys"`
	DeliveryDuration    string   `json:"delivery_duration"`    //投放周期 （天）
	KeepDeliverySwitch  int      `json:"keep_delivery_switch"` // 续投开关 1开启
	IsSearch3Online     bool     `json:"is_search_3_online"`
}
type (
	GetProjectDataList struct {
		ProjectId                  string                      `json:"project_id"`
		ProjectName                string                      `json:"project_name"`
		LandingType                int                         `json:"landing_type"`
		LandingTypeName            string                      `json:"landing_type_name"`
		ProjectStatus              int                         `json:"project_status"`
		ProjectStatusName          string                      `json:"project_status_name"`
		DeliveryMode               int                         `json:"delivery_mode"`
		CampaignBudget             string                      `json:"campaign_budget"`
		StartTime                  string                      `json:"start_time"`
		EndTime                    string                      `json:"end_time"`
		DownloadTypeName           string                      `json:"download_type_name"`
		ExternalAction             int                         `json:"external_action"`
		ExternalActionName         string                      `json:"external_action_name"`
		ActionTrackUrl             string                      `json:"action_track_url"`
		CampaignId                 int64                       `json:"campaign_id"`
		CampaignOptStatus          int                         `json:"campaign_opt_status"`
		DeepExternalActionName     string                      `json:"deep_external_action_name"`
		DeepBidType                int                         `json:"deep_bid_type"`
		DeepBidTypeName            string                      `json:"deep_bid_type_name,omitempty"`
		AdvertiserId               int64                       `json:"advertiser_id"`
		CreateTime                 string                      `json:"create_time"`
		ModifyTime                 string                      `json:"modify_time"`
		MarketingInfo              GetProjectMarketingInfo     `json:"marketing_info"`
		PromotionStrategy          GetProjectPromotionStrategy `json:"promotion_strategy"`
		AssetName                  string                      `json:"asset_name,omitempty"`
		AssetType                  int                         `json:"asset_type"`
		AssetTypeName              string                      `json:"asset_type_name"`
		DeliverySceneName          string                      `json:"delivery_scene_name"`
		ProjectBid                 string                      `json:"project_bid"`
		ProjectDeepCpaBid          string                      `json:"project_deep_cpa_bid"`
		ProjectRoiGoal             int                         `json:"project_roi_goal,omitempty"`
		AdPricing                  int                         `json:"ad_pricing"`
		AdPricingName              string                      `json:"ad_pricing_name"`
		FeedDeliverySearch         int                         `json:"feed_delivery_search,omitempty"`
		ProjectAggregateModifyTime string                      `json:"project_aggregate_modify_time"`
		ProjectStatusFirst         int                         `json:"project_status_first"`
		ProjectStatusSecond        []int                       `json:"project_status_second"`
		ProjectStatusFirstName     string                      `json:"project_status_first_name"`
		ProjectStatusSecondName    []string                    `json:"project_status_second_name"`
		DeliveryPackage            int                         `json:"delivery_package"`
		DeliveryModeInternal       int                         `json:"delivery_mode_internal"`
		ProjectFirstRoiGoal        int                         `json:"project_first_roi_goal,omitempty"`
		AdvertiserName             string                      `json:"advertiser_name"`
		AccountRole                int                         `json:"account_role"`
		SmartBidType               int                         `json:"smart_bid_type"`
		ShopMultiRoiGoals          GetProjectShopMultiRoiGoals `json:"shop_multi_roi_goals"`
		DeliveryProduct            int                         `json:"delivery_product"`
		DeliveryMedium             int                         `json:"delivery_medium"`
		DeliveryMediumName         string                      `json:"delivery_medium_name"`
		ClickCnt                   string                      `json:"click_cnt"`
		Ctr                        string                      `json:"ctr"`
		ShowCnt                    string                      `json:"show_cnt"`
		CpmPlatform                string                      `json:"cpm_platform"`
		StatCost                   string                      `json:"stat_cost"`
		AutoExtend                 GetProjectAutoExtend        `json:"auto_extend,omitempty"`
		DeliveryProductName        string                      `json:"delivery_product_name,omitempty"`
		PromotionObject            GetProjectPromotionObject   `json:"promotion_object,omitempty"`
		ConversionCost             string                      `json:"conversion_cost"`
		ConversionRate             string                      `json:"conversion_rate"`
		ConvertCnt                 string                      `json:"convert_cnt"`
	}

	GetProjectMarketingInfo struct {
		MarketingGoal      int `json:"marketingGoal"`
		DeliveryRelatedNum int `json:"deliveryRelatedNum"`
		AutoAdType         int `json:"autoAdType,omitempty"`
	}

	GetProjectPromotionStrategy struct {
	}

	GetProjectShopMultiRoiGoals struct {
	}

	GetProjectAutoExtend struct {
	}

	GetProjectPromotionObject struct {
		ProductPlatformId int64  `json:"productPlatformId"`
		ProductId         string `json:"productId"`
		PromotionType     int    `json:"promotionType"`
		UniqueProductId   string `json:"uniqueProductId"`
	}

	GetProjectMetrics struct {
		CpmPlatform string `json:"cpm_platform"`
		StatCost    string `json:"stat_cost"`
		ClickCnt    string `json:"click_cnt"`
		Ctr         string `json:"ctr"`
		ShowCnt     string `json:"show_cnt"`
	}

	GetProjectPagination struct {
		Page    int  `json:"page"`
		Limit   int  `json:"limit"`
		Total   int  `json:"total"`
		HasMore bool `json:"hasMore"`
	}

	GetProjectExtra struct {
	}
)
