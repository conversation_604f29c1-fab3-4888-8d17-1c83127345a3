package model

import (
	"time"

	"gorm.io/gorm"
)

// Config 系统配置模型
type Config struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	Key         string    `json:"key" gorm:"type:varchar(100);not null;uniqueIndex;comment:配置键"`
	Value       string    `json:"value" gorm:"type:text;comment:配置值"`
	Description string    `json:"description" gorm:"type:varchar(255);comment:配置描述"`
	Category    string    `json:"category" gorm:"type:varchar(50);default:'system';comment:配置分类"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;comment:是否为系统配置"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 表名
func (Config) TableName() string {
	return "configs"
}

// ConfigModel 配置模型操作
type ConfigModel struct {
	DB *gorm.DB
}

// NewConfigModel 创建配置模型
func NewConfigModel(db *gorm.DB) *ConfigModel {
	return &ConfigModel{DB: db}
}

// Create 创建配置
func (m *ConfigModel) Create(config *Config) error {
	return m.DB.Create(config).Error
}

// GetByKey 根据键获取配置
func (m *ConfigModel) GetByKey(key string) (*Config, error) {
	var config Config
	err := m.DB.Where("key = ?", key).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetValueByKey 根据键获取配置值
func (m *ConfigModel) GetValueByKey(key string) (string, error) {
	var config Config
	err := m.DB.Where("key = ?", key).First(&config).Error
	if err != nil {
		return "", err
	}
	return config.Value, nil
}

// Update 更新配置
func (m *ConfigModel) Update(tx *gorm.DB, config *Config) error {
	db := m.DB
	if tx != nil {
		db = tx
	}
	return db.Save(config).Error
}

// UpdateByKey 根据键更新配置值
func (m *ConfigModel) UpdateByKey(key, value string) error {
	return m.DB.Model(&Config{}).Where("key = ?", key).Update("value", value).Error
}

// UpsertByKey 根据键插入或更新配置
func (m *ConfigModel) UpsertByKey(key, value, description string) error {
	var config Config
	err := m.DB.Where("key = ?", key).First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新配置
			config = Config{
				Key:         key,
				Value:       value,
				Description: description,
				Category:    "system",
				IsSystem:    false,
			}
			return m.DB.Create(&config).Error
		}
		return err
	}

	// 更新现有配置
	config.Value = value
	if description != "" {
		config.Description = description
	}
	return m.DB.Save(&config).Error
}

// Delete 删除配置
func (m *ConfigModel) Delete(config *Config) error {
	return m.DB.Delete(config).Error
}

// DeleteByKey 根据键删除配置
func (m *ConfigModel) DeleteByKey(key string) error {
	return m.DB.Where("key = ?", key).Delete(&Config{}).Error
}

// List 获取配置列表
func (m *ConfigModel) List(page, pageSize int, conditions map[string]interface{}) ([]*Config, int64, error) {
	var configs []*Config
	var total int64

	query := m.DB.Model(&Config{})

	// 应用查询条件
	for key, value := range conditions {
		switch key {
		case "key":
			if value != nil {
				if keyStr, ok := value.(string); ok {
					query = query.Where("key LIKE ?", "%"+keyStr+"%")
				}
			}
		case "category":
			query = query.Where("category = ?", value)
		case "is_system":
			query = query.Where("is_system = ?", value)
		default:
			query = query.Where(key+" = ?", value)
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&configs).Error; err != nil {
		return nil, 0, err
	}

	return configs, total, nil
}

// GetByCategory 根据分类获取配置
func (m *ConfigModel) GetByCategory(category string) ([]*Config, error) {
	var configs []*Config
	err := m.DB.Where("category = ?", category).Find(&configs).Error
	return configs, err
}

// GetAll 获取所有配置
func (m *ConfigModel) GetAll() ([]*Config, error) {
	var configs []*Config
	err := m.DB.Find(&configs).Error
	return configs, err
}

// 预定义的配置键常量
const (
	ConfigKeyStrategyNotificationEmail = "strategy_notification_email"
	ConfigKeySystemEmail               = "system_email"
	ConfigKeyWechatWebhookURL          = "wechat_webhook_url"
	ConfigKeySystemName                = "system_name"
	ConfigKeySystemVersion             = "system_version"
)
