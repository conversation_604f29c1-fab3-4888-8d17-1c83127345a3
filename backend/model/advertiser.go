package model

import (
	"time"

	"gorm.io/gorm"
)

// Advertiser 广告主模型
type Advertiser struct {
	Id                             int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`                                         // 数据库主键ID
	AccountId                      int64     `gorm:"column:account_id;comment:关联账户ID" json:"account_id"`                                                   // 关联账户ID
	AdvertiserId                   int64     `gorm:"column:advertiser_id;comment:广告主ID" json:"advertiser_id"`                                              // 广告主ID
	AdvertiserName                 string    `gorm:"column:advertiser_name;comment:广告主名称" json:"advertiser_name"`                                          // 广告主名称
	AdvertiserRole                 int       `gorm:"column:advertiser_role;comment:广告主角色" json:"advertiser_role"`                                          // 广告主角色
	AdvertiserStatus               int       `gorm:"column:advertiser_status;comment:广告主状态" json:"advertiser_status"`                                      // 广告主状态
	GroupName                      string    `gorm:"column:group_name;comment:广告主分组" json:"group_name"`                                                    // 广告主分组
	AdvertiserBalance              string    `gorm:"column:advertiser_balance;comment:广告主账户余额" json:"advertiser_balance"`                                  // 广告主账户余额
	AdvertiserValidBalance         string    `gorm:"column:advertiser_valid_balance;comment:广告主有效余额" json:"advertiser_valid_balance"`                      // 广告主有效余额
	AdvertiserGrantBalanceValid    string    `gorm:"column:advertiser_grant_balance_valid;comment:广告主赠送余额" json:"advertiser_grant_balance_valid"`          // 广告主赠送余额
	AdvertiserNotGrantBalanceValid string    `gorm:"column:advertiser_not_grant_balance_valid;comment:广告主非赠送余额" json:"advertiser_not_grant_balance_valid"` // 广告主非赠送余额
	AdvertiserFollowed             bool      `gorm:"column:advertiser_followed;comment:广告主关注状态" json:"advertiser_followed"`                                // 广告主关注状态
	GroupId                        int64     `gorm:"column:group_id;comment:广告主分组ID" json:"group_id"`                                                      // 广告主分组ID
	AdvertiserRemark               string    `gorm:"column:advertiser_remark;comment:广告主备注" json:"advertiser_remark"`                                      // 广告主备注
	AccountRole                    int       `gorm:"column:account_role;comment:账户角色" json:"account_role"`                                                 // 账户角色
	AdvertiserStatusName           string    `gorm:"column:advertiser_status_name;comment:广告主状态名称" json:"advertiser_status_name"`                          // 广告主状态名称
	AdvertiserRoleName             string    `gorm:"column:advertiser_role_name;comment:广告主角色名称" json:"advertiser_role_name"`                              // 广告主角色名称
	CpmPlatform                    string    `gorm:"column:cmp_platform;comment:广告主千次展现成本" json:"cpm_platform"`                                            // 广告主千次展现成本
	ClickCnt                       string    `gorm:"column:click_cnt;comment:广告主点击数" json:"click_cnt"`                                                     // 广告主点击数
	Ctr                            string    `gorm:"column:ctr;comment:广告主点击率" json:"ctr"`                                                                 // 广告主点击率
	StatCost                       string    `gorm:"column:stat_cost;comment:广告主消耗" json:"stat_cost"`                                                      // 广告主消耗
	ShowCnt                        string    `gorm:"column:show_cnt;comment:广告主展示量" json:"show_cnt"`                                                       // 广告主展示量
	ConversionCost                 string    `gorm:"column:conversion_cost;comment:广告主转化成本" json:"conversion_cost"`                                        // 广告主转化成本
	ConversionRate                 string    `gorm:"column:conversion_rate;comment:广告主转化率" json:"conversion_rate"`                                         // 广告主转化率
	ConvertCnt                     string    `gorm:"column:convert_cnt;comment:广告主转化数" json:"convert_cnt"`                                                 // 广告主转化数
	AutoDeleteComment              int       `gorm:"column:auto_delete_comment;default:0;comment:广告主自动删评开关" json:"auto_delete_comment"`                    // 广告主自动删评开关 0-未开启 1-开启
	CreateTime                     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                         // 创建时间
	UpdateTime                     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                         // 更新时间
	DeleteTime                     int64     `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                                                   // 删除时间（Unix时间戳）
	StrategyIds                    []int64   `gorm:"-" json:"strategy_ids"`                                                                                // 策略ID列表（非数据库字段）

	IsFollowed bool `gorm:"column:is_followed;comment:广告主关注标记" json:"is_followed"` // 广告主关注标记（非巨量字段）
}

// TableName 指定表名
func (Advertiser) TableName() string {
	return "advertiser"
}

// AdvertiserModel 广告主模型
type AdvertiserModel struct {
	db *gorm.DB
}

// NewAdvertiserModel 创建广告主模型
func NewAdvertiserModel(db *gorm.DB) *AdvertiserModel {
	return &AdvertiserModel{
		db: db,
	}
}

// Create 创建广告主
func (m *AdvertiserModel) Create(advertiser *Advertiser) error {
	return m.db.Create(advertiser).Error
}

// BatchCreate 批量创建广告主
func (m *AdvertiserModel) BatchCreate(tx *gorm.DB, advertisers []*Advertiser) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.CreateInBatches(advertisers, 500).Error
}

// Update 更新广告主
func (m *AdvertiserModel) Update(tx *gorm.DB, advertiser *Advertiser) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Omit("is_followed").Updates(advertiser).Error
}

// MapUpdate 更新广告主
func (m *AdvertiserModel) MapUpdate(tx *gorm.DB, id int64, data map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&Advertiser{}).Where("id = ?", id).Updates(data).Error
}

// Delete 删除广告主
func (m *AdvertiserModel) Delete(advertiser *Advertiser) error {
	return m.db.Delete(advertiser).Error
}

// GetById 根据ID获取广告主
func (m *AdvertiserModel) GetById(id int64) (*Advertiser, error) {
	var advertiser Advertiser
	if err := m.db.First(&advertiser, id).Error; err != nil {
		return nil, err
	}
	return &advertiser, nil
}

// GetByAdvertiserId 根据广告主ID获取广告主
func (m *AdvertiserModel) GetByAdvertiserId(advertiserId int64) (*Advertiser, error) {
	var advertiser Advertiser
	if err := m.db.Where("advertiser_id = ?", advertiserId).First(&advertiser).Error; err != nil {
		return nil, err
	}
	return &advertiser, nil
}

// GetByAccountId 根据账户ID获取广告主列表
func (m *AdvertiserModel) GetByAccountId(accountId int64) ([]*Advertiser, error) {
	var advertisers []*Advertiser
	if err := m.db.Where("account_id = ?", accountId).Find(&advertisers).Error; err != nil {
		return nil, err
	}
	return advertisers, nil
}

// GetAdvertiserIdByAccountId 根据账户ID获取广告主ID列表（仅查询必要字段用于数据同步）
func (m *AdvertiserModel) GetAdvertiserIdByAccountId(accountId int64) ([]*Advertiser, error) {
	var advertisers []*Advertiser
	if err := m.db.Select("id, advertiser_id, delete_time").Where("account_id = ?", accountId).Find(&advertisers).Error; err != nil {
		return nil, err
	}
	return advertisers, nil
}

// List 获取广告主列表
func (m *AdvertiserModel) List(page, pageSize int64, conditions map[string]interface{}) ([]*Advertiser, int64, error) {
	var advertisers []*Advertiser
	var total int64

	db := m.db.Model(&Advertiser{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "advertiser_name" || key == "advertiser_remark" || key == "group_name" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}
	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))
	}
	// 分页查询
	if err := db.Order("is_followed DESC").Find(&advertisers).Error; err != nil {
		return nil, 0, err
	}
	if page <= 0 || pageSize <= 0 {
		total = int64(len(advertisers))
	}
	return advertisers, total, nil
}

// BatchMarkDelete 批量标记删除广告主
func (m *AdvertiserModel) BatchMarkDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Update("delete_time", time.Now().Unix()).Error
}

// BatchDelete 批量删除广告主
func (m *AdvertiserModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&Advertiser{}).Error
}

// DeleteByAdvertiserId 根据广告主ID删除广告主
func (m *AdvertiserModel) DeleteByAdvertiserId(advertiserId int64) error {
	return m.db.Where("advertiser_id = ?", advertiserId).Delete(&Advertiser{}).Error
}

// DeleteByAccountId 根据账户ID删除所有广告主
func (m *AdvertiserModel) DeleteByAccountId(accountId int64) error {
	return m.db.Where("account_id = ?", accountId).Delete(&Advertiser{}).Error
}

// SyncAdvertisers 同步指定账户下的广告主
func (m *AdvertiserModel) SyncAdvertisers(accountId int64, advertisers []*Advertiser) error {
	// 开启事务
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 删除该账户下的所有广告主
		if err := tx.Where("account_id = ?", accountId).Delete(&Advertiser{}).Error; err != nil {
			return err
		}

		// 没有新数据则直接返回
		if len(advertisers) == 0 {
			return nil
		}

		// 为每个广告主设置账户ID
		for _, adv := range advertisers {
			adv.AccountId = accountId
		}

		// 批量插入新的广告主
		if err := tx.Create(advertisers).Error; err != nil {
			return err
		}

		return nil
	})
}

// UpdateIsFollowed 更新广告主关注状态
func (m *AdvertiserModel) UpdateIsFollowed(accountId, advertiserId int64, isFollowed bool) error {
	return m.db.Model(&Advertiser{}).Where("account_id = ? and advertiser_id = ?", accountId, advertiserId).Update("is_followed", isFollowed).Error
}
