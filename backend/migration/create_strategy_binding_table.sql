-- 创建策略绑定表
CREATE TABLE IF NOT EXISTS strategy_bindings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_id INTEGER NOT NULL,
    binding_type VARCHAR(20) NOT NULL,
    binding_id VARCHAR(100) NOT NULL,
    binding_name VARCHAR(255),
    enabled BOOLEAN DEFAULT 1,
    priority INTEGER DEFAULT 0,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_strategy_bindings_strategy_id ON strategy_bindings(strategy_id);
CREATE INDEX IF NOT EXISTS idx_strategy_bindings_binding_type ON strategy_bindings(binding_type);
CREATE INDEX IF NOT EXISTS idx_strategy_bindings_binding_id ON strategy_bindings(binding_id);
CREATE INDEX IF NOT EXISTS idx_strategy_bindings_enabled ON strategy_bindings(enabled);

-- 创建唯一约束，防止重复绑定
CREATE UNIQUE INDEX IF NOT EXISTS idx_strategy_bindings_unique ON strategy_bindings(strategy_id, binding_type, binding_id);

-- 添加外键约束（可选）
-- CREATE INDEX IF NOT EXISTS idx_strategy_bindings_strategy_fk ON strategy_bindings(strategy_id);
-- CREATE INDEX IF NOT EXISTS idx_strategy_bindings_binding_fk ON strategy_bindings(binding_type, binding_id); 