package utils

import (
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"html"
	"runtime"
)

// ReminderUtil 提醒工具类
type ReminderUtil struct {
	app            *application.App
	reminderWindow *application.WebviewWindow
}

// NewReminderUtil 创建提醒工具实例
func NewReminderUtil(app *application.App) *ReminderUtil {
	return &ReminderUtil{
		app: app,
	}
}

// ShowReminder 显示提醒弹窗
func (r *ReminderUtil) ShowReminder(title, message string) *result.Result[string] {
	// 添加调试日志
	LogInfo("准备显示提醒弹窗", "title", title, "platform", runtime.GOOS)

	// 如果应用实例为空，直接返回错误
	if r.app == nil {
		LogError("应用实例为空，无法显示提醒弹窗")
		return result.ToError[string](result.ErrorSystem.AddMessage("应用实例未初始化"))
	}

	// 关闭之前的弹窗
	if r.reminderWindow != nil {
		LogInfo("关闭之前的提醒弹窗")
		r.reminderWindow.Close()
		r.reminderWindow = nil
	}

	// 转义HTML内容防止XSS
	safeTitle := html.EscapeString(title)
	safeMessage := html.EscapeString(message)

	// 简化的HTML内容，提高兼容性
	htmlContent := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>%s</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 90%%;
            min-width: 400px;
            backdrop-filter: blur(10px);
        }
        .icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 25px;
            white-space: pre-line;
            word-wrap: break-word;
            text-align: left;
            opacity: 0.95;
        }
        .timer {
            font-size: 14px;
            opacity: 0.8;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: inline-block;
        }
        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 50%%;
            transition: background 0.3s;
        }
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="close-btn" onclick="closeWindow()" title="关闭">&times;</button>
        <div class="icon">🔔</div>
        <div class="title">%s</div>
        <div class="message">%s</div>
        <div class="timer" id="timer">10秒后自动关闭</div>
    </div>
    <script>
        let countdown = 10;
        const timerElement = document.getElementById('timer');
        let intervalId;
        
        function updateTimer() {
            if (countdown > 0) {
                timerElement.textContent = countdown + '秒后自动关闭';
                countdown--;
            } else {
                closeWindow();
            }
        }
        
        function closeWindow() {
            clearInterval(intervalId);
            if (window.close) {
                window.close();
            } else {
                document.body.style.display = 'none';
            }
        }
        
        // 启动倒计时
        intervalId = setInterval(updateTimer, 1000);
        updateTimer();
        
        // ESC键关闭
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                event.preventDefault();
                closeWindow();
            }
        });
        
        // 点击背景关闭
        document.addEventListener('click', function(event) {
            if (event.target === document.body) {
                closeWindow();
            }
        });
        
        console.log('策略提醒弹窗已加载');
    </script>
</body>
</html>`, safeTitle, safeTitle, safeMessage)

	// 创建窗口选项，Windows平台特殊处理
	windowOptions := application.WebviewWindowOptions{
		Title:            safeTitle,
		Width:            600,
		Height:           400,
		MinWidth:         400,
		MinHeight:        300,
		MaxWidth:         800,
		MaxHeight:        600,
		BackgroundColour: application.NewRGB(102, 126, 234),
		HTML:             htmlContent,
	}

	// Windows平台不使用AlwaysOnTop，避免权限问题
	if runtime.GOOS != "windows" {
		// 其他平台可以使用AlwaysOnTop
		// windowOptions.AlwaysOnTop = true
	}

	// 添加Windows特定配置
	if runtime.GOOS == "windows" {
		windowOptions.Windows = application.WindowsWindow{}
	}

	LogInfo("创建提醒窗口", "width", windowOptions.Width, "height", windowOptions.Height)

	// 创建提醒窗口
	reminderWindow := r.app.NewWebviewWindowWithOptions(windowOptions)
	if reminderWindow == nil {
		LogError("创建提醒窗口失败")
		return result.ToError[string](result.ErrorSystem.AddMessage("创建提醒窗口失败"))
	}

	r.reminderWindow = reminderWindow

	// 显示窗口
	LogInfo("显示提醒窗口")
	reminderWindow.Show()

	// 尝试将窗口置于前台（Windows平台）
	if runtime.GOOS == "windows" {
		reminderWindow.Focus()
	}

	// 设置10秒后自动关闭
	go func() {
		defer func() {
			if recover() != nil {
				LogError("提醒窗口自动关闭时发生panic")
			}
		}()

		if r.reminderWindow != nil {
			LogInfo("自动关闭提醒窗口")
			r.reminderWindow.Close()
			r.reminderWindow = nil
		}
	}()

	LogInfo("提醒弹窗显示成功")
	return result.SuccessResult("提醒弹窗已显示")
}

// ShowSuccessReminder 显示成功提醒
func (r *ReminderUtil) ShowSuccessReminder(title, message string) *result.Result[string] {
	return r.ShowReminder("✅ "+title, message)
}

// ShowWarningReminder 显示警告提醒
func (r *ReminderUtil) ShowWarningReminder(title, message string) *result.Result[string] {
	return r.ShowReminder("⚠️ "+title, message)
}

// ShowErrorReminder 显示错误提醒
func (r *ReminderUtil) ShowErrorReminder(title, message string) *result.Result[string] {
	return r.ShowReminder("❌ "+title, message)
}

// ShowInfoReminder 显示信息提醒
func (r *ReminderUtil) ShowInfoReminder(title, message string) *result.Result[string] {
	return r.ShowReminder("ℹ️ "+title, message)
}

// CloseReminder 关闭提醒弹窗
func (r *ReminderUtil) CloseReminder() {
	if r.reminderWindow != nil {
		LogInfo("手动关闭提醒弹窗")
		r.reminderWindow.Close()
		r.reminderWindow = nil
	}
}
