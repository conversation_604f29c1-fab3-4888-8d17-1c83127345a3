package utils

import (
	"fmt"
	"log"
	"os/exec"
	"sync"
	"time"
)

// PerformanceData 代表性能数据
type PerformanceData struct {
	CpuUsage      float64
	MemoryUsageMB float64
	ProcessingFps float64
	ExecutionTime int64
	Timestamp     time.Time
}

// PerformanceMonitor 性能监控工具
type PerformanceMonitor struct {
	sampleInterval       time.Duration
	currentData          PerformanceData
	dataHistory          []PerformanceData
	stopChan             chan struct{}
	stopMutex            sync.Mutex
	monitoring           bool
	totalFramesProcessed int64
	lastFrameCount       int64
	lastFrameTimestamp   time.Time
}

// NewPerformanceMonitor 创建一个新的性能监控实例
func NewPerformanceMonitor(sampleIntervalMs int) *PerformanceMonitor {
	return &PerformanceMonitor{
		sampleInterval: time.Millisecond * time.Duration(sampleIntervalMs),
		stopChan:       make(chan struct{}),
	}
}

// Start 开始监控性能
func (pm *PerformanceMonitor) Start() {
	pm.stopMutex.Lock()
	defer pm.stopMutex.Unlock()

	if pm.monitoring {
		return
	}

	pm.monitoring = true
	pm.totalFramesProcessed = 0
	pm.lastFrameCount = 0
	pm.lastFrameTimestamp = time.Now()
	pm.dataHistory = nil

	go pm.monitoringLoop()

	log.Println("性能监控已启动")
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() {
	pm.stopMutex.Lock()
	defer pm.stopMutex.Unlock()

	if !pm.monitoring {
		return
	}

	close(pm.stopChan)
	pm.monitoring = false

	log.Println("性能监控已停止")
}

// Reset 重置性能监控
func (pm *PerformanceMonitor) Reset() {
	pm.Stop()
	pm.dataHistory = nil
	pm.totalFramesProcessed = 0
	pm.lastFrameCount = 0
	pm.lastFrameTimestamp = time.Now()

	pm.Start()

	log.Println("性能监控已重置")
}

// ReportFramesProcessed 报告已处理的帧数
func (pm *PerformanceMonitor) ReportFramesProcessed(frameCount int64) {
	if !pm.monitoring {
		return
	}

	pm.totalFramesProcessed += frameCount
}

// GetHistoryData 获取历史性能数据
func (pm *PerformanceMonitor) GetHistoryData() []PerformanceData {
	return pm.dataHistory
}

// GetAveragePerformance 获取平均性能数据
func (pm *PerformanceMonitor) GetAveragePerformance() PerformanceData {
	var totalCpu, totalMemory, totalFps float64
	for _, data := range pm.dataHistory {
		totalCpu += data.CpuUsage
		totalMemory += data.MemoryUsageMB
		totalFps += data.ProcessingFps
	}

	if len(pm.dataHistory) == 0 {
		return PerformanceData{}
	}

	return PerformanceData{
		CpuUsage:      totalCpu / float64(len(pm.dataHistory)),
		MemoryUsageMB: totalMemory / float64(len(pm.dataHistory)),
		ProcessingFps: totalFps / float64(len(pm.dataHistory)),
		ExecutionTime: pm.currentData.ExecutionTime,
	}
}

// GetTotalFramesProcessed 获取已处理的总帧数
func (pm *PerformanceMonitor) GetTotalFramesProcessed() int64 {
	return pm.totalFramesProcessed
}

// UpdatePerformanceData 更新性能数据
func (pm *PerformanceMonitor) UpdatePerformanceData() {
	// 获取CPU使用率
	cpuUsage := getCpuUsage()

	// 获取内存使用量
	memoryUsage := getMemoryUsage()

	// 计算处理速度 (帧/秒)
	currentFrameCount := pm.totalFramesProcessed
	framesDelta := currentFrameCount - pm.lastFrameCount
	now := time.Now()
	timeDelta := now.Sub(pm.lastFrameTimestamp).Seconds()
	fps := float64(framesDelta) / max(timeDelta, 0.001)

	// 更新最后帧数和时间戳
	pm.lastFrameCount = currentFrameCount
	pm.lastFrameTimestamp = now

	// 创建新的性能数据
	perfData := PerformanceData{
		CpuUsage:      cpuUsage,
		MemoryUsageMB: memoryUsage,
		ProcessingFps: fps,
		ExecutionTime: time.Now().UnixMilli(),
		Timestamp:     time.Now(),
	}

	// 更新当前数据
	pm.currentData = perfData

	// 添加到历史数据
	pm.dataHistory = append(pm.dataHistory, perfData)

	// 限制历史数据数量（例如，保留最近60秒的数据）
	if len(pm.dataHistory) > 60 {
		pm.dataHistory = pm.dataHistory[1:]
	}
}

// monitoringLoop 性能监控循环
func (pm *PerformanceMonitor) monitoringLoop() {
	ticker := time.NewTicker(pm.sampleInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.UpdatePerformanceData()

			// 触发性能更新事件
			// 这里我们可以添加一个事件通知机制，根据需求进行扩展
			fmt.Println("性能数据更新:", pm.currentData)
		}
	}
}

// 获取CPU使用率
func getCpuUsage() float64 {
	// 这里我们简单调用系统命令来获取CPU使用率，你可以根据自己的需求更改
	cmd := exec.Command("wmic", "cpu", "get", "loadpercentage")
	output, err := cmd.Output()
	if err != nil {
		log.Println("获取CPU使用率失败:", err)
		return 0
	}
	var cpuUsage float64
	fmt.Sscanf(string(output), "LoadPercentage\n%f", &cpuUsage)
	return cpuUsage
}

// 获取内存使用量（MB）
func getMemoryUsage() float64 {
	cmd := exec.Command("wmic", "OS", "get", "TotalVisibleMemorySize,FreePhysicalMemory")
	output, err := cmd.Output()
	if err != nil {
		log.Println("获取内存使用量失败:", err)
		return 0
	}

	var totalMemory, freeMemory float64
	fmt.Sscanf(string(output), "%f %f", &totalMemory, &freeMemory)

	usedMemory := totalMemory - freeMemory
	return usedMemory / 1024 // 转换为MB
}

// max 返回两个值中较大的一个
func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}
