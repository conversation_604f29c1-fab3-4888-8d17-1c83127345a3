package utils

import "strings"

var fontMap = map[string]string{
	"仿宋":      "C:/Windows/Fonts/simfang.ttf",
	"微软雅黑":    "C:/Windows/Fonts/msyh.ttc",
	"微软雅黑-粗体": "C:/Windows/Fonts/msyhbd.ttc",
	"黑体":      "C:/Windows/Fonts/simhei.ttf",
	"宋体":      "C:/Windows/Fonts/simsun.ttc",
}

// FontOption 字体选项结构
type FontOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// GetFontOptions 获取字体选项列表
func GetFontOptions() []FontOption {
	var options []FontOption
	for name, _ := range fontMap {
		options = append(options, FontOption{
			Label: name,
			Value: name,
		})
	}
	return options
}

func ToFontFile(name string) string {
	if path, ok := fontMap[name]; ok {
		return escapeColon(path)
	}
	return escapeColon(fontMap["仿宋"])
}

func escapeColon(s string) string {
	if strings.Contains(s, ":") {
		return strings.ReplaceAll(s, ":", "\\\\:")
	}
	return s
}

//ffmpeg -i "C:\Users\<USER>\Downloads\微信视频2025-05-23_102248_901.mp4" -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -filter_complex "[0:v]drawtext=fontfile=C\\:/Windows/Fonts/msyh.ttc:text='萨达':fontsize=30:fontcolor=#FAFF2A:x=(w*1.64)-(text_w/2):y=(h*3.06)-(text_h/2):shadowx=0:shadowy=0:shadowcolor=#273656:borderw=0:bordercolor=#FFFFFF[v]" -map a -map [v] -c:v libx264 -c:a aac -shortest -y "C:\Users\<USER>\Downloads\29326429760475708.mp4"
//[0:v]drawtext=fontfile=C\\:\Users\PC\GolandProjects\OceanEngineManager\backend\font\arial.ttf:text='字爱上':fontsize=30:fontcolor=#FFFFFF:x=(w*50.00)-(text_w/2):y=(h*34.00)-(text_h/2):shadowx=0:shadowy=0:shadowcolor=#165DFF:borderw=0:bordercolor=#FFFFFF[v]
