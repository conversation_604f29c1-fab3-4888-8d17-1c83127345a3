package utils

import (
	"fmt"
	"os"
	"path/filepath"
)

// SaveFileToLocal 将字节数据保存到本地文件
// filePath: 文件保存的目录路径
// fileName: 文件名（包含扩展名）
// data: 要保存的字节数据
// 返回: error 如果保存失败则返回错误信息
func SaveFileToLocal(filePath, fileName string, data []byte) error {
	if filePath == "" {
		return fmt.Errorf("文件路径不能为空")
	}

	if fileName == "" {
		return fmt.Errorf("文件名不能为空")
	}

	if data == nil || len(data) == 0 {
		return fmt.Errorf("文件数据不能为空")
	}

	// 确保目录存在，如果不存在则创建
	if err := os.MkdirAll(filePath, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 拼接完整的文件路径
	fullPath := filepath.Join(filePath, fileName)

	// 创建文件
	file, err := os.Create(fullPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 写入数据
	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("写入文件数据失败: %v", err)
	}

	// 确保数据被写入磁盘
	if err := file.Sync(); err != nil {
		return fmt.Errorf("同步文件数据到磁盘失败: %v", err)
	}

	return nil
}

// SaveFile 将字节数据保存到本地文件（带重复文件检查）
// filePath: 文件保存的目录路径
// fileName: 文件名（包含扩展名）
// data: 要保存的字节数据
// overwrite: 如果文件已存在是否覆盖
// 返回: (实际保存的文件名, error)
func SaveFile(filePath, fileName string, data []byte, overwrite bool) (string, error) {
	if filePath == "" {
		return "", fmt.Errorf("文件路径不能为空")
	}

	if fileName == "" {
		return "", fmt.Errorf("文件名不能为空")
	}

	if data == nil || len(data) == 0 {
		return "", fmt.Errorf("文件数据不能为空")
	}

	// 确保目录存在
	if err := os.MkdirAll(filePath, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	actualFileName := fileName
	fullPath := filepath.Join(filePath, actualFileName)

	// 如果文件已存在且不允许覆盖，则生成新的文件名
	if !overwrite {
		counter := 1
		ext := filepath.Ext(fileName)
		baseName := fileName[:len(fileName)-len(ext)]

		for {
			if _, err := os.Stat(fullPath); os.IsNotExist(err) {
				break // 文件不存在，可以使用当前文件名
			}

			// 文件存在，生成新的文件名
			actualFileName = fmt.Sprintf("%s(%d)%s", baseName, counter, ext)
			fullPath = filepath.Join(filePath, actualFileName)
			counter++
		}
	}

	// 创建文件
	file, err := os.Create(fullPath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 写入数据
	if _, err := file.Write(data); err != nil {
		return "", fmt.Errorf("写入文件数据失败: %v", err)
	}

	// 确保数据被写入磁盘
	if err := file.Sync(); err != nil {
		return "", fmt.Errorf("同步文件数据到磁盘失败: %v", err)
	}

	return fullPath, nil
}
