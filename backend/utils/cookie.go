package utils

import (
	"net/http"
	"strings"
)

// GetCsrfTokenFromCookie 从Cookie中提取CSRF令牌
func GetCsrfTokenFromCookie(cookieStr string) string {
	cookies := strings.Split(cookieStr, ";")

	// 首先尝试查找 csrftoken
	for _, cookie := range cookies {
		parts := strings.Split(strings.TrimSpace(cookie), "=")
		if len(parts) == 2 && parts[0] == "csrftoken" {
			return parts[1]
		}
	}

	// 如果没找到 csrftoken，尝试查找 passport_csrf_token
	for _, cookie := range cookies {
		parts := strings.Split(strings.TrimSpace(cookie), "=")
		if len(parts) == 2 && parts[0] == "passport_csrf_token" {
			return parts[1]
		}
	}

	// 兜底措施，提取下一个Cookie项作为CSRF令牌
	header := http.Header{}
	header.Add("Cookie", cookieStr)
	request := &http.Request{Header: header}
	for _, cookie := range request.Cookies() {
		if cookie.Name != "csrftoken" && cookie.Name != "passport_csrf_token" && len(cookie.Value) > 8 {
			return cookie.Value
		}
	}
	return ""
}
