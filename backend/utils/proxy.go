package utils

import (
	"regexp"
	"strings"
	"time"
)

// Proxy 代理结构体
type Proxy struct {
	Type      string
	IpAddress string
	Port      string
	Username  string
	Password  string
	Remark    string
	AddTime   time.Time
	Status    int
}

// ParseProxy 解析各种格式的代理字符串为 Proxy 对象
func ParseProxy(proxyString string) *Proxy {
	if len(proxyString) == 0 {
		return nil
	}

	// 清理字符串，移除前后空格
	proxyString = strings.TrimSpace(proxyString)

	// 尝试不同格式解析
	proxy := tryParseStandardFormat(proxyString)
	if proxy == nil {
		proxy = tryParsePipeFormat(proxyString)
	}
	if proxy == nil {
		proxy = tryParseColonFormat(proxyString)
	}
	if proxy == nil {
		proxy = tryParseSpaceFormat(proxyString)
	}
	if proxy == nil {
		proxy = tryParseDashFormat(proxyString)
	}
	if proxy == nil {
		proxy = tryParseSlashFormat(proxyString)
	}

	return proxy
}

// TestAllFormats 验证代理解析功能是否正常工作
func TestAllFormats() []struct {
	FormatName string
	TestCase   string
	Success    bool
} {
	results := []struct {
		FormatName string
		TestCase   string
		Success    bool
	}{}

	// 测试标准格式
	results = append(results, struct {
		FormatName string
		TestCase   string
		Success    bool
	}{
		"标准HTTP格式", "http://127.0.0.1:8888",
		testFormat("http://127.0.0.1:8888", func(p *Proxy) bool {
			return p.Type == "http" && p.IpAddress == "127.0.0.1" && p.Port == "8888"
		}),
	})

	// 测试带认证的HTTP格式
	results = append(results, struct {
		FormatName string
		TestCase   string
		Success    bool
	}{
		"带认证的HTTP格式", "http://u:p@127.0.0.1:8080",
		testFormat("http://u:p@127.0.0.1:8080", func(p *Proxy) bool {
			return p.Type == "http" && p.IpAddress == "127.0.0.1" && p.Port == "8080" && p.Username == "u" && p.Password == "p"
		}),
	})

	// 添加其他测试格式...

	return results
}

func testFormat(testCase string, validator func(p *Proxy) bool) bool {
	proxy := ParseProxy(testCase)
	if proxy == nil {
		return false
	}
	return validator(proxy)
}

// 尝试解析标准格式的代理字符串 (*********************:port 或 host:port)
func tryParseStandardFormat(proxyString string) *Proxy {
	// 支持格式: http://127.0.0.1:8888, http://u:p@127.0.0.1:8080
	regex := `^(?:(?P<type>https?|socks5)://)?(?:(?P<username>[^:@]+):(?P<password>[^@]+)@)?(?P<ip>[^:/]+):(?P<port>\d+)/?$`
	re := regexp.MustCompile(regex)

	match := re.FindStringSubmatch(proxyString)
	if len(match) > 0 {
		proxy := &Proxy{
			Type:      match[1],
			IpAddress: match[3],
			Port:      match[4],
			Username:  match[5],
			Password:  match[6],
			AddTime:   time.Now(),
			Status:    2,
		}
		return proxy
	}
	return nil
}

// 尝试解析管道符分隔的格式
func tryParsePipeFormat(proxyString string) *Proxy {
	// 支持格式: ********|2715|hzwd27x1|hzwd27x1|2025-05-27
	parts := strings.Split(proxyString, "|")
	if len(parts) >= 2 {
		proxy := &Proxy{
			IpAddress: parts[0],
			Port:      parts[1],
			Type:      "http", // 默认使用http
			AddTime:   time.Now(),
			Status:    2,
		}
		if len(parts) > 2 {
			proxy.Username = parts[2]
		}
		if len(parts) > 3 {
			proxy.Password = parts[3]
		}
		if len(parts) > 4 {
			proxy.Remark = parts[4]
		}
		return proxy
	}
	return nil
}

// 尝试解析冒号分隔的格式
func tryParseColonFormat(proxyString string) *Proxy {
	// 支持格式: **************:5520:vxqk28s1:vxqk28s1:2025-05-28
	parts := strings.Split(proxyString, ":")
	if len(parts) >= 2 {
		proxy := &Proxy{
			IpAddress: parts[0],
			Port:      parts[1],
			Type:      "http",
			AddTime:   time.Now(),
			Status:    2,
		}
		if len(parts) > 2 {
			proxy.Username = parts[2]
		}
		if len(parts) > 3 {
			proxy.Password = parts[3]
		}
		if len(parts) > 4 {
			proxy.Remark = parts[4]
		}
		return proxy
	}
	return nil
}

// 尝试解析空格分隔的格式
func tryParseSpaceFormat(proxyString string) *Proxy {
	// 支持格式: **************:5520 vxqk28s1 vxqk28s1 2025-05-28
	parts := strings.Fields(proxyString)
	if len(parts) >= 2 {
		proxy := &Proxy{
			IpAddress: parts[0],
			Port:      parts[1],
			Type:      "http",
			AddTime:   time.Now(),
			Status:    2,
		}
		if len(parts) > 2 {
			proxy.Username = parts[2]
		}
		if len(parts) > 3 {
			proxy.Password = parts[3]
		}
		if len(parts) > 4 {
			proxy.Remark = parts[4]
		}
		return proxy
	}
	return nil
}

// 尝试解析横线分隔的格式
func tryParseDashFormat(proxyString string) *Proxy {
	// 支持格式: socks5://**************:5520---vxqk28s1---vxqk28s1
	parts := strings.Split(proxyString, "---")
	if len(parts) >= 1 {
		urlProxy := tryParseStandardFormat(parts[0])
		if urlProxy != nil {
			if len(parts) > 1 {
				urlProxy.Username = parts[1]
			}
			if len(parts) > 2 {
				urlProxy.Password = parts[2]
			}
			return urlProxy
		}
	}
	return nil
}

// 尝试解析斜杠分隔的格式
func tryParseSlashFormat(proxyString string) *Proxy {
	// 支持格式: **************/5520/vxqk28s1/vxqk28s1/2025-05-28
	parts := strings.Split(proxyString, "/")
	if len(parts) >= 2 {
		proxy := &Proxy{
			IpAddress: parts[0],
			Port:      parts[1],
			Type:      "http",
			AddTime:   time.Now(),
			Status:    2,
		}
		if len(parts) > 2 {
			proxy.Username = parts[2]
		}
		if len(parts) > 3 {
			proxy.Password = parts[3]
		}
		if len(parts) > 4 {
			proxy.Remark = parts[4]
		}
		return proxy
	}
	return nil
}

// ParseProxies 批量解析代理字符串
func ParseProxies(content string) []*Proxy {
	if content == "" {
		return nil
	}

	// 按行分割
	lines := strings.Split(content, "\n")
	var proxies []*Proxy

	// 解析每一行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		proxy := ParseProxy(line)
		if proxy != nil {
			proxies = append(proxies, proxy)
		}
	}

	return proxies
}
