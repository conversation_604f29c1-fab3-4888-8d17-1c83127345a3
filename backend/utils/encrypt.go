package utils

import (
	"encoding/hex"
	"strings"
)

// Encrypt 巨量平台加密函数
func Encrypt(key string) string {
	// 将字符串转换为字节数组
	v := []byte(key)

	// 遍历字节数组并进行位异或操作
	for i := 0; i < len(v); i++ {
		v[i] = v[i] ^ 5
	}

	// 将字节数组转换为十六进制字符串
	hexString := hex.EncodeToString(v)

	// 返回小写形式的十六进制字符串
	return strings.ToLower(hexString)
}

func GetUrlEncodedBody(data map[string]string) string {
	encoded := ""
	for key, value := range data {
		if encoded != "" {
			encoded += "&"
		}
		encoded += key + "=" + value
	}
	return encoded
}

func GetQueryString(data map[string]string) string {
	query := GetUrlEncodedBody(data)
	if query != "" {
		query = "?" + query
	}
	return query
}
