package utils

import (
	"math/rand"
	"strings"
	"time"
)

// CookieHelper 提供了与 Cookie 相关的辅助功能
type CookieHelper struct{}

// ExtractCookieValue 从 Cookie 字符串中提取特定的 Cookie 值
func (ch *CookieHelper) ExtractCookieValue(cookies, key string) string {
	keyPattern := key + "="
	startIndex := strings.Index(cookies, keyPattern)
	if startIndex < 0 {
		return ""
	}

	startIndex += len(keyPattern)
	endIndex := strings.Index(cookies[startIndex:], ";")
	if endIndex < 0 {
		endIndex = len(cookies)
	} else {
		endIndex += startIndex
	}

	return cookies[startIndex:endIndex]
}

// GenerateRandomToken 生成一个随机的 CSRF 令牌
func (ch *CookieHelper) GenerateRandomToken() string {
	rand.Seed(time.Now().UnixNano())
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	token := make([]byte, 32)
	for i := range token {
		token[i] = chars[rand.Intn(len(chars))]
	}
	return string(token)
}

// GetCsrfToken 从 Cookie 中获取 CSRF 令牌，如果不存在则生成一个随机令牌
func (ch *CookieHelper) GetCsrfToken(cookie string) string {
	csrfToken := ch.ExtractCookieValue(cookie, "csrftoken")
	if csrfToken == "" {
		csrfToken = ch.GenerateRandomToken()
	}
	return csrfToken
}
