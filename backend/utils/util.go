package utils

import (
	"context"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"time"
)

var (
	StringToTimeConverter = copier.TypeConverter{
		SrcType: copier.String,
		DstType: time.Time{},
		Fn: func(src interface{}) (interface{}, error) {
			s, ok := src.(string)
			if !ok {
				return nil, errors.New("src type not matching")
			} else if s == "" {
				return time.Time{}, nil
			}
			return time.Parse(time.RFC3339, s)
		},
	}
)

// FormatStringToTimeConverter 返回一个将字符串格式化为时间的转换器
func FormatStringToTimeConverter(format string) copier.TypeConverter {
	return copier.TypeConverter{
		SrcType: copier.String,
		DstType: time.Time{},
		Fn: func(src interface{}) (interface{}, error) {
			s, ok := src.(string)
			if !ok {
				return nil, errors.New("src type not matching")
			} else if s == "" {
				return time.Time{}, nil
			}
			return time.Parse(format, s)
		},
	}
}

// EncryptText 使用简单的 XOR 加密来加密文本
func EncryptText(text string) string {
	var encrypted []byte
	for i := 0; i < len(text); i++ {
		encrypted = append(encrypted, text[i]^5)
	}
	return fmt.Sprintf("%x", encrypted)
}

// GetTodayStartTime 获取当天0点时间
func GetTodayStartTime() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
}

// GetTomorrowStartTime 获取第二天0点时间
func GetTomorrowStartTime() time.Time {
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	return time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, now.Location())
}

// GetTodayStartTimeUnix 获取当天0点时间的Unix时间戳
func GetTodayStartTimeUnix() int64 {
	return GetTodayStartTime().Unix()
}

// GetTomorrowStartTimeUnix 获取第二天0点时间的Unix时间戳
func GetTomorrowStartTimeUnix() int64 {
	return GetTomorrowStartTime().Unix()
}

// FormatTimeToString 格式化时间为字符串，格式：YYYY-MM-DD HH:MM:SS
func FormatTimeToString(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func GetDownloadsDir() (string, error) {
	home, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}

	switch runtime.GOOS {
	case "windows":
		return filepath.Join(home, "Downloads"), nil
	case "darwin": // macOS
		return filepath.Join(home, "Downloads"), nil
	case "linux":
		return filepath.Join(home, "Downloads"), nil // 有些 Linux 会改路径
	default:
		return "", fmt.Errorf("unsupported platform: %s", runtime.GOOS)
	}
}

// OpenFolder 打开指定路径的文件夹
func OpenFolder(ctx context.Context, path string) error {
	var cmd *exec.Cmd

	switch runtime.GOOS {
	case "windows":
		cmd = exec.CommandContext(ctx, "explorer", "/select,", path)
	case "darwin": // macOS
		cmd = exec.CommandContext(ctx, "open", path)
	case "linux":
		cmd = exec.CommandContext(ctx, "xdg-open", path)
	default:
		return fmt.Errorf("unsupported platform: %s", runtime.GOOS)
	}

	return cmd.Start()
}

//explorer /select, C:\Users\<USER>\Videos\1月14日(1)_clones\1月14日(1)_微调_1.mp4
