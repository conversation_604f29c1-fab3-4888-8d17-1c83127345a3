package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"
)

// FFmpegOptimizationHelper 提供与FFmpeg相关的优化辅助功能
type FFmpegOptimizationHelper struct{}

// GetHardwareAccelerationArgs 获取系统支持的硬件加速选项
func (ffmpeg *FFmpegOptimizationHelper) GetHardwareAccelerationArgs() string {
	// 在Windows操作系统上选择合适的硬件加速方式

	if isNvidiaGPUAvailable() {
		return "-hwaccel cuda -hwaccel_output_format cuda"
	} else if isAMDGPUAvailable() {
		return "-hwaccel amf"
	} else if isIntelGPUAvailable() {
		return "-hwaccel qsv -hwaccel_output_format qsv"
	}

	return ""
}

// isNvidiaGPUAvailable 检测系统是否有NVIDIA GPU
func isNvidiaGPUAvailable() bool {
	cmd := exec.Command("nvidia-smi", "-L")
	err := cmd.Run()
	return err == nil
}

// isAMDGPUAvailable 检测系统是否有AMD GPU
func isAMDGPUAvailable() bool {
	// 暂时返回 false，可以使用具体的实现方法
	return false
}

// isIntelGPUAvailable 检测系统是否有Intel GPU
func isIntelGPUAvailable() bool {
	// 暂时返回 false，可以使用具体的实现方法
	return false
}

// ProcessFilesInParallelAsync 多任务处理多个视频文件
func (ffmpeg *FFmpegOptimizationHelper) ProcessFilesInParallelAsync(
	inputFiles []string,
	outputFiles []string,
	processFunction func(inputFile, outputFile string) (bool, error),
	progress func(current, total int, file string),
	maxParallelism int,
) (map[string]bool, error) {
	if maxParallelism <= 0 {
		maxParallelism = 2 // 默认并行度
	}

	results := make(map[string]bool)
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxParallelism)

	for i, inputFile := range inputFiles {
		outputFile := inputFile
		if i < len(outputFiles) {
			outputFile = outputFiles[i]
		}

		wg.Add(1)
		semaphore <- struct{}{} // 控制并发量

		go func(inputFile, outputFile string) {
			defer wg.Done()
			result, err := processFunction(inputFile, outputFile)
			if err != nil {
				results[inputFile] = false
			} else {
				results[inputFile] = result
			}
			progress(len(results), len(inputFiles), inputFile)
			<-semaphore
		}(inputFile, outputFile)
	}

	wg.Wait()
	return results, nil
}

// GetTempDirectory 获取视频处理临时目录
func (ffmpeg *FFmpegOptimizationHelper) GetTempDirectory() string {
	tempDir := filepath.Join(os.Getenv("TEMP"), "Output", time.Now().Format("20060102_150405"))
	os.MkdirAll(tempDir, os.ModePerm)
	return tempDir
}

// AnalyzePerformance 分析视频处理性能指标
func (ffmpeg *FFmpegOptimizationHelper) AnalyzePerformance(processingTime, inputFileSize, outputFileSize int64) string {
	processingTimeSeconds := float64(processingTime) / 1000
	processingSpeed := float64(inputFileSize) / (1024 * 1024) / processingTimeSeconds
	compressionRatio := 0.0
	if inputFileSize > 0 {
		compressionRatio = float64(outputFileSize) / float64(inputFileSize)
	}

	return fmt.Sprintf("处理时间: %.2f秒 | 处理速度: %.2fMB/s | 压缩比: %.2f%%", processingTimeSeconds, processingSpeed, compressionRatio*100)
}

// GetFFmpegPath 获取FFmpeg可执行文件路径
func (ffmpeg *FFmpegOptimizationHelper) GetFFmpegPath() string {
	// 首先检查当前目录是否有ffmpeg
	appDirFfmpeg := filepath.Join(os.Getenv("PWD"), "ffmpeg")
	if _, err := os.Stat(appDirFfmpeg); err == nil {
		return appDirFfmpeg
	}

	// 然后检查系统环境变量中是否配置了ffmpeg
	cmd := exec.Command("ffmpeg", "-version")
	err := cmd.Run()
	if err == nil {
		return "ffmpeg"
	}

	return ""
}

// ExecuteFFmpegCommand 执行FFmpeg命令
func (ffmpeg *FFmpegOptimizationHelper) ExecuteFFmpegCommand(arguments string, progress func(int)) (bool, error) {
	ffmpegPath := ffmpeg.GetFFmpegPath()
	cmd := exec.Command(ffmpegPath, strings.Split(arguments, " ")...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false, fmt.Errorf("FFmpeg命令执行失败: %s, 错误: %v", string(output), err)
	}

	// 假设输出包含进度信息，解析并更新进度
	if progress != nil {
		// 这里简单地报告一个虚拟进度，实际需要根据FFmpeg输出解析实际进度
		progress(100)
	}

	return true, nil
}

// GetVideoInfo 获取视频信息（时长、分辨率等）
func (ffmpeg *FFmpegOptimizationHelper) GetVideoInfo(videoPath string) (map[string]string, error) {
	result := make(map[string]string)
	ffprobePath := filepath.Join(filepath.Dir(ffmpeg.GetFFmpegPath()), "ffprobe")
	if _, err := os.Stat(ffprobePath); err != nil {
		return result, err
	}

	cmd := exec.Command(ffprobePath, "-v", "error", "-show_format", "-show_streams", "-print_format", "json", videoPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return result, fmt.Errorf("ffprobe命令执行失败: %v", err)
	}

	// 这里可以解析JSON输出并填充result
	// 为简化，假设处理了部分输出，获取时长
	if strings.Contains(string(output), "duration") {
		result["duration"] = "1.23" // 示例: 提取的视频时长
	}

	return result, nil
}

type FfmpegVideoInfo struct {
	Path     string  `json:"path"`     // 视频文件路径
	Name     string  `json:"name"`     // 视频文件名
	Format   string  `json:"format"`   // 视频格式
	Size     float64 `json:"size"`     // 视频文件大小，单位 MB
	Width    int     `json:"width"`    // 视频宽度
	Height   int     `json:"height"`   // 视频高度
	Duration float64 `json:"duration"` // 视频时长，单位秒
}

// GetVideoInfo 调 ffprobe 拿 width/height/duration/format/size
func GetVideoInfo(path string) (*FfmpegVideoInfo, error) {
	// 检查文件是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, fmt.Errorf("视频文件不存在: %s", path)
	}

	// 执行ffprobe命令
	raw, err := exec.Command("./tools/ffprobe",
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height,duration,codec_name",
		"-show_entries", "format=size,format_name",
		"-of", "json", path).Output()
	if err != nil {
		return nil, fmt.Errorf("ffprobe执行失败: %v", err)
	}

	var ff struct {
		Streams []struct {
			Width    int    `json:"width"`
			Height   int    `json:"height"`
			Duration string `json:"duration"`
			Codec    string `json:"codec_name"`
		} `json:"streams"`
		Format struct {
			Size       string `json:"size"`
			FormatName string `json:"format_name"`
		} `json:"format"`
	}
	if err := json.Unmarshal(raw, &ff); err != nil {
		return nil, fmt.Errorf("解析ffprobe输出失败: %v", err)
	}

	// 检查是否有视频流
	if len(ff.Streams) == 0 {
		return nil, fmt.Errorf("文件没有视频流: %s", path)
	}

	// 解析时长
	dur := 0.0
	if ff.Streams[0].Duration != "" {
		dur, _ = strconv.ParseFloat(ff.Streams[0].Duration, 64)
	}

	// 解析文件大小
	sizeBytes := 0.0
	if ff.Format.Size != "" {
		sizeBytes, _ = strconv.ParseFloat(ff.Format.Size, 64)
	}

	// 获取文件名
	fileName := filepath.Base(path)

	return &FfmpegVideoInfo{
		Path:     path,
		Name:     fileName,
		Format:   ff.Format.FormatName,
		Size:     sizeBytes / (1024 * 1024), // 转换为MB
		Width:    ff.Streams[0].Width,
		Height:   ff.Streams[0].Height,
		Duration: dur,
	}, nil
}

// GetPosition 获取位置
// n: 最大长度
// position: 原位置
// return: 置换后的位置
func GetPosition(n, position int64) int64 {
	//获取因数列表
	factorList, constantList := genFactorList(n)
	return randSwapGroup(factorList, constantList, position, n)
}

// gcd 计算两个数的最大公约数
func gcd(a, b int64) int64 {
	for b != 0 {
		a, b = b, a%b
	}
	return a
}

// findCoprimeNumbers 找到 m个 与 n 互质的数
func findCoprimeNumbers(n int64, m int) []int64 {
	coprimeList := make([]int64, 0)
	for i := int64(1); i < n; i++ {
		if gcd(i, n) == 1 {
			coprimeList = append(coprimeList, i)
			if m == len(coprimeList) {
				break
			}
		}
	}
	return coprimeList
}

// isCoprimeWithAll 检查一个数是否与给定的多个数都互质
func isCoprimeWithAll(x int64, numbers ...int64) bool {
	for _, num := range numbers {
		if gcd(x, num) != 1 {
			return false
		}
	}
	return true
}

// randSwap 置换多项式
func randSwap(a, b, x, n int64) int64 {
	return (a*x + b) % n
}

// randSwapGroup 置换多项式组
func randSwapGroup(a, b []int64, x, n int64) int64 {
	for i := 0; i < len(a)-1; i++ {
		if i%2 == 0 {
			if x%2 == 0 {
				x = randSwap(a[i], b[i], x/2, (n+1)/2) * 2
			} else {
				x = randSwap(a[i+1], b[i+1], x/2, n/2)*2 + 1
			}
		} else {
			if x >= n/2 {
				x = randSwap(a[i], b[i], x-n/2, n-n/2) + n/2
			} else {
				x = randSwap(a[i+1], b[i+1], x, n/2)
			}
		}
	}
	return x
}

// genFactorList 生成因数列表
func genFactorList(n int64) (factorList, constantList []int64) {
	coprimeList := findCoprimeNumbers(n/2, 10000)
	factorList = make([]int64, 0)
	if n%2 == 0 {
		factorList = coprimeList
	} else {
		for _, v := range coprimeList {
			if isCoprimeWithAll(v, n/2+1, n) {
				factorList = append(factorList, v)
			}
		}
	}
	constantList = slices.Clone(factorList)
	slices.Reverse(constantList)
	return
}
