package utils

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// StringUtils 字符串工具类
type StringUtils struct{}

// 初始化静态变量
var randSource = rand.New(rand.NewSource(time.Now().UnixNano()))

// GenerateRandomString 生成指定长度的随机字符串
func (su *StringUtils) GenerateRandomString(length int, includeUppercase, includeLowercase, includeNumbers, includeSpecial bool) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("长度必须大于0")
	}

	if !(includeUppercase || includeLowercase || includeNumbers || includeSpecial) {
		return "", fmt.Errorf("至少需要选择一种字符类型")
	}

	uppercaseChars := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	lowercaseChars := "abcdefghijklmnopqrstuvwxyz"
	numberChars := "0123456789"
	specialChars := "!@#$%^&*()_-+=<>?"

	var allowedChars strings.Builder

	if includeUppercase {
		allowedChars.WriteString(uppercaseChars)
	}
	if includeLowercase {
		allowedChars.WriteString(lowercaseChars)
	}
	if includeNumbers {
		allowedChars.WriteString(numberChars)
	}
	if includeSpecial {
		allowedChars.WriteString(specialChars)
	}

	chars := allowedChars.String()
	result := make([]byte, length)

	for i := 0; i < length; i++ {
		result[i] = chars[randSource.Intn(len(chars))]
	}

	return string(result), nil
}

// GenerateAlphanumeric 生成仅包含字母和数字的随机字符串
func (su *StringUtils) GenerateAlphanumeric(length int) string {
	result, _ := su.GenerateRandomString(length, true, true, true, false)
	return result
}

// GenerateAlphabetic 生成仅包含字母的随机字符串
func (su *StringUtils) GenerateAlphabetic(length int) string {
	result, _ := su.GenerateRandomString(length, true, true, false, false)
	return result
}

// GenerateNumeric 生成仅包含数字的随机字符串
func (su *StringUtils) GenerateNumeric(length int) string {
	result, _ := su.GenerateRandomString(length, false, false, true, false)
	return result
}
