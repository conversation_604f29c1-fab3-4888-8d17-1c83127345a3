package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 全局日志实例
var Logger *zap.Logger

// Sugar 全局Sugar实例，用于简化API
var Sugar *zap.SugaredLogger

// StrategyLogger 策略引擎专用日志实例
var StrategyLogger *zap.Logger

// StrategySugar 策略引擎专用Sugar实例
var StrategySugar *zap.SugaredLogger

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level"`      // 日志级别
	Format     string `json:"format"`     // 日志格式 (json/text)
	Output     string `json:"output"`     // 输出目标 (file/console/both)
	MaxSize    int    `json:"maxSize"`    // 单个日志文件最大大小(MB)
	MaxBackups int    `json:"maxBackups"` // 最大备份文件数
	MaxAge     int    `json:"maxAge"`     // 日志文件最大保存天数
	Compress   bool   `json:"compress"`   // 是否压缩
	Directory  string `json:"directory"`  // 日志目录
}

// StrategyLogConfig 策略引擎日志配置（简化版）
type StrategyLogConfig struct {
	Directory string `json:"directory"` // 日志目录
}

// getExecutableDir 获取可执行文件所在目录
func getExecutableDir() string {
	execPath, err := os.Executable()
	if err != nil {
		// 如果获取失败，返回当前工作目录
		if wd, err := os.Getwd(); err == nil {
			return wd
		}
		return "."
	}
	return filepath.Dir(execPath)
}

// InitLogger 初始化日志系统
func InitLogger(config *LogConfig) error {
	if config == nil {
		// 使用相对于可执行文件的路径作为默认配置
		execDir := getExecutableDir()
		config = &LogConfig{
			Level:      "info",
			Format:     "json",
			Output:     "both",
			MaxSize:    100,
			MaxBackups: 10,
			MaxAge:     30,
			Compress:   true,
			Directory:  filepath.Join(execDir, "logs"),
		}
	} else {
		// 如果配置中的目录是相对路径，转换为相对于可执行文件的绝对路径
		if !filepath.IsAbs(config.Directory) {
			execDir := getExecutableDir()
			config.Directory = filepath.Join(execDir, config.Directory)
		}
	}

	// 确保日志目录存在
	if err := os.MkdirAll(config.Directory, os.ModePerm); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 解析日志级别
	level, err := zapcore.ParseLevel(strings.ToLower(config.Level))
	if err != nil {
		level = zapcore.InfoLevel
	}

	// 创建编码器配置 - 将元数据字段放在后面
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "zzz_timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.LevelKey = "zzz_level"
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoderConfig.CallerKey = "zzz_caller"
	encoderConfig.EncodeDuration = zapcore.StringDurationEncoder

	// 选择编码器
	var encoder zapcore.Encoder
	if strings.ToLower(config.Format) == "text" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// 创建核心配置
	var cores []zapcore.Core

	// 文件输出
	if config.Output == "file" || config.Output == "both" {
		logFile := filepath.Join(config.Directory, fmt.Sprintf("app_%s.log", time.Now().Format("20060102")))
		fileWriter := zapcore.AddSync(&lumberjack.Logger{
			Filename:   logFile,
			MaxSize:    config.MaxSize,
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,
			Compress:   config.Compress,
		})
		cores = append(cores, zapcore.NewCore(encoder, fileWriter, level))
	}

	// 控制台输出
	if config.Output == "console" || config.Output == "both" {
		consoleEncoder := encoder
		if strings.ToLower(config.Format) == "json" {
			// 控制台使用彩色输出，但保持相同的字段键名
			consoleConfig := encoderConfig
			consoleConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
			consoleEncoder = zapcore.NewConsoleEncoder(consoleConfig)
		}
		consoleWriter := zapcore.AddSync(os.Stdout)
		cores = append(cores, zapcore.NewCore(consoleEncoder, consoleWriter, level))
	}

	if len(cores) == 0 {
		return fmt.Errorf("未配置任何日志输出")
	}

	// 创建核心
	core := zapcore.NewTee(cores...)

	// 创建日志器
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zapcore.ErrorLevel))
	Sugar = Logger.Sugar()

	// 记录启动日志
	Sugar.Info("日志系统初始化完成",
		"level", level.String(),
		"format", config.Format,
		"output", config.Output,
		"directory", config.Directory,
	)

	return nil
}

// InitStrategyLogger 初始化策略引擎专用日志系统（简化版）
func InitStrategyLogger(config *StrategyLogConfig) error {
	if config == nil {
		// 使用相对于可执行文件的路径作为默认配置
		execDir := getExecutableDir()
		config = &StrategyLogConfig{
			Directory: filepath.Join(execDir, "logs", "strategy"),
		}
	} else {
		// 如果配置中的目录是相对路径，转换为相对于可执行文件的绝对路径
		if !filepath.IsAbs(config.Directory) {
			execDir := getExecutableDir()
			config.Directory = filepath.Join(execDir, config.Directory)
		}
	}

	// 确保策略日志目录存在
	if err := os.MkdirAll(config.Directory, os.ModePerm); err != nil {
		return fmt.Errorf("创建策略日志目录失败: %w", err)
	}

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "time"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.LevelKey = "level"
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	encoderConfig.CallerKey = "caller"
	encoderConfig.EncodeDuration = zapcore.StringDurationEncoder

	// 创建核心配置
	var cores []zapcore.Core

	// 文件输出
	logFile := filepath.Join(config.Directory, fmt.Sprintf("strategy_%s.log", time.Now().Format("20060102")))
	fileWriter := zapcore.AddSync(&lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    50, // 50MB
		MaxBackups: 20, // 保留20个备份
		MaxAge:     7,  // 保留7天
		Compress:   true,
	})
	cores = append(cores, zapcore.NewCore(zapcore.NewJSONEncoder(encoderConfig), fileWriter, zapcore.InfoLevel))

	// 控制台输出
	consoleConfig := encoderConfig
	consoleConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	consoleWriter := zapcore.AddSync(os.Stdout)
	cores = append(cores, zapcore.NewCore(zapcore.NewConsoleEncoder(consoleConfig), consoleWriter, zapcore.InfoLevel))

	// 创建核心
	core := zapcore.NewTee(cores...)

	// 创建策略日志器
	StrategyLogger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zapcore.ErrorLevel))
	StrategySugar = StrategyLogger.Sugar()

	// 记录启动日志
	StrategySugar.Infow("策略引擎日志系统初始化完成", "directory", config.Directory)

	return nil
}

// Sync 同步日志缓冲区
func Sync() {
	if Logger != nil {
		Logger.Sync()
	}
	if StrategyLogger != nil {
		StrategyLogger.Sync()
	}
}

// WithFields 创建带字段的日志器
func WithFields(fields map[string]interface{}) *zap.SugaredLogger {
	if Sugar == nil {
		return zap.NewNop().Sugar()
	}
	return Sugar.With(fields)
}

// WithStrategyFields 创建带字段的策略日志器
func WithStrategyFields(fields map[string]interface{}) *zap.SugaredLogger {
	if StrategySugar == nil {
		return zap.NewNop().Sugar()
	}
	return StrategySugar.With(fields)
}

// WithContext 创建带上下文的日志器
func WithContext(module, operation string, accountID int64, accountName string) *zap.SugaredLogger {
	fields := map[string]interface{}{
		"module":       module,
		"operation":    operation,
		"account_id":   accountID,
		"account_name": accountName,
	}
	return WithFields(fields)
}

// WithStrategyContext 创建带上下文的策略日志器
func WithStrategyContext(strategyID int64, strategyName, operation string) *zap.SugaredLogger {
	fields := map[string]interface{}{
		"strategy_id":   strategyID,
		"strategy_name": strategyName,
		"operation":     operation,
		"module":        "strategy_engine",
	}
	return WithStrategyFields(fields)
}

// LogDebug 调试日志
func LogDebug(msg string, fields ...interface{}) {
	if Sugar != nil {
		Sugar.Debugw(msg, fields...)
	}
}

// LogInfo 信息日志
func LogInfo(msg string, fields ...interface{}) {
	if Sugar != nil {
		Sugar.Infow(msg, fields...)
	}
}

// LogWarn 警告日志
func LogWarn(msg string, fields ...interface{}) {
	if Sugar != nil {
		Sugar.Warnw(msg, fields...)
	}
}

// LogError 错误日志
func LogError(msg string, fields ...interface{}) {
	if Sugar != nil {
		Sugar.Errorw(msg, fields...)
	}
}

// LogFatal 致命错误日志
func LogFatal(msg string, fields ...interface{}) {
	if Sugar != nil {
		Sugar.Fatalw(msg, fields...)
	}
}

// LogDebugf 格式化调试日志
func LogDebugf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Debugf(template, args...)
	}
}

// LogInfof 格式化信息日志
func LogInfof(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Infof(template, args...)
	}
}

// LogWarnf 格式化警告日志
func LogWarnf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Warnf(template, args...)
	}
}

// LogErrorf 格式化错误日志
func LogErrorf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Errorf(template, args...)
	}
}

// LogFatalf 格式化致命错误日志
func LogFatalf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Fatalf(template, args...)
	}
}

// LogErrorWithStack 记录错误日志（带堆栈信息）
func LogErrorWithStack(msg string, err error, fields ...interface{}) {
	if err == nil {
		return
	}

	allFields := append([]interface{}{"error", err.Error()}, fields...)
	LogError(msg, allFields...)
}

// LogOperation 记录操作日志
func LogOperation(module, operation, description string, accountID int64, accountName string, err error) {
	fields := map[string]interface{}{
		"module":         module,
		"operation":      operation,
		"description":    description,
		"account_id":     accountID,
		"account_name":   accountName,
		"operation_time": time.Now(),
	}

	if err != nil {
		fields["error"] = err.Error()
		LogError("操作执行失败", fields)
	} else {
		LogInfo("操作执行成功", fields)
	}
}

// 策略引擎专用日志函数（简化版）

// LogStrategyInfo 策略信息日志
func LogStrategyInfo(msg string, fields ...interface{}) {
	if StrategySugar != nil {
		StrategySugar.Infow(msg, fields...)
	}
}

// LogStrategyError 策略错误日志
func LogStrategyError(msg string, fields ...interface{}) {
	if StrategySugar != nil {
		StrategySugar.Errorw(msg, fields...)
	}
}

// LogStrategyInfof 格式化策略信息日志
func LogStrategyInfof(template string, args ...interface{}) {
	if StrategySugar != nil {
		StrategySugar.Infof(template, args...)
	}
}

// LogStrategyErrorf 格式化策略错误日志
func LogStrategyErrorf(template string, args ...interface{}) {
	if StrategySugar != nil {
		StrategySugar.Errorf(template, args...)
	}
}

// LogStrategyErrorWithStack 记录策略错误日志（带堆栈信息）
func LogStrategyErrorWithStack(msg string, err error, fields ...interface{}) {
	if err == nil {
		return
	}

	allFields := append([]interface{}{"error", err.Error()}, fields...)
	LogStrategyError(msg, allFields...)
}

// LogStrategyOperation 记录策略操作日志
func LogStrategyOperation(strategyID int64, strategyName, operation, description string, err error) {
	fields := map[string]interface{}{
		"strategy_id":    strategyID,
		"strategy_name":  strategyName,
		"operation":      operation,
		"description":    description,
		"operation_time": time.Now(),
		"module":         "strategy_engine",
	}

	if err != nil {
		fields["error"] = err.Error()
		LogStrategyError("策略操作执行失败", fields)
	} else {
		LogStrategyInfo("策略操作执行成功", fields)
	}
}

// GetCallerInfo 获取调用者信息
func GetCallerInfo() (string, int) {
	_, file, line, ok := runtime.Caller(2)
	if !ok {
		return "unknown", 0
	}
	return filepath.Base(file), line
}

// SetLevel 设置日志级别
func SetLevel(level string) error {
	if Logger == nil {
		return fmt.Errorf("日志系统未初始化")
	}

	newLevel, err := zapcore.ParseLevel(strings.ToLower(level))
	if err != nil {
		return fmt.Errorf("无效的日志级别: %s", level)
	}

	// 注意：Zap不支持运行时动态修改级别，需要重新初始化
	// 这里只是记录日志
	LogInfo("尝试设置日志级别", "new_level", newLevel.String())
	return nil
}
