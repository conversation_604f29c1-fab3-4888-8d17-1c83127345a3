package menu

import (
	"context"
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
	"log"
	"path/filepath"
	"time"
)

func NewVideoItemMenu() *application.Menu {
	// 新建一个菜单，ID 必须全局唯一
	videoItemMenu := application.NewMenu()
	videoItemMenu.Add("打开文件所在位置").OnClick(func(ctx *application.Context) {
		fmt.Println(ctx.ContextMenuData()) //取到元素传来的 data
		if filePath, ok := ctx.ContextMenuData().(string); ok {
			// 打开文件所在目录
			go func() {
				ctx, _ := context.WithTimeout(context.Background(), 2*time.Second)
				if err := utils.OpenFolder(ctx, filepath.Dir(filePath)); err != nil {
					log.Printf("打开文件所在目录失败: %v", err)
				}
			}()
		}
	})
	return videoItemMenu
}
