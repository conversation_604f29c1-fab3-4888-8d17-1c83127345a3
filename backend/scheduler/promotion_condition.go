package scheduler

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// EvaluatePromotionsConditions 评估广告列表的条件，返回满足条件的广告列表
func (ce *ConditionEvaluator) EvaluatePromotionsConditions(promotions []*model.Promotion, strategy *model.Strategy) ([]*model.Promotion, error) {
	if len(promotions) == 0 {
		utils.LogStrategyInfo("没有广告需要评估")
		return []*model.Promotion{}, nil
	}

	utils.LogStrategyInfo("开始评估广告策略条件", "promotion_count", len(promotions), "strategy_name", strategy.Name)

	// 解析策略条件
	var conditions []Condition
	if err := json.Unmarshal([]byte(strategy.Conditions), &conditions); err != nil {
		log.Printf("解析策略条件失败: %v", err)
		return nil, err
	}

	if len(conditions) == 0 {
		log.Printf("策略 %s 没有配置条件，所有广告都满足", strategy.Name)
		return promotions, nil
	}

	utils.LogStrategyInfo("策略有条件，开始评估每个广告", "strategy_name", strategy.Name, "condition_count", len(conditions))

	// 筛选满足条件的广告
	var matchedPromotions []*model.Promotion
	for i, promotion := range promotions {
		utils.LogStrategyInfo("评估广告", "index", i+1, "total", len(promotions), "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

		if ce.evaluatePromotionConditions(promotion, conditions, strategy.LogicType) {
			utils.LogStrategyInfo("广告满足策略条件", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId, "strategy_name", strategy.Name)
			matchedPromotions = append(matchedPromotions, promotion)
		} else {
			utils.LogStrategyInfo("广告不满足策略条件", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId, "strategy_name", strategy.Name)
		}
	}

	log.Printf("广告条件评估完成：总数 %d，满足条件 %d（策略: %s）",
		len(promotions), len(matchedPromotions), strategy.Name)

	utils.LogStrategyInfo("广告条件评估完成", "total_promotions", len(promotions), "matched_promotions", len(matchedPromotions), "strategy_name", strategy.Name)

	return matchedPromotions, nil
}

// evaluatePromotionConditions 评估单个广告是否满足
func (ce *ConditionEvaluator) evaluatePromotionConditions(promotion *model.Promotion, conditions []Condition, logicType string) bool {
	switch logicType {
	case "AND":
		// 所有条件都必须满足
		utils.LogStrategyInfo("使用AND逻辑评估广告条件", "promotion_id", promotion.PromotionId)
		for i, condition := range conditions {
			utils.LogStrategyInfo("评估广告条件", "index", i+1, "total", len(conditions), "type", condition.Type, "field", condition.Field, "operator", condition.Operator, "value", condition.Value)

			if !ce.evaluatePromotionCondition(condition, promotion) {
				utils.LogStrategyInfo("广告不满足条件", "promotion_id", promotion.PromotionId, "condition_index", i+1, "condition", condition)
				return false
			}
			utils.LogStrategyInfo("广告条件满足", "promotion_id", promotion.PromotionId, "condition_index", i+1, "total_conditions", len(conditions))
		}
		utils.LogStrategyInfo("广告满足所有条件", "promotion_id", promotion.PromotionId)
		return true

	case "OR":
		// 至少一个条件满足
		utils.LogStrategyInfo("使用OR逻辑评估广告条件", "promotion_id", promotion.PromotionId)
		for i, condition := range conditions {
			utils.LogStrategyInfo("评估广告条件", "index", i+1, "total", len(conditions), "type", condition.Type, "field", condition.Field, "operator", condition.Operator, "value", condition.Value)

			if ce.evaluatePromotionCondition(condition, promotion) {
				utils.LogStrategyInfo("广告满足条件", "promotion_id", promotion.PromotionId, "condition_index", i+1, "condition", condition)
				return true
			}
			utils.LogStrategyInfo("广告条件不满足", "promotion_id", promotion.PromotionId, "condition_index", i+1, "total_conditions", len(conditions))
		}
		utils.LogStrategyInfo("广告不满足任何条件", "promotion_id", promotion.PromotionId)
		return false

	default:
		log.Printf("未知的逻辑关系: %s，默认使用AND", logicType)
		// 默认使用AND逻辑
		for i, condition := range conditions {
			log.Printf("评估广告条件 %d/%d: 类型=%s, 字段=%s, 操作符=%s, 值=%v",
				i+1, len(conditions), condition.Type, condition.Field, condition.Operator, condition.Value)

			if !ce.evaluatePromotionCondition(condition, promotion) {
				log.Printf("广告 %s 不满足条件 %d: %+v", promotion.PromotionId, i+1, condition)
				return false
			}
			log.Printf("广告条件 %d/%d 满足", i+1, len(conditions))
		}
		log.Printf("广告 %s 满足所有条件", promotion.PromotionId)
		return true
	}
}

// evaluatePromotionCondition 评估单个广告的单个条件
func (ce *ConditionEvaluator) evaluatePromotionCondition(condition Condition, promotion *model.Promotion) bool {
	switch condition.Type {
	// 条件类型
	case "stat_cost":
		return ce.evaluatePromotionStatCost(condition, promotion)
	case "convert_cnt":
		return ce.evaluatePromotionConvertCnt(condition, promotion)
	case "conversion_cost":
		return ce.evaluatePromotionConversionCost(condition, promotion)
	case "ctr":
		return ce.evaluatePromotionCtr(condition, promotion)
	case "conversion_rate":
		return ce.evaluatePromotionConversionRate(condition, promotion)
	case "show_cnt":
		return ce.evaluatePromotionShowCnt(condition, promotion)
	case "click_cnt":
		return ce.evaluatePromotionClickCnt(condition, promotion)
	case "cpc_platform":
		return ce.evaluatePromotionCpc(condition, promotion)
	case "cpm_platform":
		return ce.evaluatePromotionCpm(condition, promotion)
	case "promotion_status":
		return ce.evaluatePromotionStatus(condition, promotion)
	case "ad_budget":
		return ce.evaluateAdBudget(condition, promotion)
	case "ad_bid":
		return ce.evaluateAdBid(condition, promotion)
	case "diagnosis_status":
		return ce.evaluatePromotionDiagnosisStatus(condition, promotion)
	case "material_label":
		return ce.evaluatePromotionMaterialLabel(condition, promotion)
	case "learning_timeout":
		// 暂时注释掉，因为Promotion模型中没有LearningTimeout字段
		// return ce.evaluatePromotionLearningTimeout(condition, promotion)
		log.Printf("学保超时时间条件评估暂未实现: %s", condition.Type)
		return false
	case "ad_time_special":
		return ce.evaluatePromotionAdTimeSpecial(condition, promotion)
	// 项目级条件（广告条件评估中需要获取项目信息）
	case "project_status":
		return ce.evaluateProjectStatusForPromotion(condition, promotion)
	// 广告主级条件（广告条件评估中需要获取广告主信息）
	case "account_balance":
		return ce.evaluateAccountBalanceForPromotion(condition, promotion)

	default:
		log.Printf("未知的广告条件类型: %s", condition.Type)
		return false
	}
}

// evaluatePromotionStatus 广告状态
func (ce *ConditionEvaluator) evaluatePromotionStatus(condition Condition, promotion *model.Promotion) bool {
	// 优先使用状态名称进行判断，因为状态名称更稳定易理解
	// 巨量引擎API返回的正确状态映射：
	// promotion_status = 0, name = 投放中
	// promotion_status = 61, name = 已暂停
	// promotion_status = 41, name = 审核不通过
	// promotion_status = 43, name = 账户余额不足
	// promotion_status = 22, name = 新建审核中
	// promotion_status = 31, name = 已终止

	// 状态名称映射：前端传入值 -> 实际状态名称
	statusNameMapping := map[string]string{
		constant.PromotionFrontendStatus_Active:            constant.PromotionStatusName_Active,
		constant.PromotionFrontendStatus_Paused:            constant.PromotionStatusName_Paused,
		constant.PromotionFrontendStatus_AuditFailed:       constant.PromotionStatusName_AuditFailed,
		constant.PromotionFrontendStatus_InsufficientFunds: constant.PromotionStatusName_InsufficientFunds,
		constant.PromotionFrontendStatus_AuditPending:      constant.PromotionStatusName_AuditPending,
		constant.PromotionFrontendStatus_Terminated:        constant.PromotionStatusName_Terminated,
		// 支持直接传入中文状态名称
		constant.PromotionStatusName_Active:            constant.PromotionStatusName_Active,
		constant.PromotionStatusName_Paused:            constant.PromotionStatusName_Paused,
		constant.PromotionStatusName_AuditFailed:       constant.PromotionStatusName_AuditFailed,
		constant.PromotionStatusName_InsufficientFunds: constant.PromotionStatusName_InsufficientFunds,
		constant.PromotionStatusName_AuditPending:      constant.PromotionStatusName_AuditPending,
		constant.PromotionStatusName_Terminated:        constant.PromotionStatusName_Terminated,
	}

	// 状态码映射：前端传入值 -> 实际状态码
	statusCodeMapping := map[string]int{
		constant.PromotionFrontendStatus_Active:            constant.PromotionStatus_Active,
		constant.PromotionFrontendStatus_Paused:            constant.PromotionStatus_Paused,
		constant.PromotionFrontendStatus_AuditFailed:       constant.PromotionStatus_AuditFailed,
		constant.PromotionFrontendStatus_InsufficientFunds: constant.PromotionStatus_InsufficientFunds,
		constant.PromotionFrontendStatus_AuditPending:      constant.PromotionStatus_AuditPending,
		constant.PromotionFrontendStatus_Terminated:        constant.PromotionStatus_Terminated,
	}

	var result bool
	switch condition.Operator {
	case "==":
		expectedValue := fmt.Sprintf("%v", condition.Value)

		// 优先使用状态名称进行比较
		if mappedStatusName, exists := statusNameMapping[expectedValue]; exists {
			result = promotion.PromotionStatusName == mappedStatusName
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态名称)", promotion.PromotionStatusName, "右值", expectedValue, "映射后", mappedStatusName, "结果", result)
		} else if mappedStatusCode, exists := statusCodeMapping[expectedValue]; exists {
			// 如果没有状态名称映射，使用状态码比较
			result = promotion.PromotionStatus == mappedStatusCode
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态码)", promotion.PromotionStatus, "右值", expectedValue, "映射后", mappedStatusCode, "结果", result)
		} else {
			// 直接比较状态名称或状态码
			result = promotion.PromotionStatusName == expectedValue || strconv.Itoa(promotion.PromotionStatus) == expectedValue
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(直接比较)", fmt.Sprintf("name:%s,code:%d", promotion.PromotionStatusName, promotion.PromotionStatus), "右值", expectedValue, "结果", result)
		}

	case "!=":
		expectedValue := fmt.Sprintf("%v", condition.Value)

		// 优先使用状态名称进行比较
		if mappedStatusName, exists := statusNameMapping[expectedValue]; exists {
			result = promotion.PromotionStatusName != mappedStatusName
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态名称)", promotion.PromotionStatusName, "右值", expectedValue, "映射后", mappedStatusName, "结果", result)
		} else if mappedStatusCode, exists := statusCodeMapping[expectedValue]; exists {
			// 如果没有状态名称映射，使用状态码比较
			result = promotion.PromotionStatus != mappedStatusCode
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态码)", promotion.PromotionStatus, "右值", expectedValue, "映射后", mappedStatusCode, "结果", result)
		} else {
			// 直接比较状态名称或状态码
			result = promotion.PromotionStatusName != expectedValue && strconv.Itoa(promotion.PromotionStatus) != expectedValue
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(直接比较)", fmt.Sprintf("name:%s,code:%d", promotion.PromotionStatusName, promotion.PromotionStatus), "右值", expectedValue, "结果", result)
		}

	case "in":
		result = false
		for _, value := range condition.Values {
			valueStr := fmt.Sprintf("%v", value)

			// 优先使用状态名称进行比较
			if mappedStatusName, exists := statusNameMapping[valueStr]; exists {
				if promotion.PromotionStatusName == mappedStatusName {
					result = true
					utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态名称)", promotion.PromotionStatusName, "匹配值", valueStr, "映射后", mappedStatusName, "结果", true)
					break
				}
			} else if mappedStatusCode, exists := statusCodeMapping[valueStr]; exists {
				// 如果没有状态名称映射，使用状态码比较
				if promotion.PromotionStatus == mappedStatusCode {
					result = true
					utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(状态码)", promotion.PromotionStatus, "匹配值", valueStr, "映射后", mappedStatusCode, "结果", true)
					break
				}
			} else {
				// 直接比较状态名称或状态码
				if promotion.PromotionStatusName == valueStr || strconv.Itoa(promotion.PromotionStatus) == valueStr {
					result = true
					utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值(直接比较)", fmt.Sprintf("name:%s,code:%d", promotion.PromotionStatusName, promotion.PromotionStatus), "匹配值", valueStr, "结果", true)
					break
				}
			}
		}
		if !result {
			utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值", fmt.Sprintf("name:%s,code:%d", promotion.PromotionStatusName, promotion.PromotionStatus), "右值列表", condition.Values, "结果", false)
		}

	default:
		utils.LogStrategyInfo("[条件判断] 类型: promotion_status, 操作符: "+condition.Operator, "左值", fmt.Sprintf("name:%s,code:%d", promotion.PromotionStatusName, promotion.PromotionStatus), "右值", condition.Value, "结果", false, "原因", "不支持的操作符")
		return false
	}

	return result
}

// evaluateAdBudget 广告预算
func (ce *ConditionEvaluator) evaluateAdBudget(condition Condition, promotion *model.Promotion) bool {
	budget, err := strconv.ParseFloat(promotion.AdBudget, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: ad_budget, 操作符: "+condition.Operator, "左值", promotion.AdBudget, "右值", condition.Value, "结果", false, "原因", "解析广告预算失败")
		return false
	}
	return ce.evaluateNumericCondition("ad_budget", budget, condition)
}

// evaluateAdBid 广告出价
func (ce *ConditionEvaluator) evaluateAdBid(condition Condition, promotion *model.Promotion) bool {
	bid, err := strconv.ParseFloat(promotion.AdBid, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: ad_bid, 操作符: "+condition.Operator, "左值", promotion.AdBid, "右值", condition.Value, "结果", false, "原因", "解析广告出价失败")
		return false
	}
	return ce.evaluateNumericCondition("ad_bid", bid, condition)
}

// evaluatePromotionStatCost 广告消耗
func (ce *ConditionEvaluator) evaluatePromotionStatCost(condition Condition, promotion *model.Promotion) bool {
	cost, err := strconv.ParseFloat(promotion.StatCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", promotion.StatCost, "右值", condition.Value, "结果", false, "原因", "解析广告消耗失败")
		return false
	}

	var result bool
	switch condition.Operator {
	case ">":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost > threshold
	case ">=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost >= threshold
	case "<":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost < threshold
	case "<=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost <= threshold
	case "==":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost == threshold
	case "!=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = cost != threshold
	case "between":
		result = cost >= condition.Min && cost <= condition.Max
		utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "最小值", condition.Min, "最大值", condition.Max, "结果", result)
		return result
	default:
		utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", false, "原因", "不支持的操作符")
		return false
	}

	utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 操作符: "+condition.Operator, "左值", cost, "右值", condition.Value, "结果", result)
	return result
}

// evaluatePromotionClickCnt 广告点击量
func (ce *ConditionEvaluator) evaluatePromotionClickCnt(condition Condition, promotion *model.Promotion) bool {
	clicks, err := strconv.ParseFloat(promotion.ClickCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: click_cnt, 操作符: "+condition.Operator, "左值", promotion.ClickCnt, "右值", condition.Value, "结果", false, "原因", "解析广告点击量失败")
		return false
	}
	return ce.evaluateNumericCondition("click_cnt", clicks, condition)
}

// evaluatePromotionShowCnt 广告展示量
func (ce *ConditionEvaluator) evaluatePromotionShowCnt(condition Condition, promotion *model.Promotion) bool {
	shows, err := strconv.ParseFloat(promotion.ShowCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: show_cnt, 操作符: "+condition.Operator, "左值", promotion.ShowCnt, "右值", condition.Value, "结果", false, "原因", "解析广告展现量失败")
		return false
	}
	return ce.evaluateNumericCondition("show_cnt", shows, condition)
}

// evaluatePromotionConvertCnt 广告转化量
func (ce *ConditionEvaluator) evaluatePromotionConvertCnt(condition Condition, promotion *model.Promotion) bool {
	converts, err := strconv.ParseFloat(promotion.ConvertCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: convert_cnt, 操作符: "+condition.Operator, "左值", promotion.ConvertCnt, "右值", condition.Value, "结果", false, "原因", "解析广告转化量失败")
		return false
	}
	return ce.evaluateNumericCondition("convert_cnt", converts, condition)
}

// evaluatePromotionCtr 广告点击率
func (ce *ConditionEvaluator) evaluatePromotionCtr(condition Condition, promotion *model.Promotion) bool {
	ctr := promotion.Ctr
	return ce.evaluateNumericCondition("ctr", ctr, condition)
}

// evaluatePromotionConversionRate 广告转化率
func (ce *ConditionEvaluator) evaluatePromotionConversionRate(condition Condition, promotion *model.Promotion) bool {
	rate := promotion.ConversionRate
	return ce.evaluateNumericCondition("conversion_rate", rate, condition)
}

// evaluatePromotionConversionCost 广告转化成本
func (ce *ConditionEvaluator) evaluatePromotionConversionCost(condition Condition, promotion *model.Promotion) bool {
	cost, err := strconv.ParseFloat(promotion.ConversionCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: conversion_cost, 操作符: "+condition.Operator, "左值", promotion.ConversionCost, "右值", condition.Value, "结果", false, "原因", "解析广告转化成本失败")
		return false
	}
	return ce.evaluateNumericCondition("conversion_cost", cost, condition)
}

// evaluatePromotionCpc 广告平均点击价格
func (ce *ConditionEvaluator) evaluatePromotionCpc(condition Condition, promotion *model.Promotion) bool {
	cost, err := strconv.ParseFloat(promotion.StatCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: cpc_platform, 操作符: "+condition.Operator, "左值", promotion.StatCost, "右值", condition.Value, "结果", false, "原因", "解析广告消耗失败")
		return false
	}
	clicks, err := strconv.ParseFloat(promotion.ClickCnt, 64)
	if err != nil || clicks == 0 {
		utils.LogStrategyInfo("[条件判断] 类型: cpc_platform, 操作符: "+condition.Operator, "左值", promotion.ClickCnt, "右值", condition.Value, "结果", false, "原因", "解析广告点击量失败或为0")
		return false
	}
	cpc := cost / clicks
	return ce.evaluateNumericCondition("cpc_platform", cpc, condition)
}

// evaluatePromotionCpm 广告千次展现价格
func (ce *ConditionEvaluator) evaluatePromotionCpm(condition Condition, promotion *model.Promotion) bool {
	cost, err := strconv.ParseFloat(promotion.StatCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: cpm_platform, 操作符: "+condition.Operator, "左值", promotion.StatCost, "右值", condition.Value, "结果", false, "原因", "解析广告消耗失败")
		return false
	}
	shows, err := strconv.ParseFloat(promotion.ShowCnt, 64)
	if err != nil || shows == 0 {
		utils.LogStrategyInfo("[条件判断] 类型: cpm_platform, 操作符: "+condition.Operator, "左值", promotion.ShowCnt, "右值", condition.Value, "结果", false, "原因", "解析广告展现量失败或为0")
		return false
	}
	cpm := (cost / shows) * 1000
	return ce.evaluateNumericCondition("cpm_platform", cpm, condition)
}

// evaluatePromotionDiagnosisStatus 广告诊断状态
func (ce *ConditionEvaluator) evaluatePromotionDiagnosisStatus(condition Condition, promotion *model.Promotion) bool {
	// 这里需要根据实际的诊断状态字段进行评估
	// 暂时返回false，因为需要确认诊断状态字段的具体实现
	log.Printf("诊断状态条件评估暂未实现: %s", condition.Type)
	return false
}

// evaluatePromotionMaterialLabel 广告素材标签
func (ce *ConditionEvaluator) evaluatePromotionMaterialLabel(condition Condition, promotion *model.Promotion) bool {
	// 这里需要根据实际的素材标签字段进行评估
	// 暂时返回false，因为需要确认素材标签字段的具体实现
	log.Printf("素材标签条件评估暂未实现: %s", condition.Type)
	return false
}

// evaluatePromotionAdTimeSpecial 广告新建后特殊时间
func (ce *ConditionEvaluator) evaluatePromotionAdTimeSpecial(condition Condition, promotion *model.Promotion) bool {
	timeDiff := time.Since(promotion.CreateTime).Minutes()
	return ce.evaluateNumericCondition("ad_time_special", timeDiff, condition)
}

// evaluateAccountBalanceForPromotion 评估广告所属账户的余额
func (ce *ConditionEvaluator) evaluateAccountBalanceForPromotion(condition Condition, promotion *model.Promotion) bool {
	// 获取账户信息
	account, err := ce.svcCtx.AccountModel.GetById(promotion.AccountId)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: account_balance", "promotion_id", promotion.PromotionId, "account_id", promotion.AccountId, "结果", false, "原因", "获取账户信息失败")
		return false
	}

	if account == nil {
		utils.LogStrategyInfo("[条件判断] 类型: account_balance", "promotion_id", promotion.PromotionId, "account_id", promotion.AccountId, "结果", false, "原因", "账户不存在")
		return false
	}

	// 假设账户模型有余额字段，如果没有需要根据实际模型调整
	// TODO: 根据实际的Account模型字段调整
	var balance float64 = 0 // 临时设为0，需要根据实际字段调整

	var result bool
	switch condition.Operator {
	case ">":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = balance > threshold
	case ">=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = balance >= threshold
	case "<":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = balance < threshold
	case "<=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = balance <= threshold
	case "==":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = balance == threshold
	case "between":
		result = balance >= condition.Min && balance <= condition.Max
		utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "最小值", condition.Min, "最大值", condition.Max, "结果", result)
		return result
	default:
		utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", false, "原因", "不支持的操作符")
		return false
	}

	utils.LogStrategyInfo("[条件判断] 类型: account_balance, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "balance", balance, "threshold", condition.Value, "结果", result)
	return result
}
