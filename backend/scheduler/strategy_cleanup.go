package scheduler

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// StrategyCleanupService 策略清理服务
type StrategyCleanupService struct {
	svcCtx      *svc.ServiceContext
	mu          sync.RWMutex
	running     bool
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	cleanupChan chan struct{}
	interval    time.Duration // 清理间隔
}

// NewStrategyCleanupService 创建策略清理服务
func NewStrategyCleanupService(svcCtx *svc.ServiceContext) *StrategyCleanupService {
	ctx, cancel := context.WithCancel(context.Background())
	return &StrategyCleanupService{
		svcCtx:      svcCtx,
		ctx:         ctx,
		cancel:      cancel,
		cleanupChan: make(chan struct{}, 1),
		interval:    1 * time.Minute,
	}
}

// Start 启动策略清理服务
func (scs *StrategyCleanupService) Start() error {
	scs.mu.Lock()
	defer scs.mu.Unlock()

	if scs.running {
		return fmt.Errorf("策略清理服务已在运行")
	}

	// 重新创建context和channels，确保资源是全新的
	scs.ctx, scs.cancel = context.WithCancel(context.Background())
	scs.cleanupChan = make(chan struct{}, 1)

	scs.running = true
	scs.wg.Add(1)
	go scs.cleanupLoop()

	utils.LogStrategyInfo("策略清理服务已启动")
	return nil
}

// Stop 停止策略清理服务
func (scs *StrategyCleanupService) Stop() error {
	scs.mu.Lock()
	defer scs.mu.Unlock()

	if !scs.running {
		return nil
	}

	scs.running = false

	if scs.cancel != nil {
		scs.cancel()
	}

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		scs.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
	case <-time.After(5 * time.Second):
		utils.LogStrategyError("策略清理服务停止超时")
	}

	utils.LogStrategyInfo("策略清理服务已停止")
	return nil
}

// SetInterval 设置清理间隔
func (scs *StrategyCleanupService) SetInterval(interval time.Duration) {
	scs.mu.Lock()
	defer scs.mu.Unlock()
	scs.interval = interval
}

// GetInterval 获取清理间隔
func (scs *StrategyCleanupService) GetInterval() time.Duration {
	scs.mu.RLock()
	defer scs.mu.RUnlock()
	return scs.interval
}

// ForceCleanup 强制执行清理
func (scs *StrategyCleanupService) ForceCleanup() error {
	scs.mu.RLock()
	if !scs.running {
		scs.mu.RUnlock()
		return fmt.Errorf("策略清理服务未运行")
	}
	scs.mu.RUnlock()

	// 发送清理信号
	select {
	case scs.cleanupChan <- struct{}{}:
		return nil
	default:
		return nil
	}
}

// IsRunning 检查服务是否运行
func (scs *StrategyCleanupService) IsRunning() bool {
	scs.mu.RLock()
	defer scs.mu.RUnlock()
	return scs.running
}

// cleanupLoop 清理循环
func (scs *StrategyCleanupService) cleanupLoop() {
	defer scs.wg.Done()

	ticker := time.NewTicker(scs.interval)
	defer ticker.Stop()

	// 启动时立即执行一次清理
	scs.performCleanup()

	for {
		select {
		case <-ticker.C:
			scs.performCleanup()
		case <-scs.cleanupChan:
			scs.performCleanup()
		case <-scs.ctx.Done():
			return
		}
	}
}

// performCleanup 执行清理操作
func (scs *StrategyCleanupService) performCleanup() {
	// 获取所有项目
	projects, _, err := scs.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{})
	if err != nil {
		utils.LogStrategyError("查询项目失败", "error", err)
		return
	}

	// 获取所有存在的策略ID（包括未启用的策略）
	validStrategyIDs, err := scs.getValidStrategyIDs()
	if err != nil {
		utils.LogStrategyError("获取有效策略ID失败", "error", err)
		return
	}

	// 清理每个项目的无效策略
	cleanedCount := 0
	for _, project := range projects {
		if err := scs.cleanupProjectStrategies(project, validStrategyIDs); err != nil {
			utils.LogStrategyError("清理项目策略失败", "project_name", project.ProjectName, "error", err)
		} else {
			cleanedCount++
		}
	}
}

// getValidStrategyIDs 获取所有有效的策略ID
func (scs *StrategyCleanupService) getValidStrategyIDs() (map[string]bool, error) {
	// 获取所有存在的策略（不管是否启用），只清理不存在的策略
	strategies, err := scs.svcCtx.StrategyModel.GetAllStrategies()
	if err != nil {
		return nil, err
	}

	validIDs := make(map[string]bool)
	for _, strategy := range strategies {
		validIDs[strconv.FormatInt(strategy.ID, 10)] = true
	}

	return validIDs, nil
}

// cleanupProjectStrategies 清理单个项目的策略
func (scs *StrategyCleanupService) cleanupProjectStrategies(project *model.Project, validStrategyIDs map[string]bool) error {
	// 检查项目是否有策略绑定
	if project.StrategiesId == "" {
		return nil // 没有策略绑定，跳过
	}

	// 解析策略ID
	strategyIDs := strings.Split(project.StrategiesId, ",")
	var validIDs []string
	var hasInvalidStrategy bool

	for _, id := range strategyIDs {
		id = strings.TrimSpace(id)
		if id == "" {
			continue
		}

		if validStrategyIDs[id] {
			validIDs = append(validIDs, id)
		} else {
			hasInvalidStrategy = true
		}
	}

	// 如果有无效策略，更新项目
	if hasInvalidStrategy {
		newStrategiesID := strings.Join(validIDs, ",")
		if newStrategiesID != project.StrategiesId {
			updateData := map[string]interface{}{
				"strategies_id": newStrategiesID,
			}

			if err := scs.svcCtx.ProjectModel.MapUpdate(nil, project.Id, updateData); err != nil {
				return fmt.Errorf("更新项目策略失败: %w", err)
			}
		}
	}

	return nil
}
