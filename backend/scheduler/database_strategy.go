package scheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// DatabaseStrategy 数据库策略实现 - 专门负责策略动作执行
type DatabaseStrategy struct {
	strategy *model.Strategy
	svcCtx   *svc.ServiceContext
	app      *application.App
}

// NewDatabaseStrategy 创建数据库策略实例
func NewDatabaseStrategy(strategy *model.Strategy, svcCtx *svc.ServiceContext, app *application.App) *DatabaseStrategy {
	return &DatabaseStrategy{
		strategy: strategy,
		svcCtx:   svcCtx,
		app:      app,
	}
}

// executeStrategyActionsForPromotions 对满足条件的广告执行策略动作
func (ds *DatabaseStrategy) executeStrategyActionsForPromotions(ctx context.Context, project *model.Project, strategy *model.Strategy, promotions []*model.Promotion) error {
	// 解析策略动作
	var actions []map[string]interface{}
	if err := json.Unmarshal([]byte(strategy.Actions), &actions); err != nil {
		return fmt.Errorf("解析策略动作失败: %w", err)
	}

	if len(actions) == 0 {
		utils.LogStrategyInfo("策略没有配置动作", "strategy_name", strategy.Name)
		return nil
	}

	// 对每个满足条件的广告执行动作
	for _, promotion := range promotions {
		utils.LogStrategyInfo("对广告执行策略动作", "promotion_name", promotion.PromotionName, "strategy_name", strategy.Name)

		// 执行每个动作
		for i, action := range actions {
			actionType := action["type"].(string)
			utils.LogStrategyInfo("执行动作", "index", i+1, "type", actionType, "promotion_name", promotion.PromotionName)

			var actionErr error
			switch actionType {
			// 通知动作 - 企业微信通知默认调用即成功
			case "wechat_notification":
				ds.executeNotificationAction(action, project, promotion)
				// 企业微信通知动作不检查错误，默认调用即成功
				actionErr = nil

			// 广告动作（适用于promotion和global策略）
			case "pause_ad":
				actionErr = ds.executePromotionPauseAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行暂停广告动作失败", "error", actionErr.Error())
				}
			case "resume_ad":
				actionErr = ds.executePromotionResumeAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行恢复广告动作失败", "error", actionErr.Error())
				}
			case "delete_ad":
				actionErr = ds.executePromotionDeleteAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行删除广告动作失败", "error", actionErr.Error())
				}
			case "delete_and_create_ad":
				actionErr = ds.executePromotionDeleteAndCreateAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行删除并新建广告动作失败", "error", actionErr.Error())
				}
			case "copy_ad":
				actionErr = ds.executePromotionCopyAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行复制广告动作失败", "error", actionErr.Error())
				}
			case "copy_multiple_ads":
				actionErr = ds.executePromotionCopyMultipleAction(action, promotion)
				if actionErr != nil {
					utils.LogStrategyError("执行复制多个广告动作失败", "error", actionErr.Error())
				}

			// 项目动作（仅适用于project策略，但在广告层面不会执行）
			case "copy_project":
				utils.LogStrategyInfo("项目复制动作应在项目策略中执行，广告级别跳过", "action_type", actionType)
			case "enable_project":
				utils.LogStrategyInfo("项目启用动作应在项目策略中执行，广告级别跳过", "action_type", actionType)
			case "delete_project":
				utils.LogStrategyInfo("项目删除动作应在项目策略中执行，广告级别跳过", "action_type", actionType)

			default:
				utils.LogStrategyInfo("未知的动作类型", "type", actionType)
			}

			// 如果动作执行失败，返回错误
			if actionErr != nil {
				return fmt.Errorf("执行动作失败 [%s]: %w", actionType, actionErr)
			}
		}
	}

	return nil
}

// executeStrategyActionsForAdvertiser 对满足条件的广告主执行策略动作
func (ds *DatabaseStrategy) executeStrategyActionsForAdvertiser(ctx context.Context, advertiser *model.Advertiser, strategy *model.Strategy) error {
	// 解析策略动作
	var actions []map[string]interface{}
	if err := json.Unmarshal([]byte(strategy.Actions), &actions); err != nil {
		return fmt.Errorf("解析策略动作失败: %w", err)
	}

	if len(actions) == 0 {
		utils.LogStrategyInfo("策略没有配置动作", "strategy_name", strategy.Name)
		return nil
	}

	utils.LogStrategyInfo("对广告主执行策略动作", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)

	// 执行每个动作
	for i, action := range actions {
		actionType := action["type"].(string)
		utils.LogStrategyInfo("执行动作", "index", i+1, "type", actionType, "advertiser_name", advertiser.AdvertiserName)

		var actionErr error
		switch actionType {
		// 通知动作 - 企业微信通知默认调用即成功
		case "wechat_notification":
			ds.executeAdvertiserNotificationAction(action, advertiser)
			// 企业微信通知动作不检查错误，默认调用即成功
			actionErr = nil

		// 广告主级别的动作（如果有的话）
		case "pause_advertiser":
			utils.LogStrategyInfo("暂停广告主动作暂未实现", "advertiser_name", advertiser.AdvertiserName)
			// TODO: 实现广告主暂停逻辑

		case "resume_advertiser":
			utils.LogStrategyInfo("恢复广告主动作暂未实现", "advertiser_name", advertiser.AdvertiserName)
			// TODO: 实现广告主恢复逻辑

		default:
			utils.LogStrategyInfo("未知的广告主动作类型", "type", actionType)
		}

		// 如果动作执行失败，返回错误
		if actionErr != nil {
			return fmt.Errorf("执行动作失败 [%s]: %w", actionType, actionErr)
		}
	}

	return nil
}

// executeStrategyActionsForProject 对满足条件的项目执行策略动作
func (ds *DatabaseStrategy) executeStrategyActionsForProject(ctx context.Context, project *model.Project, strategy *model.Strategy) error {
	// 解析策略动作
	var actions []map[string]interface{}
	if err := json.Unmarshal([]byte(strategy.Actions), &actions); err != nil {
		return fmt.Errorf("解析策略动作失败: %w", err)
	}

	if len(actions) == 0 {
		utils.LogStrategyInfo("策略没有配置动作", "strategy_name", strategy.Name)
		return nil
	}

	utils.LogStrategyInfo("对项目执行策略动作", "project_name", project.ProjectName, "strategy_name", strategy.Name)

	// 执行每个动作
	for i, action := range actions {
		actionType := action["type"].(string)
		utils.LogStrategyInfo("执行动作", "index", i+1, "type", actionType, "project_name", project.ProjectName)

		var actionErr error
		switch actionType {
		// 通知动作 - 企业微信通知默认调用即成功
		case "wechat_notification":
			ds.executeProjectNotificationAction(action, project)
			// 企业微信通知动作不检查错误，默认调用即成功
			actionErr = nil

		// 项目级别的动作
		case "copy_project":
			actionErr = ds.executeProjectCopyAction(action, project)
			if actionErr != nil {
				utils.LogStrategyError("执行项目复制动作失败", "error", actionErr.Error())
			}
		case "enable_project":
			actionErr = ds.executeProjectEnableAction(action, project)
			if actionErr != nil {
				utils.LogStrategyError("执行项目启用动作失败", "error", actionErr.Error())
			}
		case "pause_project":
			actionErr = ds.executeProjectPauseAction(action, project)
			if actionErr != nil {
				utils.LogStrategyError("执行项目暂停动作失败", "error", actionErr.Error())
			}
		case "delete_project":
			actionErr = ds.executeProjectDeleteAction(action, project)
			if actionErr != nil {
				utils.LogStrategyError("执行项目删除动作失败", "error", actionErr.Error())
			}

		// 广告动作（项目级别不执行）
		case "pause_ad", "resume_ad", "delete_ad", "delete_and_create_ad", "copy_ad", "copy_multiple_ads":
			utils.LogStrategyInfo("广告动作应在广告策略中执行，项目级别跳过", "action_type", actionType)

		default:
			utils.LogStrategyInfo("未知的项目动作类型", "type", actionType)
		}

		// 如果动作执行失败，返回错误
		if actionErr != nil {
			return fmt.Errorf("执行动作失败 [%s]: %w", actionType, actionErr)
		}
	}

	return nil
}

// ==================== 具体动作实现 ====================

// executeAdvertiserNotificationAction 对广告主执行通知动作
func (ds *DatabaseStrategy) executeAdvertiserNotificationAction(action map[string]interface{}, advertiser *model.Advertiser) error {
	// 从动作配置中获取自定义消息
	customMessage := ""
	if config, ok := action["config"].(map[string]interface{}); ok {
		if msg, ok := config["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 从action.message获取
	if customMessage == "" {
		if msg, ok := action["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 生成广告主的Markdown格式的消息内容
	message := ds.generateAdvertiserMarkdownMessage(customMessage, advertiser)

	// 从数据库获取策略通知邮箱配置
	emails := make([]string, 0)

	// 首先尝试从配置表获取策略通知邮箱
	configService := service.NewConfigService(ds.svcCtx)
	emailResult := configService.GetStrategyNotificationEmail()
	if emailResult.Code == 0 && emailResult.Data != "" {
		emails = append(emails, emailResult.Data)
	} else {
		// 如果配置表中没有，尝试获取系统默认邮箱
		systemEmailResult := configService.GetSystemEmail()
		if systemEmailResult.Code == 0 && systemEmailResult.Data != "" {
			emails = append(emails, systemEmailResult.Data)
		}
	}

	// 如果没有配置邮箱，直接返回，不进行通知
	if len(emails) == 0 {
		return nil
	}

	// 发送企业微信消息
	bot.SendWechatMessageSimple(emails, message)

	// 记录日志
	utils.LogStrategyInfo("发送广告主通知", "strategy_name", ds.strategy.Name, "advertiser_name", advertiser.AdvertiserName, "message", message)
	return nil
}

// executeNotificationAction 对广告执行通知动作
func (ds *DatabaseStrategy) executeNotificationAction(action map[string]interface{}, project *model.Project, promotion *model.Promotion) error {
	// 从动作配置中获取自定义消息
	customMessage := ""
	if config, ok := action["config"].(map[string]interface{}); ok {
		if msg, ok := config["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 从action.message获取
	if customMessage == "" {
		if msg, ok := action["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 生成Markdown格式的丰富消息内容
	message := ds.generateMarkdownMessage(customMessage, project, promotion)

	// 从数据库获取策略通知邮箱配置
	emails := make([]string, 0)

	// 首先尝试从配置表获取策略通知邮箱
	configService := service.NewConfigService(ds.svcCtx)
	emailResult := configService.GetStrategyNotificationEmail()
	if emailResult.Code == 0 && emailResult.Data != "" {
		emails = append(emails, emailResult.Data)
	} else {
		// 如果配置表中没有，尝试获取系统默认邮箱
		systemEmailResult := configService.GetSystemEmail()
		if systemEmailResult.Code == 0 && systemEmailResult.Data != "" {
			emails = append(emails, systemEmailResult.Data)
		}
	}

	// 如果没有配置邮箱，直接返回，不进行通知
	if len(emails) == 0 {
		return nil
	}

	// 发送企业微信消息
	bot.SendWechatMessageSimple(emails, message)

	// 记录日志
	utils.LogStrategyInfo("发送通知", "strategy_name", ds.strategy.Name, "promotion_name", promotion.PromotionName, "message", message)
	return nil
}

// executePromotionPauseAction 执行广告暂停动作
func (ds *DatabaseStrategy) executePromotionPauseAction(action map[string]interface{}, promotion *model.Promotion) error {
	utils.LogStrategyInfo("暂停广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 构建状态映射：promotionId -> status (1表示暂停)
	statusMap := map[string]int{
		promotion.PromotionId: 1, // 1表示暂停
	}

	// 构建请求参数
	req := types.UpdatePromotionStatusReq{
		AccountId:    promotion.AccountId,
		AdvertiserId: promotion.AdvertiserId,
		StatusMap:    statusMap,
	}

	// 调用PromotionService的UpdatePromotionStatus方法
	promotionService := service.NewPromotionService(ds.svcCtx)
	// 设置app属性以便发送操作日志事件
	promotionService.SetApp(ds.app)
	result := promotionService.UpdatePromotionStatus(req)

	if result.Code != 0 {
		return fmt.Errorf("暂停广告失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("广告暂停成功", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)
	return nil
}

// executePromotionResumeAction 执行广告启用动作
func (ds *DatabaseStrategy) executePromotionResumeAction(action map[string]interface{}, promotion *model.Promotion) error {
	utils.LogStrategyInfo("恢复广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 构建状态映射：promotionId -> status (0表示启用)
	statusMap := map[string]int{
		promotion.PromotionId: 0, // 0表示启用
	}

	// 构建请求参数
	req := types.UpdatePromotionStatusReq{
		AccountId:    promotion.AccountId,
		AdvertiserId: promotion.AdvertiserId,
		StatusMap:    statusMap,
	}

	// 调用PromotionService的UpdatePromotionStatus方法
	promotionService := service.NewPromotionService(ds.svcCtx)
	// 设置app属性以便发送操作日志事件
	promotionService.SetApp(ds.app)
	result := promotionService.UpdatePromotionStatus(req)

	if result.Code != 0 {
		return fmt.Errorf("启用广告失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("广告启用成功", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)
	return nil
}

// executePromotionDeleteAction 执行广告删除动作
func (ds *DatabaseStrategy) executePromotionDeleteAction(action map[string]interface{}, promotion *model.Promotion) error {
	utils.LogStrategyInfo("删除广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 构建请求参数
	req := types.DeletePromotionAsyncReq{
		AccountId:    promotion.AccountId,
		AdvertiserId: fmt.Sprintf("%d", promotion.AdvertiserId),
		PromotionIds: []string{promotion.PromotionId}, // 单个广告ID数组
	}

	promotionService := service.NewPromotionService(ds.svcCtx)
	promotionService.SetApp(ds.app)
	result := promotionService.DeletePromotionAsync(req)

	if result.Code != 0 {
		return fmt.Errorf("删除广告失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("广告删除成功", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)
	return nil
}

// executePromotionDeleteAndCreateAction 执行广告删除并新建动作
func (ds *DatabaseStrategy) executePromotionDeleteAndCreateAction(action map[string]interface{}, promotion *model.Promotion) error {
	utils.LogStrategyInfo("删除并新建广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 删除原广告
	deleteReq := types.DeletePromotionAsyncReq{
		AccountId:    promotion.AccountId,
		AdvertiserId: fmt.Sprintf("%d", promotion.AdvertiserId),
		PromotionIds: []string{promotion.PromotionId},
	}

	promotionService := service.NewPromotionService(ds.svcCtx)
	promotionService.SetApp(ds.app)

	// 删除广告
	deleteResult := promotionService.DeletePromotionAsync(deleteReq)
	if deleteResult.Code != 0 {
		return fmt.Errorf("删除广告失败: %s", deleteResult.Msg)
	}

	utils.LogStrategyInfo("原广告删除成功", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 复制广告到同一个项目
	newPromotionName := promotion.PromotionName + "_copy"
	return ds.copyPromotionWithVideoMaterial(promotion, newPromotionName)
}

// executePromotionCopyAction 执行广告复制动作
func (ds *DatabaseStrategy) executePromotionCopyAction(action map[string]interface{}, promotion *model.Promotion) error {
	utils.LogStrategyInfo("复制广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId)

	// 生成新广告名称
	newPromotionName := promotion.PromotionName + "_copy"
	return ds.copyPromotionWithVideoMaterial(promotion, newPromotionName)
}

// executePromotionCopyMultipleAction 执行广告复制多个动作
func (ds *DatabaseStrategy) executePromotionCopyMultipleAction(action map[string]interface{}, promotion *model.Promotion) error {
	// 获取复制数量配置
	copyCount := 1 // 默认复制1个
	if config, ok := action["config"].(map[string]interface{}); ok {
		if count, ok := config["copy_count"].(float64); ok && count > 0 {
			copyCount = int(count)
		}
	}

	// 限制复制数量
	if copyCount > 10 {
		copyCount = 10
		utils.LogStrategyInfo("复制数量超过限制，自动调整为10个", "promotion_name", promotion.PromotionName)
	}

	utils.LogStrategyInfo("复制多个广告", "promotion_name", promotion.PromotionName, "promotion_id", promotion.PromotionId, "copy_count", copyCount)

	// 批量复制广告
	successCount := 0
	for i := 0; i < copyCount; i++ {
		newPromotionName := fmt.Sprintf("%s_copy_%d", promotion.PromotionName, i+1)
		if err := ds.copyPromotionWithVideoMaterial(promotion, newPromotionName); err != nil {
			utils.LogStrategyError("广告复制失败", "old_name", promotion.PromotionName, "new_name", newPromotionName, "index", i+1, "error", err.Error())
		} else {
			successCount++
			utils.LogStrategyInfo("广告复制成功", "old_name", promotion.PromotionName, "new_name", newPromotionName, "index", i+1)
		}
	}

	if successCount == 0 {
		return fmt.Errorf("所有广告复制都失败")
	}

	utils.LogStrategyInfo("广告批量复制完成", "promotion_name", promotion.PromotionName, "success_count", successCount, "total_count", copyCount)
	return nil
}

// copyPromotionWithVideoMaterial 复制广告并包含视频素材信息的公共方法
func (ds *DatabaseStrategy) copyPromotionWithVideoMaterial(promotion *model.Promotion, newPromotionName string) error {
	// 先获取广告详情以获取视频素材信息
	promotionService := service.NewPromotionService(ds.svcCtx)
	promotionService.SetApp(ds.app)

	// 获取广告详情
	detailReq := types.GetPromotionInfoReq{
		AccountId:             promotion.AccountId,
		AdvertiserId:          promotion.AdvertiserId,
		PromotionIds:          []string{promotion.PromotionId},
		NeedInvisibleMaterial: false,
	}

	detailResult := promotionService.GetPromotionInfo(detailReq)
	if detailResult.Code != 0 {
		return fmt.Errorf("获取广告详情失败: %s", detailResult.Msg)
	}

	// 从详情中提取视频素材信息
	var videoMaterialInfo []bot.VideoMaterialInfo
	if detailResult.Data.Data != nil && len(detailResult.Data.Data) > 0 {
		if promotionData, exists := detailResult.Data.Data[promotion.PromotionId]; exists {
			if len(promotionData.MaterialGroup.VideoMaterialInfo) > 0 {
				// 映射视频素材信息
				for _, sourceVideo := range promotionData.MaterialGroup.VideoMaterialInfo {
					video := bot.VideoMaterialInfo{
						ImageMode: sourceVideo.ImageMode,
						ImageInfo: func() []bot.ImageInfo {
							var images []bot.ImageInfo
							for _, img := range sourceVideo.ImageInfo {
								images = append(images, bot.ImageInfo{
									Height:  img.Height,
									Width:   img.Width,
									WebURI:  img.WebURI,
									SignURL: img.SignURL,
								})
							}
							return images
						}(),
						VideoInfo: bot.VideoInfo{
							VideoID:         sourceVideo.VideoInfo.VideoID,
							Status:          sourceVideo.VideoInfo.Status,
							VideoDuration:   sourceVideo.VideoInfo.VideoDuration,
							InitialSize:     sourceVideo.VideoInfo.InitialSize,
							VideoUnique:     sourceVideo.VideoInfo.VideoUnique,
							FileMD5:         sourceVideo.VideoInfo.FileMD5,
							ThumbHeight:     sourceVideo.VideoInfo.ThumbHeight,
							ThumbWidth:      sourceVideo.VideoInfo.ThumbWidth,
							ThumbURI:        sourceVideo.VideoInfo.ThumbURI,
							OriginalFileURI: sourceVideo.VideoInfo.OriginalFileURI,
							Duration:        sourceVideo.VideoInfo.Duration,
							ErrorDesc:       sourceVideo.VideoInfo.ErrorDesc,
							UserReference:   sourceVideo.VideoInfo.UserReference,
							Width:           sourceVideo.VideoInfo.Width,
							Height:          sourceVideo.VideoInfo.Height,
							VID:             sourceVideo.VideoInfo.VID,
							UploadID:        sourceVideo.VideoInfo.UploadID,
							CoverURI:        sourceVideo.VideoInfo.CoverURI,
							Codec:           sourceVideo.VideoInfo.Codec,
							Bitrate:         sourceVideo.VideoInfo.Bitrate,
						},
						CopyMode: sourceVideo.CopyMode,
					}
					videoMaterialInfo = append(videoMaterialInfo, video)
				}
			}
		}
	}

	copyReq := types.CopyPromotionReq{
		AccountId:         promotion.AccountId,
		AdvertiserId:      promotion.AdvertiserId,
		SourcePromotionId: promotion.PromotionId,
		NewPromotionName:  newPromotionName,
		NewProjectId:      *promotion.ProjectId, // 使用原项目ID
		VideoMaterialInfo: videoMaterialInfo,
	}

	result := promotionService.CopyPromotion(copyReq)
	utils.LogStrategyInfo("复制广告返回", "result", result)

	// 检查外层结果
	if result.Code != 0 {
		return fmt.Errorf("复制广告失败: %s", result.Msg)
	}

	// {"code":0,"msg":"success","data":{"code":50100,"data":{...},"msg":"当前项目下最多创建一个广告"}}
	// 需要检查 result.Data 中的内层 code 字段
	if result.Data.Code != 0 {
		return fmt.Errorf("复制广告失败: %s", result.Data.Msg)
	}

	utils.LogStrategyInfo("广告复制成功", "old_name", promotion.PromotionName, "new_name", newPromotionName)
	return nil
}

// ==================== 消息生成工具 ====================

// generateMarkdownMessage 生成Markdown格式的消息内容
func (ds *DatabaseStrategy) generateMarkdownMessage(customMessage string, project *model.Project, promotion *model.Promotion) string {
	// 获取当前时间
	now := time.Now()
	timeStr := now.Format("2006-01-02 15:04:05")

	// 构建Markdown消息
	var message strings.Builder

	// 标题 - 使用更醒目的样式
	message.WriteString("## 🚨 策略触发通知\n\n")
	message.WriteString(fmt.Sprintf("**⏰ 触发时间：** %s \n\n", timeStr))

	// 策略信息
	message.WriteString("### 📋 策略信息\n")
	message.WriteString(fmt.Sprintf("- **策略名称：** `%s`\n", ds.strategy.Name))
	message.WriteString(fmt.Sprintf("- **策略类型：** 广告策略\n"))
	message.WriteString(fmt.Sprintf("- **策略描述：** %s\n", ds.strategy.Description))

	// 项目信息
	message.WriteString("\n### 📂 项目信息\n")
	message.WriteString(fmt.Sprintf("- **项目名称：** `%s`\n", project.ProjectName))
	message.WriteString(fmt.Sprintf("- **项目ID：** `%s`\n", project.ProjectId))
	message.WriteString(fmt.Sprintf("- **项目状态：** %s\n", ds.getProjectStatusText(project.ProjectStatus)))
	if project.ProjectStatusName != "" {
		message.WriteString(fmt.Sprintf("- **状态详情：** %s\n", project.ProjectStatusName))
	}
	if project.CampaignBudget != "" {
		message.WriteString(fmt.Sprintf("- **项目预算：** ¥%s\n", project.CampaignBudget))
	}
	if project.ProjectBid != "" {
		message.WriteString(fmt.Sprintf("- **项目出价：** ¥%s\n", project.ProjectBid))
	}

	// 广告信息
	message.WriteString("\n### 📱 广告信息\n")
	message.WriteString(fmt.Sprintf("- **广告名称：** `%s`\n", promotion.PromotionName))
	message.WriteString(fmt.Sprintf("- **广告ID：** `%s`\n", promotion.PromotionId))
	message.WriteString(fmt.Sprintf("- **广告状态：** %s\n", ds.getPromotionStatusText(&promotion.PromotionStatus)))
	if promotion.PromotionStatusName != "" {
		message.WriteString(fmt.Sprintf("- **状态详情：** %s\n", promotion.PromotionStatusName))
	}
	if promotion.AdBudget != "" {
		message.WriteString(fmt.Sprintf("- **广告预算：** ¥%s\n", promotion.AdBudget))
	}
	if promotion.AdBid != "" {
		message.WriteString(fmt.Sprintf("- **广告出价：** ¥%s\n", promotion.AdBid))
	}
	if promotion.LandingTypeName != "" {
		message.WriteString(fmt.Sprintf("- **落地页类型：** %s\n", promotion.LandingTypeName))
	}
	if promotion.AdPricingName != "" {
		message.WriteString(fmt.Sprintf("- **定价方式：** %s\n", promotion.AdPricingName))
	}
	if promotion.DeliverySceneName != "" {
		message.WriteString(fmt.Sprintf("- **投放场景：** %s\n", promotion.DeliverySceneName))
	}

	// 数据信息 - 更详细的数据展示
	message.WriteString("\n### 📊 数据信息\n")

	// 基础数据
	if promotion.StatCost != "" {
		message.WriteString(fmt.Sprintf("- **💰 消费：** ¥%s\n", promotion.StatCost))
	}
	if promotion.ShowCnt != "" {
		message.WriteString(fmt.Sprintf("- **👁️ 展现：** %s\n", promotion.ShowCnt))
	}
	if promotion.ClickCnt != "" {
		message.WriteString(fmt.Sprintf("- **🖱️ 点击：** %s\n", promotion.ClickCnt))
	}
	if promotion.ConvertCnt != "" {
		message.WriteString(fmt.Sprintf("- **🎯 转化：** %s\n", promotion.ConvertCnt))
	}

	// 比率数据
	if promotion.Ctr > 0 {
		message.WriteString(fmt.Sprintf("- **📈 点击率：** %.2f%%\n", promotion.Ctr))
	}
	if promotion.ConversionRate > 0 {
		message.WriteString(fmt.Sprintf("- **📊 转化率：** %.2f%%\n", promotion.ConversionRate))
	}

	// 成本数据
	if promotion.CpcPlatform != "" {
		message.WriteString(fmt.Sprintf("- **💸 平均点击成本：** ¥%s\n", promotion.CpcPlatform))
	}
	if promotion.ConversionCost != "" {
		message.WriteString(fmt.Sprintf("- **💳 转化成本：** ¥%s\n", promotion.ConversionCost))
	}
	if promotion.CpmPlatform != "" {
		message.WriteString(fmt.Sprintf("- **📊 千次展现价格：** ¥%s\n", promotion.CpmPlatform))
	}

	// 时间信息
	if promotion.StartTime != "" {
		message.WriteString(fmt.Sprintf("- **🕐 开始时间：** %s\n", promotion.StartTime))
	}
	if promotion.EndTime != "" {
		message.WriteString(fmt.Sprintf("- **🕙 结束时间：** %s\n", promotion.EndTime))
	}
	if promotion.ModifyTime != "" {
		message.WriteString(fmt.Sprintf("- **🔄 修改时间：** %s\n", promotion.ModifyTime))
	}

	// 自定义消息
	if customMessage != "" {
		message.WriteString("\n### 💬 详细说明\n")
		message.WriteString(fmt.Sprintf("> %s\n", customMessage))
	}

	// 添加分隔线和提示
	message.WriteString("\n---\n")
	message.WriteString("*此通知由巨量引擎账户管理系统自动发送，请及时处理相关策略触发事件。*\n")

	return message.String()
}

// generateAdvertiserMarkdownMessage 生成广告主的Markdown格式的消息内容
func (ds *DatabaseStrategy) generateAdvertiserMarkdownMessage(customMessage string, advertiser *model.Advertiser) string {
	// 获取当前时间
	now := time.Now()
	timeStr := now.Format("2006-01-02 15:04:05")

	// 构建Markdown消息
	var message strings.Builder

	// 标题
	message.WriteString("## 🚨 广告主策略触发通知\n\n")
	message.WriteString(fmt.Sprintf("**⏰ 触发时间：** %s \n\n", timeStr))

	// 策略信息
	message.WriteString("### 📋 策略信息\n")
	message.WriteString(fmt.Sprintf("- **策略名称：** `%s`\n", ds.strategy.Name))
	message.WriteString(fmt.Sprintf("- **策略类型：** 广告主策略\n"))
	message.WriteString(fmt.Sprintf("- **策略描述：** %s\n", ds.strategy.Description))

	// 广告主信息
	message.WriteString("\n### 🏢 广告主信息\n")
	message.WriteString(fmt.Sprintf("- **广告主名称：** `%s`\n", advertiser.AdvertiserName))
	message.WriteString(fmt.Sprintf("- **广告主ID：** `%d`\n", advertiser.AdvertiserId))
	message.WriteString(fmt.Sprintf("- **广告主状态：** %s\n", ds.getAdvertiserStatusText(advertiser.AdvertiserStatus)))
	if advertiser.AdvertiserStatusName != "" {
		message.WriteString(fmt.Sprintf("- **状态详情：** %s\n", advertiser.AdvertiserStatusName))
	}
	if advertiser.AdvertiserRoleName != "" {
		message.WriteString(fmt.Sprintf("- **广告主角色：** %s\n", advertiser.AdvertiserRoleName))
	}
	if advertiser.GroupName != "" {
		message.WriteString(fmt.Sprintf("- **分组名称：** %s\n", advertiser.GroupName))
	}
	if advertiser.AdvertiserRemark != "" {
		message.WriteString(fmt.Sprintf("- **备注信息：** %s\n", advertiser.AdvertiserRemark))
	}

	// 账户信息
	message.WriteString("\n### 💰 账户信息\n")
	if advertiser.AdvertiserBalance != "" {
		message.WriteString(fmt.Sprintf("- **💰 账户余额：** ¥%s\n", advertiser.AdvertiserBalance))
	}
	if advertiser.AdvertiserValidBalance != "" {
		message.WriteString(fmt.Sprintf("- **✅ 有效余额：** ¥%s\n", advertiser.AdvertiserValidBalance))
	}
	if advertiser.AdvertiserGrantBalanceValid != "" {
		message.WriteString(fmt.Sprintf("- **🎁 赠送余额：** ¥%s\n", advertiser.AdvertiserGrantBalanceValid))
	}
	if advertiser.AdvertiserNotGrantBalanceValid != "" {
		message.WriteString(fmt.Sprintf("- **💳 非赠送余额：** ¥%s\n", advertiser.AdvertiserNotGrantBalanceValid))
	}

	// 数据信息
	message.WriteString("\n### 📊 数据信息\n")
	if advertiser.StatCost != "" {
		message.WriteString(fmt.Sprintf("- **💰 消费：** ¥%s\n", advertiser.StatCost))
	}
	if advertiser.ShowCnt != "" {
		message.WriteString(fmt.Sprintf("- **👁️ 展现：** %s\n", advertiser.ShowCnt))
	}
	if advertiser.ClickCnt != "" {
		message.WriteString(fmt.Sprintf("- **🖱️ 点击：** %s\n", advertiser.ClickCnt))
	}
	if advertiser.ConvertCnt != "" {
		message.WriteString(fmt.Sprintf("- **🎯 转化：** %s\n", advertiser.ConvertCnt))
	}

	// 比率数据
	if advertiser.Ctr != "" {
		message.WriteString(fmt.Sprintf("- **📈 点击率：** %s%%\n", advertiser.Ctr))
	}
	if advertiser.ConversionRate != "" {
		message.WriteString(fmt.Sprintf("- **📊 转化率：** %s%%\n", advertiser.ConversionRate))
	}

	// 成本数据
	if advertiser.ConversionCost != "" {
		message.WriteString(fmt.Sprintf("- **💳 转化成本：** ¥%s\n", advertiser.ConversionCost))
	}
	if advertiser.CpmPlatform != "" {
		message.WriteString(fmt.Sprintf("- **📊 千次展现价格：** ¥%s\n", advertiser.CpmPlatform))
	}

	// 自定义消息
	if customMessage != "" {
		message.WriteString("\n### 💬 详细说明\n")
		message.WriteString(fmt.Sprintf("> %s\n", customMessage))
	}

	// 添加分隔线和提示
	message.WriteString("\n---\n")
	message.WriteString("*此通知由巨量引擎账户管理系统自动发送，请及时处理相关策略触发事件。*\n")

	return message.String()
}

// getAdvertiserStatusText 获取广告主状态文本
func (ds *DatabaseStrategy) getAdvertiserStatusText(status int) string {
	switch status {
	case 1:
		return "正常"
	case 2:
		return "暂停"
	case 3:
		return "删除"
	default:
		return "未知状态"
	}
}

// getPromotionStatusText 获取广告状态文本
func (ds *DatabaseStrategy) getPromotionStatusText(status *int) string {
	if status == nil {
		return "未知"
	}

	// 巨量引擎API返回的状态映射
	switch *status {
	case constant.PromotionStatus_Active:
		return constant.PromotionStatusName_Active
	case constant.PromotionStatus_Paused:
		return constant.PromotionStatusName_Paused
	case constant.PromotionStatus_AuditFailed:
		return constant.PromotionStatusName_AuditFailed
	case constant.PromotionStatus_InsufficientFunds:
		return constant.PromotionStatusName_InsufficientFunds
	case constant.PromotionStatus_AuditPending:
		return constant.PromotionStatusName_AuditPending
	case constant.PromotionStatus_Terminated:
		return constant.PromotionStatusName_Terminated
	default:
		return "未知状态"
	}
}

// getActionDescription 获取动作描述
func (ds *DatabaseStrategy) getActionDescription(actionType string, action map[string]interface{}) string {
	switch actionType {
	case "wechat_notification":
		if config, ok := action["config"].(map[string]interface{}); ok {
			if msg, ok := config["message"].(string); ok && msg != "" {
				return fmt.Sprintf("发送企业微信通知: %s", msg)
			}
		}
		return "发送企业微信通知"
	case "pause_ad":
		return "暂停广告"
	case "resume_ad":
		return "启用广告"
	case "delete_ad":
		return "删除广告"
	case "delete_and_create_ad":
		return "删除并新建广告"
	case "copy_ad":
		return "复制广告"
	case "copy_multiple_ads":
		if config, ok := action["config"].(map[string]interface{}); ok {
			if count, ok := config["copy_count"].(float64); ok && count > 0 {
				return fmt.Sprintf("复制%d个广告", int(count))
			}
		}
		return "复制多个广告"
	case "copy_project":
		return "复制项目"
	case "enable_project":
		return "启用项目"
	case "pause_project":
		return "暂停项目"
	case "delete_project":
		return "删除项目"
	default:
		return fmt.Sprintf("未知动作: %s", actionType)
	}
}

// ==================== 项目动作实现 ====================

// executeProjectNotificationAction 对项目执行通知动作
func (ds *DatabaseStrategy) executeProjectNotificationAction(action map[string]interface{}, project *model.Project) error {
	// 从动作配置中获取自定义消息
	customMessage := ""
	if config, ok := action["config"].(map[string]interface{}); ok {
		if msg, ok := config["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 从action.message获取
	if customMessage == "" {
		if msg, ok := action["message"].(string); ok && msg != "" {
			customMessage = msg
		}
	}

	// 生成项目的Markdown格式的消息内容
	message := ds.generateProjectMarkdownMessage(customMessage, project)

	// 从数据库获取策略通知邮箱配置
	emails := make([]string, 0)

	// 首先尝试从配置表获取策略通知邮箱
	configService := service.NewConfigService(ds.svcCtx)
	emailResult := configService.GetStrategyNotificationEmail()
	if emailResult.Code == 0 && emailResult.Data != "" {
		emails = append(emails, emailResult.Data)
	} else {
		// 如果配置表中没有，尝试获取系统默认邮箱
		systemEmailResult := configService.GetSystemEmail()
		if systemEmailResult.Code == 0 && systemEmailResult.Data != "" {
			emails = append(emails, systemEmailResult.Data)
		}
	}

	// 如果没有配置邮箱，直接返回，不进行通知
	if len(emails) == 0 {
		return nil
	}

	// 发送企业微信消息
	bot.SendWechatMessageSimple(emails, message)

	// 记录日志
	utils.LogStrategyInfo("发送项目通知", "strategy_name", ds.strategy.Name, "project_name", project.ProjectName, "message", message)
	return nil
}

// executeProjectCopyAction 执行项目复制动作
func (ds *DatabaseStrategy) executeProjectCopyAction(action map[string]interface{}, project *model.Project) error {
	utils.LogStrategyInfo("复制项目", "project_name", project.ProjectName, "project_id", project.ProjectId)

	// 生成新项目名称
	newProjectName := project.ProjectName + "_copy"

	// 构建复制项目请求
	copyReq := types.CopyProjectReq{
		AccountId:          project.AccountId,
		SourceProjectID:    project.ProjectId,
		SourceAdvertiserID: project.AdvertiserId, // 源项目广告主ID
		AdvertiserID:       project.AdvertiserId, // 目标广告主ID（复制到同一个广告主下）
		NewProjectName:     newProjectName,
		CopyCount:          1, // 默认复制1个
	}

	// 调用ProjectService的CopyProject方法
	projectService := service.NewProjectService(ds.svcCtx)
	projectService.SetApp(ds.app)
	result := projectService.CopyProject(copyReq)

	if result.Code != 0 {
		return fmt.Errorf("复制项目失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("项目复制成功", "old_name", project.ProjectName, "new_name", newProjectName)
	return nil
}

// executeProjectEnableAction 执行项目启用动作
func (ds *DatabaseStrategy) executeProjectEnableAction(action map[string]interface{}, project *model.Project) error {
	utils.LogStrategyInfo("启用项目", "project_name", project.ProjectName, "project_id", project.ProjectId)

	statusMap := map[string]int{
		project.ProjectId: 0, // 0表示启用
	}

	req := types.UpdateProjectStatusReq{
		AccountId:    project.AccountId,
		AdvertiserId: project.AdvertiserId,
		StatusMap:    statusMap,
	}

	projectService := service.NewProjectService(ds.svcCtx)
	projectService.SetApp(ds.app)
	result := projectService.UpdateProjectStatus(req)

	if result.Code != 0 {
		return fmt.Errorf("启用项目失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("项目启用成功", "project_name", project.ProjectName, "project_id", project.ProjectId)
	return nil
}

// executeProjectDeleteAction 执行项目删除动作
func (ds *DatabaseStrategy) executeProjectDeleteAction(action map[string]interface{}, project *model.Project) error {
	utils.LogStrategyInfo("删除项目", "project_name", project.ProjectName, "project_id", project.ProjectId)

	// 构建删除项目请求
	deleteReq := types.DeleteProjectAsyncReq{
		AccountId:    project.AccountId,
		AdvertiserId: fmt.Sprintf("%d", project.AdvertiserId),
		ProjectIds:   []string{project.ProjectId},
	}

	// 调用ProjectService的DeleteProjectAsync方法
	projectService := service.NewProjectService(ds.svcCtx)
	projectService.SetApp(ds.app)
	result := projectService.DeleteProjectAsync(deleteReq)

	if result.Code != 0 {
		return fmt.Errorf("删除项目失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("项目删除成功", "project_name", project.ProjectName, "project_id", project.ProjectId)
	return nil
}

// executeProjectPauseAction 执行项目暂停动作
func (ds *DatabaseStrategy) executeProjectPauseAction(action map[string]interface{}, project *model.Project) error {
	utils.LogStrategyInfo("暂停项目", "project_name", project.ProjectName, "project_id", project.ProjectId)

	statusMap := map[string]int{
		project.ProjectId: 1, // 1表示暂停
	}

	req := types.UpdateProjectStatusReq{
		AccountId:    project.AccountId,
		AdvertiserId: project.AdvertiserId,
		StatusMap:    statusMap,
	}

	projectService := service.NewProjectService(ds.svcCtx)
	projectService.SetApp(ds.app)
	result := projectService.UpdateProjectStatus(req)

	if result.Code != 0 {
		return fmt.Errorf("暂停项目失败: %s", result.Msg)
	}

	utils.LogStrategyInfo("项目暂停成功", "project_name", project.ProjectName, "project_id", project.ProjectId)
	return nil
}

// generateProjectMarkdownMessage 生成项目的Markdown格式的消息内容
func (ds *DatabaseStrategy) generateProjectMarkdownMessage(customMessage string, project *model.Project) string {
	// 获取当前时间
	now := time.Now()
	timeStr := now.Format("2006-01-02 15:04:05")

	// 构建Markdown消息
	var message strings.Builder

	// 标题
	message.WriteString("## 🚨 项目策略触发通知\n\n")
	message.WriteString(fmt.Sprintf("**⏰ 触发时间：** %s\n\n", timeStr))

	// 策略信息
	message.WriteString("### 📋 策略信息\n")
	message.WriteString(fmt.Sprintf("- **策略名称：** `%s`\n", ds.strategy.Name))
	message.WriteString(fmt.Sprintf("- **策略类型：** 项目策略\n"))
	message.WriteString(fmt.Sprintf("- **策略描述：** %s\n", ds.strategy.Description))

	// 项目信息
	message.WriteString("\n### 📂 项目信息\n")
	message.WriteString(fmt.Sprintf("- **项目名称：** `%s`\n", project.ProjectName))
	message.WriteString(fmt.Sprintf("- **项目ID：** `%s`\n", project.ProjectId))
	message.WriteString(fmt.Sprintf("- **项目状态：** %s\n", ds.getProjectStatusText(project.ProjectStatus)))
	if project.ProjectStatusName != "" {
		message.WriteString(fmt.Sprintf("- **状态详情：** %s\n", project.ProjectStatusName))
	}
	if project.CampaignBudget != "" {
		message.WriteString(fmt.Sprintf("- **项目预算：** ¥%s\n", project.CampaignBudget))
	}
	if project.ProjectBid != "" {
		message.WriteString(fmt.Sprintf("- **项目出价：** ¥%s\n", project.ProjectBid))
	}

	// 数据信息
	message.WriteString("\n### 📊 数据信息\n")
	message.WriteString(fmt.Sprintf("- **💰 消费：** ¥%s\n", project.StatCost))
	message.WriteString(fmt.Sprintf("- **👁️ 展现：** %s\n", project.ShowCnt))
	message.WriteString(fmt.Sprintf("- **🖱️ 点击：** %s\n", project.ClickCnt))
	message.WriteString(fmt.Sprintf("- **🎯 转化：** %s\n", project.ConvertCnt))
	message.WriteString(fmt.Sprintf("- **💳 转化成本：** ¥%s\n", project.ConversionCost))
	message.WriteString(fmt.Sprintf("- **📊 转化率：** %.2f%%\n", project.ConversionRate))
	message.WriteString(fmt.Sprintf("- **📈 点击率：** %.2f%%\n", project.Ctr))
	message.WriteString(fmt.Sprintf("- 📊 千次展现价格：** ¥%s\n", project.CpmPlatform))

	// 自定义消息
	if customMessage != "" {
		message.WriteString("\n### 💬 详细说明\n")
		message.WriteString(fmt.Sprintf("> %s\n", customMessage))
	}

	// 添加分隔线和提示
	message.WriteString("\n---\n")
	message.WriteString("*此通知由巨量引擎账户管理系统自动发送，请及时处理相关策略触发事件。*\n")

	return message.String()
}

// getProjectStatusText 获取项目状态文本
func (ds *DatabaseStrategy) getProjectStatusText(status int) string {
	switch status {
	case 0:
		return "启用"
	case 1:
		return "禁用"
	default:
		return "未知状态"
	}
}
