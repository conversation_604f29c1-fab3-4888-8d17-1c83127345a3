package scheduler

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// StrategyExecutor 策略执行器
type StrategyExecutor struct {
	svcCtx                *svc.ServiceContext
	promotionQueryService *PromotionQueryService
	conditionEvaluator    *ConditionEvaluator
}

// NewStrategyExecutor 创建策略执行器
func NewStrategyExecutor(svcCtx *svc.ServiceContext) *StrategyExecutor {
	return &StrategyExecutor{
		svcCtx:                svcCtx,
		promotionQueryService: NewPromotionQueryService(svcCtx),
		conditionEvaluator:    NewConditionEvaluator(svcCtx),
	}
}

// ExecuteStrategy 根据策略类型执行策略
func (se *StrategyExecutor) ExecuteStrategy(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) error {
	utils.LogStrategyInfo("开始执行策略",
		"strategy_type", string(target.Type),
		"binding_type", string(target.BindingType),
		"binding_id", target.BindingID,
		"target_id", target.TargetID,
		"strategy_name", strategy.Name)

	startTime := time.Now()
	var err error
	var conditionMet bool
	var actionCompleted bool
	var errorMessage string
	var targetName string

	switch target.Type {
	case StrategyTypeAdvertiser:
		err, conditionMet, actionCompleted, errorMessage, targetName = se.executeAdvertiserStrategy(ctx, target, strategy)
	case StrategyTypeProject:
		err, conditionMet, actionCompleted, errorMessage, targetName = se.executeProjectStrategy(ctx, target, strategy)
	case StrategyTypePromotion:
		err, conditionMet, actionCompleted, errorMessage, targetName = se.executePromotionStrategy(ctx, target, strategy)
	case StrategyTypeGlobal:
		err, conditionMet, actionCompleted, errorMessage, targetName = se.executeGlobalStrategy(ctx, target, strategy)
	default:
		err = fmt.Errorf("未知的策略类型: %s", target.Type)
		conditionMet = false
		actionCompleted = false
		errorMessage = err.Error()
	}

	duration := time.Since(startTime).Milliseconds()

	// 记录策略执行日志
	go se.logStrategyExecutionWithTarget(
		strategy.ID,
		strategy.Name,
		target,
		targetName,
		strategy.Conditions,
		conditionMet,
		strategy.Actions,
		"", // actionDetails is not available here, so pass an empty string
		actionCompleted,
		errorMessage,
		startTime,
		duration,
	)

	return err
}

// logStrategyExecution 记录策略执行日志
func (se *StrategyExecutor) logStrategyExecution(
	strategyID int64,
	strategyName string,
	targetType string,
	targetID string,
	targetName string,
	conditions string,
	conditionMet bool,
	actions string,
	actionCompleted bool,
	errorMessage string,
	executionTime time.Time,
	duration int64,
) {
	log := &model.StrategyLog{
		StrategyID:      strategyID,
		StrategyName:    strategyName,
		TargetType:      targetType,
		TargetID:        targetID,
		TargetName:      targetName,
		AdvertiserName:  "",
		ProjectName:     "",
		PromotionName:   "",
		Conditions:      conditions,
		ConditionMet:    conditionMet,
		Actions:         actions,
		ActionCompleted: actionCompleted,
		ErrorMessage:    errorMessage,
		ExecutionTime:   executionTime,
		Duration:        duration,
	}

	// 根据目标类型填充相应的名称字段
	switch targetType {
	case "advertiser":
		log.AdvertiserName = targetName
	case "project":
		log.ProjectName = targetName
		// 获取项目所属的广告主名称
		if project, err := se.svcCtx.ProjectModel.GetByProjectId(targetID); err == nil && project != nil {
			if advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId); err == nil && advertiser != nil {
				log.AdvertiserName = advertiser.AdvertiserName
			}
		}
	case "promotion":
		log.PromotionName = targetName
		// 获取广告所属的项目和广告主名称
		if promotion, err := se.svcCtx.PromotionModel.GetByPromotionId(targetID); err == nil && promotion != nil {
			if promotion.ProjectId != nil {
				if project, err := se.svcCtx.ProjectModel.GetByProjectId(*promotion.ProjectId); err == nil && project != nil {
					log.ProjectName = project.ProjectName
					if advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId); err == nil && advertiser != nil {
						log.AdvertiserName = advertiser.AdvertiserName
					}
				}
			}
		}
	case "global":
		// 全局策略需要根据绑定类型来填充名称字段
		// 这里我们需要从策略执行目标中获取绑定信息
		// 由于logStrategyExecution方法没有传递绑定信息，我们需要通过其他方式获取
		// 暂时使用targetName作为主要显示名称，具体字段填充在策略执行时处理
		log.TargetName = targetName
	}

	if err := se.svcCtx.StrategyLogModel.Create(log); err != nil {
		utils.LogStrategyError("记录策略执行日志失败", "error", err.Error())
	}
}

// logStrategyExecutionWithTarget 记录策略执行日志（带目标信息）
func (se *StrategyExecutor) logStrategyExecutionWithTarget(
	strategyID int64,
	strategyName string,
	target *StrategyExecutionTarget,
	targetName string,
	conditions string,
	conditionMet bool,
	actions string,
	actionDetails string,
	actionCompleted bool,
	errorMessage string,
	executionTime time.Time,
	duration int64,
) {
	log := &model.StrategyLog{
		StrategyID:      strategyID,
		StrategyName:    strategyName,
		TargetType:      string(target.Type),
		TargetID:        target.TargetID,
		TargetName:      targetName,
		AdvertiserName:  "",
		ProjectName:     "",
		PromotionName:   "",
		Conditions:      conditions,
		ConditionMet:    conditionMet,
		Actions:         actions,
		ActionDetails:   actionDetails,
		ActionCompleted: actionCompleted,
		ErrorMessage:    errorMessage,
		ExecutionTime:   executionTime,
		Duration:        duration,
	}

	// 根据目标类型填充相应的名称字段
	switch target.Type {
	case StrategyTypeAdvertiser:
		log.AdvertiserName = targetName
	case StrategyTypeProject:
		log.ProjectName = targetName
		// 获取项目所属的广告主名称
		if project, err := se.svcCtx.ProjectModel.GetByProjectId(target.TargetID); err == nil && project != nil {
			if advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId); err == nil && advertiser != nil {
				log.AdvertiserName = advertiser.AdvertiserName
			}
		}
	case StrategyTypePromotion:
		log.PromotionName = targetName
		// 获取广告所属的项目和广告主名称
		if promotion, err := se.svcCtx.PromotionModel.GetByPromotionId(target.TargetID); err == nil && promotion != nil {
			if promotion.ProjectId != nil {
				if project, err := se.svcCtx.ProjectModel.GetByProjectId(*promotion.ProjectId); err == nil && project != nil {
					log.ProjectName = project.ProjectName
					if advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId); err == nil && advertiser != nil {
						log.AdvertiserName = advertiser.AdvertiserName
					}
				}
			}
		}
	case StrategyTypeGlobal:
		// 全局策略根据绑定类型填充名称字段
		switch target.BindingType {
		case BindingTypeAdvertiser:
			log.AdvertiserName = target.BindingName
			// 全局策略绑定到广告主时，目标名称显示为"全局策略"
			log.TargetName = "全局策略"
		case BindingTypeProject:
			log.ProjectName = target.BindingName
			// 获取项目所属的广告主名称
			if project, err := se.svcCtx.ProjectModel.GetByProjectId(target.BindingID); err == nil && project != nil {
				if advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId); err == nil && advertiser != nil {
					log.AdvertiserName = advertiser.AdvertiserName
				}
			}
			// 全局策略绑定到项目时，目标名称显示为"全局策略"
			log.TargetName = "全局策略"
		}
	}

	if err := se.svcCtx.StrategyLogModel.Create(log); err != nil {
		utils.LogStrategyError("记录策略执行日志失败", "error", err.Error())
	}
}

// executeAdvertiserStrategy 执行广告主策略
func (se *StrategyExecutor) executeAdvertiserStrategy(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行广告主策略", "advertiser_id", target.TargetID, "strategy_name", strategy.Name)

	// 获取广告主ID
	advertiserID, err := strconv.ParseInt(target.TargetID, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err), false, false, err.Error(), ""
	}

	// 根据广告主ID获取广告主信息
	advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserID)
	if err != nil {
		return fmt.Errorf("获取广告主信息失败: %w", err), false, false, err.Error(), ""
	}

	if advertiser == nil {
		return fmt.Errorf("广告主不存在: %s", target.TargetID), false, false, "广告主不存在", ""
	}

	// 评估广告主是否满足策略条件
	matched, err := se.conditionEvaluator.EvaluateAdvertiserConditions(advertiser, strategy)
	if err != nil {
		return fmt.Errorf("评估广告主条件失败: %w", err), false, false, err.Error(), advertiser.AdvertiserName
	}

	if !matched {
		utils.LogStrategyInfo("广告主不满足策略条件，跳过执行", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)
		return nil, false, false, "", advertiser.AdvertiserName
	}

	utils.LogStrategyInfo("广告主满足策略条件，开始执行动作", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)

	// 执行广告主级别的策略动作
	dbStrategy := NewDatabaseStrategy(strategy, se.svcCtx, nil)
	err = dbStrategy.executeStrategyActionsForAdvertiser(ctx, advertiser, strategy)
	if err != nil {
		return fmt.Errorf("执行广告主策略动作失败: %w", err), true, false, err.Error(), advertiser.AdvertiserName
	}

	utils.LogStrategyInfo("广告主策略执行完成", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)
	return nil, true, true, "", advertiser.AdvertiserName
}

// executeProjectStrategy 执行项目策略
func (se *StrategyExecutor) executeProjectStrategy(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行项目策略", "project_id", target.TargetID, "strategy_name", strategy.Name)

	//获取项目信息
	project, err := se.svcCtx.ProjectModel.GetByProjectId(target.TargetID)
	if err != nil {
		return fmt.Errorf("获取项目信息失败: %w", err), false, false, err.Error(), ""
	}

	if project == nil {
		return fmt.Errorf("项目不存在: %s", target.TargetID), false, false, "项目不存在", ""
	}

	// 检查广告主更新时间，如果超过1分钟则更新项目信息
	advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId)
	if err != nil {
		utils.LogStrategyError("获取广告主信息失败", "project_name", project.ProjectName, "advertiser_id", project.AdvertiserId, "error", err.Error())
	} else if advertiser != nil {
		// 检查广告主更新时间是否超过1分钟
		if time.Since(advertiser.UpdateTime) > time.Minute {
			utils.LogStrategyInfo("广告主数据超过1分钟，开始更新项目和广告信息",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName,
				"last_update", advertiser.UpdateTime.Format("2006-01-02 15:04:05"))

			// 调用子账户项目列表API更新项目信息
			if err := se.updateAdvertiserProjectInfo(advertiser); err != nil {
				utils.LogStrategyError("更新广告主项目信息失败",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName,
					"error", err.Error())
			} else {
				utils.LogStrategyInfo("广告主项目信息更新成功",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName)

				// 重新获取项目信息，确保使用最新数据
				if updatedProject, err := se.svcCtx.ProjectModel.GetByProjectId(target.TargetID); err == nil && updatedProject != nil {
					project = updatedProject
					utils.LogStrategyInfo("项目信息已更新为最新数据", "project_name", project.ProjectName)
				}
			}

			// 调用子账户广告列表API更新广告信息
			if err := se.updateAdvertiserPromotionInfo(advertiser); err != nil {
				utils.LogStrategyError("更新广告主广告信息失败",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName,
					"error", err.Error())
			} else {
				utils.LogStrategyInfo("广告主广告信息更新成功",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName)
			}
		}
	}

	// 评估项目是否满足策略条件
	matched, err := se.conditionEvaluator.EvaluateProjectConditions(project, strategy)
	if err != nil {
		return fmt.Errorf("评估项目条件失败: %w", err), false, false, err.Error(), project.ProjectName
	}

	if !matched {
		utils.LogStrategyInfo("项目不满足策略条件，跳过执行", "project_name", project.ProjectName, "strategy_name", strategy.Name)
		return nil, false, false, "", project.ProjectName
	}

	utils.LogStrategyInfo("项目满足策略条件，开始执行动作", "project_name", project.ProjectName, "strategy_name", strategy.Name)

	// 执行项目级别的策略动作
	dbStrategy := NewDatabaseStrategy(strategy, se.svcCtx, nil)
	err = dbStrategy.executeStrategyActionsForProject(ctx, project, strategy)
	if err != nil {
		return fmt.Errorf("执行项目策略动作失败: %w", err), true, false, err.Error(), project.ProjectName
	}

	utils.LogStrategyInfo("项目策略执行完成", "project_name", project.ProjectName, "strategy_name", strategy.Name)
	return nil, true, true, "", project.ProjectName
}

// executePromotionStrategy 执行广告策略
func (se *StrategyExecutor) executePromotionStrategy(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行广告策略", "promotion_id", target.TargetID, "strategy_name", strategy.Name)

	// 获取广告信息 - 直接使用promotion_id查询，不需要解析为整数
	promotion, err := se.svcCtx.PromotionModel.GetByPromotionId(target.TargetID)
	if err != nil {
		return fmt.Errorf("获取广告信息失败: %w", err), false, false, err.Error(), ""
	}

	if promotion == nil {
		return fmt.Errorf("广告不存在: %s", target.TargetID), false, false, "广告不存在", ""
	}

	// 检查广告主更新时间，如果超过1分钟则更新广告信息
	advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(promotion.AdvertiserId)
	if err != nil {
		utils.LogStrategyError("获取广告主信息失败", "promotion_name", promotion.PromotionName, "advertiser_id", promotion.AdvertiserId, "error", err.Error())
	} else if advertiser != nil {
		// 检查广告主更新时间是否超过1分钟
		if time.Since(advertiser.UpdateTime) > time.Minute {
			utils.LogStrategyInfo("广告主数据超过1分钟，开始更新广告信息",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName,
				"last_update", advertiser.UpdateTime.Format("2006-01-02 15:04:05"))

			// 调用子账户广告列表API更新广告信息
			if err := se.updateAdvertiserPromotionInfo(advertiser); err != nil {
				utils.LogStrategyError("更新广告主广告信息失败",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName,
					"error", err.Error())
				// 更新失败不影响策略继续执行，只记录错误日志
			} else {
				utils.LogStrategyInfo("广告主广告信息更新成功",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName)

				// 重新获取广告信息，确保使用最新数据
				if updatedPromotion, err := se.svcCtx.PromotionModel.GetByPromotionId(target.TargetID); err == nil && updatedPromotion != nil {
					promotion = updatedPromotion
					utils.LogStrategyInfo("广告信息已更新为最新数据", "promotion_name", promotion.PromotionName)
				}
			}
		}
	}

	// 检查广告状态 - 只有投放中的广告才会执行策略
	// 使用双重检查：状态码和状态名称，确保准确性
	isActiveByCode := promotion.PromotionStatus == constant.PromotionStatus_Active         // 投放中状态码为0
	isActiveByName := promotion.PromotionStatusName == constant.PromotionStatusName_Active // 投放中状态名称

	if !isActiveByCode && !isActiveByName {
		utils.LogStrategyInfo("广告非投放中状态，跳过策略执行",
			"promotion_name", promotion.PromotionName,
			"status_code", promotion.PromotionStatus,
			"status_name", promotion.PromotionStatusName,
			"strategy_name", strategy.Name)
		return nil, false, false, "广告非投放中状态", promotion.PromotionName
	}

	// 评估广告是否满足策略条件
	promotions := []*model.Promotion{promotion}
	matchedPromotions, err := se.conditionEvaluator.EvaluatePromotionsConditions(promotions, strategy)
	if err != nil {
		return fmt.Errorf("评估广告条件失败: %w", err), false, false, err.Error(), promotion.PromotionName
	}

	if len(matchedPromotions) == 0 {
		utils.LogStrategyInfo("广告不满足策略条件，跳过执行", "promotion_name", promotion.PromotionName, "strategy_name", strategy.Name)
		return nil, false, false, "", promotion.PromotionName
	}

	// 获取项目信息（用于策略动作执行）
	if promotion.ProjectId == nil {
		return fmt.Errorf("广告未关联项目"), false, false, "广告未关联项目", promotion.PromotionName
	}

	project, err := se.svcCtx.ProjectModel.GetByProjectId(*promotion.ProjectId)
	if err != nil {
		return fmt.Errorf("获取广告所属项目信息失败: %w", err), false, false, err.Error(), promotion.PromotionName
	}

	// 执行策略动作
	dbStrategy := NewDatabaseStrategy(strategy, se.svcCtx, nil)
	err = dbStrategy.executeStrategyActionsForPromotions(ctx, project, strategy, matchedPromotions)
	if err != nil {
		return fmt.Errorf("执行广告策略动作失败: %w", err), true, false, err.Error(), promotion.PromotionName
	}

	return nil, true, true, "", promotion.PromotionName
}

// executeGlobalStrategy 执行全局策略
func (se *StrategyExecutor) executeGlobalStrategy(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行全局策略",
		"binding_type", string(target.BindingType),
		"binding_id", target.BindingID,
		"strategy_name", strategy.Name)

	switch target.BindingType {
	case BindingTypeAdvertiser:
		return se.executeGlobalStrategyForAdvertiser(ctx, target, strategy)
	case BindingTypeProject:
		return se.executeGlobalStrategyForProject(ctx, target, strategy)
	default:
		return fmt.Errorf("全局策略不支持的绑定类型: %s", target.BindingType), false, false, "不支持的绑定类型", ""
	}
}

// executeGlobalStrategyForAdvertiser 执行绑定到广告主的全局策略
func (se *StrategyExecutor) executeGlobalStrategyForAdvertiser(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行绑定到广告主的全局策略", "advertiser_id", target.BindingID, "strategy_name", strategy.Name)

	// 获取广告主信息
	advertiserID, err := strconv.ParseInt(target.BindingID, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err), false, false, err.Error(), ""
	}

	advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserID)
	if err != nil {
		return fmt.Errorf("获取广告主信息失败: %w", err), false, false, err.Error(), ""
	}

	if advertiser == nil {
		return fmt.Errorf("广告主不存在: %s", target.BindingID), false, false, "广告主不存在", ""
	}

	// 检查广告主更新时间，如果超过1分钟则更新项目和广告信息
	if time.Since(advertiser.UpdateTime) > time.Minute {
		utils.LogStrategyInfo("广告主数据超过1分钟，开始更新项目和广告信息",
			"advertiser_id", advertiser.AdvertiserId,
			"advertiser_name", advertiser.AdvertiserName,
			"last_update", advertiser.UpdateTime.Format("2006-01-02 15:04:05"))

		// 调用子账户项目列表API更新项目信息
		if err := se.updateAdvertiserProjectInfo(advertiser); err != nil {
			utils.LogStrategyError("更新广告主项目信息失败",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName,
				"error", err.Error())
		} else {
			utils.LogStrategyInfo("广告主项目信息更新成功",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName)
		}

		// 调用子账户广告列表API更新广告信息
		if err := se.updateAdvertiserPromotionInfo(advertiser); err != nil {
			utils.LogStrategyError("更新广告主广告信息失败",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName,
				"error", err.Error())
		} else {
			utils.LogStrategyInfo("广告主广告信息更新成功",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName)
		}
	}

	// 获取广告主下的所有项目
	projects, _, err := se.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{
		"advertiser_id": advertiserID,
	})
	if err != nil {
		return fmt.Errorf("获取广告主项目列表失败: %w", err), false, false, err.Error(), advertiser.AdvertiserName
	}

	if len(projects) == 0 {
		utils.LogStrategyInfo("广告主下没有项目", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)
		return nil, false, false, "广告主下没有项目", advertiser.AdvertiserName
	}

	utils.LogStrategyInfo("开始处理广告主下的项目", "advertiser_name", advertiser.AdvertiserName, "project_count", len(projects), "strategy_name", strategy.Name)

	// 对每个项目下的所有广告执行策略
	var allMatchedPromotions []*model.Promotion
	var targetProject *model.Project

	for _, project := range projects {
		// 获取项目下的启用广告
		promotions, err := se.promotionQueryService.GetEnabledPromotionsByProject(project)
		if err != nil {
			utils.LogStrategyError("获取项目广告失败", "project_name", project.ProjectName, "error", err.Error())
			continue
		}

		if len(promotions) == 0 {
			utils.LogStrategyInfo("项目下没有启用广告", "project_name", project.ProjectName)
			continue
		}

		// 评估广告条件
		matchedPromotions, err := se.conditionEvaluator.EvaluatePromotionsConditions(promotions, strategy)
		if err != nil {
			utils.LogStrategyError("评估项目广告条件失败", "project_name", project.ProjectName, "error", err.Error())
			continue
		}

		if len(matchedPromotions) > 0 {
			allMatchedPromotions = append(allMatchedPromotions, matchedPromotions...)
			if targetProject == nil {
				targetProject = project // 使用第一个有匹配广告的项目作为目标项目
			}
			utils.LogStrategyInfo("项目有匹配广告", "project_name", project.ProjectName, "matched_count", len(matchedPromotions))
		}
	}

	if len(allMatchedPromotions) == 0 {
		utils.LogStrategyInfo("广告主下没有满足条件的广告", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)
		return nil, false, false, "广告主下没有满足条件的广告", advertiser.AdvertiserName
	}

	utils.LogStrategyInfo("广告主全局策略找到匹配广告", "advertiser_name", advertiser.AdvertiserName, "total_matched", len(allMatchedPromotions), "strategy_name", strategy.Name)

	// 执行策略动作
	dbStrategy := NewDatabaseStrategy(strategy, se.svcCtx, nil)
	err = dbStrategy.executeStrategyActionsForPromotions(ctx, targetProject, strategy, allMatchedPromotions)
	if err != nil {
		return fmt.Errorf("执行广告主全局策略动作失败: %w", err), true, false, err.Error(), advertiser.AdvertiserName
	}

	return nil, true, true, "", advertiser.AdvertiserName
}

// executeGlobalStrategyForProject 执行绑定到项目的全局策略
func (se *StrategyExecutor) executeGlobalStrategyForProject(ctx context.Context, target *StrategyExecutionTarget, strategy *model.Strategy) (error, bool, bool, string, string) {
	utils.LogStrategyInfo("执行绑定到项目的全局策略", "project_id", target.BindingID, "strategy_name", strategy.Name)

	// 获取项目信息 - 直接使用project_id查询，不需要解析为整数
	project, err := se.svcCtx.ProjectModel.GetByProjectId(target.BindingID)
	if err != nil {
		return fmt.Errorf("获取项目信息失败: %w", err), false, false, err.Error(), ""
	}

	if project == nil {
		return fmt.Errorf("项目不存在: %s", target.BindingID), false, false, "项目不存在", ""
	}

	// 检查广告主更新时间，如果超过1分钟则更新项目信息
	advertiser, err := se.svcCtx.AdvertiserModel.GetByAdvertiserId(project.AdvertiserId)
	if err != nil {
		utils.LogStrategyError("获取广告主信息失败", "project_name", project.ProjectName, "advertiser_id", project.AdvertiserId, "error", err.Error())
	} else if advertiser != nil {
		// 检查广告主更新时间是否超过1分钟
		if time.Since(advertiser.UpdateTime) > time.Minute {
			utils.LogStrategyInfo("广告主数据超过1分钟，开始更新项目和广告信息",
				"advertiser_id", advertiser.AdvertiserId,
				"advertiser_name", advertiser.AdvertiserName,
				"last_update", advertiser.UpdateTime.Format("2006-01-02 15:04:05"))

			// 调用子账户项目列表API更新项目信息
			if err := se.updateAdvertiserProjectInfo(advertiser); err != nil {
				utils.LogStrategyError("更新广告主项目信息失败",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName,
					"error", err.Error())
			} else {
				utils.LogStrategyInfo("广告主项目信息更新成功",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName)

				// 重新获取项目信息，确保使用最新数据
				if updatedProject, err := se.svcCtx.ProjectModel.GetByProjectId(target.BindingID); err == nil && updatedProject != nil {
					project = updatedProject
					utils.LogStrategyInfo("项目信息已更新为最新数据", "project_name", project.ProjectName)
				}
			}

			// 调用子账户广告列表API更新广告信息
			if err := se.updateAdvertiserPromotionInfo(advertiser); err != nil {
				utils.LogStrategyError("更新广告主广告信息失败",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName,
					"error", err.Error())
			} else {
				utils.LogStrategyInfo("广告主广告信息更新成功",
					"advertiser_id", advertiser.AdvertiserId,
					"advertiser_name", advertiser.AdvertiserName)
			}
		}
	}

	// 获取项目下的启用广告
	promotions, err := se.promotionQueryService.GetEnabledPromotionsByProject(project)
	if err != nil {
		return fmt.Errorf("获取项目广告失败: %w", err), false, false, err.Error(), project.ProjectName
	}

	if len(promotions) == 0 {
		utils.LogStrategyInfo("项目下没有启用广告", "project_name", project.ProjectName, "strategy_name", strategy.Name)
		return nil, false, false, "项目下没有启用广告", project.ProjectName
	}

	// 评估广告条件
	matchedPromotions, err := se.conditionEvaluator.EvaluatePromotionsConditions(promotions, strategy)
	if err != nil {
		return fmt.Errorf("评估项目广告条件失败: %w", err), false, false, err.Error(), project.ProjectName
	}

	if len(matchedPromotions) == 0 {
		utils.LogStrategyInfo("项目下没有满足条件的广告", "project_name", project.ProjectName, "strategy_name", strategy.Name)
		return nil, false, false, "项目下没有满足条件的广告", project.ProjectName
	}

	utils.LogStrategyInfo("项目全局策略找到匹配广告", "project_name", project.ProjectName, "matched_count", len(matchedPromotions), "strategy_name", strategy.Name)

	// 执行策略动作
	dbStrategy := NewDatabaseStrategy(strategy, se.svcCtx, nil)
	err = dbStrategy.executeStrategyActionsForPromotions(ctx, project, strategy, matchedPromotions)
	if err != nil {
		return fmt.Errorf("执行项目全局策略动作失败: %w", err), true, false, err.Error(), project.ProjectName
	}

	return nil, true, true, "", project.ProjectName
}

// updateAdvertiserProjectInfo 更新广告主下的项目信息
func (se *StrategyExecutor) updateAdvertiserProjectInfo(advertiser *model.Advertiser) error {
	// 获取广告主对应的账户信息
	account, err := se.svcCtx.AccountModel.GetByAccountId(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := se.svcCtx.GetBot(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用子账户项目列表API获取最新数据
	aadvid := fmt.Sprintf("%d", advertiser.AdvertiserId)
	page := 1
	pageSize := 100

	utils.LogStrategyInfo("开始调用子账户项目列表API",
		"advertiser_id", advertiser.AdvertiserId,
		"aadvid", aadvid)

	resp, err := bot.GetSubAccountProjectListSimple(aadvid, page, pageSize, account.Cookie)
	if err != nil {
		return fmt.Errorf("调用子账户项目列表API失败: %w", err)
	}

	if resp.Code != 0 {
		return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	// 如果有项目数据，更新到数据库
	if len(resp.Data.Projects) > 0 {
		utils.LogStrategyInfo("获取到项目数据，开始更新数据库",
			"advertiser_id", advertiser.AdvertiserId,
			"project_count", len(resp.Data.Projects))

		// 转换API数据为数据库模型
		projectsToUpdate := make([]*model.Project, 0, len(resp.Data.Projects))
		for _, apiProject := range resp.Data.Projects {
			// 转换字符串类型的ID为int64
			campaignId := int64(0)
			if apiProject.CampaignId != "" {
				if id, err := strconv.ParseInt(apiProject.CampaignId, 10, 64); err == nil {
					campaignId = id
				}
			}

			// 转换字符串类型的外部行为为int
			externalAction := 0
			if apiProject.ExternalAction != "" {
				if action, err := strconv.Atoi(apiProject.ExternalAction); err == nil {
					externalAction = action
				}
			}

			// 转换字符串类型的广告定价为int
			adPricing := 0
			if apiProject.AdPricing != "" {
				if pricing, err := strconv.Atoi(apiProject.AdPricing); err == nil {
					adPricing = pricing
				}
			}

			project := &model.Project{
				AccountId:            advertiser.AccountId,
				ProjectId:            apiProject.ProjectId,
				ProjectName:          apiProject.ProjectName,
				ProjectStatus:        apiProject.ProjectStatus,
				ProjectStatusName:    apiProject.ProjectStatusName,
				AdvertiserId:         advertiser.AdvertiserId,
				AdvertiserName:       advertiser.AdvertiserName,
				CampaignBudget:       apiProject.CampaignBudget,
				ProjectBid:           apiProject.ProjectBid,
				ProjectDeepCpaBid:    apiProject.ProjectDeepCpaBid,
				ProjectRoiGoal:       apiProject.ProjectRoiGoal,
				ProjectFirstRoiGoal:  apiProject.ProjectFirstRoiGoal,
				LandingType:          apiProject.LandingType,
				LandingTypeName:      apiProject.LandingTypeName,
				DeliveryMode:         apiProject.DeliveryMode,
				DeliveryModeInternal: apiProject.DeliveryModeInternal,
				ExternalAction:       externalAction,
				ExternalActionName:   apiProject.ExternalActionName,
				AdPricing:            adPricing,
				AdPricingName:        apiProject.AdPricingName,
				StartTime:            apiProject.StartTime,
				EndTime:              apiProject.EndTime,
				ModifyTime:           apiProject.ModifyTime,
				CampaignId:           campaignId,
				UpdateTime:           time.Now(),
			}

			// 获取已存在的项目信息，保留ID和创建时间
			if existingProject, err := se.svcCtx.ProjectModel.GetByProjectId(apiProject.ProjectId); err == nil && existingProject != nil {
				project.Id = existingProject.Id
				project.CreateTime = existingProject.CreateTime
			}

			projectsToUpdate = append(projectsToUpdate, project)
		}

		// 批量更新项目信息
		for _, project := range projectsToUpdate {
			if err := se.svcCtx.ProjectModel.Update(nil, project); err != nil {
				utils.LogStrategyError("更新项目信息失败",
					"project_id", project.ProjectId,
					"project_name", project.ProjectName,
					"error", err.Error())
				// 继续处理其他项目，不因单个项目更新失败而中断
			}
		}

		utils.LogStrategyInfo("项目信息更新完成",
			"advertiser_id", advertiser.AdvertiserId,
			"updated_count", len(projectsToUpdate))
	}

	// 更新广告主的更新时间
	advertiser.UpdateTime = time.Now()
	if err := se.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		utils.LogStrategyError("更新广告主时间失败",
			"advertiser_id", advertiser.AdvertiserId,
			"error", err.Error())
		// 不返回错误，因为项目信息已经更新成功
	}

	return nil
}

// updateAdvertiserPromotionInfo 更新广告主下的广告信息
func (se *StrategyExecutor) updateAdvertiserPromotionInfo(advertiser *model.Advertiser) error {
	// 获取广告主对应的账户信息
	account, err := se.svcCtx.AccountModel.GetByAccountId(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := se.svcCtx.GetBot(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用子账户广告列表API获取最新数据
	aadvid := fmt.Sprintf("%d", advertiser.AdvertiserId)
	page := 1
	pageSize := 100

	utils.LogStrategyInfo("开始调用子账户广告列表API",
		"advertiser_id", advertiser.AdvertiserId,
		"aadvid", aadvid)

	resp, err := bot.GetSubAccountPromotionList(aadvid, page, pageSize, account.Cookie)
	if err != nil {
		return fmt.Errorf("调用子账户广告列表API失败: %w", err)
	}

	if resp.Code != 0 {
		return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	// 如果有广告数据，更新到数据库
	if len(resp.Data.Ads) > 0 {
		utils.LogStrategyInfo("获取到广告数据，开始更新数据库",
			"advertiser_id", advertiser.AdvertiserId,
			"promotion_count", len(resp.Data.Ads))

		// 转换API数据为数据库模型
		promotionsToUpdate := make([]*model.Promotion, 0, len(resp.Data.Ads))
		for _, apiPromotion := range resp.Data.Ads {
			// 转换字符串类型的ID为int64
			campaignId := int64(0)
			if apiPromotion.CampaignId != "" {
				if id, err := strconv.ParseInt(apiPromotion.CampaignId, 10, 64); err == nil {
					campaignId = id
				}
			}

			// 转换字符串类型的外部行为为int
			externalAction := 0
			if apiPromotion.ExternalAction != "" {
				if action, err := strconv.Atoi(apiPromotion.ExternalAction); err == nil {
					externalAction = action
				}
			}

			// 转换字符串类型的广告定价为int
			adPricing := 0
			if apiPromotion.AdPricing != "" {
				if pricing, err := strconv.Atoi(apiPromotion.AdPricing); err == nil {
					adPricing = pricing
				}
			}

			// 转换字符串类型的广告ID为int64
			adId := int64(0)
			if apiPromotion.AdId != "" {
				if id, err := strconv.ParseInt(apiPromotion.AdId, 10, 64); err == nil {
					adId = id
				}
			}

			promotion := &model.Promotion{
				AccountId:            advertiser.AccountId,
				PromotionId:          apiPromotion.PromotionId,
				PromotionName:        apiPromotion.PromotionName,
				AdvertiserId:         advertiser.AdvertiserId,
				AdvertiserName:       advertiser.AdvertiserName,
				ProjectId:            &apiPromotion.ProjectId,
				ProjectName:          apiPromotion.ProjectName,
				PromotionStatus:      apiPromotion.PromotionStatus,
				PromotionStatusName:  apiPromotion.PromotionStatusName,
				AdId:                 adId,
				AdBudget:             apiPromotion.AdBudget,
				AdBid:                apiPromotion.AdBid,
				ProjectBid:           apiPromotion.ProjectBid,
				LandingType:          apiPromotion.LandingType,
				LandingTypeName:      apiPromotion.LandingTypeName,
				DeliveryMode:         apiPromotion.DeliveryMode,
				DeliveryModeInternal: apiPromotion.DeliveryModeInternal,
				ExternalAction:       externalAction,
				ExternalActionName:   apiPromotion.ExternalActionName,
				AdPricing:            adPricing,
				AdPricingName:        apiPromotion.AdPricingName,
				StartTime:            apiPromotion.StartTime,
				EndTime:              apiPromotion.EndTime,
				ModifyTime:           apiPromotion.ModifyTime,
				CampaignId:           campaignId,
				UpdateTime:           time.Now(),
			}

			// 获取已存在的广告信息，保留ID和创建时间
			if existingPromotion, err := se.svcCtx.PromotionModel.GetByPromotionId(apiPromotion.PromotionId); err == nil && existingPromotion != nil {
				promotion.Id = existingPromotion.Id
				promotion.CreateTime = existingPromotion.CreateTime
			}

			promotionsToUpdate = append(promotionsToUpdate, promotion)
		}

		// 批量更新广告信息
		for _, promotion := range promotionsToUpdate {
			if err := se.svcCtx.PromotionModel.Update(nil, promotion); err != nil {
				utils.LogStrategyError("更新广告信息失败",
					"promotion_id", promotion.PromotionId,
					"promotion_name", promotion.PromotionName,
					"error", err.Error())
				// 继续处理其他广告，不因单个广告更新失败而中断
			}
		}

		utils.LogStrategyInfo("广告信息更新完成",
			"advertiser_id", advertiser.AdvertiserId,
			"updated_count", len(promotionsToUpdate))
	}

	// 更新广告主的更新时间
	advertiser.UpdateTime = time.Now()
	if err := se.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		utils.LogStrategyError("更新广告主时间失败",
			"advertiser_id", advertiser.AdvertiserId,
			"error", err.Error())
		// 不返回错误，因为广告信息已经更新成功
	}

	return nil
}

// getErrorMessage 获取错误消息
func getErrorMessage(err error) string {
	if err != nil {
		return err.Error()
	}
	return ""
}
