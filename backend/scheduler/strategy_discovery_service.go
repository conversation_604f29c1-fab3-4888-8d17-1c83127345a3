package scheduler

import (
	"fmt"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// StrategyDiscoveryService 策略发现服务
type StrategyDiscoveryService struct {
	svcCtx *svc.ServiceContext
}

// NewStrategyDiscoveryService 创建策略发现服务
func NewStrategyDiscoveryService(svcCtx *svc.ServiceContext) *StrategyDiscoveryService {
	return &StrategyDiscoveryService{
		svcCtx: svcCtx,
	}
}

// DiscoverStrategyTargets 发现所有策略执行目标
func (sds *StrategyDiscoveryService) DiscoverStrategyTargets() ([]*StrategyExecutionTarget, error) {
	var allTargets []*StrategyExecutionTarget

	// 获取所有启用的策略
	strategies, err := sds.svcCtx.StrategyModel.GetEnabledStrategies()
	if err != nil {
		return nil, fmt.Errorf("获取启用策略失败: %w", err)
	}

	if len(strategies) == 0 {
		utils.LogStrategyInfo("没有启用的策略")
		return allTargets, nil
	}

	utils.LogStrategyInfo("发现启用策略", "strategy_count", len(strategies))

	// 为每个策略发现执行目标
	for _, strategy := range strategies {
		targets, err := sds.DiscoverTargetsForStrategy(strategy)
		if err != nil {
			utils.LogStrategyError("发现策略目标失败", "strategy_name", strategy.Name, "error", err.Error())
			continue
		}
		allTargets = append(allTargets, targets...)
	}

	utils.LogStrategyInfo("策略目标发现完成", "total_targets", len(allTargets))
	return allTargets, nil
}

// DiscoverTargetsForStrategy 为单个策略发现执行目标
func (sds *StrategyDiscoveryService) DiscoverTargetsForStrategy(strategy *model.Strategy) ([]*StrategyExecutionTarget, error) {
	// 解析策略类型
	strategyType := StrategyTypeGlobal // 默认值
	if strategy.Type != "" {
		strategyType = StrategyType(strategy.Type)
	}

	utils.LogStrategyInfo("为策略发现目标", "strategy_name", strategy.Name, "strategy_type", string(strategyType))

	switch strategyType {
	case StrategyTypeAdvertiser:
		return sds.discoverAdvertiserStrategyTargets(strategy)
	case StrategyTypeProject:
		return sds.discoverProjectStrategyTargets(strategy)
	case StrategyTypePromotion:
		return sds.discoverPromotionStrategyTargets(strategy)
	case StrategyTypeGlobal:
		return sds.discoverGlobalStrategyTargets(strategy)
	default:
		return nil, fmt.Errorf("未知的策略类型: %s", strategyType)
	}
}

// discoverAdvertiserStrategyTargets 发现广告主策略目标
func (sds *StrategyDiscoveryService) discoverAdvertiserStrategyTargets(strategy *model.Strategy) ([]*StrategyExecutionTarget, error) {
	var targets []*StrategyExecutionTarget

	// 获取绑定到此策略的广告主
	bindings, err := sds.svcCtx.StrategyBindingModel.GetByStrategyID(strategy.ID)
	if err != nil {
		return nil, fmt.Errorf("获取广告主策略绑定失败: %w", err)
	}

	for _, binding := range bindings {
		if binding.BindingType == "advertiser" {
			target := &StrategyExecutionTarget{
				Type:         StrategyTypeAdvertiser,
				BindingType:  BindingTypeAdvertiser,
				BindingID:    binding.BindingID,
				BindingName:  binding.BindingName,
				TargetID:     binding.BindingID, // 广告主策略：目标就是绑定的广告主
				TargetType:   "advertiser",
				TargetName:   binding.BindingName, // 使用绑定名称作为目标名称
				StrategyID:   fmt.Sprintf("%d", strategy.ID),
				StrategyName: strategy.Name,
			}
			targets = append(targets, target)
		}
	}

	utils.LogStrategyInfo("发现广告主策略目标", "strategy_name", strategy.Name, "target_count", len(targets))
	return targets, nil
}

// discoverProjectStrategyTargets 发现项目策略目标
func (sds *StrategyDiscoveryService) discoverProjectStrategyTargets(strategy *model.Strategy) ([]*StrategyExecutionTarget, error) {
	var targets []*StrategyExecutionTarget

	// 获取绑定到此策略的项目
	bindings, err := sds.svcCtx.StrategyBindingModel.GetByStrategyID(strategy.ID)
	if err != nil {
		return nil, fmt.Errorf("获取项目策略绑定失败: %w", err)
	}

	for _, binding := range bindings {
		if binding.BindingType == "project" {
			target := &StrategyExecutionTarget{
				Type:         StrategyTypeProject,
				BindingType:  BindingTypeProject,
				BindingID:    binding.BindingID,
				BindingName:  binding.BindingName,
				TargetID:     binding.BindingID, // 项目策略：目标就是绑定的项目
				TargetType:   "project",
				TargetName:   binding.BindingName, // 使用绑定名称作为目标名称
				StrategyID:   fmt.Sprintf("%d", strategy.ID),
				StrategyName: strategy.Name,
			}
			targets = append(targets, target)
		}
	}

	utils.LogStrategyInfo("发现项目策略目标", "strategy_name", strategy.Name, "target_count", len(targets))
	return targets, nil
}

// discoverPromotionStrategyTargets 发现广告策略目标
func (sds *StrategyDiscoveryService) discoverPromotionStrategyTargets(strategy *model.Strategy) ([]*StrategyExecutionTarget, error) {
	var targets []*StrategyExecutionTarget

	// 获取绑定到此策略的广告
	bindings, err := sds.svcCtx.StrategyBindingModel.GetByStrategyID(strategy.ID)
	if err != nil {
		return nil, fmt.Errorf("获取广告策略绑定失败: %w", err)
	}

	for _, binding := range bindings {
		if binding.BindingType == "promotion" {
			target := &StrategyExecutionTarget{
				Type:         StrategyTypePromotion,
				BindingType:  BindingTypePromotion,
				BindingID:    binding.BindingID,
				BindingName:  binding.BindingName,
				TargetID:     binding.BindingID, // 广告策略：目标就是绑定的广告
				TargetType:   "promotion",
				TargetName:   binding.BindingName, // 使用绑定名称作为目标名称
				StrategyID:   fmt.Sprintf("%d", strategy.ID),
				StrategyName: strategy.Name,
			}
			targets = append(targets, target)
		}
	}

	utils.LogStrategyInfo("发现广告策略目标", "strategy_name", strategy.Name, "target_count", len(targets))
	return targets, nil
}

// discoverGlobalStrategyTargets 发现全局策略目标
func (sds *StrategyDiscoveryService) discoverGlobalStrategyTargets(strategy *model.Strategy) ([]*StrategyExecutionTarget, error) {
	var targets []*StrategyExecutionTarget

	// 获取策略的所有绑定
	bindings, err := sds.svcCtx.StrategyBindingModel.GetByStrategyID(strategy.ID)
	if err != nil {
		return nil, fmt.Errorf("获取全局策略绑定失败: %w", err)
	}

	for _, binding := range bindings {
		var targetType string
		switch binding.BindingType {
		case "advertiser":
			targetType = "advertiser_global"
		case "project":
			targetType = "project_global"
		default:
			continue // 全局策略只支持绑定到广告主或项目
		}

		target := &StrategyExecutionTarget{
			Type:         StrategyTypeGlobal,
			BindingType:  StrategyBindingType(binding.BindingType),
			BindingID:    binding.BindingID,
			BindingName:  binding.BindingName,
			TargetID:     binding.BindingID,
			TargetType:   targetType,
			TargetName:   binding.BindingName, // 使用绑定名称作为目标名称
			StrategyID:   fmt.Sprintf("%d", strategy.ID),
			StrategyName: strategy.Name,
		}
		targets = append(targets, target)
	}

	utils.LogStrategyInfo("发现全局策略目标", "strategy_name", strategy.Name, "target_count", len(targets))
	return targets, nil
}
