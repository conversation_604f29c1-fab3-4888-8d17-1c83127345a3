package scheduler

import (
	"fmt"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// PromotionQueryService 广告查询服务
type PromotionQueryService struct {
	svcCtx *svc.ServiceContext
}

// NewPromotionQueryService 创建广告查询服务
func NewPromotionQueryService(svcCtx *svc.ServiceContext) *PromotionQueryService {
	return &PromotionQueryService{
		svcCtx: svcCtx,
	}
}

// GetEnabledPromotionsByProjectID 根据项目ID获取启用状态的广告列表
func (pqs *PromotionQueryService) GetEnabledPromotionsByProjectID(projectID string) ([]*model.Promotion, error) {
	// 获取项目详情 - 直接使用项目ID字符串查询，不需要解析为整数
	project, err := pqs.svcCtx.ProjectModel.GetByProjectId(projectID)
	if err != nil {
		utils.LogStrategyError("获取项目详情失败", "project_id", projectID, "error", err.Error())
		return nil, fmt.Errorf("获取项目详情失败: %w", err)
	}

	if project == nil {
		utils.LogStrategyError("项目不存在", "project_id", projectID)
		return nil, fmt.Errorf("项目不存在")
	}

	// 验证项目的ProjectId字段
	if project.ProjectId == "" {
		utils.LogStrategyInfo("项目的ProjectId为空", "project_name", project.ProjectName, "project_id", projectID)
		return []*model.Promotion{}, nil
	}

	utils.LogStrategyInfo("查询项目下的启用广告", "project_name", project.ProjectName, "project_id", projectID, "api_project_id", project.ProjectId)

	// 查询项目下的启用状态广告
	promotions, err := pqs.svcCtx.PromotionModel.GetEnabledPromotionsByProjectId(project.ProjectId)
	if err != nil {
		utils.LogStrategyError("查询项目启用状态广告失败", "project_name", project.ProjectName, "project_id", projectID, "error", err.Error())
		return nil, fmt.Errorf("查询项目广告失败: %w", err)
	}

	utils.LogStrategyInfo("项目启用广告查询完成", "project_name", project.ProjectName, "project_id", projectID, "promotion_count", len(promotions))

	return promotions, nil
}

// GetEnabledPromotionsByProject 根据项目对象获取启用状态的广告列表
func (pqs *PromotionQueryService) GetEnabledPromotionsByProject(project *model.Project) ([]*model.Promotion, error) {
	if project == nil {
		return nil, fmt.Errorf("项目对象为空")
	}

	// 验证项目的ProjectId字段
	if project.ProjectId == "" {
		utils.LogStrategyInfo("项目的ProjectId为空", "project_name", project.ProjectName, "project_db_id", project.Id)
		return []*model.Promotion{}, nil
	}

	utils.LogStrategyInfo("查询项目下的启用广告", "project_name", project.ProjectName, "project_db_id", project.Id, "api_project_id", project.ProjectId)

	// 查询项目下的启用状态广告
	promotions, err := pqs.svcCtx.PromotionModel.GetEnabledPromotionsByProjectId(project.ProjectId)
	if err != nil {
		utils.LogStrategyError("查询项目启用状态广告失败", "project_name", project.ProjectName, "project_db_id", project.Id, "error", err.Error())
		return nil, fmt.Errorf("查询项目广告失败: %w", err)
	}

	utils.LogStrategyInfo("项目启用广告查询完成", "project_name", project.ProjectName, "project_db_id", project.Id, "promotion_count", len(promotions))

	return promotions, nil
}

// GetPromotionsByAdvertiserID 根据广告主ID获取广告列表
func (pqs *PromotionQueryService) GetPromotionsByAdvertiserID(advertiserID string) ([]*model.Promotion, error) {
	_, err := strconv.ParseInt(advertiserID, 10, 64)
	if err != nil {
		utils.LogStrategyError("解析广告主ID失败", "advertiser_id", advertiserID, "error", err.Error())
		return nil, fmt.Errorf("解析广告主ID失败: %w", err)
	}

	// 这里可以根据需要实现广告主级别的广告查询
	// 当前先返回空列表，后续可以扩展
	utils.LogStrategyInfo("广告主级别的广告查询暂未实现", "advertiser_id", advertiserID)
	return []*model.Promotion{}, nil
}

// GetPromotionsByAccountID 根据账户ID获取广告列表
func (pqs *PromotionQueryService) GetPromotionsByAccountID(accountID string) ([]*model.Promotion, error) {
	_, err := strconv.ParseInt(accountID, 10, 64)
	if err != nil {
		utils.LogStrategyError("解析账户ID失败", "account_id", accountID, "error", err.Error())
		return nil, fmt.Errorf("解析账户ID失败: %w", err)
	}

	// 这里可以根据需要实现账户级别的广告查询
	// 当前先返回空列表，后续可以扩展
	utils.LogStrategyInfo("账户级别的广告查询暂未实现", "account_id", accountID)
	return []*model.Promotion{}, nil
}
