package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/interfaces"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// SchedulerManager 调度器管理器 - 负责整个调度系统的生命周期管理
type SchedulerManager struct {
	svcCtx                 *svc.ServiceContext
	strategyEngine         *StrategyEngine
	strategyCleanupService *StrategyCleanupService
	app                    *application.App
	mu                     sync.RWMutex
	running                bool
	ctx                    context.Context
	cancel                 context.CancelFunc
}

// NewSchedulerManager 创建新的调度器管理器
func NewSchedulerManager(svcCtx *svc.ServiceContext) interfaces.SchedulerManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &SchedulerManager{
		svcCtx:                 svcCtx,
		strategyEngine:         NewStrategyEngine(svcCtx),
		strategyCleanupService: NewStrategyCleanupService(svcCtx),
		ctx:                    ctx,
		cancel:                 cancel,
	}

	return manager
}

// Start 启动调度器
func (sm *SchedulerManager) Start(ctx context.Context, app *application.App) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.running {
		return fmt.Errorf("调度器已在运行")
	}

	sm.app = app
	utils.LogInfo("启动调度器管理器...")

	// 新版策略引擎会直接从数据库获取策略，无需预加载

	// 启动策略引擎
	if err := sm.strategyEngine.Start(); err != nil {
		return fmt.Errorf("启动策略引擎失败: %w", err)
	}

	// 启动策略清理服务
	if err := sm.strategyCleanupService.Start(); err != nil {
		utils.LogError("启动策略清理服务失败", "error", err.Error())
		// 不阻止启动，继续运行
	}

	sm.running = true
	utils.LogInfo("调度器管理器启动成功")

	return nil
}

// Stop 停止调度器
func (sm *SchedulerManager) Stop() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.running {
		return nil
	}

	utils.LogInfo("停止调度器管理器...")

	// 停止策略引擎
	sm.strategyEngine.Stop()

	// 停止策略清理服务
	if err := sm.strategyCleanupService.Stop(); err != nil {
		utils.LogError("停止策略清理服务失败", "error", err.Error())
	}

	sm.running = false
	utils.LogInfo("调度器管理器已停止")

	return nil
}

// ExecuteStrategyNow 立即执行指定策略
func (sm *SchedulerManager) ExecuteStrategyNow(strategyID int64) error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if !sm.running {
		return fmt.Errorf("调度器未运行")
	}

	// 获取策略信息
	strategy, err := sm.svcCtx.StrategyModel.GetById(strategyID)
	if err != nil {
		return fmt.Errorf("获取策略信息失败: %w", err)
	}

	if !strategy.Enabled {
		return fmt.Errorf("策略已禁用")
	}

	utils.LogStrategyInfo("立即执行策略", "strategy_name", strategy.Name, "strategy_id", strategyID)

	// 通过策略引擎执行策略
	strategyKey := fmt.Sprintf("db_strategy_%d", strategyID)
	return sm.strategyEngine.ExecuteStrategyNow(strategyKey)
}

// RefreshStrategies 刷新策略（重新从数据库加载）
func (sm *SchedulerManager) RefreshStrategies() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if !sm.running {
		return fmt.Errorf("调度器未运行")
	}

	utils.LogStrategyInfo("刷新策略...")

	// 停止当前策略引擎
	sm.strategyEngine.Stop()

	// 创建新的策略引擎
	sm.strategyEngine = NewStrategyEngine(sm.svcCtx)

	// 新版策略引擎会自动从数据库获取策略，无需手动加载

	// 重新启动策略引擎
	if err := sm.strategyEngine.Start(); err != nil {
		return fmt.Errorf("重新启动策略引擎失败: %w", err)
	}

	utils.LogStrategyInfo("策略刷新完成")
	return nil
}

// GetEngineStatus 获取引擎状态
func (sm *SchedulerManager) GetEngineStatus() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	status := map[string]interface{}{
		"running": sm.running,
	}

	if sm.running && sm.strategyEngine != nil {
		engineStatus := sm.strategyEngine.GetEngineStatus()
		status["engine"] = engineStatus
	}

	return status
}

// ForceUpdateMetrics 强制更新监控指标
func (sm *SchedulerManager) ForceUpdateMetrics() {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if !sm.running {
		return
	}

	utils.LogStrategyInfo("强制更新监控指标...")
}

// ForceCleanupStrategies 强制执行策略清理
func (sm *SchedulerManager) ForceCleanupStrategies() error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if !sm.running {
		return fmt.Errorf("调度器未运行")
	}

	if sm.strategyCleanupService == nil {
		return fmt.Errorf("策略清理服务未初始化")
	}

	return sm.strategyCleanupService.ForceCleanup()
}

// SetCleanupInterval 设置清理间隔
func (sm *SchedulerManager) SetCleanupInterval(interval time.Duration) error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if sm.strategyCleanupService == nil {
		return fmt.Errorf("策略清理服务未初始化")
	}

	sm.strategyCleanupService.SetInterval(interval)
	return nil
}

// 注意：loadStrategiesFromDatabase 和 createStrategyFromModel 方法已移除
// 新版策略引擎会直接从数据库获取和执行策略，无需预加载和注册机制
