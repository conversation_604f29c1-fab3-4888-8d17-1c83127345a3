package scheduler

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// EvaluateProjectPromotionsConditions 评估项目下所有广告的条件，返回满足条件的广告列表
func (ce *ConditionEvaluator) EvaluateProjectPromotionsConditions(project *model.Project, strategy *model.Strategy) ([]*model.Promotion, error) {
	// 验证项目ID是否有效
	if project.ProjectId == "" {
		log.Printf("项目 %s 的ProjectId为空，跳过处理", project.ProjectName)
		return []*model.Promotion{}, nil
	}

	// 获取项目下的启用状态广告
	promotions, err := ce.svcCtx.PromotionModel.GetEnabledPromotionsByProjectId(project.ProjectId)
	if err != nil {
		log.Printf("获取项目 %s 的启用状态广告列表失败: %v", project.ProjectName, err)
		return nil, err
	}

	if len(promotions) == 0 {
		log.Printf("项目 %s 下没有启用状态的广告", project.ProjectName)
		return []*model.Promotion{}, nil
	}

	utils.LogStrategyInfo("项目下有启用状态的广告，开始评估策略条件", "project_name", project.ProjectName, "promotion_count", len(promotions), "strategy_name", strategy.Name)

	// 使用新的方法评估条件
	return ce.EvaluatePromotionsConditions(promotions, strategy)
}

// EvaluateProjectConditions 评估项目是否满足策略条件
func (ce *ConditionEvaluator) EvaluateProjectConditions(project *model.Project, strategy *model.Strategy) (bool, error) {
	utils.LogStrategyInfo("开始评估项目策略条件", "project_name", project.ProjectName, "strategy_name", strategy.Name)

	// 解析策略条件
	var conditions []Condition
	if err := json.Unmarshal([]byte(strategy.Conditions), &conditions); err != nil {
		return false, fmt.Errorf("解析策略条件失败: %w", err)
	}

	if len(conditions) == 0 {
		utils.LogStrategyInfo("策略没有配置条件，项目满足条件", "project_name", project.ProjectName, "strategy_name", strategy.Name)
		return true, nil
	}

	// 评估项目是否满足条件
	result := ce.evaluateProjectConditions(project, conditions, strategy.LogicType)

	utils.LogStrategyInfo("项目条件评估完成", "project_name", project.ProjectName, "strategy_name", strategy.Name, "result", result)
	return result, nil
}

// evaluateProjectConditions 评估项目条件
func (ce *ConditionEvaluator) evaluateProjectConditions(project *model.Project, conditions []Condition, logicType string) bool {
	switch logicType {
	case "AND":
		// 所有条件都必须满足
		for i, condition := range conditions {
			if !ce.evaluateProjectCondition(condition, project) {
				utils.LogStrategyInfo("项目不满足条件", "project_name", project.ProjectName, "condition_index", i+1, "condition", condition)
				return false
			}
		}
		return true

	case "OR":
		// 至少一个条件满足
		for i, condition := range conditions {
			if ce.evaluateProjectCondition(condition, project) {
				utils.LogStrategyInfo("项目满足条件", "project_name", project.ProjectName, "condition_index", i+1, "condition", condition)
				return true
			}
		}
		return false

	default:
		// 默认使用AND逻辑
		for i, condition := range conditions {
			if !ce.evaluateProjectCondition(condition, project) {
				utils.LogStrategyInfo("项目不满足条件", "project_name", project.ProjectName, "condition_index", i+1, "condition", condition)
				return false
			}
		}
		return true
	}
}

// evaluateProjectCondition 评估单个项目条件
func (ce *ConditionEvaluator) evaluateProjectCondition(condition Condition, project *model.Project) bool {
	switch condition.Type {
	case "project_status":
		return ce.evaluateProjectStatus(condition, project)
	case "stat_cost":
		return ce.evaluateProjectStatCost(condition, project)
	case "show_cnt":
		return ce.evaluateProjectShowCnt(condition, project)
	case "convert_cnt":
		return ce.evaluateProjectConvertCnt(condition, project)
	case "conversion_cost":
		return ce.evaluateProjectConversionCost(condition, project)
	case "conversion_rate":
		return ce.evaluateProjectConversionRate(condition, project)
	case "click_cnt":
		return ce.evaluateProjectClickCnt(condition, project)
	case "ctr":
		return ce.evaluateProjectCtr(condition, project)
	case "cpm_platform":
		return ce.evaluateProjectCpm(condition, project)
	default:
		utils.LogStrategyInfo("未知的项目条件类型", "type", condition.Type)
		return false
	}
}

// evaluateProjectStatus 评估项目状态
func (ce *ConditionEvaluator) evaluateProjectStatus(condition Condition, project *model.Project) bool {
	// 项目状态映射：前端字符串值 -> 后端状态名称
	statusMapping := map[string]string{
		"active": "启用",
		"paused": "暂停",
	}

	var result bool
	switch condition.Operator {
	case "==":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		expectedStatusName := statusMapping[expectedStatus]

		// 如果没有找到映射，直接使用原值
		if expectedStatusName == "" {
			expectedStatusName = expectedStatus
		}

		result = project.ProjectStatusName == expectedStatusName
	case "!=":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		expectedStatusName := statusMapping[expectedStatus]

		// 如果没有找到映射，直接使用原值
		if expectedStatusName == "" {
			expectedStatusName = expectedStatus
		}

		result = project.ProjectStatusName != expectedStatusName
	case "in":
		for _, status := range condition.Values {
			expectedStatusName := statusMapping[status]

			// 如果没有找到映射，直接使用原值
			if expectedStatusName == "" {
				expectedStatusName = status
			}

			if project.ProjectStatusName == expectedStatusName {
				result = true
				break
			}
		}
	default:
		utils.LogStrategyInfo("[条件判断] 类型: project_status, 操作符: "+condition.Operator, "project_name", project.ProjectName, "project_status_name", project.ProjectStatusName, "结果", false, "原因", "不支持的操作符")
		return false
	}

	utils.LogStrategyInfo("[条件判断] 类型: project_status, 操作符: "+condition.Operator, "project_name", project.ProjectName, "project_status_name", project.ProjectStatusName, "expected", condition.Value, "结果", result)
	return result
}

// evaluateProjectStatCost 评估项目消耗
func (ce *ConditionEvaluator) evaluateProjectStatCost(condition Condition, project *model.Project) bool {
	cost, err := strconv.ParseFloat(project.StatCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 解析项目消耗失败", "stat_cost", project.StatCost, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("stat_cost", cost, condition)
}

// evaluateProjectShowCnt 评估项目展示量
func (ce *ConditionEvaluator) evaluateProjectShowCnt(condition Condition, project *model.Project) bool {
	shows, err := strconv.ParseFloat(project.ShowCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: show_cnt, 解析项目展示量失败", "show_cnt", project.ShowCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("show_cnt", shows, condition)
}

// evaluateProjectConvertCnt 评估项目转化数
func (ce *ConditionEvaluator) evaluateProjectConvertCnt(condition Condition, project *model.Project) bool {
	converts, err := strconv.ParseFloat(project.ConvertCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: convert_cnt, 解析项目转化数失败", "convert_cnt", project.ConvertCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("convert_cnt", converts, condition)
}

// evaluateProjectConversionCost 评估项目转化成本
func (ce *ConditionEvaluator) evaluateProjectConversionCost(condition Condition, project *model.Project) bool {
	cost, err := strconv.ParseFloat(project.ConversionCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: conversion_cost, 解析项目转化成本失败", "conversion_cost", project.ConversionCost, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("conversion_cost", cost, condition)
}

// evaluateProjectConversionRate 评估项目转化率
func (ce *ConditionEvaluator) evaluateProjectConversionRate(condition Condition, project *model.Project) bool {
	rate, err := strconv.ParseFloat(project.ConversionRate, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: conversion_rate, 解析项目转化率失败", "conversion_rate", project.ConversionRate, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("conversion_rate", rate, condition)
}

// evaluateProjectClickCnt 评估项目点击数
func (ce *ConditionEvaluator) evaluateProjectClickCnt(condition Condition, project *model.Project) bool {
	clicks, err := strconv.ParseFloat(project.ClickCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: click_cnt, 解析项目点击数失败", "click_cnt", project.ClickCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("click_cnt", clicks, condition)
}

// evaluateProjectCtr 评估项目点击率
func (ce *ConditionEvaluator) evaluateProjectCtr(condition Condition, project *model.Project) bool {
	ctr, err := strconv.ParseFloat(project.Ctr, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: ctr, 解析项目点击率失败", "ctr", project.Ctr, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("ctr", ctr, condition)
}

// evaluateProjectCpm 评估项目千次展现价格
func (ce *ConditionEvaluator) evaluateProjectCpm(condition Condition, project *model.Project) bool {
	cpm, err := strconv.ParseFloat(project.CpmPlatform, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: cpm_platform, 解析项目千次展现价格失败", "cpm_platform", project.CpmPlatform, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("cpm_platform", cpm, condition)
}

// evaluateProjectStatusForPromotion 评估广告所属项目的状态
func (ce *ConditionEvaluator) evaluateProjectStatusForPromotion(condition Condition, promotion *model.Promotion) bool {
	if promotion.ProjectId == nil {
		utils.LogStrategyInfo("[条件判断] 类型: project_status", "promotion_id", promotion.PromotionId, "结果", false, "原因", "广告未关联项目")
		return false
	}

	// 直接使用项目ID字符串查询，不需要解析为整数
	project, err := ce.svcCtx.ProjectModel.GetByProjectId(*promotion.ProjectId)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: project_status", "promotion_id", promotion.PromotionId, "project_id", *promotion.ProjectId, "结果", false, "原因", "获取项目信息失败")
		return false
	}

	if project == nil {
		utils.LogStrategyInfo("[条件判断] 类型: project_status", "promotion_id", promotion.PromotionId, "project_id", *promotion.ProjectId, "结果", false, "原因", "项目不存在")
		return false
	}

	// 项目状态映射：前端字符串值 -> 后端状态名称
	statusMapping := map[string]string{
		"active": "启用",
		"paused": "暂停",
	}

	var result bool
	switch condition.Operator {
	case "==":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		expectedStatusName := statusMapping[expectedStatus]

		// 如果没有找到映射，直接使用原值
		if expectedStatusName == "" {
			expectedStatusName = expectedStatus
		}

		result = project.ProjectStatusName == expectedStatusName
	case "!=":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		expectedStatusName := statusMapping[expectedStatus]

		// 如果没有找到映射，直接使用原值
		if expectedStatusName == "" {
			expectedStatusName = expectedStatus
		}

		result = project.ProjectStatusName != expectedStatusName
	case "in":
		for _, status := range condition.Values {
			expectedStatusName := statusMapping[status]

			// 如果没有找到映射，直接使用原值
			if expectedStatusName == "" {
				expectedStatusName = status
			}

			if project.ProjectStatusName == expectedStatusName {
				result = true
				break
			}
		}
	default:
		utils.LogStrategyInfo("[条件判断] 类型: project_status, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "project_status_name", project.ProjectStatusName, "结果", false, "原因", "不支持的操作符")
		return false
	}

	utils.LogStrategyInfo("[条件判断] 类型: project_status, 操作符: "+condition.Operator, "promotion_id", promotion.PromotionId, "project_status_name", project.ProjectStatusName, "expected", condition.Value, "结果", result)
	return result
}
