package scheduler

import (
	"fmt"
)

// StrategyType 策略类型
type StrategyType string

const (
	// StrategyTypeAdvertiser 广告主策略 - 只针对广告主执行操作
	StrategyTypeAdvertiser StrategyType = "advertiser"

	// StrategyTypeProject 项目策略 - 只针对项目执行操作
	StrategyTypeProject StrategyType = "project"

	// StrategyTypePromotion 广告策略 - 只针对广告执行操作
	StrategyTypePromotion StrategyType = "promotion"

	// StrategyTypeGlobal 全局策略 - 根据绑定对象应用到其下属的所有广告
	// 绑定到广告主：应用到该广告主下所有广告
	// 绑定到项目：应用到该项目下所有广告
	StrategyTypeGlobal StrategyType = "global"
)

// StrategyBindingType 策略绑定类型
type StrategyBindingType string

const (
	// BindingTypeAdvertiser 绑定到广告主
	BindingTypeAdvertiser StrategyBindingType = "advertiser"

	// BindingTypeProject 绑定到项目
	BindingTypeProject StrategyBindingType = "project"

	// BindingTypePromotion 绑定到广告
	BindingTypePromotion StrategyBindingType = "promotion"
)

// StrategyExecutionTarget 策略执行目标
type StrategyExecutionTarget struct {
	Type         StrategyType        `json:"type"`          // 策略类型
	BindingType  StrategyBindingType `json:"binding_type"`  // 绑定类型
	BindingID    string              `json:"binding_id"`    // 绑定对象ID
	BindingName  string              `json:"binding_name"`  // 绑定对象名称
	TargetID     string              `json:"target_id"`     // 实际执行目标ID
	TargetType   string              `json:"target_type"`   // 实际执行目标类型
	TargetName   string              `json:"target_name"`   // 实际执行目标名称
	StrategyID   string              `json:"strategy_id"`   // 策略ID
	StrategyName string              `json:"strategy_name"` // 策略名称
}

// String 返回目标的字符串表示
func (set *StrategyExecutionTarget) String() string {
	return fmt.Sprintf("%s:%s->%s", set.Type, set.BindingID, set.TargetID)
}

// GetStrategyTypeDisplay 获取策略类型显示名称
func GetStrategyTypeDisplay(strategyType StrategyType) string {
	switch strategyType {
	case StrategyTypeAdvertiser:
		return "广告主策略"
	case StrategyTypeProject:
		return "项目策略"
	case StrategyTypePromotion:
		return "广告策略"
	case StrategyTypeGlobal:
		return "全局策略"
	default:
		return "未知策略"
	}
}

// GetBindingTypeDisplay 获取绑定类型显示名称
func GetBindingTypeDisplay(bindingType StrategyBindingType) string {
	switch bindingType {
	case BindingTypeAdvertiser:
		return "广告主"
	case BindingTypeProject:
		return "项目"
	case BindingTypePromotion:
		return "广告"
	default:
		return "未知绑定"
	}
}

// IsValidStrategyBinding 检查策略类型和绑定类型是否匹配
func IsValidStrategyBinding(strategyType StrategyType, bindingType StrategyBindingType) bool {
	switch strategyType {
	case StrategyTypeAdvertiser:
		return bindingType == BindingTypeAdvertiser
	case StrategyTypeProject:
		return bindingType == BindingTypeProject
	case StrategyTypePromotion:
		return bindingType == BindingTypePromotion
	case StrategyTypeGlobal:
		// 全局策略可以绑定到广告主或项目
		return bindingType == BindingTypeAdvertiser || bindingType == BindingTypeProject
	default:
		return false
	}
}
