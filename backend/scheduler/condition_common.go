package scheduler

import (
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// convertToFloat64 将接口转换为float64
func (ce *ConditionEvaluator) convertToFloat64(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float64:
		return v, true
	case string:
		converted, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0, false
		}
		return converted, true
	default:
		return 0, false
	}
}

// evaluateNumericCondition 通用数值条件评估函数
func (ce *ConditionEvaluator) evaluateNumericCondition(conditionType string, actualValue float64, condition Condition) bool {
	// 使用更友好的字段名称进行日志输出
	displayName := condition.FieldName
	if displayName == "" {
		displayName = conditionType
	}
	var result bool
	switch condition.Operator {
	case ">":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue > threshold
	case ">=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue >= threshold
	case "<":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue < threshold
	case "<=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue <= threshold
	case "==":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue == threshold
	case "!=":
		threshold, ok := ce.convertToFloat64(condition.Value)
		if !ok {
			utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "条件值转换失败")
			return false
		}
		result = actualValue != threshold
	case "between":
		result = actualValue >= condition.Min && actualValue <= condition.Max
		utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "最小值", condition.Min, "最大值", condition.Max, "结果", result)
		return result
	default:
		utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", false, "原因", "不支持的操作符")
		return false
	}
	utils.LogStrategyInfo("[条件判断] "+displayName+", 操作符: "+condition.Operator, "左值", actualValue, "右值", condition.Value, "结果", result)
	return result
}
