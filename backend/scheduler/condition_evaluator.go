package scheduler

import (
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// 使用统一的StrategyCondition结构体，移除重复定义
type Condition = types.StrategyCondition

// ConditionEvaluator 条件评估器
type ConditionEvaluator struct {
	svcCtx *svc.ServiceContext
}

// NewConditionEvaluator 创建条件评估器
func NewConditionEvaluator(svcCtx *svc.ServiceContext) *ConditionEvaluator {
	return &ConditionEvaluator{
		svcCtx: svcCtx,
	}
}
