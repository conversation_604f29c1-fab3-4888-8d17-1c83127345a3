package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// StrategyEngine 策略引擎 - 基于新的策略类型系统
type StrategyEngine struct {
	strategyDiscovery *StrategyDiscoveryService
	strategyExecutor  *StrategyExecutor
	svcCtx            *svc.ServiceContext
	mu                sync.RWMutex
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	running           bool
	stopChan          chan struct{}
}

// NewStrategyEngine 创建新的策略引擎
func NewStrategyEngine(svcCtx *svc.ServiceContext) *StrategyEngine {
	ctx, cancel := context.WithCancel(context.Background())
	return &StrategyEngine{
		strategyDiscovery: NewStrategyDiscoveryService(svcCtx),
		strategyExecutor:  NewStrategyExecutor(svcCtx),
		svcCtx:            svcCtx,
		ctx:               ctx,
		cancel:            cancel,
		stopChan:          make(chan struct{}),
	}
}

// Start 启动策略引擎
func (se *StrategyEngine) Start() error {
	se.mu.Lock()
	defer se.mu.Unlock()

	if se.running {
		return fmt.Errorf("策略引擎已在运行")
	}

	// 重新创建context和channels，确保资源是全新的
	se.ctx, se.cancel = context.WithCancel(context.Background())
	se.stopChan = make(chan struct{})

	// 启动策略执行循环
	se.wg.Add(1)
	go se.strategyExecutionLoop()

	se.running = true
	utils.LogStrategyInfo("策略引擎已启动")

	// 立即执行一次策略检查
	go func() {
		time.Sleep(1 * time.Second)
		se.executeAllStrategies()
	}()

	return nil
}

// Stop 停止策略引擎
func (se *StrategyEngine) Stop() {
	se.mu.Lock()
	defer se.mu.Unlock()

	if !se.running {
		return
	}

	se.running = false

	// 发送停止信号和取消context
	select {
	case se.stopChan <- struct{}{}:
	default:
	}

	if se.cancel != nil {
		se.cancel()
	}

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		se.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
	case <-time.After(5 * time.Second):
		utils.LogStrategyError("策略引擎停止超时")
	}

	utils.LogStrategyInfo("策略引擎已停止")
}

// ExecuteStrategyNow 立即执行指定策略
func (se *StrategyEngine) ExecuteStrategyNow(strategyID string) error {
	se.mu.RLock()
	running := se.running
	se.mu.RUnlock()

	if !running {
		return fmt.Errorf("策略引擎未运行")
	}

	// 解析策略ID (格式: db_strategy_123)
	var dbStrategyID int64
	if _, err := fmt.Sscanf(strategyID, "db_strategy_%d", &dbStrategyID); err != nil {
		return fmt.Errorf("无效的策略ID格式: %s", strategyID)
	}

	// 获取策略信息
	strategy, err := se.svcCtx.StrategyModel.GetById(dbStrategyID)
	if err != nil {
		return fmt.Errorf("获取策略信息失败: %w", err)
	}

	if !strategy.Enabled {
		return fmt.Errorf("策略已禁用: %s", strategy.Name)
	}

	// 使用新的策略发现服务发现执行目标
	targets, err := se.strategyDiscovery.DiscoverTargetsForStrategy(strategy)
	if err != nil {
		return fmt.Errorf("发现策略执行目标失败: %w", err)
	}

	if len(targets) == 0 {
		return nil
	}

	// 使用新的策略执行器执行策略
	for _, target := range targets {
		if err := se.strategyExecutor.ExecuteStrategy(se.ctx, target, strategy); err != nil {
			utils.LogStrategyError("执行策略失败", "strategy_name", strategy.Name, "target", target.String(), "error", err.Error())
		}
	}

	return nil
}

// strategyExecutionLoop 策略执行循环
func (se *StrategyEngine) strategyExecutionLoop() {
	defer se.wg.Done()

	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			se.executeAllStrategies()
		case <-se.stopChan:
			return
		case <-se.ctx.Done():
			return
		}
	}
}

// executeAllStrategies 执行所有启用的策略
func (se *StrategyEngine) executeAllStrategies() {
	// 检查是否已经被停止
	select {
	case <-se.ctx.Done():
		return
	default:
	}

	// 获取所有启用的策略
	strategies, err := se.svcCtx.StrategyModel.GetEnabledStrategies()
	if err != nil {
		return
	}

	if len(strategies) == 0 {
		return
	}

	executedCount := 0
	skippedCount := 0
	errorCount := 0

	for _, strategy := range strategies {
		// 在每个策略执行前检查是否需要停止
		select {
		case <-se.ctx.Done():
			return
		default:
		}

		result := se.executeStrategy(strategy)
		switch result {
		case "executed":
			executedCount++
		case "skipped":
			skippedCount++
		case "error":
			errorCount++
		}
	}

}

// executeStrategy 执行单个策略
func (se *StrategyEngine) executeStrategy(strategy *model.Strategy) string {
	// 检查是否已经被停止
	select {
	case <-se.ctx.Done():
		return "skipped"
	default:
	}

	// 检查策略执行频率
	if strategy.LastTriggered != nil {
		nextExecutionTime := strategy.LastTriggered.Add(time.Duration(strategy.MonitorFrequency) * time.Second)
		if time.Now().Before(nextExecutionTime) {
			return "skipped"
		}
	}

	// 使用策略发现服务发现执行目标
	targets, err := se.strategyDiscovery.DiscoverTargetsForStrategy(strategy)
	if err != nil {
		utils.LogStrategyError("发现策略执行目标失败", "strategy_name", strategy.Name, "error", err.Error())
		return "error"
	}

	if len(targets) == 0 {
		return "skipped"
	}

	// 使用策略执行器执行策略
	successCount := 0
	errorCount := 0
	for _, target := range targets {
		// 在每个目标执行前检查是否需要停止
		select {
		case <-se.ctx.Done():
			return "skipped"
		default:
		}

		if err := se.strategyExecutor.ExecuteStrategy(se.ctx, target, strategy); err != nil {
			utils.LogStrategyError("执行策略失败", "strategy_name", strategy.Name, "error", err.Error())
			errorCount++
		} else {
			successCount++
		}
	}

	// 更新策略触发信息
	if successCount > 0 {
		if err := se.svcCtx.StrategyModel.UpdateTriggerInfo(strategy.ID); err != nil {
			utils.LogStrategyError("更新策略触发信息失败", "strategy_name", strategy.Name, "error", err.Error())
		}
	}

	if errorCount > 0 {
		return "error"
	} else if successCount > 0 {
		return "executed"
	} else {
		return "skipped"
	}
}

// IsRunning 安全地检查引擎是否运行
func (se *StrategyEngine) IsRunning() bool {
	se.mu.RLock()
	defer se.mu.RUnlock()
	return se.running
}

// GetEngineStatus 获取引擎状态
func (se *StrategyEngine) GetEngineStatus() map[string]interface{} {
	se.mu.RLock()
	defer se.mu.RUnlock()

	status := map[string]interface{}{
		"running": se.running,
		"version": "v2.0",
	}

	if se.running {
		strategies, err := se.svcCtx.StrategyModel.GetEnabledStrategies()
		if err == nil {
			status["enabled_strategies"] = len(strategies)
		}
	}

	return status
}
