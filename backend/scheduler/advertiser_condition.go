package scheduler

import (
	"encoding/json"
	"fmt"
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// EvaluateAdvertiserConditions 评估广告主是否满足策略条件
func (ce *ConditionEvaluator) EvaluateAdvertiserConditions(advertiser *model.Advertiser, strategy *model.Strategy) (bool, error) {
	utils.LogStrategyInfo("开始评估广告主策略条件", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)

	// 解析策略条件
	var conditions []Condition
	if err := json.Unmarshal([]byte(strategy.Conditions), &conditions); err != nil {
		return false, fmt.Errorf("解析策略条件失败: %w", err)
	}

	if len(conditions) == 0 {
		utils.LogStrategyInfo("策略没有配置条件，广告主满足条件", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name)
		return true, nil
	}

	// 评估广告主是否满足条件
	result := ce.evaluateAdvertiserConditions(advertiser, conditions, strategy.LogicType)

	utils.LogStrategyInfo("广告主条件评估完成", "advertiser_name", advertiser.AdvertiserName, "strategy_name", strategy.Name, "result", result)
	return result, nil
}

// evaluateAdvertiserConditions 评估广告主条件
func (ce *ConditionEvaluator) evaluateAdvertiserConditions(advertiser *model.Advertiser, conditions []Condition, logicType string) bool {
	switch logicType {
	case "AND":
		// 所有条件都必须满足
		for i, condition := range conditions {
			if !ce.evaluateAdvertiserCondition(condition, advertiser) {
				utils.LogStrategyInfo("广告主不满足条件", "advertiser_name", advertiser.AdvertiserName, "condition_index", i+1, "condition", condition)
				return false
			}
		}
		return true

	case "OR":
		// 至少一个条件满足
		for i, condition := range conditions {
			if ce.evaluateAdvertiserCondition(condition, advertiser) {
				utils.LogStrategyInfo("广告主满足条件", "advertiser_name", advertiser.AdvertiserName, "condition_index", i+1, "condition", condition)
				return true
			}
		}
		return false

	default:
		// 默认使用AND逻辑
		for i, condition := range conditions {
			if !ce.evaluateAdvertiserCondition(condition, advertiser) {
				utils.LogStrategyInfo("广告主不满足条件", "advertiser_name", advertiser.AdvertiserName, "condition_index", i+1, "condition", condition)
				return false
			}
		}
		return true
	}
}

// evaluateAdvertiserCondition 评估单个广告主条件
func (ce *ConditionEvaluator) evaluateAdvertiserCondition(condition Condition, advertiser *model.Advertiser) bool {
	switch condition.Type {
	case "advertiser_status":
		return ce.evaluateAdvertiserStatus(condition, advertiser)
	case "account_balance":
		return ce.evaluateAdvertiserAccountBalance(condition, advertiser)
	case "stat_cost":
		return ce.evaluateAdvertiserStatCost(condition, advertiser)
	case "show_cnt":
		return ce.evaluateAdvertiserShowCnt(condition, advertiser)
	case "convert_cnt":
		return ce.evaluateAdvertiserConvertCnt(condition, advertiser)
	case "conversion_cost":
		return ce.evaluateAdvertiserConversionCost(condition, advertiser)
	case "conversion_rate":
		return ce.evaluateAdvertiserConversionRate(condition, advertiser)
	case "click_cnt":
		return ce.evaluateAdvertiserClickCnt(condition, advertiser)
	case "ctr":
		return ce.evaluateAdvertiserCtr(condition, advertiser)
	case "cmp_platform":
		return ce.evaluateAdvertiserCpm(condition, advertiser)
	// 可以根据需要添加更多广告主级别的条件类型
	default:
		utils.LogStrategyInfo("未知的广告主条件类型", "type", condition.Type)
		return false
	}
}

// evaluateAdvertiserStatus 评估广告主状态
func (ce *ConditionEvaluator) evaluateAdvertiserStatus(condition Condition, advertiser *model.Advertiser) bool {
	// 广告主状态映射：前端字符串值 -> 后端数字值
	statusMapping := map[string]int{
		"active":  1, // 正常
		"paused":  2, // 暂停
		"deleted": 3, // 删除
	}

	var result bool
	switch condition.Operator {
	case "==":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		// 如果前端传入的是字符串状态值，转换为数字值
		if mappedStatus, exists := statusMapping[expectedStatus]; exists {
			result = advertiser.AdvertiserStatus == mappedStatus
		} else {
			// 直接比较数字值
			if expectedStatusInt, err := strconv.Atoi(expectedStatus); err == nil {
				result = advertiser.AdvertiserStatus == expectedStatusInt
			}
		}
	case "!=":
		expectedStatus := fmt.Sprintf("%v", condition.Value)
		// 如果前端传入的是字符串状态值，转换为数字值
		if mappedStatus, exists := statusMapping[expectedStatus]; exists {
			result = advertiser.AdvertiserStatus != mappedStatus
		} else {
			// 直接比较数字值
			if expectedStatusInt, err := strconv.Atoi(expectedStatus); err == nil {
				result = advertiser.AdvertiserStatus != expectedStatusInt
			}
		}
	case "in":
		for _, status := range condition.Values {
			// 如果前端传入的是字符串状态值，转换为数字值
			if mappedStatus, exists := statusMapping[status]; exists {
				if advertiser.AdvertiserStatus == mappedStatus {
					result = true
					break
				}
			} else {
				// 直接比较数字值
				if statusInt, err := strconv.Atoi(status); err == nil && advertiser.AdvertiserStatus == statusInt {
					result = true
					break
				}
			}
		}
	default:
		utils.LogStrategyInfo("[条件判断] 类型: advertiser_status, 操作符不支持", "operator", condition.Operator)
		return false
	}

	utils.LogStrategyInfo("[条件判断] 类型: advertiser_status, 操作符: "+condition.Operator, "左值", advertiser.AdvertiserStatus, "右值", condition.Value, "结果", result)
	return result
}

// evaluateAdvertiserAccountBalance 评估广告主账户余额
func (ce *ConditionEvaluator) evaluateAdvertiserAccountBalance(condition Condition, advertiser *model.Advertiser) bool {
	balance, err := strconv.ParseFloat(advertiser.AdvertiserBalance, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: account_balance, 解析余额失败", "balance", advertiser.AdvertiserBalance, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("account_balance", balance, condition)
}

// evaluateAdvertiserStatCost 评估广告主消耗
func (ce *ConditionEvaluator) evaluateAdvertiserStatCost(condition Condition, advertiser *model.Advertiser) bool {
	cost, err := strconv.ParseFloat(advertiser.StatCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: stat_cost, 解析广告主消耗失败", "stat_cost", advertiser.StatCost, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("stat_cost", cost, condition)
}

// evaluateAdvertiserShowCnt 评估广告主展示量
func (ce *ConditionEvaluator) evaluateAdvertiserShowCnt(condition Condition, advertiser *model.Advertiser) bool {
	shows, err := strconv.ParseFloat(advertiser.ShowCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: show_cnt, 解析广告主展示量失败", "show_cnt", advertiser.ShowCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("show_cnt", shows, condition)
}

// evaluateAdvertiserConvertCnt 评估广告主转化数
func (ce *ConditionEvaluator) evaluateAdvertiserConvertCnt(condition Condition, advertiser *model.Advertiser) bool {
	converts, err := strconv.ParseFloat(advertiser.ConvertCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: convert_cnt, 解析广告主转化数失败", "convert_cnt", advertiser.ConvertCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("convert_cnt", converts, condition)
}

// evaluateAdvertiserConversionCost 评估广告主转化成本
func (ce *ConditionEvaluator) evaluateAdvertiserConversionCost(condition Condition, advertiser *model.Advertiser) bool {
	cost, err := strconv.ParseFloat(advertiser.ConversionCost, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: conversion_cost, 解析广告主转化成本失败", "conversion_cost", advertiser.ConversionCost, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("conversion_cost", cost, condition)
}

// evaluateAdvertiserConversionRate 评估广告主转化率
func (ce *ConditionEvaluator) evaluateAdvertiserConversionRate(condition Condition, advertiser *model.Advertiser) bool {
	rate, err := strconv.ParseFloat(advertiser.ConversionRate, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: conversion_rate, 解析广告主转化率失败", "conversion_rate", advertiser.ConversionRate, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("conversion_rate", rate, condition)
}

// evaluateAdvertiserClickCnt 评估广告主点击数
func (ce *ConditionEvaluator) evaluateAdvertiserClickCnt(condition Condition, advertiser *model.Advertiser) bool {
	clicks, err := strconv.ParseFloat(advertiser.ClickCnt, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: click_cnt, 解析广告主点击数失败", "click_cnt", advertiser.ClickCnt, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("click_cnt", clicks, condition)
}

// evaluateAdvertiserCtr 评估广告主点击率
func (ce *ConditionEvaluator) evaluateAdvertiserCtr(condition Condition, advertiser *model.Advertiser) bool {
	ctr, err := strconv.ParseFloat(advertiser.Ctr, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: ctr, 解析广告主点击率失败", "ctr", advertiser.Ctr, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("ctr", ctr, condition)
}

// evaluateAdvertiserCpm 评估广告主千次展现价格
func (ce *ConditionEvaluator) evaluateAdvertiserCpm(condition Condition, advertiser *model.Advertiser) bool {
	cpm, err := strconv.ParseFloat(advertiser.CpmPlatform, 64)
	if err != nil {
		utils.LogStrategyInfo("[条件判断] 类型: cpm_platform, 解析广告主千次展现价格失败", "cmp_platform", advertiser.CpmPlatform, "error", err.Error())
		return false
	}
	return ce.evaluateNumericCondition("cmp_platform", cpm, condition)
}
