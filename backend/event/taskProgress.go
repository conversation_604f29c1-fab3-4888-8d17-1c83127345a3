package event

import (
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/ffmpeg"
)

// 全局应用实例，用于发送事件
var eventApp *application.App

// SetApp 设置应用实例（在应用启动时调用一次即可）
func SetApp(app *application.App) {
	eventApp = app
}

// TaskProgress 任务进度
type TaskProgress struct {
	TaskId           string `json:"task_id"`            // 任务ID
	TaskName         string `json:"task_name"`          // 任务名称
	TaskDescription  string `json:"task_description"`   // 任务描述
	StepDescription  string `json:"step_description"`   // 当前步骤描述
	TotalSteps       int    `json:"total_steps"`        // 总步骤数
	CurrentStep      int    `json:"current_step"`       // 当前步骤（从1开始）
	StepProgress     int    `json:"step_progress"`      // 当前步骤进度（0-100）
	NextStepProgress int    `json:"next_step_progress"` // 下一步骤进度（0-100）
	StepTime         int64  `json:"step_time"`          // 预估步骤耗时(ms)
	Status           string `json:"status"`             // 任务状态：start,running, completed, error
	ErrorMessage     string `json:"error_message"`      // 错误信息（如果有）
}

// NewTaskProgress 创建新的任务进度
func NewTaskProgress(taskName, taskDescription string, totalSteps int) *TaskProgress {
	task := &TaskProgress{
		TaskId:           fmt.Sprintf("%d", ffmpeg.GetNoRepeatId()),
		TaskName:         taskName,
		TaskDescription:  taskDescription,
		TotalSteps:       totalSteps,
		CurrentStep:      0,
		StepDescription:  "",
		StepProgress:     0,
		NextStepProgress: 0,
		StepTime:         0,
		Status:           constant.Process_Event_TaskStart,
		ErrorMessage:     "",
	}

	// 发送任务开始事件
	return task.emitEvent(constant.Runtime_Event_MainProcess)
}

// UpdateProgress 更新进度
func (t *TaskProgress) UpdateProgress(step int, stepDescription string, stepProgress, nextStepProgress int, stepTime int64) *TaskProgress {
	t.CurrentStep = step
	t.StepDescription = stepDescription
	t.StepProgress = stepProgress
	t.NextStepProgress = nextStepProgress
	if t.NextStepProgress < t.StepProgress {
		t.NextStepProgress = t.StepProgress
	}
	t.StepTime = stepTime
	t.Status = constant.Process_Event_TaskProgress

	// 发送进度更新事件
	return t.emitEvent(constant.Runtime_Event_MainProcess)
}

// Complete 完成任务
func (t *TaskProgress) Complete() {
	t.Status = constant.Process_Event_TaskEnd
	t.CurrentStep = t.TotalSteps
	t.StepProgress = 100
	t.NextStepProgress = 100
	// 发送任务完成事件
	t.emitEvent(constant.Runtime_Event_MainProcess)
}

// Error 任务出错
func (t *TaskProgress) Error(errorMessage string) {
	t.Status = constant.Process_Event_TaskError
	t.ErrorMessage = errorMessage
	// 发送任务错误事件
	t.emitEvent(constant.Runtime_Event_MainProcess)
}

// emitEvent 发送事件（内部方法）
func (t *TaskProgress) emitEvent(eventName string) *TaskProgress {
	eventApp.EmitEvent(eventName, t)
	return t
}
