package event

import (
	"context"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
	"log"
	"time"
)

func InitEvents(app *application.App) {
	app.OnEvent(constant.Runtime_Event_OpenFolder, func(event *application.CustomEvent) {
		// 处理打开文件夹事件
		if filePath, ok := event.Data.(string); ok {
			// 打开文件所在目录
			go func() {
				ctx, _ := context.WithTimeout(context.Background(), 2*time.Second)
				if err := utils.OpenFolder(ctx, filePath); err != nil {
					log.Printf("打开文件所在目录失败: %v", err)
				}
			}()
		} else {
			app.Logger.Error("打开文件夹事件数据格式错误")
		}
	})

	// 设置任务进度工具的应用实例
	SetApp(app)
}
