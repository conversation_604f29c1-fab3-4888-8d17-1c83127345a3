package result

var (
	ErrorReqParam         = ErrorResult[any](10000, "请求参数错误")
	ErrorBindingParam     = ErrorResult[any](10001, "绑定参数错误")
	ErrorAdd              = ErrorResult[any](10100, "添加失败")
	ErrorUpdate           = ErrorResult[any](10101, "更新失败")
	ErrorDelete           = ErrorResult[any](10102, "删除失败")
	ErrorSelect           = ErrorResult[any](10103, "查询失败")
	ErrorAnalyze          = ErrorResult[any](10104, "分析失败")
	ErrorExportExcel      = ErrorResult[any](10105, "导出excel失败")
	ErrorCopy             = ErrorResult[any](10106, "复制失败")
	ErrorSubDirExist      = ErrorResult[any](10107, "子目录已存在")
	ErrorToken            = ErrorResult[any](10108, "生成token失败")
	ErrorUserLogin        = ErrorResult[any](10109, "用户名或密码错误")
	ErrorNotFound         = ErrorResult[any](10110, "未查询到数据")
	ErrorUserInfo         = ErrorResult[any](10111, "获取用户信息失败")
	ErrorGeneratePassword = ErrorResult[any](10112, "生成密码失败")
	ErrorTxCommit         = ErrorResult[any](10113, "事务提交失败")
	ErrorDataVerify       = ErrorResult[any](10114, "数据校验失败")
	ErrorDisableLogin     = ErrorResult[any](10115, "用户已禁用")
	ErrorFfmpeg           = ErrorResult[any](10116, "ffmpeg执行失败")
	ErrorDirNotExist      = ErrorResult[any](10117, "目录不存在")
	ErrorSystem           = ErrorResult[any](10118, "系统错误")
	ErrorSendPhoneCode    = ErrorResult[any](10119, "发送验证码失败")
	ErrorNotFoundWindow   = ErrorResult[any](10120, "未找到窗口")
	ErrorNewBot           = ErrorResult[any](10121, "创建Bot失败")
	ErrorExecute          = ErrorResult[any](10122, "执行失败")
	ErrorFileNotExist     = ErrorResult[any](10123, "文件不存在")
)

func ErrorResult[T any](code int, msg string) *Result[T] {
	return &Result[T]{
		Code: code,
		Msg:  msg,
	}
}
func ErrorSimpleResult[T any](msg string) *Result[T] {
	return &Result[T]{
		Code: 20000,
		Msg:  msg,
	}
}

// ToError 将一个类型的错误转换为另一个类型的错误
func ToError[T any](result *Result[any]) *Result[T] {
	return &Result[T]{
		Code: result.Code,
		Msg:  result.Msg,
	}
}
