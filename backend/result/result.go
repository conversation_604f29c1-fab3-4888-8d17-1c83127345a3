package result

type Result[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

func SuccessResult[T any](data T) *Result[T] {
	return NewResult[T](0, "success", data)
}
func ListResult(data any, page, pageSize, total int64) *Result[map[string]any] {
	return NewResult[map[string]any](0, "success", map[string]any{"list": data, "page": page, "pageSize": pageSize, "total": total})
}
func SimpleResult(msg string) *Result[any] {
	return NewResult[any](0, msg, nil)
}
func DataResult[T any](msg string, data T) *Result[T] {
	return NewResult[T](0, msg, data)
}
func NewResult[T any](code int, msg string, data T) *Result[T] {
	return &Result[T]{
		Code: code,
		Msg:  msg,
		Data: data,
	}
}

func (r Result[T]) AddError(err error) *Result[T] {
	if err != nil {
		r.Msg += "：" + err.Error()
	}
	return &r
}

func (r Result[T]) AddMessage(msg string) *Result[T] {
	r.Msg += "：" + msg
	return &r
}
