package config

import (
	"sync/atomic"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	App struct {
		Name        string `json:"name"`
		Version     string `json:"version"`
		Environment string `json:"environment"`
		Language    string `json:"language"`
		UpdateCheck bool   `json:"updateCheck"`
		Debug       bool   `json:"debug"`
	} `json:"app"`
	Ui struct {
		Theme  string `json:"theme"`
		Window struct {
			AlwaysOnTop bool `json:"alwaysOnTop"`
			Frameless   bool `json:"frameless"`
		} `json:"window"`
	} `json:"ui"`
	Storage struct {
		DownloadDir string `json:"downloadDir"`
		CacheDir    string `json:"cacheDir"`
		MaterialDir string `json:"materialDir"`
		MaxCacheMB  int    `json:"maxCacheMB"`
	} `json:"storage"`
	Update struct {
		Channel      string `json:"channel"`
		AutoDownload bool   `json:"autoDownload"`
		Endpoint     string `json:"endpoint"`
	} `json:"update"`
	Log struct {
		Level      string `json:"level"`      // 日志级别 (debug/info/warn/error/fatal)
		Format     string `json:"format"`     // 日志格式 (json/text)
		Output     string `json:"output"`     // 输出目标 (file/console/both)
		MaxSize    int    `json:"maxSize"`    // 单个日志文件最大大小(MB)
		MaxBackups int    `json:"maxBackups"` // 最大备份文件数
		MaxAge     int    `json:"maxAge"`     // 日志文件最大保存天数
		Compress   bool   `json:"compress"`   // 是否压缩
		Directory  string `json:"directory"`  // 日志目录
	} `json:"log"`
}

var globalConfig atomic.Value

func initConfig(path string) error {
	v := viper.New()
	v.SetConfigFile(path) // 自动识别扩展名 (.json / .yaml ...)
	v.AutomaticEnv()      // 环境变量覆盖，同名用下划线 LOG_LEVEL
	if err := v.ReadInConfig(); err != nil {
		return err
	}
	var c Config
	if err := v.Unmarshal(&c); err != nil {
		return err
	} else {
		globalConfig.Store(&c) // 将加载的配置存入全局变量
	}

	// 热更新（可选）
	v.WatchConfig()
	v.OnConfigChange(func(_ fsnotify.Event) {
		var c Config
		if err := viper.Unmarshal(&c); err == nil {
			globalConfig.Store(&c)
		}
	})

	return nil
}
func GetConfig() *Config {
	if cfg := globalConfig.Load(); cfg != nil {
		return cfg.(*Config)
	}
	return nil
}
func init() {
	if err := initConfig("./etc/config.json"); err != nil {
		panic("Failed to load config: " + err.Error())
	}
}
