package ffmpeg

import (
	"context"
	"errors"
	"fmt"
	ffmpeg "github.com/u2takey/ffmpeg-go"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"log"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"
)

func start(cmd *exec.Cmd) error {
	// 启动命令
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow: true,
	}
	return cmd.Run()
}

//	type TextLine struct {
//		Text        string  `json:"text,omitempty"`          // 文本内容
//		FontFile    string  `json:"font_file,omitempty"`     // 字体文件
//		FontSize    int64   `json:"font_size,omitempty"`     // 字体大小
//		FontColor   string  `json:"font_color,omitempty"`    // 字体颜色
//		PositionX   float64 `json:"position_x,omitempty"`    // X轴位置
//		PositionY   float64 `json:"position_y,omitempty"`    // Y轴位置
//		Box         int64   `json:"box,omitempty"`           // 是否启用背景框（0、禁用 1、启用）
//		BoxColor    string  `json:"box_color,omitempty"`     // 背景框颜色
//		BoxBorderRw int64   `json:"box_border_rw,omitempty"` // 背景框宽度
//		ShadowWx    int64   `json:"shadow_wx,omitempty"`     // 文本阴影的水平偏移
//		ShadowWy    int64   `json:"shadow_wy,omitempty"`     // 文本阴影的垂直偏移
//		ShadowColor string  `json:"shadow_color,omitempty"`  // 文本阴影颜色
//		BorderRw    int64   `json:"border_rw,omitempty"`     // 文本框边框宽度
//		BorderColor string  `json:"border_color,omitempty"`  // 文本框边框颜色
//	}
//
// 获取添加文字的Filter
func getTextFilter(t *model.TextInfo, preTag string) string {
	filter := ""
	if t == nil {
		return filter
	}
	//fontSize := int64(float64(t.FontSize) * 0.75) // css字体大小->ffmpeg字体大小
	fontSize := t.FontSize // css字体大小->ffmpeg字体大小
	filter += fmt.Sprintf("%sdrawtext=fontfile=%s:text='%s':fontsize=%d:fontcolor=%s:x=(w*%.2f)-(text_w/2):y=(h*%.2f)-(text_h/2)", preTag, t.FontFile, t.Text, fontSize, t.FontColor, t.PositionX/100, t.PositionY/100)
	t.Box--                             // 将Box从1-2转换为0-1
	if t.Box == 1 && t.BoxColor != "" { // 是否启用背景框（0、禁用 1、启用）
		filter += fmt.Sprintf(":box=%d:boxcolor=%s:boxborderw=%d", t.Box, t.BoxColor, t.BoxBorderRw)
	}
	if t.ShadowColor != "" { // 文本阴影颜色
		filter += fmt.Sprintf(":shadowx=%d:shadowy=%d:shadowcolor=%s", t.ShadowWx, t.ShadowWy, t.ShadowColor)
	}
	if t.BorderColor != "" { // 文字轮廓颜色
		filter += fmt.Sprintf(":borderw=%d:bordercolor=%s", t.BorderRw, t.BorderColor)
	}
	filter += "[v]"
	return filter
}

// 获取单个视频的Filter
func getSingleVideoFilter(t *model.TextInfo, width, height int, scaleMode string) string {
	var filter, videoTag string
	if t == nil {
		videoTag = "[v]"
	} else {
		videoTag = "[v3]"
	}
	//调整视频像素大小
	if scaleMode == constant.ClipTask_ScaleMode_WidthFix { //保证横向完全填充
		filter += fmt.Sprintf("[0:v]scale=%d:trunc(ow/a/2)*2,pad=%d:'max(%d,trunc(ow/a/2)*2)':(ow-iw)/2:(oh-ih)/2,crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2,setsar=1%s;", width, width, height, width, height, videoTag)
	} else if scaleMode == constant.ClipTask_ScaleMode_HeightFix { //保证纵向完全填充
		filter += fmt.Sprintf("[0:v]scale=trunc(oh*a/2)*2:%d,pad='max(%d,trunc(oh*a/2)*2)':%d:(ow-iw)/2:(oh-ih)/2,crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2,setsar=1%s;", height, width, height, width, height, videoTag)
	} else if scaleMode == constant.ClipTask_ScaleMode_ScaleToFill { //不保持纵横比缩放,拉伸至填满指定的尺寸
		filter += fmt.Sprintf("[0:v]scale=%d:%d,pad=%d:%d:(ow-iw)/2:(oh-ih)/2,setsar=1%s;", width, height, width, height, videoTag)
	} else {
		videoTag = "[0:v]"
	}
	//添加标题
	filter += getTextFilter(t, videoTag)
	return filter
}

func generateVideoFileName(inputFile string, width, height int, scaleMode string) string {
	ffmpeg.Input(inputFile).Compile()
	//调整视频像素大小
	if scaleMode == constant.ClipTask_ScaleMode_WidthFix { //保证横向完全填充
		return fmt.Sprintf("[0:v]scale=%d:trunc(ow/a/2)*2;pad=%d:'max(%d,trunc(ow/a/2)*2)':(ow-iw)/2:(oh-ih)/2;crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2;setsar=1[v]", width, width, height, width, height)
	} else if scaleMode == constant.ClipTask_ScaleMode_HeightFix { //保证纵向完全填充
		return fmt.Sprintf("[0:v]scale=trunc(oh*a/2)*2:%d;pad='max(%d,trunc(oh*a/2)*2)':%d:(ow-iw)/2:(oh-ih)/2;crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2;setsar=1[v]", height, width, height, width, height)
	} else if scaleMode == constant.ClipTask_ScaleMode_ScaleToFill { //不保持纵横比缩放,拉伸至填满指定的尺寸
		return fmt.Sprintf("[0:v]scale=%d:%d;pad=%d:%d:(ow-iw)/2:(oh-ih)/2[v1];[v1]setsar=1[v]", width, height, width, height)
	} else {
		return ""
	}
}

// 获取合成视频的Filter
func getConcatVideoFilter(videoLength int, mixedMode int64, t *model.TextInfo, hasHeadCover bool) string {
	var filter, videoTag string
	videoTag = "[v]"
	filter += "[0:a]anull[a];[0:v]fps=fps=30,settb=AVTB[v];"
	for i := 1; i < videoLength; i++ {
		filter += fmt.Sprintf("[%d:v]fps=fps=30,settb=AVTB[v%d];", i, i)
	}
	//拼接视频							  0 1 2 3 4
	for i := 1; i < videoLength; i++ { // s s s s f
		filter += fmt.Sprintf("[v][a][v%d][%d:a]concat=n=2:v=1:a=1[v][a];", i, i)
	}
	if mixedMode == constant.ClipTask_MusicMixMode_Mixd {
		//合成音频
		filter += fmt.Sprintf("[a][%d:a]amix=inputs=2:duration=first:dropout_transition=3[a];", videoLength)
	} else if mixedMode == constant.ClipTask_MusicMixMode_OnlyBgm {
		//什么都不用做
	} else if mixedMode == constant.ClipTask_MusicMixMode_NoBgm {
		//什么都不用做
	}
	filter += "[v]setsar=1[v]"
	//添加标题
	filter += getTextFilter(t, videoTag)
	return filter
}

// 生成视频首帧和尾帧 ffmpeg -loop 1 -i cover.jpg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 0.1 -c:v libx264 -c:a aac -shortest -y tail_frame.mp4
func getSingleFrameVideo(imageFile string, width, height int, scaleMode, outputFile string) []string {
	input := make([]string, 0, 20)
	input = append(input, "-f", "image2")
	input = append(input, "-loop", "1")
	input = append(input, "-i", imageFile)
	input = append(input, "-f", "lavfi")
	input = append(input, "-i", "anullsrc=channel_layout=stereo:sample_rate=44100")
	input = append(input, "-filter_complex")
	//调整视频像素大小
	if scaleMode == constant.ClipTask_ScaleMode_WidthFix { //保证横向完全填充
		input = append(input, fmt.Sprintf("[0:v]scale=%d:trunc(ow/a/2)*2[v0];[v0]pad=%d:'max(%d,trunc(ow/a/2)*2)':(ow-iw)/2:(oh-ih)/2[v1];[v1]crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2[v2];[v2]setsar=1[v]", width, width, height, width, height))
	} else if scaleMode == constant.ClipTask_ScaleMode_HeightFix { //保证纵向完全填充
		input = append(input, fmt.Sprintf("[0:v]scale=trunc(oh*a/2)*2:%d[v0];[v0]pad='max(%d,trunc(oh*a/2)*2)':%d:(ow-iw)/2:(oh-ih)/2[v1];[v1]crop=%d:%d:(in_w-out_w)/2:(in_h-out_h)/2[v2];[v2]setsar=1[v]", height, width, height, width, height))
	} else if scaleMode == constant.ClipTask_ScaleMode_ScaleToFill { //不保持纵横比缩放,拉伸至填满指定的尺寸
		input = append(input, fmt.Sprintf("[0:v]scale=%d:%d[v0];[v0]pad=%d:%d:(ow-iw)/2:(oh-ih)/2[v1];[v1]setsar=1[v]", width, height, width, height))
	}

	input = append(input, "-t", "0.5")
	input = append(input, "-c:v", "libx264")
	input = append(input, "-c:a", "aac")
	input = append(input, "-map", "[v]")
	input = append(input, "-map", "1:a")
	input = append(input, "-shortest")
	input = append(input, "-y", outputFile)
	return input
}

// 设置视频封面 ffmpeg -i in/29048173858895208.mp4 -i in/cover.jpg -c copy -map 0 -map 1 -c:v:1 png -disposition:v:1 attached_pic -y in/pp.mp4
func getHasCoverVideo(videoFile, coverFile, outputFile string) []string {
	input := make([]string, 0, 20)
	input = append(input, "-i", videoFile)
	input = append(input, "-i", coverFile)
	input = append(input, "-c", "copy")
	input = append(input, "-map", "0")
	input = append(input, "-map", "1")
	input = append(input, "-c:v:1", "png")
	input = append(input, "-disposition:v:1", "attached_pic")
	input = append(input, "-y", outputFile)
	return input
}

// 合成视频列表
func getConcatVideo(videoList []string, t *model.TextInfo, bgm string, mixedMode int64, hasHeadCover bool, outputFile string) []string {
	var (
		input  []string
		filter string
		audio  string
	)
	//拼接输入视频
	for _, src := range videoList {
		input = append(input, "-i", src)
	}
	//没有bgm则混合模式为 no-bgm
	if bgm == "" {
		mixedMode = constant.ClipTask_MusicMixMode_NoBgm
	}
	if mixedMode != constant.ClipTask_MusicMixMode_NoBgm {
		//拼接背景音乐
		input = append(input, "-stream_loop", "-1", "-i", bgm)
	}
	//获取过滤链
	filter = getConcatVideoFilter(len(videoList), mixedMode, t, hasHeadCover)
	if mixedMode == constant.ClipTask_MusicMixMode_Mixd {
		audio = "[a]"
	} else if mixedMode == constant.ClipTask_MusicMixMode_OnlyBgm {
		audio = fmt.Sprintf("%d:a", len(videoList))
	} else if mixedMode == constant.ClipTask_MusicMixMode_NoBgm {
		audio = "[a]"
	}
	input = append(input, "-filter_complex", filter)
	//映射音视频，输出视频
	input = append(input, "-map", audio)
	input = append(input,
		"-map", "[v]",
		"-c:v", "libx264",
		"-c:a", "aac",
		"-vsync", "vfr",
		"-shortest",
		"-y",
		outputFile)
	return input
}

// 生成单个视频
func getSingleVideo(inputFile string, t *model.TextInfo, isOriginSound bool, width, height int, scaleMode string, outputFile string) []string {
	var (
		input    []string
		filter   string
		audioTag string
	)
	if isOriginSound {
		audioTag = "a"
	} else {
		audioTag = "1:a"
	}
	//拼接输入视频和静音轨
	input = append(input, "-i", inputFile)
	input = append(input, "-f", "lavfi", "-i", "anullsrc=channel_layout=stereo:sample_rate=44100")
	//获取过滤链
	filter = getSingleVideoFilter(t, width, height, scaleMode)
	input = append(input, "-filter_complex", filter)
	//映射音视频，输出视频
	input = append(input,
		"-map", audioTag,
		"-map", "[v]",
		"-c:v", "libx264",
		"-c:a", "aac",
		"-shortest",
		"-y",
		outputFile)
	return input
}

func StartGenSingleVideo(ctx context.Context, inputFile string, t *model.TextInfo, isOriginSound bool, width, height int, scaleMode string) (string, error) {
	// 获取用户下载目录
	outputDir := config.GetConfig().Storage.DownloadDir
	outputFile := filepath.Join(outputDir, fmt.Sprintf("%s_%d.mp4", strings.TrimSuffix(filepath.Base(inputFile), filepath.Ext(inputFile)), GetNoRepeatId()))
	cmd := exec.CommandContext(ctx, "./tools/ffmpeg", getSingleVideo(inputFile, t, isOriginSound, width, height, scaleMode, outputFile)...)
	fmt.Printf("startGenSingleVideo command: %s\n", cmd.String())
	return outputFile, start(cmd)
}

func startGenConcatVideo(ctx context.Context, videoList []string, t *model.TextInfo, bgm string, mixedMode int64, hasHeadCover bool) (string, error) {
	outputDir := config.GetConfig().Storage.DownloadDir
	outputFile := filepath.Join(outputDir, strconv.FormatInt(GetNoRepeatId(), 10)+".mp4")
	cmd := exec.CommandContext(ctx, "./tools/ffmpeg", getConcatVideo(videoList, t, bgm, mixedMode, hasHeadCover, outputFile)...)
	fmt.Printf("startGenConcatVideo command: %s\n", cmd.String())
	return outputFile, start(cmd)
}
func StartGenHasCoverVideo(ctx context.Context, videoFile, coverFile string) (string, error) {
	// 获取用户下载目录
	outputDir := config.GetConfig().Storage.DownloadDir
	outputFile := filepath.Join(outputDir, strconv.FormatInt(GetNoRepeatId(), 10)+".mp4")
	cmd := exec.CommandContext(ctx, "./tools/ffmpeg", getHasCoverVideo(videoFile, coverFile, outputFile)...)
	fmt.Printf("startGenHasCoverVideo command: %s\n", cmd.String())
	return outputFile, start(cmd)
}

func startGenSingleFrameVideo(ctx context.Context, inputFile string, width, height int, scaleMode string) (string, error) {
	outputFile := "in/" + strconv.FormatInt(GetNoRepeatId(), 10) + ".mp4"
	cmd := exec.CommandContext(ctx, "./tools/ffmpeg", getSingleFrameVideo(inputFile, width, height, scaleMode, outputFile)...)
	fmt.Printf("startGenSingleFrameVideo command: %s\n", cmd.String())
	return outputFile, start(cmd)
}

// 删除文件列表

func removeFileList(fileList []string) error {
	for _, filePath := range fileList {
		err := os.Remove(filePath)
		if err != nil {
			return err
		}
	}
	return nil
}

func GenerateVideo(task *model.ClipTask, bgm, headCover, tailCover string, videoList []string) (string, error) {
	singleVideoList := make([]string, 0, len(videoList)+2)
	//使用预制的封面
	singleVideoList = append(singleVideoList, headCover)
	// 使用预制的视频片段
	singleVideoList = append(singleVideoList, videoList...)
	// 使用预制的尾帧
	singleVideoList = append(singleVideoList, tailCover)

	//过滤掉空字符串
	filterSingleVideoList := SliceFilter(singleVideoList, func(s string) bool {
		return s != ""
	})
	hasHeadCover := false
	if headCover != "" {
		hasHeadCover = true
	}
	//创建生成视频的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()
	concatVideoFile, err := startGenConcatVideo(ctx, filterSingleVideoList, nil, bgm, task.MusicMixMode, hasHeadCover)
	if err != nil {
		log.Println("startGenConcatVideo Error: ", filterSingleVideoList, nil, bgm, task.MusicMixMode, hasHeadCover)
		return "", errors.New(fmt.Sprintf("合成视频失败,背景音乐url：%s %s", bgm, err.Error()))
	}
	var latestVideo string
	if headCover != "" {
		//创建生成视频的上下文
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()
		latestVideo, err = StartGenHasCoverVideo(ctx, concatVideoFile, tailCover)
		if err != nil {
			log.Println("startGenSingleFrameVideo Error: ", concatVideoFile, tailCover)
			return "", errors.New(fmt.Sprintf("生成视频尾帧失败,视频尾帧url：%s %s", tailCover, err.Error()))
		}
	} else {
		latestVideo = concatVideoFile
	}
	return latestVideo, nil
}

/**
* 生成视频
* @param task 任务信息
* @param globalTextList 全局字幕列表
* @param bgmList 背景音乐列表
* @param groupList 摄像头分组列表
* @param videoMap 摄像头视频列表
* @param textMap 摄像头字幕列表
* @param coverMap 摄像头封面列表
* @param position 位置
* @return 视频地址
 */

// GenerateVideoByPosition 全局字幕 + 背景音乐 + 头部封面 + 尾部封面 + 第一组视频 + 第二组视频 + ... + 最后一组视频 + 第一组字幕 + 第二组字幕 + ... + 最后一组字幕 + 第一组转场 + 第二组转场 + ... + 最后一组转场，position 从0开始
func GenerateVideoByPosition(clipTask *model.ClipTask, bgmList []string, headCoverList []string, tailCoverList []string, groupList []model.ClipTaskVideoGroup, position int64, outFilePath string) (string, error) {
	indexList, err := getIndexList(bgmList, headCoverList, tailCoverList, groupList, position)
	if err != nil {
		return "", err
	}
	//根据索引取数据
	videoList := make([]string, 0, len(groupList))
	for i, group := range groupList {
		videoList = append(videoList, group.VideoUrl[indexList[i+3]])
	}
	var (
		bgm       string
		headCover string
		tailCover string
	)
	if len(bgmList) > 0 {
		bgm = bgmList[indexList[0]]
	}
	if len(headCoverList) > 0 {
		headCover = headCoverList[indexList[1]]
	}
	if len(tailCoverList) > 0 {
		tailCover = tailCoverList[indexList[2]]
	}
	//生成视频
	videoUrl, err := GenerateVideo(clipTask, bgm, headCover, tailCover, videoList)
	if err != nil {
		return "", err
	}
	//修改文件名
	newNameList := make([]int, len(indexList))
	for i, index := range indexList {
		newNameList[i] = index + 1
	}
	// 确保目标文件夹存在
	if err := os.MkdirAll(outFilePath, os.ModePerm); err != nil {
		fmt.Printf("创建文件夹失败: %v\n", err)
		return videoUrl, err
	}
	newName := fmt.Sprintf("%s/%s_%v.mp4", outFilePath, clipTask.ClipTaskName, newNameList)
	if err := os.Rename(videoUrl, newName); err != nil {
		fmt.Printf("重命名失败: %v\n", err)
		return videoUrl, err
	}
	return newName, nil
}

// getIndexList 计算索引列表  背景音乐 + 头部封面 + 尾部封面 + 第一组视频 + 第二组视频 + ... + 最后一组视频
func getIndexList(bgmList []string, headCoverList []string, tailCoverList []string, groupList []model.ClipTaskVideoGroup, position int64) ([]int, error) {
	//获取维度列表
	numList := make([]int, 0, len(groupList)+3)
	numList = append(numList, len(bgmList))
	numList = append(numList, len(headCoverList))
	numList = append(numList, len(tailCoverList))
	for _, group := range groupList {
		numList = append(numList, len(group.VideoUrl))
	}
	//计算因数
	factorNum := int64(1)
	for _, num := range numList {
		if num > 0 {
			factorNum *= int64(num)
		}
	}
	if factorNum <= position {
		return nil, errors.New("position out of range: factorNum=" + strconv.FormatInt(factorNum, 10) + ", position=" + strconv.FormatInt(position, 10))
	}
	//计算索引列表
	indexList := make([]int, 0, len(numList))
	for _, num := range numList {
		if num > 0 {
			factorNum /= int64(num)
			indexList = append(indexList, int(position/factorNum))
			position %= factorNum
		} else {
			indexList = append(indexList, int(position/factorNum)-1)
		}
	}
	return indexList, nil
}

func init() {
	//检查上级目录是否存在，不存在则创建
	if err := os.MkdirAll("./in", 0755); err != nil {
		log.Println("Error creating directory in:", err)
		return
	}
	//检查上级目录是否存在，不存在则创建
	if err := os.MkdirAll("./out", 0755); err != nil {
		log.Println("Error creating directory out:", err)
		return
	}
	//检查上级目录是否存在，不存在则创建
	if err := os.MkdirAll("./temp", 0755); err != nil {
		log.Println("Error creating directory temp:", err)
		return
	}
}

// VideoVariationOptions 视频变体生成选项
type VideoVariationOptions struct {
	InputPath     string              // 输入视频路径
	OutputPath    string              // 输出视频路径
	AspectRatio   string              // 视频比例
	Width         int                 // 视频宽度
	Height        int                 // 视频高度
	Effects       []types.VideoEffect // 效果列表
	IsOriginSound bool                // 是否保留原声音
}

// GenerateVideoVariation 生成视频变体
func GenerateVideoVariation(ctx context.Context, options VideoVariationOptions) error {
	// 构建基础命令
	args := []string{
		"-i", options.InputPath,
		"-c:v", "libx264",
		"-preset", "medium",
		"-crf", "23",
	}

	// 处理分辨率
	//if options.Width > 0 && options.Height > 0 {
	//	args = append(args, "-vf", fmt.Sprintf("scale=%d:%d", options.Width, options.Height))
	//}

	filterArgs := make([]string, 0)
	// 处理比例
	switch options.AspectRatio {
	case "9:16":
		//args = append(args, "-filter_complex", "[0:v]scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2")
		filterArgs = append(filterArgs, "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2")
	case "16:9":
		filterArgs = append(filterArgs, "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2")
	default:
		filterArgs = append(filterArgs, fmt.Sprintf("scale=%d:%d:force_original_aspect_ratio=decrease,pad=%d:%d:(ow-iw)/2:(oh-ih)/2", options.Width, options.Height, options.Width, options.Height))
	}

	// 应用效果
	for _, effect := range options.Effects {
		switch effect {
		case "crop_scale":
			// 随机裁剪和缩放
			scale := 0.95 + rand.Float64()*0.1 // 0.95-1.05
			filterArgs = append(filterArgs, fmt.Sprintf("scale=iw*%f:ih*%f", scale, scale))
		case "rotation":
			// 随机旋转（-1到1度）
			angle := -1 + rand.Float64()*2
			filterArgs = append(filterArgs, fmt.Sprintf("rotate=%f*PI/180", angle))
		case "brightness":
			// 随机亮度和对比度调整
			brightness := -0.1 + rand.Float64()*0.2 // -0.1到0.1
			contrast := 0.9 + rand.Float64()*0.2    // 0.9到1.1
			filterArgs = append(filterArgs, fmt.Sprintf("eq=brightness=%f:contrast=%f", brightness, contrast))
		case "color":
			// 随机色彩调整
			saturation := 0.9 + rand.Float64()*0.2 // 0.9到1.1
			filterArgs = append(filterArgs, fmt.Sprintf("eq=saturation=%f", saturation))
		case "speed":
			// 随机速度调整（0.95到1.05倍）
			//speed := 0.95 + rand.Float64()*0.1
			//args = append(args, fmt.Sprintf("[0:v]setpts=%f*PTS[v];[0:a]atempo=%f[a]", 1/speed, speed))
			//filterArgs = append(filterArgs, fmt.Sprintf("[0:v]setpts=%f*PTS[v];[0:a]atempo=%f[a]", 1/speed, speed))
		case "sharpen":
			// 随机锐化或模糊
			if rand.Float64() > 0.5 {
				filterArgs = append(filterArgs, "unsharp=3:3:1.5")
			} else {
				filterArgs = append(filterArgs, "boxblur=1:1")
			}
		case "volume":
			// 随机音量调整（-3dB到3dB）
			//volume := -3 + rand.Float64()*6
			//args = append(args, "-af", fmt.Sprintf("volume=%fdB", volume))

		case "bitrate":
			// 随机码率调整（-10%到+10%）
			//bitrate := 2000 + rand.Intn(400) // 1800-2200kbps
			//args = append(args, "-b:v", fmt.Sprintf("%dk", bitrate))
		}
	}
	filterArgs = append(filterArgs, "scale='floor(iw/2)*2:floor(ih/2)*2'")
	args = append(args, "-filter_complex", "[0:v]"+strings.Join(filterArgs, ",")+"[v]")
	// 处理音频
	if options.IsOriginSound {
		args = append(args, "-c:a", "aac", "-b:a", "128k")
	} else {
		args = append(args, "-an")
	}
	// 映射视频和音频流
	args = append(args, "-map", "[v]")
	if options.IsOriginSound {
		args = append(args, "-map", "0:a")
	} else {
		//args = append(args, "-map", "[a]")
	}
	// 添加输出文件
	args = append(args, "-y", options.OutputPath)
	fmt.Println("ffmpeg", args)
	// 执行命令
	cmd := exec.CommandContext(ctx, "./tools/ffmpeg", args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("生成视频变体失败: %v", err)
	}

	return nil
}
