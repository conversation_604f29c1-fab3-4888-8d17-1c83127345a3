package ffmpeg

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"
)

func ToFontFile(name string) string {
	switch name {
	case "SimSun":
		name = "宋体"
	case "SimHei":
		name = "黑体"
	case "FangSong":
		name = "仿宋"
	case "Microsoft YaHei":
		name = "微软雅黑"
	}
	switch name {
	case "黑体":
		return "/usr/share/fonts/winfont/simhei.ttf"
	case "宋体":
		return "/usr/share/fonts/winfont/simsun.ttc"
	case "仿宋":
		return "/usr/share/fonts/winfont/simfang.ttf"
	case "微软雅黑":
		return "/usr/share/fonts/winfont/msyh.ttc"
	case "微软雅黑-粗体":
		return "/usr/share/fonts/winfont/msyhbd.ttc"
	case "普惠体":
		return "/usr/share/fonts/winfont/albb_ht_bold.ttf"
	default:
		return "/usr/share/fonts/winfont/msyhbd.ttc"
	}
}

// [{"URL":"https://res-pub.e-idear.com/enginer/upload/oceanengine/20240808/1723083987955_108x108.jpg","MD5":"ae88e9077cadd3c05e491e4947852232"}]
func GetFileMD5(filename string) (string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()
	//MD5
	var buf bytes.Buffer
	if _, err = io.Copy(&buf, file); err != nil {
		return "", err
	}
	hash := md5.New()
	_, err = io.Copy(hash, &buf)
	if err != nil {
		return "", err
	}
	md5Hash := hex.EncodeToString(hash.Sum(nil))
	return md5Hash, nil
}

var (
	//全局异步
	globalSyncMap = sync.Map{}
)

// GetNoRepeatId 生成随机ID (前40位时间戳，后24位随机数)
func GetNoRepeatId() int64 {
	//获取当前时间(秒)
	curTimeSec := time.Now().Unix()
	//生成随机数
	randInt64 := rand.Int63()
	//生成随机ID (前40位时间戳，后24位随机数)
	randId := (curTimeSec << 24) | (randInt64 & 0xFFFFFF)
	return randId
}

// SliceFilter 过滤切片
func SliceFilter[T any](arr []T, fn func(T) bool) []T {
	result := make([]T, 0, len(arr))
	for _, v := range arr {
		if fn(v) {
			result = append(result, v)
		}
	}
	return result
}

type TaskFunc func() error

// SyncTask 执行异步任务
func SyncTask(taskFuncList ...TaskFunc) *SyncTaskType {
	t := SyncTaskType{&sync.WaitGroup{}, false, make(chan error, 1), &sync.Mutex{}}
	t.AddSyncTask(taskFuncList...)
	return &t
}

type SyncTaskType struct {
	wg       *sync.WaitGroup
	hasError bool
	errChan  chan error
	mu       *sync.Mutex
}

// Wait 等待异步任务完成
func (s *SyncTaskType) Wait() error {
	waitChan := make(chan error, 1)
	go func() {
		s.wg.Wait()
		waitChan <- nil
		close(waitChan)
	}()
	select {
	case err := <-s.errChan:
		return err
	case <-waitChan:
		s.mu.Lock()
		defer s.mu.Unlock()
		if s.hasError {
			return <-s.errChan
		}
		return nil
	}
}

// AddSyncTask 继续添加异步任务
func (s *SyncTaskType) AddSyncTask(taskFuncList ...TaskFunc) {
	s.wg.Add(len(taskFuncList))
	for _, task := range taskFuncList {
		go func(wg *sync.WaitGroup, tf TaskFunc) {
			defer wg.Done()
			err := tf()
			if err != nil {
				s.mu.Lock()
				defer s.mu.Unlock()
				if !s.hasError {
					s.hasError = true
					s.errChan <- err
					close(s.errChan)
				}
			}
		}(s.wg, task)
	}
}

// EncodeUrl 编码 URL
func EncodeUrl(rawURL string) (string, error) {
	// 解析 URL
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}
	// 将 URL 的路径部分按 "/" 分割，对每个分段进行编码
	segments := strings.Split(u.Path, "/")
	for i, seg := range segments {
		segments[i] = url.PathEscape(seg)
	}
	encodedPath := strings.Join(segments, "/")
	// 保留 u.Path 的原始值，仅设置 u.RawPath 为编码后的字符串
	u.RawPath = encodedPath

	// 输出编码后的 URL
	return u.String(), err
}

func DownloadFile(url, filename string) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	// 检查 HTTP 响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("bad status: %s", resp.Status)
	}
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return err
	}
	return nil
}
