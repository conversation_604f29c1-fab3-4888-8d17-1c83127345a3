package svc

import (
	"errors"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/interfaces"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config               config.Config
	DB                   *gorm.DB
	AccountModel         *model.AccountModel
	OperationLogModel    *model.OperationLogModel
	ProxyModel           *model.ProxyModel
	AdvertiserModel      *model.AdvertiserModel
	ProjectModel         *model.ProjectModel
	PromotionModel       *model.PromotionModel
	TextInfoModel        *model.TextInfoModel
	StrategyModel        *model.StrategyModel
	StrategyBindingModel *model.StrategyBindingModel
	StrategyLogModel     *model.StrategyLogModel
	ClipTaskModel        *model.ClipTaskModel
	ConfigModel          *model.ConfigModel
	ApiRecordModel       *model.ApiRecordModel
	DeletedCommentModel  *model.DeletedCommentModel
	SchedulerManager     interfaces.SchedulerManager
}

func NewServiceContext(c config.Config) *ServiceContext {
	db, err := gorm.Open(sqlite.Open("data.db"), &gorm.Config{})
	if err != nil {
		utils.LogFatal("连接数据库失败", "error", err.Error())
	}

	// 自动迁移数据表
	err = autoMigrate(db)
	if err != nil {
		utils.LogFatal("数据表迁移失败", "error", err.Error())
	}

	return &ServiceContext{
		Config:               c,
		DB:                   db,
		AccountModel:         model.NewAccountModel(db),
		OperationLogModel:    model.NewOperationLogModel(db),
		ProxyModel:           model.NewProxyModel(db),
		AdvertiserModel:      model.NewAdvertiserModel(db),
		ProjectModel:         model.NewProjectModel(db),
		PromotionModel:       model.NewPromotionModel(db),
		TextInfoModel:        model.NewTextInfoModel(db),
		StrategyModel:        model.NewStrategyModel(db),
		StrategyBindingModel: model.NewStrategyBindingModel(db),
		StrategyLogModel:     model.NewStrategyLogModel(db),
		ClipTaskModel:        model.NewClipTaskModel(db),
		ConfigModel:          model.NewConfigModel(db),
		ApiRecordModel:       model.NewApiRecordModel(db),
		DeletedCommentModel:  model.NewDeletedCommentModel(db),
		// SchedulerManager 将在需要时初始化
	}
}

// 自动迁移数据表结构
func autoMigrate(db *gorm.DB) error {
	utils.LogInfo("开始自动迁移数据表...")

	// 自动迁移所有模型
	err := db.AutoMigrate(
		&model.Account{},
		&model.OperationLog{},
		&model.Proxy{},
		&model.Advertiser{},
		&model.Project{},
		&model.Promotion{},
		&model.TextInfo{},
		&model.Strategy{},
		&model.StrategyBinding{},
		&model.StrategyLog{},
		&model.ClipTask{},
		&model.ApiRecord{},
		&model.Config{},
		&model.DeletedComment{},
	)

	if err != nil {
		return err
	}

	utils.LogInfo("数据表迁移完成")
	return nil
}

func (s *ServiceContext) GetBot(accountId int64) (*bot.Bot, *model.Account, error) {
	// 获取账户信息
	accountInfo, err := s.AccountModel.GetByAccountId(accountId)
	if err != nil {
		return nil, nil, err
	}

	if accountInfo.Cookie == "" {
		return nil, nil, errors.New("账户Cookie不存在")
	}

	proxyInfo, err := s.ProxyModel.GetById(accountInfo.ProxyId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil, err
	}

	proxyUrl := ""
	if proxyInfo != nil {
		proxyUrl = proxyInfo.FormattedProxy()
	}

	b, err := bot.NewBot(proxyUrl)
	if err != nil {
		return nil, nil, err
	}

	// 设置API记录模型，用于debug模式下记录API调用
	b.ApiRecordModel = s.ApiRecordModel
	b.Debug = config.GetConfig().App.Debug
	return b, accountInfo, nil
}
