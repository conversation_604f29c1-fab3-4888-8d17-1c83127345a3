package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"io/ioutil"
	"os"
	"time"
)

// VideoProcessingPreset 表示视频处理预设
type VideoProcessingPreset struct {
	ID                 uuid.UUID `json:"id"`
	Name               string    `json:"name"`
	Description        string    `json:"description"`
	Type               string    `json:"type"`
	AdjustResolution   bool      `json:"adjust_resolution"`
	ResolutionIndex    int       `json:"resolution_index"`
	AdjustSpeed        bool      `json:"adjust_speed"`
	SpeedValue         int       `json:"speed_value"`
	TransitionType     int       `json:"transition_type"`
	TransitionDuration float64   `json:"transition_duration"`
	Tags               []string  `json:"tags"`
	UsageCount         int       `json:"usage_count"`
	LastUsedAt         time.Time `json:"last_used_at"`
	CreatedAt          time.Time `json:"created_at"`
}

// PresetService 预设服务
type PresetService struct {
	presetDirectory string
	presets         []VideoProcessingPreset
}

// NewPresetService 创建预设服务
func NewPresetService() *PresetService {
	presetDirectory := fmt.Sprintf("%s/OceanEngineManager/Presets", os.Getenv("APPDATA"))
	if _, err := os.Stat(presetDirectory); os.IsNotExist(err) {
		os.MkdirAll(presetDirectory, os.ModePerm)
	}

	return &PresetService{
		presetDirectory: presetDirectory,
		presets:         []VideoProcessingPreset{},
	}
}

// LoadPresetsAsync 异步加载所有预设
func (ps *PresetService) LoadPresetsAsync() error {
	ps.presets = nil

	// 加载内置预设
	ps.LoadBuiltInPresets()

	// 加载用户自定义预设
	files, err := ioutil.ReadDir(ps.presetDirectory)
	if err != nil {
		return err
	}

	for _, file := range files {
		if file.IsDir() || file.Name() == "" || file.Name() == "." || file.Name() == ".." {
			continue
		}
		if file.Name()[len(file.Name())-5:] == ".json" {
			filePath := fmt.Sprintf("%s/%s", ps.presetDirectory, file.Name())
			preset, err := LoadPresetFromFile(filePath)
			if err != nil {
				fmt.Printf("加载预设文件出错: %s, 错误: %v\n", filePath, err)
				continue
			}
			ps.presets = append(ps.presets, preset)
		}
	}
	return nil
}

// LoadBuiltInPresets 加载内置预设
func (ps *PresetService) LoadBuiltInPresets() {
	ps.presets = append(ps.presets, VideoProcessingPreset{
		ID:               uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Name:             "高清输出",
		Description:      "1080P高清视频输出设置",
		Type:             "System",
		AdjustResolution: true,
		ResolutionIndex:  1, // 1080P
		Tags:             []string{"系统", "高清"},
	})
	ps.presets = append(ps.presets, VideoProcessingPreset{
		ID:               uuid.MustParse("00000000-0000-0000-0000-000000000002"),
		Name:             "社交媒体",
		Description:      "适合上传至社交媒体的压缩设置",
		Type:             "System",
		AdjustResolution: true,
		ResolutionIndex:  2, // 720P
		Tags:             []string{"系统", "社交媒体"},
	})
	ps.presets = append(ps.presets, VideoProcessingPreset{
		ID:          uuid.MustParse("00000000-0000-0000-0000-000000000003"),
		Name:        "快速节奏",
		Description: "增加视频播放速度，适合快节奏视频",
		Type:        "System",
		AdjustSpeed: true,
		SpeedValue:  120,
		Tags:        []string{"系统", "速度"},
	})
	ps.presets = append(ps.presets, VideoProcessingPreset{
		ID:          uuid.MustParse("00000000-0000-0000-0000-000000000004"),
		Name:        "慢动作",
		Description: "降低视频播放速度，突出关键细节",
		Type:        "System",
		AdjustSpeed: true,
		SpeedValue:  70,
		Tags:        []string{"系统", "速度"},
	})
	ps.presets = append(ps.presets, VideoProcessingPreset{
		ID:                 uuid.MustParse("00000000-0000-0000-0000-000000000005"),
		Name:               "淡入淡出转场",
		Description:        "使用淡入淡出效果平滑过渡",
		Type:               "System",
		TransitionType:     1, // 淡入淡出
		TransitionDuration: 1.0,
		Tags:               []string{"系统", "转场"},
	})
}

// LoadPresetFromFile 从文件加载预设
func LoadPresetFromFile(filePath string) (VideoProcessingPreset, error) {
	var preset VideoProcessingPreset
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return preset, err
	}

	err = json.Unmarshal(data, &preset)
	if err != nil {
		return preset, err
	}

	return preset, nil
}

// SavePresetToFile 保存预设到文件
func SavePresetToFile(preset VideoProcessingPreset, filePath string) error {
	data, err := json.MarshalIndent(preset, "", "  ")
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(filePath, data, 0644)
	if err != nil {
		return err
	}

	return nil
}

// SavePreset 保存预设
func (ps *PresetService) SavePreset(preset VideoProcessingPreset) error {
	// 如果是系统预设，则不能修改，先克隆一个
	if preset.Type == "System" {
		preset = preset.Clone()
	}

	preset.Type = "Custom"
	preset.LastUsedAt = time.Now()

	// 检查是否已经存在
	existingPreset := ps.GetPreset(preset.ID)
	if existingPreset != nil {
		// 更新已有的预设
		*existingPreset = preset
	} else {
		// 添加新的预设
		ps.presets = append(ps.presets, preset)
	}

	// 保存到文件
	filePath := fmt.Sprintf("%s/%s.json", ps.presetDirectory, preset.ID.String())
	err := SavePresetToFile(preset, filePath)
	if err != nil {
		return err
	}

	return nil
}

// GetPreset 获取单个预设
func (ps *PresetService) GetPreset(id uuid.UUID) *VideoProcessingPreset {
	for _, preset := range ps.presets {
		if preset.ID == id {
			return &preset
		}
	}
	return nil
}

// DeletePreset 删除预设
func (ps *PresetService) DeletePreset(id uuid.UUID) error {
	preset := ps.GetPreset(id)
	if preset == nil || preset.Type == "System" {
		return errors.New("无法删除系统预设或找不到预设")
	}

	// 从列表中删除
	for i, p := range ps.presets {
		if p.ID == id {
			ps.presets = append(ps.presets[:i], ps.presets[i+1:]...)
			break
		}
	}

	// 删除文件
	filePath := fmt.Sprintf("%s/%s.json", ps.presetDirectory, id.String())
	err := os.Remove(filePath)
	if err != nil {
		return err
	}

	return nil
}

// Clone 克隆一个预设
func (preset VideoProcessingPreset) Clone() VideoProcessingPreset {
	return VideoProcessingPreset{
		ID:                 uuid.New(),
		Name:               preset.Name,
		Description:        preset.Description,
		Type:               "Custom", // 克隆为自定义预设
		AdjustResolution:   preset.AdjustResolution,
		ResolutionIndex:    preset.ResolutionIndex,
		AdjustSpeed:        preset.AdjustSpeed,
		SpeedValue:         preset.SpeedValue,
		TransitionType:     preset.TransitionType,
		TransitionDuration: preset.TransitionDuration,
		Tags:               append([]string{}, preset.Tags...),
		UsageCount:         0,
		LastUsedAt:         time.Now(),
		CreatedAt:          time.Now(),
	}
}

// GetPopularPresets 获取最常使用的预设
func (ps *PresetService) GetPopularPresets(count int) []VideoProcessingPreset {
	// 按照使用次数降序排列
	var sortedPresets []VideoProcessingPreset
	for _, preset := range ps.presets {
		sortedPresets = append(sortedPresets, preset)
	}

	// 简单的排序：按照 UsageCount 排序
	for i := 0; i < len(sortedPresets); i++ {
		for j := i + 1; j < len(sortedPresets); j++ {
			if sortedPresets[i].UsageCount < sortedPresets[j].UsageCount {
				sortedPresets[i], sortedPresets[j] = sortedPresets[j], sortedPresets[i]
			}
		}
	}

	// 返回前 count 个预设
	if len(sortedPresets) < count {
		return sortedPresets
	}
	return sortedPresets[:count]
}

// GetRecentPresets 获取最近使用的预设
func (ps *PresetService) GetRecentPresets(count int) []VideoProcessingPreset {
	// 按照最后使用时间降序排列
	var sortedPresets []VideoProcessingPreset
	for _, preset := range ps.presets {
		sortedPresets = append(sortedPresets, preset)
	}

	// 简单的排序：按照 LastUsedAt 排序
	for i := 0; i < len(sortedPresets); i++ {
		for j := i + 1; j < len(sortedPresets); j++ {
			if sortedPresets[i].LastUsedAt.Before(sortedPresets[j].LastUsedAt) {
				sortedPresets[i], sortedPresets[j] = sortedPresets[j], sortedPresets[i]
			}
		}
	}

	// 返回前 count 个预设
	if len(sortedPresets) < count {
		return sortedPresets
	}
	return sortedPresets[:count]
}

//
//func main() {
//	// 示例：创建预设服务实例
//	presetService := NewPresetService()
//
//	// 示例：加载所有预设
//	err := presetService.LoadPresetsAsync()
//	if err != nil {
//		fmt.Println("加载预设失败:", err)
//	}
//
//	// 示例：保存新的自定义预设
//	newPreset := VideoProcessingPreset{
//		ID:          uuid.New(),
//		Name:        "我的预设",
//		Description: "这个是我的自定义预设",
//		Type:        "Custom",
//		CreatedAt:   time.Now(),
//		LastUsedAt:  time.Now(),
//	}
//	err = presetService.SavePreset(newPreset)
//	if err != nil {
//		fmt.Println("保存预设失败:", err)
//	}
//
//	// 示例：删除预设
//	err = presetService.DeletePreset(newPreset.ID)
//	if err != nil {
//		fmt.Println("删除预设失败:", err)
//	}
//}
