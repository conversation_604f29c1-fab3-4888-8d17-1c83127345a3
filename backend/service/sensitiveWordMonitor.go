package service

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// SensitiveWordMonitorService 敏感词监控服务
type SensitiveWordMonitorService struct {
	svcCtx   *svc.ServiceContext
	ctx      context.Context
	app      *application.App
	running  bool
	stop<PERSON>han chan struct{}
	wg       sync.WaitGroup
	mu       sync.RWMutex
	interval time.Duration

	// 执行状态跟踪
	executionState struct {
		mu             sync.RWMutex
		isExecuting    bool           // 是否正在执行监控任务
		lastStartTime  time.Time      // 上次开始执行时间
		lastEndTime    time.Time      // 上次结束执行时间
		executionCount int64          // 执行次数
		currentAccount *model.Account // 当前正在处理的账户
	}

	// 删除统计
	deleteStats struct {
		mu           sync.RWMutex
		totalChecked int64     // 总检查评论数
		totalDeleted int64     // 总删除评论数
		totalFailed  int64     // 总删除失败数
		lastReset    time.Time // 上次重置时间
	}
}

// SensitiveCommentItem 包含敏感词的评论项
type SensitiveCommentItem struct {
	Comment       bot.CommentItem `json:"comment"`        // 评论信息
	SensitiveWord string          `json:"sensitive_word"` // 敏感词
}

// NewSensitiveWordMonitorService 创建敏感词监控服务
func NewSensitiveWordMonitorService(svcCtx *svc.ServiceContext) *SensitiveWordMonitorService {
	return &SensitiveWordMonitorService{
		svcCtx:   svcCtx,
		stopChan: make(chan struct{}),
		interval: 5 * time.Minute, // 默认5分钟轮询一次
		deleteStats: struct {
			mu           sync.RWMutex
			totalChecked int64     // 总检查评论数
			totalDeleted int64     // 总删除评论数
			totalFailed  int64     // 总删除失败数
			lastReset    time.Time // 上次重置时间
		}{
			lastReset: time.Now(),
		},
	}
}

func (s *SensitiveWordMonitorService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *SensitiveWordMonitorService) SetApp(app *application.App) {
	s.app = app
}

// Start 启动敏感词监控服务
func (s *SensitiveWordMonitorService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("敏感词监控服务已在运行")
	}

	utils.LogInfo("启动敏感词监控服务...")

	s.running = true
	s.wg.Add(1)
	go s.monitorLoop()

	utils.LogInfo("敏感词监控服务启动成功")
	return nil
}

// Stop 停止敏感词监控服务
func (s *SensitiveWordMonitorService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	utils.LogInfo("停止敏感词监控服务...")

	// 先设置running为false，防止新的操作开始
	s.running = false

	// 关闭停止通道
	close(s.stopChan)

	// 添加超时机制，防止无限期等待
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		utils.LogInfo("监控循环已正常停止")
	case <-time.After(10 * time.Second):
		utils.LogWarn("停止监控服务超时，强制停止")
	}

	// 重新创建stopChan，以便服务可以重新启动
	s.stopChan = make(chan struct{})

	utils.LogInfo("敏感词监控服务已停止")
	return nil
}

// SetInterval 设置监控间隔
func (s *SensitiveWordMonitorService) SetInterval(interval time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.interval = interval
}

// GetStatus 获取服务状态
func (s *SensitiveWordMonitorService) GetStatus() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取删除统计
	deleteStats := s.GetDeleteStats()

	// 获取执行状态
	s.executionState.mu.RLock()
	executionState := map[string]interface{}{
		"is_executing":    s.executionState.isExecuting,
		"execution_count": s.executionState.executionCount,
		"last_start_time": s.executionState.lastStartTime.Format("2006-01-02 15:04:05"),
		"last_end_time":   s.executionState.lastEndTime.Format("2006-01-02 15:04:05"),
	}

	// 如果正在执行，添加当前执行信息
	if s.executionState.isExecuting {
		executionDuration := time.Since(s.executionState.lastStartTime)
		executionState["execution_duration"] = executionDuration.String()
		executionState["execution_duration_seconds"] = int64(executionDuration.Seconds())

		if s.executionState.currentAccount != nil {
			executionState["current_account"] = map[string]interface{}{
				"account_id":   s.executionState.currentAccount.AccountId,
				"account_name": s.executionState.currentAccount.AccountName,
			}
		}
	}
	s.executionState.mu.RUnlock()

	// 获取敏感词配置状态
	sensitiveWords, err := s.getSensitiveWords()
	sensitiveWordsStatus := map[string]interface{}{
		"configured": false,
		"word_count": 0,
		"error":      nil,
	}

	if err != nil {
		sensitiveWordsStatus["error"] = err.Error()
	} else if sensitiveWords != "" {
		words := strings.Split(sensitiveWords, ",")
		wordCount := 0
		for _, word := range words {
			if strings.TrimSpace(word) != "" {
				wordCount++
			}
		}
		sensitiveWordsStatus["configured"] = true
		sensitiveWordsStatus["word_count"] = wordCount
	}

	return map[string]interface{}{
		"running":         s.running,
		"interval":        s.interval.String(),
		"delete_stats":    deleteStats,
		"execution_state": executionState,
		"sensitive_words": sensitiveWordsStatus,
	}
}

// monitorLoop 监控循环
func (s *SensitiveWordMonitorService) monitorLoop() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.interval)
	defer ticker.Stop()

	utils.LogInfo("敏感词监控循环已启动", "interval", s.interval.String())

	// 立即执行一次
	s.executeMonitorTask()

	for {
		select {
		case <-ticker.C:
			s.executeMonitorTask()
		case <-s.stopChan:
			utils.LogInfo("敏感词监控循环已停止")
			return
		case <-s.ctx.Done():
			utils.LogInfo("敏感词监控循环已停止")
			return
		}
	}
}

// executeMonitorTask 执行监控任务（带防重复执行保护）
func (s *SensitiveWordMonitorService) executeMonitorTask() {
	// 检查是否已有任务在运行
	s.executionState.mu.Lock()
	if s.executionState.isExecuting {
		s.executionState.mu.Unlock()
		utils.LogWarn("监控任务正在执行中，跳过本次执行",
			"last_start_time", s.executionState.lastStartTime.Format("2006-01-02 15:04:05"),
			"execution_duration", time.Since(s.executionState.lastStartTime).String())
		return
	}

	// 设置执行状态
	s.executionState.isExecuting = true
	s.executionState.lastStartTime = time.Now()
	s.executionState.executionCount++
	s.executionState.mu.Unlock()

	utils.LogInfo("开始执行监控任务",
		"execution_count", s.executionState.executionCount,
		"start_time", s.executionState.lastStartTime.Format("2006-01-02 15:04:05"))

	// 执行监控任务
	func() {
		defer func() {
			// 更新执行状态
			s.executionState.mu.Lock()
			s.executionState.isExecuting = false
			s.executionState.lastEndTime = time.Now()
			s.executionState.currentAccount = nil
			executionDuration := s.executionState.lastEndTime.Sub(s.executionState.lastStartTime)
			s.executionState.mu.Unlock()

			utils.LogInfo("监控任务执行完成",
				"execution_count", s.executionState.executionCount,
				"duration", executionDuration.String(),
				"end_time", s.executionState.lastEndTime.Format("2006-01-02 15:04:05"))
		}()

		// 检查是否有敏感词配置
		sensitiveWords, err := s.getSensitiveWords()
		if err != nil {
			utils.LogError("获取敏感词配置失败，跳过本次执行", "error", err.Error())
			return
		}

		if sensitiveWords == "" {
			utils.LogInfo("没有配置敏感词，跳过评论检查")
			return
		}

		s.checkAllProjectsComments()
	}()
}

// checkAllProjectsComments 检查所有账户的广告主评论
func (s *SensitiveWordMonitorService) checkAllProjectsComments() {
	utils.LogInfo("开始检查所有账户的广告主评论...")

	// 获取所有账户
	accounts, err := s.svcCtx.AccountModel.ListAll()
	if err != nil {
		utils.LogError("获取账户列表失败", "error", err.Error())
		return
	}

	if len(accounts) == 0 {
		utils.LogInfo("没有找到账户，跳过评论检查")
		return
	}

	utils.LogInfo("开始检查账户广告主评论", "account_count", len(accounts))

	// 统计开启自动删除评论的广告主总数
	totalEnabledAdvertisers := 0
	for _, account := range accounts {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断评论检查")
			return
		default:
		}

		// 获取账户下的所有广告主
		advertisers, err := s.svcCtx.AdvertiserModel.GetByAccountId(account.AccountId)
		if err != nil {
			utils.LogError("获取账户广告主失败", "account_id", account.AccountId, "error", err.Error())
			continue
		}

		// 统计开启自动删除评论的广告主数量
		enabledCount := 0
		for _, advertiser := range advertisers {
			if advertiser.AutoDeleteComment == 1 {
				enabledCount++
			}
		}
		totalEnabledAdvertisers += enabledCount

		if enabledCount > 0 {
			utils.LogInfo("账户下开启自动删除评论的广告主",
				"account_id", account.AccountId,
				"account_name", account.AccountName,
				"total_advertisers", len(advertisers),
				"enabled_advertisers", enabledCount)
		}
	}

	utils.LogInfo("自动删除评论统计", "total_enabled_advertisers", totalEnabledAdvertisers)

	if totalEnabledAdvertisers == 0 {
		utils.LogInfo("没有开启自动删除评论的广告主，跳过评论检查")
		return
	}

	for _, account := range accounts {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断评论检查")
			return
		default:
		}

		s.checkAccountProjectsComments(account)
	}

	utils.LogInfo("所有广告主评论检查完成")
}

// checkAccountProjectsComments 检查指定账户下所有广告主的评论
func (s *SensitiveWordMonitorService) checkAccountProjectsComments(account *model.Account) {
	// 更新当前处理的账户
	s.executionState.mu.Lock()
	s.executionState.currentAccount = account
	s.executionState.mu.Unlock()

	utils.LogInfo("检查账户广告主评论", "account_id", account.AccountId, "account_name", account.AccountName)

	// 获取账户下的所有广告主
	advertisers, err := s.svcCtx.AdvertiserModel.GetByAccountId(account.AccountId)
	if err != nil {
		utils.LogError("获取账户广告主失败", "account_id", account.AccountId, "error", err.Error())
		return
	}

	if len(advertisers) == 0 {
		utils.LogInfo("账户下没有广告主", "account_id", account.AccountId)
		return
	}

	utils.LogInfo("检查广告主评论", "account_id", account.AccountId, "advertiser_count", len(advertisers))

	for _, advertiser := range advertisers {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断广告主评论检查")
			return
		default:
		}

		time.Sleep(1 * time.Second)
		s.checkAdvertiserComments(account, advertiser)
	}

	// 清除当前处理的账户
	s.executionState.mu.Lock()
	s.executionState.currentAccount = nil
	s.executionState.mu.Unlock()
}

// checkAdvertiserComments 检查指定广告主的评论
func (s *SensitiveWordMonitorService) checkAdvertiserComments(account *model.Account, advertiser *model.Advertiser) {
	utils.LogInfo("检查广告主评论", "advertiser_id", advertiser.AdvertiserId, "advertiser_name", advertiser.AdvertiserName)

	// 检查是否开启自动删除评论
	if advertiser.AutoDeleteComment != 1 {
		utils.LogInfo("广告主未开启自动删除评论，跳过评论检查",
			"advertiser_id", advertiser.AdvertiserId,
			"advertiser_name", advertiser.AdvertiserName,
			"auto_delete_comment", advertiser.AutoDeleteComment)
		return
	}

	// 获取广告主的评论列表
	comments, err := s.getCommentsForAdvertiser(account, advertiser)
	if err != nil {
		utils.LogError("获取广告主评论失败", "advertiser_id", advertiser.AdvertiserId, "error", err.Error())
		return
	}

	if len(comments) == 0 {
		utils.LogInfo("广告主下没有评论", "advertiser_id", advertiser.AdvertiserId)
		return
	}

	utils.LogInfo("检查广告主评论敏感词", "advertiser_id", advertiser.AdvertiserId, "comment_count", len(comments))

	// 获取敏感词配置（只获取一次）
	sensitiveWords, err := s.getSensitiveWords()
	if err != nil {
		utils.LogError("获取敏感词配置失败", "error", err.Error())
		return
	}

	if sensitiveWords == "" {
		utils.LogInfo("没有配置敏感词，跳过评论检查", "advertiser_id", advertiser.AdvertiserId)
		return
	}

	// 收集包含敏感词的评论
	var sensitiveComments []SensitiveCommentItem

	// 检查每个评论是否包含敏感词
	for _, comment := range comments {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断评论敏感词检查")
			return
		default:
		}

		// 增加检查统计
		s.deleteStats.mu.Lock()
		s.deleteStats.totalChecked++
		s.deleteStats.mu.Unlock()

		// 检查评论内容是否包含敏感词
		foundWords := model.CheckSensitiveWords(comment.Text, sensitiveWords)
		if len(foundWords) > 0 {
			utils.LogWarn("发现包含敏感词的评论",
				"comment_id", comment.CommentId,
				"comment_text", comment.Text,
				"sensitive_words", foundWords)

			sensitiveComments = append(sensitiveComments, SensitiveCommentItem{
				Comment:       comment,
				SensitiveWord: foundWords[0], // 只记录第一个敏感词
			})
		}
	}

	// 批量删除敏感评论
	if len(sensitiveComments) > 0 {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，跳过敏感评论删除")
			return
		default:
		}

		utils.LogInfo("开始批量删除敏感评论", "sensitive_count", len(sensitiveComments))
		s.batchDeleteSensitiveComments(account, advertiser, sensitiveComments)
	}
}

// getCommentsForAdvertiser 获取指定广告主的评论列表
func (s *SensitiveWordMonitorService) getCommentsForAdvertiser(account *model.Account, advertiser *model.Advertiser) ([]bot.CommentItem, error) {
	// 使用服务上下文的GetBot方法创建bot实例
	b, accountInfo, err := s.svcCtx.GetBot(account.AccountId)
	if err != nil {
		return nil, fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 构建评论查询请求
	req := &bot.GetCommentListReq{
		StartTime:    time.Now().AddDate(0, 0, -7).Format("2006-01-02"), // 查询最近7天的评论
		EndTime:      time.Now().Format("2006-01-02"),
		Content:      "",
		Level:        1,
		HideStatus:   0,
		CommentType:  0,
		EmotionType:  100,
		ItemIds:      []string{},
		AuthorIds:    []string{},
		AdIds:        []string{},
		CreativeIds:  []string{},
		PromotionIds: []string{},
		RequestType:  2,
		Page:         1,
		PageSize:     100, // 每页100
		Total:        0,
		OrderByField: "create_time",
		OrderByType:  1, // 降序
	}

	// 获取评论列表，使用广告主ID
	resp, err := b.GetCommentList(fmt.Sprintf("%d", advertiser.AdvertiserId), req, accountInfo.Cookie)
	if err != nil {
		return nil, fmt.Errorf("获取评论列表失败: %w", err)
	}

	if resp.Code != 0 {
		return nil, fmt.Errorf("获取评论列表失败: %s", resp.Message)
	}

	return resp.Data.Comments, nil
}

// getSensitiveWords 获取敏感词配置
func (s *SensitiveWordMonitorService) getSensitiveWords() (string, error) {
	// 从config表中获取敏感词配置
	config, err := s.svcCtx.ConfigModel.GetByKey("sensitive_words")
	if err != nil {
		return "", err
	}

	if config == nil {
		return "", nil // 没有配置敏感词
	}

	return config.Value, nil
}

// deleteSensitiveComment 删除包含敏感词的评论
func (s *SensitiveWordMonitorService) deleteSensitiveComment(account *model.Account, advertiser *model.Advertiser, comment bot.CommentItem, sensitiveWord string) {
	utils.LogInfo("开始删除包含敏感词的评论",
		"comment_id", comment.CommentId,
		"comment_text", comment.Text,
		"sensitive_word", sensitiveWord,
		"advertiser_id", advertiser.AdvertiserId,
		"advertiser_name", advertiser.AdvertiserName)

	// 记录删除的评论到数据库
	deletedComment := &model.DeletedComment{
		CommentId:     comment.CommentId,
		CommentText:   comment.Text,
		SensitiveWord: sensitiveWord,
		AccountName:   account.AccountName,
		ProjectName:   comment.CommercialInfo.PromotionName, // 从评论中获取推广计划名称作为项目名称
		PromotionName: comment.CommercialInfo.PromotionName, // 从评论中获取推广计划名称
	}

	if err := s.svcCtx.DeletedCommentModel.Create(deletedComment); err != nil {
		utils.LogError("保存删除记录失败", "comment_id", comment.CommentId, "error", err.Error())
	}

	// 执行删除评论操作
	success := s.executeDeleteComment(account, advertiser, comment, sensitiveWord)

	// 记录操作日志
	go func() {
		if s.app != nil {
			operation := "删除敏感评论"
			description := fmt.Sprintf("删除包含敏感词的评论，评论ID: %s，敏感词: %s，广告主: %s，推广计划: %s",
				comment.CommentId, sensitiveWord, advertiser.AdvertiserName, comment.CommercialInfo.PromotionName)

			if !success {
				operation = "删除敏感评论失败"
				description += "（删除操作失败）"
			}

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   account.AccountName,
				AccountId:     account.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     operation,
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	if success {
		utils.LogInfo("敏感评论删除完成", "comment_id", comment.CommentId)

		// 增加删除成功统计
		s.deleteStats.mu.Lock()
		s.deleteStats.totalDeleted++
		s.deleteStats.mu.Unlock()
	} else {
		utils.LogError("敏感评论删除失败", "comment_id", comment.CommentId)

		// 增加删除失败统计
		s.deleteStats.mu.Lock()
		s.deleteStats.totalFailed++
		s.deleteStats.mu.Unlock()
	}
}

// executeDeleteComment 执行删除评论操作
func (s *SensitiveWordMonitorService) executeDeleteComment(account *model.Account, advertiser *model.Advertiser, comment bot.CommentItem, sensitiveWord string) bool {
	const maxRetries = 3
	const retryDelay = 2 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断单个删除操作")
			return false
		default:
		}

		utils.LogInfo("尝试删除评论",
			"attempt", attempt,
			"max_retries", maxRetries,
			"comment_id", comment.CommentId)

		success, err := s.performDeleteComment(account, advertiser, comment)
		if err != nil {
			utils.LogError("删除评论出错",
				"attempt", attempt,
				"comment_id", comment.CommentId,
				"error", err.Error())

			if attempt < maxRetries {
				utils.LogInfo("等待重试", "delay", retryDelay.String())

				// 在重试等待期间也检查停止信号
				select {
				case <-s.stopChan:
					utils.LogInfo("检测到停止信号，中断单个删除重试")
					return false
				case <-time.After(retryDelay):
					// 继续重试
				}
				continue
			}

			utils.LogError("删除评论最终失败",
				"comment_id", comment.CommentId,
				"max_retries_reached", true,
				"error", err.Error())
			return false
		}

		if success {
			utils.LogInfo("删除评论成功",
				"attempt", attempt,
				"comment_id", comment.CommentId)
			return true
		}

		// 删除失败但不是网络错误，可能是权限问题或其他业务错误
		utils.LogWarn("删除评论失败",
			"attempt", attempt,
			"comment_id", comment.CommentId,
			"will_retry", attempt < maxRetries)

		if attempt < maxRetries {
			// 在重试等待期间也检查停止信号
			select {
			case <-s.stopChan:
				utils.LogInfo("检测到停止信号，中断单个删除重试")
				return false
			case <-time.After(retryDelay):
				// 继续重试
			}
		}
	}

	return false
}

// performDeleteComment 执行单次删除评论操作
func (s *SensitiveWordMonitorService) performDeleteComment(account *model.Account, advertiser *model.Advertiser, comment bot.CommentItem) (bool, error) {
	// 获取bot实例和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(account.AccountId)
	if err != nil {
		return false, fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 安全地获取AwemeId的int64值
	iesCoreUserID := s.getAwemeIdAsInt64(comment.CommentItem.ItemUser.AwemeId)

	// 构建删除评论请求
	req := &bot.DeleteCommentReq{
		Items: []bot.DeleteCommentItem{
			{
				CommentID:     comment.CommentId,
				AppID:         1128, // 默认应用ID
				IesCoreUserID: iesCoreUserID,
			},
		},
	}

	// 调用删除评论API
	resp, err := b.DeleteComment(fmt.Sprintf("%d", advertiser.AdvertiserId), req, accountInfo.Cookie)
	if err != nil {
		return false, fmt.Errorf("调用删除评论API失败: %w", err)
	}

	// 检查API响应
	if resp.Code != 0 {
		return false, fmt.Errorf("删除评论API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	// 检查删除结果
	if result, exists := resp.Data.Result[comment.CommentId]; exists {
		if result {
			utils.LogInfo("评论删除成功",
				"comment_id", comment.CommentId,
				"advertiser_id", advertiser.AdvertiserId)
			return true, nil
		} else {
			return false, fmt.Errorf("评论删除失败，API返回失败状态")
		}
	}

	return false, fmt.Errorf("删除结果中未找到评论ID: %s", comment.CommentId)
}

// getAwemeIdAsInt64 安全地将AwemeId转换为int64
func (s *SensitiveWordMonitorService) getAwemeIdAsInt64(awemeId interface{}) int64 {
	if awemeId == nil {
		return 0
	}

	switch v := awemeId.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		// 尝试将字符串转换为int64
		if id, err := strconv.ParseInt(v, 10, 64); err == nil {
			return id
		}
		return 0
	default:
		return 0
	}
}

// ManualCheck 手动触发检查
func (s *SensitiveWordMonitorService) ManualCheck() error {
	s.mu.RLock()
	if !s.running {
		s.mu.RUnlock()
		return fmt.Errorf("敏感词监控服务未运行")
	}
	s.mu.RUnlock()

	utils.LogInfo("手动触发敏感词检查...")

	// 检查是否有敏感词配置
	sensitiveWords, err := s.getSensitiveWords()
	if err != nil {
		utils.LogError("获取敏感词配置失败，跳过手动检查", "error", err.Error())
		return fmt.Errorf("获取敏感词配置失败: %w", err)
	}

	if sensitiveWords == "" {
		utils.LogInfo("没有配置敏感词，跳过手动检查")
		return fmt.Errorf("没有配置敏感词，请先配置敏感词")
	}

	s.checkAllProjectsComments()
	return nil
}

// StartMonitor 启动监控服务（API方法）
func (s *SensitiveWordMonitorService) StartMonitor() *result.Result[types.SensitiveWordMonitorResult] {
	if err := s.Start(); err != nil {
		return result.ToError[types.SensitiveWordMonitorResult](result.ErrorAdd.AddError(err))
	}

	resp := types.SensitiveWordMonitorResult{
		Success: true,
		Message: "敏感词监控服务启动成功",
	}

	return result.SuccessResult(resp)
}

// StopMonitor 停止监控服务（API方法）
func (s *SensitiveWordMonitorService) StopMonitor() *result.Result[types.SensitiveWordMonitorResult] {
	if err := s.Stop(); err != nil {
		return result.ToError[types.SensitiveWordMonitorResult](result.ErrorDelete.AddError(err))
	}

	resp := types.SensitiveWordMonitorResult{
		Success: true,
		Message: "敏感词监控服务停止成功",
	}

	return result.SuccessResult(resp)
}

// GetMonitorStatus 获取监控服务状态（API方法）
func (s *SensitiveWordMonitorService) GetMonitorStatus() *result.Result[types.SensitiveWordMonitorStatus] {
	status := s.GetStatus()

	resp := types.SensitiveWordMonitorStatus{
		Running:        status["running"].(bool),
		Interval:       status["interval"].(string),
		DeleteStats:    status["delete_stats"].(map[string]interface{}),
		ExecutionState: status["execution_state"].(map[string]interface{}),
		SensitiveWords: status["sensitive_words"].(map[string]interface{}),
	}

	return result.SuccessResult(resp)
}

// SetMonitorInterval 设置监控间隔（API方法）
func (s *SensitiveWordMonitorService) SetMonitorInterval(intervalSeconds int64) *result.Result[types.SensitiveWordMonitorResult] {
	if intervalSeconds < 60 {
		return result.ToError[types.SensitiveWordMonitorResult](result.ErrorAdd.AddError(fmt.Errorf("监控间隔不能少于60秒")))
	}

	interval := time.Duration(intervalSeconds) * time.Second
	s.SetInterval(interval)

	resp := types.SensitiveWordMonitorResult{
		Success: true,
		Message: fmt.Sprintf("监控间隔设置成功：%s", interval.String()),
	}

	return result.SuccessResult(resp)
}

// TriggerManualCheck 手动触发检查（API方法）
func (s *SensitiveWordMonitorService) TriggerManualCheck() *result.Result[types.SensitiveWordMonitorResult] {
	if err := s.ManualCheck(); err != nil {
		return result.ToError[types.SensitiveWordMonitorResult](result.ErrorAdd.AddError(err))
	}

	resp := types.SensitiveWordMonitorResult{
		Success: true,
		Message: "手动检查已触发",
	}

	return result.SuccessResult(resp)
}

// GetDeleteStats 获取删除统计信息
func (s *SensitiveWordMonitorService) GetDeleteStats() map[string]interface{} {
	s.deleteStats.mu.RLock()
	defer s.deleteStats.mu.RUnlock()

	return map[string]interface{}{
		"total_checked": s.deleteStats.totalChecked,
		"total_deleted": s.deleteStats.totalDeleted,
		"total_failed":  s.deleteStats.totalFailed,
		"success_rate":  s.calculateSuccessRate(),
		"last_reset":    s.deleteStats.lastReset.Format("2006-01-02 15:04:05"),
		"running_time":  time.Since(s.deleteStats.lastReset).String(),
	}
}

// ResetDeleteStats 重置删除统计
func (s *SensitiveWordMonitorService) ResetDeleteStats() {
	s.deleteStats.mu.Lock()
	defer s.deleteStats.mu.Unlock()

	s.deleteStats.totalChecked = 0
	s.deleteStats.totalDeleted = 0
	s.deleteStats.totalFailed = 0
	s.deleteStats.lastReset = time.Now()

	utils.LogInfo("删除统计已重置")
}

// calculateSuccessRate 计算删除成功率
func (s *SensitiveWordMonitorService) calculateSuccessRate() float64 {
	total := s.deleteStats.totalDeleted + s.deleteStats.totalFailed
	if total == 0 {
		return 0.0
	}
	return float64(s.deleteStats.totalDeleted) / float64(total) * 100
}

// GetDeleteStatsAPI 获取删除统计信息（API方法）
func (s *SensitiveWordMonitorService) GetDeleteStatsAPI() *result.Result[map[string]interface{}] {
	stats := s.GetDeleteStats()
	return result.SuccessResult(stats)
}

// ResetDeleteStatsAPI 重置删除统计（API方法）
func (s *SensitiveWordMonitorService) ResetDeleteStatsAPI() *result.Result[types.SensitiveWordMonitorResult] {
	s.ResetDeleteStats()

	resp := types.SensitiveWordMonitorResult{
		Success: true,
		Message: "删除统计已重置",
	}

	return result.SuccessResult(resp)
}

// batchDeleteSensitiveComments 批量删除包含敏感词的评论
func (s *SensitiveWordMonitorService) batchDeleteSensitiveComments(account *model.Account, advertiser *model.Advertiser, sensitiveComments []SensitiveCommentItem) {
	utils.LogInfo("开始批量删除包含敏感词的评论", "sensitive_count", len(sensitiveComments))

	// 如果敏感评论数量较少，使用单个删除
	if len(sensitiveComments) <= 3 {
		for _, sensitiveComment := range sensitiveComments {
			// 检查是否需要停止
			select {
			case <-s.stopChan:
				utils.LogInfo("检测到停止信号，中断单个删除循环")
				return
			default:
			}

			s.deleteSensitiveComment(account, advertiser, sensitiveComment.Comment, sensitiveComment.SensitiveWord)
		}
		return
	}

	// 批量删除逻辑
	success := s.executeBatchDeleteComment(account, advertiser, sensitiveComments)

	if success {
		utils.LogInfo("批量删除敏感评论完成", "sensitive_count", len(sensitiveComments))
	} else {
		utils.LogError("批量删除敏感评论失败", "sensitive_count", len(sensitiveComments))
	}
}

// executeBatchDeleteComment 执行批量删除评论操作
func (s *SensitiveWordMonitorService) executeBatchDeleteComment(account *model.Account, advertiser *model.Advertiser, sensitiveComments []SensitiveCommentItem) bool {
	const maxRetries = 2
	const retryDelay = 3 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			utils.LogInfo("检测到停止信号，中断批量删除操作")
			return false
		default:
		}

		utils.LogInfo("尝试批量删除评论",
			"attempt", attempt,
			"max_retries", maxRetries,
			"comment_count", len(sensitiveComments))

		success, err := s.performBatchDeleteComment(account, advertiser, sensitiveComments)
		if err != nil {
			utils.LogError("批量删除评论出错",
				"attempt", attempt,
				"comment_count", len(sensitiveComments),
				"error", err.Error())

			if attempt < maxRetries {
				utils.LogInfo("等待重试", "delay", retryDelay.String())

				// 在重试等待期间也检查停止信号
				select {
				case <-s.stopChan:
					utils.LogInfo("检测到停止信号，中断批量删除重试")
					return false
				case <-time.After(retryDelay):
					// 继续重试
				}
				continue
			}

			utils.LogError("批量删除评论最终失败",
				"comment_count", len(sensitiveComments),
				"max_retries_reached", true,
				"error", err.Error())
			return false
		}

		if success {
			utils.LogInfo("批量删除评论成功",
				"attempt", attempt,
				"comment_count", len(sensitiveComments))
			return true
		}

		// 删除失败但不是网络错误，可能是权限问题或其他业务错误
		utils.LogWarn("批量删除评论失败",
			"attempt", attempt,
			"comment_count", len(sensitiveComments),
			"will_retry", attempt < maxRetries)

		if attempt < maxRetries {
			// 在重试等待期间也检查停止信号
			select {
			case <-s.stopChan:
				utils.LogInfo("检测到停止信号，中断批量删除重试")
				return false
			case <-time.After(retryDelay):
				// 继续重试
			}
		}
	}

	return false
}

// performBatchDeleteComment 执行单次批量删除评论操作
func (s *SensitiveWordMonitorService) performBatchDeleteComment(account *model.Account, advertiser *model.Advertiser, sensitiveComments []SensitiveCommentItem) (bool, error) {
	// 获取bot实例和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(account.AccountId)
	if err != nil {
		return false, fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 提取评论列表用于批量删除
	comments := make([]bot.CommentItem, 0, len(sensitiveComments))
	for _, sensitiveComment := range sensitiveComments {
		comments = append(comments, sensitiveComment.Comment)
	}

	// 调用批量删除评论API（使用带评论项信息的方法）
	resp, err := b.BatchDeleteCommentWithItems(fmt.Sprintf("%d", advertiser.AdvertiserId), comments, accountInfo.Cookie)
	if err != nil {
		return false, fmt.Errorf("调用批量删除评论API失败: %w", err)
	}

	// 检查API响应
	if resp.Code != 0 {
		return false, fmt.Errorf("批量删除评论API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	// 检查删除结果并更新统计
	successCount := 0
	failedCount := 0

	for _, sensitiveComment := range sensitiveComments {
		commentId := sensitiveComment.Comment.CommentId

		// 记录删除的评论到数据库
		deletedComment := &model.DeletedComment{
			CommentId:     commentId,
			CommentText:   sensitiveComment.Comment.Text,
			SensitiveWord: sensitiveComment.SensitiveWord,
			AccountName:   account.AccountName,
			ProjectName:   sensitiveComment.Comment.CommercialInfo.PromotionName,
			PromotionName: sensitiveComment.Comment.CommercialInfo.PromotionName,
		}

		if err := s.svcCtx.DeletedCommentModel.Create(deletedComment); err != nil {
			utils.LogError("保存删除记录失败", "comment_id", commentId, "error", err.Error())
		}

		// 检查删除结果
		if result, exists := resp.Data.Result[commentId]; exists && result {
			successCount++
			utils.LogInfo("评论删除成功", "comment_id", commentId)
		} else {
			failedCount++
			utils.LogError("评论删除失败", "comment_id", commentId)
		}
	}

	// 更新统计
	s.deleteStats.mu.Lock()
	s.deleteStats.totalDeleted += int64(successCount)
	s.deleteStats.totalFailed += int64(failedCount)
	s.deleteStats.mu.Unlock()

	utils.LogInfo("批量删除评论完成",
		"total_count", len(sensitiveComments),
		"success_count", successCount,
		"failed_count", failedCount)

	// 如果大部分评论删除成功，认为批量删除成功
	successRate := float64(successCount) / float64(len(sensitiveComments))
	return successRate >= 0.8, nil // 80%成功率认为成功
}
