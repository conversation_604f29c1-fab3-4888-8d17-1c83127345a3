package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/ffmpeg"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// ClipTaskService 混剪任务服务
type ClipTaskService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewClipTaskService 创建混剪任务服务
func NewClipTaskService(svcCtx *svc.ServiceContext) *ClipTaskService {
	return &ClipTaskService{
		svcCtx: svcCtx,
	}
}

func (s *ClipTaskService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *ClipTaskService) SetApp(app *application.App) {
	s.app = app
}

// GetClipTask 获取混剪任务详情
func (s *ClipTaskService) GetClipTask(req reqType.GetClipTaskReq) *result.Result[*model.ClipTask] {
	var clipTask *model.ClipTask
	var err error

	if req.Id > 0 {
		clipTask, err = s.svcCtx.ClipTaskModel.GetById(req.Id)
	} else if req.ClipTaskId != "" {
		clipTask, err = s.svcCtx.ClipTaskModel.GetByClipTaskId(req.ClipTaskId)
	} else {
		return result.ToError[*model.ClipTask](result.ErrorDataVerify.AddError(fmt.Errorf("必须提供ID或ClipTaskId")))
	}

	if err != nil {
		return result.ToError[*model.ClipTask](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult[*model.ClipTask](clipTask)
}

// UpdateClipTask 更新混剪任务
func (s *ClipTaskService) UpdateClipTask(req reqType.UpdateClipTaskReq) *result.Result[*model.ClipTask] {
	// 验证任务是否存在
	_, err := s.svcCtx.ClipTaskModel.GetById(req.Id)
	if err != nil {
		return result.ToError[*model.ClipTask](result.ErrorSelect.AddError(err))
	}

	// 更新字段
	updateData := make(map[string]interface{})
	if req.ClipTaskName != "" {
		updateData["clip_task_name"] = req.ClipTaskName
	}
	if req.AspectRatio != "" {
		updateData["aspect_ratio"] = req.AspectRatio
	}
	if req.ScaleMode != "" {
		updateData["scale_mode"] = req.ScaleMode
	}
	if req.MusicMixMode > 0 {
		updateData["music_mix_mode"] = req.MusicMixMode
	}
	if req.GenTotal > 0 {
		updateData["gen_total"] = req.GenTotal
	}
	if req.RemainingGenCount >= 0 {
		updateData["remaining_gen_count"] = req.RemainingGenCount
	}
	if req.GenStatus > 0 {
		updateData["gen_status"] = req.GenStatus
	}

	updateData["updated_at"] = time.Now()

	// 执行更新
	if err := s.svcCtx.ClipTaskModel.MapUpdate(nil, req.Id, updateData); err != nil {
		return result.ToError[*model.ClipTask](result.ErrorUpdate.AddError(err))
	}

	// 获取更新后的数据
	updatedTask, err := s.svcCtx.ClipTaskModel.GetById(req.Id)
	if err != nil {
		return result.ToError[*model.ClipTask](result.ErrorSelect.AddError(err))
	}

	utils.LogInfo("混剪任务更新成功", "id", req.Id, "name", updatedTask.ClipTaskName)
	return result.SuccessResult[*model.ClipTask](updatedTask)
}

// DeleteClipTask 删除混剪任务
func (s *ClipTaskService) DeleteClipTask(req reqType.DeleteClipTaskReq) *result.Result[bool] {
	clipTask, err := s.svcCtx.ClipTaskModel.GetById(req.Id)
	if err != nil {
		return result.ToError[bool](result.ErrorSelect.AddError(err))
	}

	if err := s.svcCtx.ClipTaskModel.Delete(clipTask); err != nil {
		return result.ToError[bool](result.ErrorDelete.AddError(err))
	}

	utils.LogInfo("混剪任务删除成功", "id", req.Id, "name", clipTask.ClipTaskName)
	return result.SuccessResult[bool](true)
}

// List 获取混剪任务列表
func (s *ClipTaskService) List(req reqType.ListClipTasksReq) *result.Result[*reqType.ListClipTasksResp] {
	conditions := make(map[string]interface{})

	if req.ClipTaskName != "" {
		conditions["clip_task_name"] = req.ClipTaskName
	}
	if req.GenStatus > 0 {
		conditions["gen_status"] = req.GenStatus
	}

	clipTasks, total, err := s.svcCtx.ClipTaskModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[*reqType.ListClipTasksResp](result.ErrorSelect.AddError(err))
	}

	resp := &reqType.ListClipTasksResp{
		List:     clipTasks,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult[*reqType.ListClipTasksResp](resp)
}

// BatchDeleteClipTasks 批量删除混剪任务
func (s *ClipTaskService) BatchDeleteClipTasks(req reqType.BatchDeleteClipTasksReq) *result.Result[bool] {
	if err := s.svcCtx.ClipTaskModel.BatchDelete(req.Ids); err != nil {
		return result.ToError[bool](result.ErrorDelete.AddError(err))
	}

	utils.LogInfo("批量删除混剪任务成功", "count", len(req.Ids))
	return result.SuccessResult[bool](true)
}

// UpdateClipTaskStatus 更新混剪任务状态
func (s *ClipTaskService) UpdateClipTaskStatus(req reqType.UpdateClipTaskStatusReq) *result.Result[bool] {
	if err := s.svcCtx.ClipTaskModel.UpdateGenStatus(req.Id, req.GenStatus); err != nil {
		return result.ToError[bool](result.ErrorUpdate.AddError(err))
	}

	utils.LogInfo("混剪任务状态更新成功", "id", req.Id, "status", req.GenStatus)
	return result.SuccessResult[bool](true)
}

// CreateClipTask 创建混剪任务
func (s *ClipTaskService) CreateClipTask(task *model.ClipTask) *result.Result[*model.ClipTask] {
	// 1. 验证参数规范性
	if err := s.validateClipTaskParams(task); err != nil {
		return result.ToError[*model.ClipTask](result.ErrorDataVerify.AddError(err))
	}

	// 2. 验证文件存在性
	if err := s.validateClipTaskFiles(task); err != nil {
		return result.ToError[*model.ClipTask](result.ErrorFileNotExist.AddError(err))
	}

	// 3. 进行素材预处理
	if err := s.preprocessClipTaskMaterials(task); err != nil {
		return result.ToError[*model.ClipTask](result.ErrorFfmpeg.AddError(err))
	}

	// 4. 设置任务基本信息
	task.CreatedAt = time.Now()
	task.UpdatedAt = time.Now()
	task.GenStatus = constant.ClipTask_GenStatus_Ready

	if err := s.svcCtx.ClipTaskModel.Create(task); err != nil {
		return result.ToError[*model.ClipTask](result.ErrorAdd.AddError(err))
	}
	return result.SuccessResult[*model.ClipTask](task)
}

// validateClipTaskParams 验证ClipTask参数规范性
func (s *ClipTaskService) validateClipTaskParams(task *model.ClipTask) error {
	// 验证ScaleMode
	validScaleModes := map[string]bool{
		constant.ClipTask_ScaleMode_WidthFix:    true,
		constant.ClipTask_ScaleMode_HeightFix:   true,
		constant.ClipTask_ScaleMode_ScaleToFill: true,
	}
	if !validScaleModes[task.ScaleMode] {
		return fmt.Errorf("无效的缩放模式: %s，有效值为: %s, %s, %s",
			task.ScaleMode,
			constant.ClipTask_ScaleMode_WidthFix,
			constant.ClipTask_ScaleMode_HeightFix,
			constant.ClipTask_ScaleMode_ScaleToFill)
	}

	// 验证MusicMixMode
	validMusicMixModes := map[int64]bool{
		constant.ClipTask_MusicMixMode_Mixd:    true,
		constant.ClipTask_MusicMixMode_OnlyBgm: true,
		constant.ClipTask_MusicMixMode_NoBgm:   true,
	}
	if !validMusicMixModes[task.MusicMixMode] {
		return fmt.Errorf("无效的音乐混合模式: %d，有效值为: %d(混合), %d(仅BGM), %d(无BGM)",
			task.MusicMixMode,
			constant.ClipTask_MusicMixMode_Mixd,
			constant.ClipTask_MusicMixMode_OnlyBgm,
			constant.ClipTask_MusicMixMode_NoBgm)
	}

	// 验证GenStatus
	validGenStatuses := map[int64]bool{
		constant.ClipTask_GenStatus_Ready:      true,
		constant.ClipTask_GenStatus_Generating: true,
	}
	if task.GenStatus != 0 && !validGenStatuses[task.GenStatus] {
		return fmt.Errorf("无效的生成状态: %d，有效值为: %d(准备), %d(生成中)",
			task.GenStatus,
			constant.ClipTask_GenStatus_Ready,
			constant.ClipTask_GenStatus_Generating)
	}

	return nil
}

// validateClipTaskFiles 验证ClipTask文件存在性
func (s *ClipTaskService) validateClipTaskFiles(task *model.ClipTask) error {
	// 验证音乐文件
	for _, musicUrl := range task.MusicUrl {
		if musicUrl != "" {
			if _, err := os.Stat(musicUrl); os.IsNotExist(err) {
				return fmt.Errorf("音乐文件不存在: %s", musicUrl)
			}
		}
	}

	// 验证头部封面文件
	for _, headCover := range task.HeadCover {
		if headCover != "" {
			if _, err := os.Stat(headCover); os.IsNotExist(err) {
				return fmt.Errorf("头部封面文件不存在: %s", headCover)
			}
		}
	}

	// 验证尾部封面文件
	for _, tailCover := range task.TailCover {
		if tailCover != "" {
			if _, err := os.Stat(tailCover); os.IsNotExist(err) {
				return fmt.Errorf("尾部封面文件不存在: %s", tailCover)
			}
		}
	}

	// 验证视频文件
	for _, group := range task.GroupList {
		for _, videoUrl := range group.VideoUrl {
			if videoUrl != "" {
				if _, err := os.Stat(videoUrl); os.IsNotExist(err) {
					return fmt.Errorf("视频文件不存在: %s", videoUrl)
				}
			}
		}
	}

	return nil
}

// preprocessClipTaskMaterials 预处理ClipTask素材
func (s *ClipTaskService) preprocessClipTaskMaterials(task *model.ClipTask) error {
	width := 0
	height := 0
	switch task.AspectRatio {
	case "9:16":
		width = 1080
		height = 1920
	case "16:9":
		width = 1920
		height = 1080
	case "1:1":
		width = 1080
		height = 1080
	case "4:3":
		width = 1440
		height = 1080
	default:
		return errors.New("不支持的视频比例")
	}
	// 处理音乐文件
	for i, musicUrl := range task.MusicUrl {
		if musicUrl != "" {
			processedPath, err := s.processMusicFile(musicUrl)
			if err != nil {
				return fmt.Errorf("处理音乐文件失败 %s: %v", musicUrl, err)
			}
			task.MusicUrl[i] = processedPath
		}
	}

	// 处理头部封面文件
	for i, headCover := range task.HeadCover {
		if headCover != "" {
			processedPath, err := s.processCoverFile(headCover)
			if err != nil {
				return fmt.Errorf("处理头部封面文件失败 %s: %v", headCover, err)
			}
			task.HeadCover[i] = processedPath
		}
	}

	// 处理尾部封面文件
	for i, tailCover := range task.TailCover {
		if tailCover != "" {
			processedPath, err := s.processCoverFile(tailCover)
			if err != nil {
				return fmt.Errorf("处理尾部封面文件失败 %s: %v", tailCover, err)
			}
			task.TailCover[i] = processedPath
		}
	}

	// 处理视频文件
	for groupIdx, group := range task.GroupList {
		for videoIdx, videoUrl := range group.VideoUrl {
			if videoUrl != "" {
				processedPath, err := s.processVideoFile(videoUrl, group.IsOriginSound, width, height, task.ScaleMode)
				if err != nil {
					return fmt.Errorf("处理视频文件失败 %s: %v", videoUrl, err)
				}
				task.GroupList[groupIdx].VideoUrl[videoIdx] = processedPath
			}
		}
	}

	return nil
}

// processMusicFile 处理音乐文件
func (s *ClipTaskService) processMusicFile(filePath string) (string, error) {
	// 计算文件MD5
	hash, err := s.calculateFileMD5(filePath)
	if err != nil {
		return "", fmt.Errorf("计算音乐文件MD5失败: %v", err)
	}

	// 获取文件扩展名
	ext := filepath.Ext(filePath)
	fileName := hash + ext

	// 检查缓存目录
	cacheDir := "./temp/ffmpeg/music"
	cachedPath := filepath.Join(cacheDir, fileName)

	// 如果缓存文件已存在，直接返回
	if _, err := os.Stat(cachedPath); err == nil {
		utils.LogInfo("音乐文件已存在缓存", "path", cachedPath)
		return cachedPath, nil
	}

	// 确保缓存目录存在
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return "", fmt.Errorf("创建音乐缓存目录失败: %v", err)
	}

	// 复制原文件到缓存目录（音乐文件不需要额外处理）
	if err := s.copyFile(filePath, cachedPath); err != nil {
		return "", fmt.Errorf("复制音乐文件到缓存失败: %v", err)
	}

	utils.LogInfo("音乐文件已缓存", "original", filePath, "cached", cachedPath)
	return cachedPath, nil
}

// processCoverFile 处理封面文件
func (s *ClipTaskService) processCoverFile(filePath string) (string, error) {
	// 计算文件MD5
	hash, err := s.calculateFileMD5(filePath)
	if err != nil {
		return "", fmt.Errorf("计算封面文件MD5失败: %v", err)
	}

	// 获取文件扩展名
	ext := filepath.Ext(filePath)
	fileName := hash + ext

	// 检查缓存目录
	cacheDir := "./temp/ffmpeg/cover"
	cachedPath := filepath.Join(cacheDir, fileName)

	// 如果缓存文件已存在，直接返回
	if _, err := os.Stat(cachedPath); err == nil {
		utils.LogInfo("封面文件已存在缓存", "path", cachedPath)
		return cachedPath, nil
	}

	// 确保缓存目录存在
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return "", fmt.Errorf("创建封面缓存目录失败: %v", err)
	}

	// 调用ffmpeg处理封面文件
	processedPath, err := ffmpeg.StartGenHasCoverVideo(s.ctx, filePath, filePath)
	if err != nil {
		return "", fmt.Errorf("ffmpeg处理封面文件失败: %v", err)
	}

	// 移动处理后的文件到缓存目录
	if err := s.moveFile(processedPath, cachedPath); err != nil {
		return "", fmt.Errorf("移动封面文件到缓存失败: %v", err)
	}

	utils.LogInfo("封面文件已处理并缓存", "original", filePath, "cached", cachedPath)
	return cachedPath, nil
}

// processVideoFile 处理视频文件
func (s *ClipTaskService) processVideoFile(filePath string, isOriginSound bool, width, height int, scaleMode string) (string, error) {
	// 计算文件MD5
	hash, err := s.calculateFileMD5(filePath)
	if err != nil {
		return "", fmt.Errorf("计算视频文件MD5失败: %v", err)
	}

	// 视频文件处理后统一为mp4格式
	fileName := hash + ".mp4"

	// 检查缓存目录
	cacheDir := config.GetConfig().Storage.MaterialDir + "/video"
	cachedPath := filepath.Join(cacheDir, fileName)

	// 如果缓存文件已存在，直接返回
	if _, err := os.Stat(cachedPath); err == nil {
		utils.LogInfo("视频文件已存在缓存", "path", cachedPath)
		return cachedPath, nil
	}

	// 确保缓存目录存在
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return "", fmt.Errorf("创建视频缓存目录失败: %v", err)
	}

	// 调用ffmpeg处理视频文件
	// 这里需要传入默认参数，实际使用时可能需要根据具体需求调整
	processedPath, err := ffmpeg.StartGenSingleVideo(s.ctx, filePath, nil, isOriginSound, width, height, scaleMode)
	if err != nil {
		return "", fmt.Errorf("ffmpeg处理视频文件失败: %v", err)
	}

	// 移动处理后的文件到缓存目录
	if err := s.moveFile(processedPath, cachedPath); err != nil {
		return "", fmt.Errorf("移动视频文件到缓存失败: %v", err)
	}

	utils.LogInfo("视频文件已处理并缓存", "original", filePath, "cached", cachedPath)
	return cachedPath, nil
}

// calculateFileMD5 计算文件MD5哈希值
func (s *ClipTaskService) calculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// copyFile 复制文件
func (s *ClipTaskService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return destFile.Sync()
}

// moveFile 移动文件
func (s *ClipTaskService) moveFile(src, dst string) error {
	// 先尝试重命名（如果在同一个文件系统上）
	if err := os.Rename(src, dst); err == nil {
		return nil
	}

	// 如果重命名失败，则复制后删除原文件
	if err := s.copyFile(src, dst); err != nil {
		return err
	}

	return os.Remove(src)
}

// StartClipTask 启动混剪任务
func (s *ClipTaskService) StartClipTask(req *reqType.StartClipTaskReq) *result.Result[*reqType.StartClipTaskResp] {
	//查询任务信息
	clipTask, err := s.svcCtx.ClipTaskModel.GetById(req.Id)
	if err != nil {
		return result.ToError[*reqType.StartClipTaskResp](result.ErrorSelect.AddError(err))
	}

	outFilePath := config.GetConfig().Storage.MaterialDir + "/clip"
	total := max(len(clipTask.MusicUrl), 1) * max(len(clipTask.HeadCover), 1) * max(len(clipTask.TailCover), 1)
	for _, group := range clipTask.GroupList {
		total *= max(len(group.VideoUrl), 1)
	}

	var videoPaths []string
	var failedPaths []string

	for i := int64(0); i < req.Count; i++ {
		position := utils.GetPosition(int64(total), clipTask.GenTotal-clipTask.RemainingGenCount+i)
		//将position 转化为 索引
		outputFile, err := ffmpeg.GenerateVideoByPosition(clipTask, clipTask.MusicUrl, clipTask.HeadCover, clipTask.TailCover, clipTask.GroupList, position, outFilePath)
		if err != nil {
			utils.LogError("生成混剪视频失败", "position", position, "error", err)
			failedPaths = append(failedPaths, fmt.Sprintf("position_%d", position))
			continue
		}

		absPath, err := filepath.Abs(outputFile)
		if err != nil {
			utils.LogError("获取绝对路径失败", "outputFile", outputFile, "error", err)
			failedPaths = append(failedPaths, outputFile)
			continue
		}
		videoPaths = append(videoPaths, absPath)
	}

	//修改混剪任务详情
	if err := s.svcCtx.ClipTaskModel.UpdateRemainingGenCount(req.Id, clipTask.RemainingGenCount-req.Count); err != nil {
		return result.ToError[*reqType.StartClipTaskResp](result.ErrorUpdate.AddError(err))
	}

	// 构建响应
	resp := &reqType.StartClipTaskResp{
		Success:     len(videoPaths) > 0,
		Message:     fmt.Sprintf("成功生成 %d 个混剪视频，失败 %d 个", len(videoPaths), len(failedPaths)),
		VideoPaths:  videoPaths,
		FailedPaths: failedPaths,
	}

	utils.LogInfo("混剪任务执行完成", "taskId", req.Id, "successCount", len(videoPaths), "failedCount", len(failedPaths))
	return result.SuccessResult(resp)
}
