package service

import (
	"context"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// SensitiveWordService 敏感词服务
type SensitiveWordService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewSensitiveWordService 创建敏感词服务
func NewSensitiveWordService(svcCtx *svc.ServiceContext) *SensitiveWordService {
	return &SensitiveWordService{
		svcCtx: svcCtx,
	}
}

func (s *SensitiveWordService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *SensitiveWordService) SetApp(app *application.App) {
	s.app = app
}

// GetSensitiveWords 获取敏感词配置
func (s *SensitiveWordService) GetSensitiveWords() *result.Result[string] {
	config, err := s.svcCtx.ConfigModel.GetByKey("sensitive_words")
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}

	if config == nil {
		return result.SuccessResult("")
	}

	return result.SuccessResult(config.Value)
}

// SetSensitiveWords 设置敏感词配置
func (s *SensitiveWordService) SetSensitiveWords(words string) *result.Result[any] {
	// 使用 UpsertByKey 方法，自动处理插入或更新
	if err := s.svcCtx.ConfigModel.UpsertByKey("sensitive_words", words, "敏感词配置"); err != nil {
		return result.ToError[any](result.ErrorUpdate.AddError(err))
	}

	return result.SimpleResult("敏感词配置保存成功")
}

// GetDeletedComments 获取被删除的评论列表
func (s *SensitiveWordService) GetDeletedComments(req types.DeletedCommentListReq) *result.Result[types.DeletedCommentListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.CommentText != "" {
		conditions["comment_text"] = req.CommentText
	}
	if req.SensitiveWord != "" {
		conditions["sensitive_word"] = req.SensitiveWord
	}
	if req.AccountName != "" {
		conditions["account_name"] = req.AccountName
	}
	if req.ProjectName != "" {
		conditions["project_name"] = req.ProjectName
	}
	if req.PromotionName != "" {
		conditions["promotion_name"] = req.PromotionName
	}

	// 调用model层获取数据
	comments, total, err := s.svcCtx.DeletedCommentModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.DeletedCommentListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.DeletedCommentListResp{
		Total:    total,
		List:     comments,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetDeletedCommentStat 获取删除评论统计信息
func (s *SensitiveWordService) GetDeletedCommentStat() *result.Result[map[string]interface{}] {
	stat, err := s.svcCtx.DeletedCommentModel.GetStat()
	if err != nil {
		return result.ToError[map[string]interface{}](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult(stat)
}

// CheckSensitiveWord 检查文本是否包含敏感词
func (s *SensitiveWordService) CheckSensitiveWord(text string) *result.Result[types.CheckSensitiveWordResp] {
	// 获取敏感词配置
	config, err := s.svcCtx.ConfigModel.GetByKey("sensitive_words")
	if err != nil {
		return result.ToError[types.CheckSensitiveWordResp](result.ErrorSelect.AddError(err))
	}

	var sensitiveWords string
	if config != nil {
		sensitiveWords = config.Value
	}

	// 检查敏感词
	foundWords := model.CheckSensitiveWords(text, sensitiveWords)

	resp := types.CheckSensitiveWordResp{
		ContainsSensitive: len(foundWords) > 0,
		SensitiveWords:    foundWords,
		Text:              text,
	}

	return result.SuccessResult(resp)
}
