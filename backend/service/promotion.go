package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"

	"github.com/jinzhu/copier"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// PromotionService 广告推广服务
type PromotionService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewPromotionService 创建广告推广服务
func NewPromotionService(svcCtx *svc.ServiceContext) *PromotionService {
	return &PromotionService{
		svcCtx: svcCtx,
	}
}

func (s *PromotionService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *PromotionService) SetApp(app *application.App) {
	s.app = app
}

// GetPromotionList 从数据库获取广告列表
func (s *PromotionService) GetPromotionList(req types.PromotionListReq) *result.Result[types.PromotionListResp] {
	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.AccountId > 0 {
		conditions["account_id"] = req.AccountId
	}
	if req.PromotionName != "" {
		conditions["promotion_name"] = req.PromotionName
	}
	if req.AdvertiserId > 0 {
		conditions["advertiser_id"] = req.AdvertiserId
	}
	if req.ProjectId != "" {
		conditions["project_id"] = req.ProjectId
	}
	if req.Status != "" {
		conditions["promotion_status"] = req.Status
	}
	if req.Remark != "" {
		conditions["remark"] = req.Remark
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	promotions, total, err := s.svcCtx.PromotionModel.List(int(req.Page), int(req.PageSize), conditions)
	if err != nil {
		return result.ToError[types.PromotionListResp](result.ErrorSelect.AddError(err))
	}

	// 为每个广告获取策略绑定信息
	for _, promotion := range promotions {
		strategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("promotion", promotion.PromotionId)
		if err == nil {
			strategyIds := make([]int64, len(strategies))
			for i, strategy := range strategies {
				strategyIds[i] = strategy.ID
			}
			promotion.StrategyIds = strategyIds
		}
	}

	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     req.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询广告列表",
				Description:   fmt.Sprintf("账号ID: %d，查询广告列表", req.AccountId),
				OperationTime: time.Now(),
			})
		}
	}()

	resp := types.PromotionListResp{
		Total:    total,
		List:     promotions,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetPromotionListAsync 获取广告统计列表
func (s *PromotionService) GetPromotionListAsync(req types.AdvertiserReq) *result.Result[bot.GetPromotionResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetPromotionResp](result.ErrorSelect.AddError(err))
	}
	resp, err := b.GetPromotionList(req.Page, req.PageSize, 1, req.Keyword, req.ProjectId, req.PromotionId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.GetPromotionResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询广告列表",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，查询广告列表", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// DeletePromotionAsync 异步删除广告
func (s *PromotionService) DeletePromotionAsync(req types.DeletePromotionAsyncReq) *result.Result[bot.DeletePromotionResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.DeletePromotionResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.DeletePromotion(req.AdvertiserId, req.PromotionIds, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.DeletePromotionResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "删除广告主下广告",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，删除[%s]广告主下广告[%v]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.PromotionIds),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// BatchDeletePromotionAsync 异步批量删除广告
func (s *PromotionService) BatchDeletePromotionAsync(req types.BatchDeletePromotionAsyncReq) *result.Result[bot.BatchDeletePromotionResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.BatchDeletePromotionResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.BatchDeletePromotion(req.AccountDetailList, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.BatchDeletePromotionResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			descStr := ""
			for _, item := range req.AccountDetailList {
				descStr += fmt.Sprintf("[%s]广告主下广告[%v],  ", item.AdvertiserId, item.Id)
			}
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "跨广告主删除广告",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，跨广告主删除广告,删除 %s", accountInfo.AccountId, accountInfo.AccountName, descStr),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// UpdatePromotionStatus 更新广告状态
func (s *PromotionService) UpdatePromotionStatus(req types.UpdatePromotionStatusReq) *result.Result[bot.UpdatePromotionStatusResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.UpdatePromotionStatusResp](result.ErrorNewBot.AddError(err))
	}

	resp, err := b.UpdatePromotionStatus(req.AdvertiserId, req.StatusMap, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.UpdatePromotionStatusResp](result.ErrorUpdate.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			descStr := ""
			for promotionId, status := range req.StatusMap {
				descStr += fmt.Sprintf("广告[%s]状态更新为[%d], ", promotionId, status)
			}
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新广告状态",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下更新广告状态: %s", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, descStr),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// UpdatePromotionName 修改广告名称
func (s *PromotionService) UpdatePromotionName(req types.UpdatePromotionNameReq) *result.Result[bot.UpdatePromotionNameResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.UpdatePromotionNameResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.UpdatePromotionName(req.AdvertiserId, req.PromotionId, req.Name, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.UpdatePromotionNameResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "修改广告名称",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下修改广告[%s]名称为[%s]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.PromotionId, req.Name),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// CopyPromotion 复制广告
func (s *PromotionService) CopyPromotion(req types.CopyPromotionReq) *result.Result[bot.CreatePromotionResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.CreatePromotionResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.CopyPromotion(req.AdvertiserId, req.SourcePromotionId, req.NewPromotionName, req.NewProjectId, accountInfo.Cookie, req.VideoMaterialInfo)
	if err != nil {
		return result.ToError[bot.CreatePromotionResp](result.ErrorSelect.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "复制广告",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下复制广告[%s]为[%s]到项目[%s]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.SourcePromotionId, req.NewPromotionName, req.NewProjectId),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// BatchCopyPromotion 批量复制广告
func (s *PromotionService) BatchCopyPromotion(req types.BatchCopyPromotionReq) *result.Result[map[string]interface{}] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[map[string]interface{}](result.ErrorSelect.AddError(err))
	}

	results, errors := b.BatchCopyPromotion(req.AdvertiserId, req.SourcePromotionIds, req.NamePrefix, req.TargetProjectId, accountInfo.Cookie)

	// 构建返回结果
	resultMap := map[string]interface{}{
		"success_count": len(results),
		"error_count":   len(errors),
		"success_list":  results,
		"error_list":    errors,
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			descStr := fmt.Sprintf("复制 %d 个广告，成功 %d 个，失败 %d 个", len(req.SourcePromotionIds), len(results), len(errors))
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "批量复制广告",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下批量复制广告到项目[%s]，%s", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.TargetProjectId, descStr),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(resultMap)
}

// RefreshPromotions 刷新指定账户下的广告数据
func (s *PromotionService) RefreshPromotions(accountId int64) *result.Result[string] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(accountId)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}

	// 循环获取所有广告数据
	page := int64(1)
	pageSize := int64(100) // 每次获取100条数据
	hasMore := true
	allPromotions := make([]model.Promotion, 0, 100)

	for hasMore {
		fmt.Println("刷新广告列表 页数:", page)
		resp, err := b.GetPromotionList(page, pageSize, 1, "", "", "", accountInfo.Cookie)
		if err != nil {
			return result.ToError[string](result.ErrorSelect.AddError(err))
		}

		// 转换API响应数据为model.Promotion
		tempPromotionList := make([]model.Promotion, 0, 100)

		if err := copier.CopyWithOption(&tempPromotionList, resp.Data.DataList, copier.Option{Converters: []copier.TypeConverter{utils.FormatStringToTimeConverter(time.DateTime)}}); err != nil {
			return result.ToError[string](result.ErrorCopy.AddError(err))
		}
		allPromotions = append(allPromotions, tempPromotionList...)
		// 检查是否还有更多数据
		hasMore = resp.Data.Pagination.HasMore
		page++
	}

	// 获取数据库中该账户下的广告ID列表（只查询必要字段）
	PromotionIdList, err := s.svcCtx.PromotionModel.GetPromotionIdByAccountId(accountId)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}

	// 创建数据库中已存在的promotion_id映射
	promotionIdMap := make(map[string]*model.Promotion)
	for _, promo := range PromotionIdList {
		promotionIdMap[promo.PromotionId] = promo
	}

	// 创建API返回的promotion_id映射
	apiPromotionMap := make(map[string]*model.Promotion)
	for i := range allPromotions {
		apiPromotionMap[allPromotions[i].PromotionId] = &allPromotions[i]
	}

	// 开始数据同步处理
	var toCreate []*model.Promotion // 需要新增的
	var toUpdate []*model.Promotion // 需要更新的
	var toDelete []string           // 需要标记删除的广告ID

	// 处理API返回的数据（新增或更新）
	for promotionId, apiPromo := range apiPromotionMap {
		apiPromo.AccountId = accountId // 设置账户ID
		if dbPromo, exists := promotionIdMap[promotionId]; exists {
			apiPromo.Id = dbPromo.Id                 // 保持数据库ID不变
			apiPromo.CreateTime = dbPromo.CreateTime // 保持创建时间不变
			apiPromo.UpdateTime = time.Now()         // 更新时间为当前时间
			toUpdate = append(toUpdate, apiPromo)
		} else { // 数据库中不存在，需要新增
			toCreate = append(toCreate, apiPromo)
		}
	}

	// 处理需要标记删除的数据（数据库中存在但API中不存在）
	for promotionId, dbPromoId := range promotionIdMap {
		if _, exists := apiPromotionMap[promotionId]; !exists {
			// API中不存在，需要标记删除
			if dbPromoId.DeleteTime == 0 { // 检查是否已经标记删除（时间戳为0表示未删除）
				toDelete = append(toDelete, promotionId)
			}
		}
	}

	// 执行数据库操作
	if len(toCreate) > 0 {
		if err := s.svcCtx.PromotionModel.BatchCreate(nil, toCreate); err != nil {
			return result.ToError[string](result.ErrorAdd.AddError(err))
		}
	}
	// 更新操作
	if len(toUpdate) > 0 {
		for _, promo := range toUpdate {
			if err := s.svcCtx.PromotionModel.Update(nil, promo); err != nil {
				return result.ToError[string](result.ErrorUpdate.AddError(err))
			}
		}
	}
	// 标记删除操作
	if len(toDelete) > 0 {
		if err := s.svcCtx.PromotionModel.BatchMarkDelete(toDelete); err != nil {
			return result.ToError[string](result.ErrorDelete.AddError(err))
		}
	}
	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，刷新广告数据。新增: %d, 更新: %d, 删除: %d",
				accountInfo.AccountId, accountInfo.AccountName, len(toCreate), len(toUpdate), len(toDelete))

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "刷新广告数据",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult[string](fmt.Sprintf("刷新广告数据成功，新增: %d, 更新: %d, 删除: %d",
		len(toCreate), len(toUpdate), len(toDelete)))
}

// GetPromotionInfo 获取广告详情
func (s *PromotionService) GetPromotionInfo(req types.GetPromotionInfoReq) *result.Result[bot.GetPromotionInfoResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetPromotionInfoResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.GetPromotionInfo(req.AdvertiserId, req.PromotionIds, req.NeedInvisibleMaterial, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.GetPromotionInfoResp](result.ErrorSelect.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取广告详情",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下获取广告详情，广告数量: %d", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, len(req.PromotionIds)),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// UpdatePromotionBid 修改广告出价
func (s *PromotionService) UpdatePromotionBid(req types.UpdatePromotionBidReq) *result.Result[bot.UpdatePromotionBidResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.UpdatePromotionBidResp](result.ErrorNewBot.AddError(err))
	}

	resp, err := b.UpdatePromotionBid(req.AdvertiserId, req.Bids, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.UpdatePromotionBidResp](result.ErrorUpdate.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			bidDescStr := ""
			for promotionId, bid := range req.Bids {
				bidDescStr += fmt.Sprintf("广告[%s]出价更新为[%s], ", promotionId, bid)
			}
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Promontion,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "修改广告出价",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下修改广告出价: %s", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, bidDescStr),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}
