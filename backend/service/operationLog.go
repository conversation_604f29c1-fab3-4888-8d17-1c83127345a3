package service

import (
	"context"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"
	"time"
)

// OperationLogService 操作日志服务
type OperationLogService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewOperationLogService 创建操作日志服务
func NewOperationLogService(svcCtx *svc.ServiceContext) *OperationLogService {
	return &OperationLogService{
		svcCtx: svcCtx,
	}
}
func (s *OperationLogService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *OperationLogService) SetApp(app *application.App) {
	s.app = app
}

// GetOperationLogList 获取操作日志列表
func (s *OperationLogService) GetOperationLogList(req reqType.OperationLogListReq) *result.Result[types.OperationLogListResp] {
	// 调用model层获取数据
	logs, total, err := s.svcCtx.OperationLogModel.List(req)
	if err != nil {
		return result.ToError[types.OperationLogListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.OperationLogListResp{
		Total:    total,
		List:     logs,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetOperationLogById 根据ID获取操作日志详情
func (s *OperationLogService) GetOperationLogById(id int64) *result.Result[model.OperationLog] {
	log, err := s.svcCtx.OperationLogModel.GetById(id)
	if err != nil {
		return result.ToError[model.OperationLog](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult[model.OperationLog](*log)
}

// CreateOperationLog 创建操作日志
func (s *OperationLogService) CreateOperationLog(log *model.OperationLog) *result.Result[any] {
	if err := s.svcCtx.OperationLogModel.Create(log); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	return result.SimpleResult("创建操作日志成功")
}

// BatchCreateOperationLog 批量创建操作日志
func (s *OperationLogService) BatchCreateOperationLog(logs []*model.OperationLog) *result.Result[any] {
	if err := s.svcCtx.OperationLogModel.BatchCreate(logs); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	return result.SimpleResult("批量创建操作日志成功")
}

// DeleteOperationLogByTime 根据时间删除操作日志
func (s *OperationLogService) DeleteOperationLogByTime(beforeTime time.Time) *result.Result[any] {
	if err := s.svcCtx.OperationLogModel.DeleteByTime(beforeTime); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除操作日志成功")
}

// BatchDeleteOperationLog 批量删除操作日志
func (s *OperationLogService) BatchDeleteOperationLog(ids []int64) *result.Result[any] {
	if err := s.svcCtx.OperationLogModel.DeleteBatch(ids); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("批量删除操作日志成功")
}

// GetOperationLogStats 获取操作日志统计信息
func (s *OperationLogService) GetOperationLogStats(startTime, endTime time.Time) *result.Result[map[string]int64] {
	stats, err := s.svcCtx.OperationLogModel.GetStatsByTimeRange(startTime, endTime)
	if err != nil {
		return result.ToError[map[string]int64](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(stats)
}
