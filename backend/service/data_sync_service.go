package service

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

type DataSyncService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
	mu     sync.RWMutex
}

func NewDataSyncService(svcCtx *svc.ServiceContext) *DataSyncService {
	return &DataSyncService{
		svcCtx: svcCtx,
	}
}

// OnStartup Wails服务启动时调用
func (s *DataSyncService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

// SetApp 设置应用上下文
func (s *DataSyncService) SetApp(app *application.App) {
	s.app = app
}

// SyncAdvertiserInfo 同步单个广告主信息
func (s *DataSyncService) SyncAdvertiserInfo(advertiserID int64) error {
	advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserID)
	if err != nil {
		return fmt.Errorf("获取广告主信息失败: %w", err)
	}

	if advertiser == nil {
		return fmt.Errorf("广告主不存在")
	}

	// 获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	_, _, err = s.svcCtx.GetBot(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用API获取广告主列表，更新广告主信息
	// 这里可以根据需要调用相应的API
	utils.LogInfo("广告主信息同步完成", "advertiser_id", advertiserID)

	// 更新广告主的更新时间
	advertiser.UpdateTime = time.Now()
	if err := s.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		return fmt.Errorf("更新广告主时间失败: %w", err)
	}

	return nil
}

// SyncAdvertiserProjectInfo 同步广告主下的项目信息
func (s *DataSyncService) SyncAdvertiserProjectInfo(advertiserID int64) error {
	advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserID)
	if err != nil {
		return fmt.Errorf("获取广告主信息失败: %w", err)
	}

	if advertiser == nil {
		return fmt.Errorf("广告主不存在")
	}

	// 获取广告主对应的账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用子账户项目列表API获取最新数据
	aadvid := fmt.Sprintf("%d", advertiser.AdvertiserId)
	page := 1
	pageSize := 100

	utils.LogInfo("开始同步项目信息",
		"advertiser_id", advertiser.AdvertiserId,
		"aadvid", aadvid)

	// 收集所有API返回的项目ID
	allApiProjectIds := make([]string, 0)
	allProjectsToUpdate := make([]*model.Project, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetSubAccountProjectList(aadvid, page, pageSize, account.Cookie, "", "")
		if err != nil {
			return fmt.Errorf("调用子账户项目列表API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.TotalPage
			utils.LogInfo("获取到分页信息",
				"advertiser_id", advertiser.AdvertiserId,
				"total_pages", totalPages,
				"total_count", resp.Data.Pagination.TotalCount)
		}

		// 如果有项目数据，处理当前页数据
		if len(resp.Data.Projects) > 0 {
			utils.LogInfo("获取到项目数据",
				"advertiser_id", advertiser.AdvertiserId,
				"current_page", currentPage,
				"project_count", len(resp.Data.Projects))

			// 转换API数据为数据库模型
			for _, apiProject := range resp.Data.Projects {
				// 转换字符串类型的ID为int64
				campaignId := int64(0)
				if apiProject.CampaignId != "" {
					if id, err := strconv.ParseInt(apiProject.CampaignId, 10, 64); err == nil {
						campaignId = id
					}
				}

				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiProject.ExternalAction != "" {
					if action, err := strconv.Atoi(apiProject.ExternalAction); err == nil {
						externalAction = action
					}
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiProject.AdPricing != "" {
					if pricing, err := strconv.Atoi(apiProject.AdPricing); err == nil {
						adPricing = pricing
					}
				}

				project := &model.Project{
					AccountId:            advertiser.AccountId,
					ProjectId:            apiProject.ProjectId,
					ProjectName:          apiProject.ProjectName,
					ProjectStatus:        apiProject.ProjectStatus,
					ProjectStatusName:    apiProject.ProjectStatusName,
					AdvertiserId:         advertiser.AdvertiserId,
					AdvertiserName:       advertiser.AdvertiserName,
					CampaignBudget:       apiProject.CampaignBudget,
					ProjectBid:           apiProject.ProjectBid,
					ProjectDeepCpaBid:    apiProject.ProjectDeepCpaBid,
					ProjectRoiGoal:       apiProject.ProjectRoiGoal,
					ProjectFirstRoiGoal:  apiProject.ProjectFirstRoiGoal,
					LandingType:          apiProject.LandingType,
					LandingTypeName:      apiProject.LandingTypeName,
					DeliveryMode:         apiProject.DeliveryMode,
					DeliveryModeInternal: apiProject.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiProject.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiProject.AdPricingName,
					StartTime:            apiProject.StartTime,
					EndTime:              apiProject.EndTime,
					ModifyTime:           apiProject.ModifyTime,
					CampaignId:           campaignId,
					UpdateTime:           time.Now(),
				}

				// 获取已存在的项目信息，保留ID和创建时间
				if existingProject, err := s.svcCtx.ProjectModel.GetByProjectId(apiProject.ProjectId); err == nil && existingProject != nil {
					project.Id = existingProject.Id
					project.CreateTime = existingProject.CreateTime
				}

				allProjectsToUpdate = append(allProjectsToUpdate, project)
				allApiProjectIds = append(allApiProjectIds, apiProject.ProjectId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.Projects)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建项目信息
	if len(allProjectsToUpdate) > 0 {
		utils.LogInfo("开始批量更新项目信息",
			"advertiser_id", advertiser.AdvertiserId,
			"total_projects", len(allProjectsToUpdate))

		for _, project := range allProjectsToUpdate {
			if project.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.ProjectModel.Update(nil, project); err != nil {
					utils.LogError("更新项目信息失败",
						"project_id", project.ProjectId,
						"project_name", project.ProjectName,
						"error", err.Error())
					// 继续处理其他项目，不因单个项目更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				project.CreateTime = time.Now()
				if err := s.svcCtx.ProjectModel.Create(project); err != nil {
					utils.LogError("创建项目信息失败",
						"project_id", project.ProjectId,
						"project_name", project.ProjectName,
						"error", err.Error())
					// 继续处理其他项目，不因单个项目创建失败而中断
				}
			}
		}

		// 获取数据库中该广告主下的所有项目
		existingProjects, _, err := s.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{"advertiser_id": advertiser.AdvertiserId})
		if err != nil {
			utils.LogError("获取现有项目失败",
				"advertiser_id", advertiser.AdvertiserId,
				"error", err.Error())
		} else {
			// 找出需要删除的项目（在数据库中存在但API中不存在）
			projectsToDelete := make([]string, 0)
			for _, existingProject := range existingProjects {
				found := false
				for _, apiProjectId := range allApiProjectIds {
					if existingProject.ProjectId == apiProjectId {
						found = true
						break
					}
				}
				if !found {
					projectsToDelete = append(projectsToDelete, existingProject.ProjectId)
				}
			}

			// 标记删除不存在的项目
			if len(projectsToDelete) > 0 {
				if err := s.svcCtx.ProjectModel.BatchMarkDelete(projectsToDelete); err != nil {
					utils.LogError("批量标记删除项目失败",
						"advertiser_id", advertiser.AdvertiserId,
						"delete_count", len(projectsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期项目",
						"advertiser_id", advertiser.AdvertiserId,
						"delete_count", len(projectsToDelete))
				}
			}
		}

		utils.LogInfo("项目信息更新完成",
			"advertiser_id", advertiser.AdvertiserId,
			"updated_count", len(allProjectsToUpdate),
			"total_pages", totalPages)
	} else {
		// 如果API没有返回项目数据，将该广告主下的所有项目标记为删除
		existingProjects, _, err := s.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{"advertiser_id": advertiser.AdvertiserId})
		if err != nil {
			utils.LogError("获取现有项目失败",
				"advertiser_id", advertiser.AdvertiserId,
				"error", err.Error())
		} else if len(existingProjects) > 0 {
			projectIds := make([]string, 0, len(existingProjects))
			for _, project := range existingProjects {
				projectIds = append(projectIds, project.ProjectId)
			}

			if err := s.svcCtx.ProjectModel.BatchMarkDelete(projectIds); err != nil {
				utils.LogError("批量标记删除所有项目失败",
					"advertiser_id", advertiser.AdvertiserId,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有项目（API无数据）",
					"advertiser_id", advertiser.AdvertiserId,
					"delete_count", len(projectIds))
			}
		}
	}

	// 更新广告主的更新时间
	advertiser.UpdateTime = time.Now()
	if err := s.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		utils.LogError("更新广告主时间失败",
			"advertiser_id", advertiser.AdvertiserId,
			"error", err.Error())
		// 不返回错误，因为项目信息已经更新成功
	}

	return nil
}

// SyncAdvertiserPromotionInfo 同步广告主下的广告信息
func (s *DataSyncService) SyncAdvertiserPromotionInfo(advertiserID int64) error {
	advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserID)
	if err != nil {
		return fmt.Errorf("获取广告主信息失败: %w", err)
	}

	if advertiser == nil {
		return fmt.Errorf("广告主不存在")
	}

	// 获取广告主对应的账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(advertiser.AccountId)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用子账户广告列表API获取最新数据
	aadvid := fmt.Sprintf("%d", advertiser.AdvertiserId)
	page := 1
	pageSize := 100

	utils.LogInfo("开始同步广告信息",
		"advertiser_id", advertiser.AdvertiserId,
		"aadvid", aadvid)

	// 收集所有API返回的广告ID
	allApiPromotionIds := make([]string, 0)
	allPromotionsToUpdate := make([]*model.Promotion, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetSubAccountPromotionList(aadvid, page, pageSize, account.Cookie)
		if err != nil {
			return fmt.Errorf("调用子账户广告列表API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.TotalPage
			utils.LogInfo("获取到分页信息",
				"advertiser_id", advertiser.AdvertiserId,
				"total_pages", totalPages,
				"total_count", resp.Data.Pagination.TotalCount)
		}

		// 如果有广告数据，处理当前页数据
		if len(resp.Data.Ads) > 0 {
			utils.LogInfo("获取到广告数据",
				"advertiser_id", advertiser.AdvertiserId,
				"current_page", currentPage,
				"promotion_count", len(resp.Data.Ads))

			// 转换API数据为数据库模型
			for _, apiPromotion := range resp.Data.Ads {
				// 转换字符串类型的ID为int64
				campaignId := int64(0)
				if apiPromotion.CampaignId != "" {
					if id, err := strconv.ParseInt(apiPromotion.CampaignId, 10, 64); err == nil {
						campaignId = id
					}
				}

				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiPromotion.ExternalAction != "" {
					if action, err := strconv.Atoi(apiPromotion.ExternalAction); err == nil {
						externalAction = action
					}
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiPromotion.AdPricing != "" {
					if pricing, err := strconv.Atoi(apiPromotion.AdPricing); err == nil {
						adPricing = pricing
					}
				}

				// 转换字符串类型的广告ID为int64
				adId := int64(0)
				if apiPromotion.AdId != "" {
					if id, err := strconv.ParseInt(apiPromotion.AdId, 10, 64); err == nil {
						adId = id
					}
				}

				promotion := &model.Promotion{
					AccountId:            advertiser.AccountId,
					PromotionId:          apiPromotion.PromotionId,
					PromotionName:        apiPromotion.PromotionName,
					AdvertiserId:         advertiser.AdvertiserId,
					AdvertiserName:       advertiser.AdvertiserName,
					ProjectId:            &apiPromotion.ProjectId,
					ProjectName:          apiPromotion.ProjectName,
					PromotionStatus:      apiPromotion.PromotionStatus,
					PromotionStatusName:  apiPromotion.PromotionStatusName,
					AdId:                 adId,
					AdBudget:             apiPromotion.AdBudget,
					AdBid:                apiPromotion.AdBid,
					ProjectBid:           apiPromotion.ProjectBid,
					LandingType:          apiPromotion.LandingType,
					LandingTypeName:      apiPromotion.LandingTypeName,
					DeliveryMode:         apiPromotion.DeliveryMode,
					DeliveryModeInternal: apiPromotion.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiPromotion.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiPromotion.AdPricingName,
					StartTime:            apiPromotion.StartTime,
					EndTime:              apiPromotion.EndTime,
					ModifyTime:           apiPromotion.ModifyTime,
					CampaignId:           campaignId,
					UpdateTime:           time.Now(),
				}

				// 获取已存在的广告信息，保留ID和创建时间
				if existingPromotion, err := s.svcCtx.PromotionModel.GetByPromotionId(apiPromotion.PromotionId); err == nil && existingPromotion != nil {
					promotion.Id = existingPromotion.Id
					promotion.CreateTime = existingPromotion.CreateTime
				}

				allPromotionsToUpdate = append(allPromotionsToUpdate, promotion)
				allApiPromotionIds = append(allApiPromotionIds, apiPromotion.PromotionId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.Ads)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建广告信息
	if len(allPromotionsToUpdate) > 0 {
		utils.LogInfo("开始批量更新广告信息",
			"advertiser_id", advertiser.AdvertiserId,
			"total_promotions", len(allPromotionsToUpdate))

		for _, promotion := range allPromotionsToUpdate {
			if promotion.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.PromotionModel.Update(nil, promotion); err != nil {
					utils.LogError("更新广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				promotion.CreateTime = time.Now()
				if err := s.svcCtx.PromotionModel.Create(promotion); err != nil {
					utils.LogError("创建广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告创建失败而中断
				}
			}
		}

		// 获取数据库中该广告主下的所有广告
		existingPromotions, err := s.svcCtx.PromotionModel.GetByAdvertiserId(advertiser.AdvertiserId)
		if err != nil {
			utils.LogError("获取现有广告失败",
				"advertiser_id", advertiser.AdvertiserId,
				"error", err.Error())
		} else {
			// 找出需要删除的广告（在数据库中存在但API中不存在）
			promotionsToDelete := make([]string, 0)
			for _, existingPromotion := range existingPromotions {
				found := false
				for _, apiPromotionId := range allApiPromotionIds {
					if existingPromotion.PromotionId == apiPromotionId {
						found = true
						break
					}
				}
				if !found {
					promotionsToDelete = append(promotionsToDelete, existingPromotion.PromotionId)
				}
			}

			// 标记删除不存在的广告
			if len(promotionsToDelete) > 0 {
				if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionsToDelete); err != nil {
					utils.LogError("批量标记删除广告失败",
						"advertiser_id", advertiser.AdvertiserId,
						"delete_count", len(promotionsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期广告",
						"advertiser_id", advertiser.AdvertiserId,
						"delete_count", len(promotionsToDelete))
				}
			}
		}

		utils.LogInfo("广告信息更新完成",
			"advertiser_id", advertiser.AdvertiserId,
			"updated_count", len(allPromotionsToUpdate),
			"total_pages", totalPages)
	} else {
		// 如果API没有返回广告数据，将该广告主下的所有广告标记为删除
		existingPromotions, err := s.svcCtx.PromotionModel.GetByAdvertiserId(advertiser.AdvertiserId)
		if err != nil {
			utils.LogError("获取现有广告失败",
				"advertiser_id", advertiser.AdvertiserId,
				"error", err.Error())
		} else if len(existingPromotions) > 0 {
			promotionIds := make([]string, 0, len(existingPromotions))
			for _, promotion := range existingPromotions {
				promotionIds = append(promotionIds, promotion.PromotionId)
			}

			if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionIds); err != nil {
				utils.LogError("批量标记删除所有广告失败",
					"advertiser_id", advertiser.AdvertiserId,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有广告（API无数据）",
					"advertiser_id", advertiser.AdvertiserId,
					"delete_count", len(promotionIds))
			}
		}
	}

	// 更新广告主的更新时间
	advertiser.UpdateTime = time.Now()
	if err := s.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		utils.LogError("更新广告主时间失败",
			"advertiser_id", advertiser.AdvertiserId,
			"error", err.Error())
		// 不返回错误，因为广告信息已经更新成功
	}

	return nil
}

// SyncAccountData 同步账户下的所有数据（使用GetProjectList等全量API）
func (s *DataSyncService) SyncAccountData(accountID int64) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	utils.LogInfo("开始同步账户数据", "account_id", accountID)

	// 同步所有项目信息（使用GetProjectList API）
	if err := s.SyncAllProjectInfo(accountID); err != nil {
		utils.LogError("同步所有项目信息失败",
			"account_id", accountID,
			"error", err.Error())
		return fmt.Errorf("同步所有项目信息失败: %w", err)
	}

	// 同步所有广告信息
	if err := s.SyncAllPromotionInfo(accountID); err != nil {
		utils.LogError("同步所有广告信息失败",
			"account_id", accountID,
			"error", err.Error())
		return fmt.Errorf("同步所有广告信息失败: %w", err)
	}

	utils.LogInfo("账户数据同步完成", "account_id", accountID)
	return nil
}

// SyncAllData 同步所有数据
func (s *DataSyncService) SyncAllData() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	utils.LogInfo("开始同步所有数据")

	// 获取所有账户
	accounts, err := s.svcCtx.AccountModel.ListAll()
	if err != nil {
		return fmt.Errorf("获取账户列表失败: %w", err)
	}

	if len(accounts) == 0 {
		utils.LogInfo("没有账户数据")
		return nil
	}

	// 同步每个账户的数据
	for _, account := range accounts {
		if err := s.SyncAccountData(account.AccountId); err != nil {
			utils.LogError("同步账户数据失败",
				"account_id", account.AccountId,
				"error", err.Error())
			continue
		}
	}

	utils.LogInfo("所有数据同步完成")
	return nil
}

// SyncProjectPromotionInfo 同步项目下的广告信息
func (s *DataSyncService) SyncProjectPromotionInfo(accountID int64, advertiserID int64, projectID string) error {
	// 获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountID)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(accountID)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用按项目获取广告列表API
	aadvid := fmt.Sprintf("%d", advertiserID)
	page := 1
	pageSize := 100

	utils.LogInfo("开始同步项目下的广告信息",
		"account_id", accountID,
		"advertiser_id", advertiserID,
		"project_id", projectID)

	// 收集所有API返回的广告ID
	allApiPromotionIds := make([]string, 0)
	allPromotionsToUpdate := make([]*model.Promotion, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetSubAccountPromotionListByProject(aadvid, page, pageSize, account.Cookie, []string{projectID})
		if err != nil {
			return fmt.Errorf("调用项目广告列表API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.TotalPage
			utils.LogInfo("获取到分页信息",
				"account_id", accountID,
				"advertiser_id", advertiserID,
				"project_id", projectID,
				"total_pages", totalPages,
				"total_count", resp.Data.Pagination.TotalCount)
		}

		// 如果有广告数据，处理当前页数据
		if len(resp.Data.Ads) > 0 {
			utils.LogInfo("获取到项目广告数据",
				"account_id", accountID,
				"advertiser_id", advertiserID,
				"project_id", projectID,
				"current_page", currentPage,
				"promotion_count", len(resp.Data.Ads))

			// 转换API数据为数据库模型
			for _, apiPromotion := range resp.Data.Ads {
				// 转换字符串类型的ID为int64
				campaignId := int64(0)
				if apiPromotion.CampaignId != "" {
					if id, err := strconv.ParseInt(apiPromotion.CampaignId, 10, 64); err == nil {
						campaignId = id
					}
				}

				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiPromotion.ExternalAction != "" {
					if action, err := strconv.Atoi(apiPromotion.ExternalAction); err == nil {
						externalAction = action
					}
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiPromotion.AdPricing != "" {
					if pricing, err := strconv.Atoi(apiPromotion.AdPricing); err == nil {
						adPricing = pricing
					}
				}

				// 转换字符串类型的广告ID为int64
				adId := int64(0)
				if apiPromotion.AdId != "" {
					if id, err := strconv.ParseInt(apiPromotion.AdId, 10, 64); err == nil {
						adId = id
					}
				}

				promotion := &model.Promotion{
					AccountId:            accountID,
					PromotionId:          apiPromotion.PromotionId,
					PromotionName:        apiPromotion.PromotionName,
					AdvertiserId:         advertiserID,
					AdvertiserName:       "", // 这里可以从广告主模型中获取
					ProjectId:            &projectID,
					ProjectName:          apiPromotion.ProjectName,
					PromotionStatus:      apiPromotion.PromotionStatus,
					PromotionStatusName:  apiPromotion.PromotionStatusName,
					AdId:                 adId,
					AdBudget:             apiPromotion.AdBudget,
					AdBid:                apiPromotion.AdBid,
					ProjectBid:           apiPromotion.ProjectBid,
					LandingType:          apiPromotion.LandingType,
					LandingTypeName:      apiPromotion.LandingTypeName,
					DeliveryMode:         apiPromotion.DeliveryMode,
					DeliveryModeInternal: apiPromotion.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiPromotion.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiPromotion.AdPricingName,
					StartTime:            apiPromotion.StartTime,
					EndTime:              apiPromotion.EndTime,
					ModifyTime:           apiPromotion.ModifyTime,
					CampaignId:           campaignId,
					UpdateTime:           time.Now(),
				}

				// 获取已存在的广告信息，保留ID和创建时间
				if existingPromotion, err := s.svcCtx.PromotionModel.GetByPromotionId(apiPromotion.PromotionId); err == nil && existingPromotion != nil {
					promotion.Id = existingPromotion.Id
					promotion.CreateTime = existingPromotion.CreateTime
				}

				allPromotionsToUpdate = append(allPromotionsToUpdate, promotion)
				allApiPromotionIds = append(allApiPromotionIds, apiPromotion.PromotionId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.Ads)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建广告信息
	if len(allPromotionsToUpdate) > 0 {
		utils.LogInfo("开始批量更新项目广告信息",
			"account_id", accountID,
			"advertiser_id", advertiserID,
			"project_id", projectID,
			"total_promotions", len(allPromotionsToUpdate))

		for _, promotion := range allPromotionsToUpdate {
			if promotion.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.PromotionModel.Update(nil, promotion); err != nil {
					utils.LogError("更新项目广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"project_id", projectID,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				promotion.CreateTime = time.Now()
				if err := s.svcCtx.PromotionModel.Create(promotion); err != nil {
					utils.LogError("创建项目广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"project_id", projectID,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告创建失败而中断
				}
			}
		}

		// 获取数据库中该项目下的所有广告
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"project_id": projectID})
		if err != nil {
			utils.LogError("获取现有项目广告失败",
				"account_id", accountID,
				"project_id", projectID,
				"error", err.Error())
		} else {
			// 找出需要删除的广告（在数据库中存在但API中不存在）
			promotionsToDelete := make([]string, 0)
			for _, existingPromotion := range existingPromotions {
				found := false
				for _, apiPromotionId := range allApiPromotionIds {
					if existingPromotion.PromotionId == apiPromotionId {
						found = true
						break
					}
				}
				if !found {
					promotionsToDelete = append(promotionsToDelete, existingPromotion.PromotionId)
				}
			}

			// 标记删除不存在的广告
			if len(promotionsToDelete) > 0 {
				if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionsToDelete); err != nil {
					utils.LogError("批量标记删除项目广告失败",
						"account_id", accountID,
						"advertiser_id", advertiserID,
						"project_id", projectID,
						"delete_count", len(promotionsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期项目广告",
						"account_id", accountID,
						"advertiser_id", advertiserID,
						"project_id", projectID,
						"delete_count", len(promotionsToDelete))
				}
			}
		}

		utils.LogInfo("项目广告信息更新完成",
			"account_id", accountID,
			"advertiser_id", advertiserID,
			"project_id", projectID,
			"updated_count", len(allPromotionsToUpdate),
			"total_pages", totalPages)
	} else {
		// 如果API没有返回广告数据，将该项目下的所有广告标记为删除
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"project_id": projectID})
		if err != nil {
			utils.LogError("获取现有项目广告失败",
				"account_id", accountID,
				"project_id", projectID,
				"error", err.Error())
		} else if len(existingPromotions) > 0 {
			promotionIds := make([]string, 0, len(existingPromotions))
			for _, promotion := range existingPromotions {
				promotionIds = append(promotionIds, promotion.PromotionId)
			}

			if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionIds); err != nil {
				utils.LogError("批量标记删除所有项目广告失败",
					"account_id", accountID,
					"project_id", projectID,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有项目广告（API无数据）",
					"account_id", accountID,
					"project_id", projectID,
					"delete_count", len(promotionIds))
			}
		}
	}

	return nil
}

// SyncAllProjectInfo 同步所有项目信息（使用GetProjectList API）
func (s *DataSyncService) SyncAllProjectInfo(accountID int64) error {
	// 获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountID)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(accountID)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用GetProjectList API获取所有项目
	page := int64(1)
	pageSize := int64(100)
	searchType := 0
	keyword := ""
	projectId := ""

	utils.LogInfo("开始同步所有项目信息",
		"account_id", accountID,
		"page", page,
		"pageSize", pageSize)

	// 收集所有API返回的项目ID
	allApiProjectIds := make([]string, 0)
	allProjectsToUpdate := make([]*model.Project, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetProjectList(page, pageSize, searchType, keyword, projectId, account.Cookie)
		if err != nil {
			return fmt.Errorf("调用GetProjectList API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.Total
			utils.LogInfo("获取到分页信息",
				"account_id", accountID,
				"total_pages", totalPages,
				"has_more", resp.Data.Pagination.HasMore)
		}

		// 如果有项目数据，处理当前页数据
		if len(resp.Data.DataList) > 0 {
			utils.LogInfo("获取到项目数据",
				"account_id", accountID,
				"current_page", currentPage,
				"project_count", len(resp.Data.DataList))

			// 转换API数据为数据库模型
			for _, apiProject := range resp.Data.DataList {
				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiProject.ExternalAction != 0 {
					externalAction = apiProject.ExternalAction
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiProject.AdPricing != 0 {
					adPricing = apiProject.AdPricing
				}

				project := &model.Project{
					AccountId:            accountID,
					ProjectId:            apiProject.ProjectId,
					ProjectName:          apiProject.ProjectName,
					ProjectStatus:        apiProject.ProjectStatus,
					ProjectStatusName:    apiProject.ProjectStatusName,
					AdvertiserId:         apiProject.AdvertiserId,
					AdvertiserName:       apiProject.AdvertiserName,
					CampaignBudget:       apiProject.CampaignBudget,
					ProjectBid:           apiProject.ProjectBid,
					ProjectDeepCpaBid:    apiProject.ProjectDeepCpaBid,
					ProjectRoiGoal:       apiProject.ProjectRoiGoal,
					ProjectFirstRoiGoal:  apiProject.ProjectFirstRoiGoal,
					LandingType:          apiProject.LandingType,
					LandingTypeName:      apiProject.LandingTypeName,
					DeliveryMode:         apiProject.DeliveryMode,
					DeliveryModeInternal: apiProject.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiProject.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiProject.AdPricingName,
					StartTime:            apiProject.StartTime,
					EndTime:              apiProject.EndTime,
					ModifyTime:           apiProject.ModifyTime,
					CampaignId:           apiProject.CampaignId,
					UpdateTime:           time.Now(),
					// 新增字段
					ProjectStatusFirst:      apiProject.ProjectStatusFirst,
					ProjectStatusSecond:     apiProject.ProjectStatusSecond,
					ProjectStatusFirstName:  apiProject.ProjectStatusFirstName,
					ProjectStatusSecondName: apiProject.ProjectStatusSecondName,
				}

				// 获取已存在的项目信息，保留ID和创建时间
				if existingProject, err := s.svcCtx.ProjectModel.GetByProjectId(apiProject.ProjectId); err == nil && existingProject != nil {
					project.Id = existingProject.Id
					project.CreateTime = existingProject.CreateTime
				}

				allProjectsToUpdate = append(allProjectsToUpdate, project)
				allApiProjectIds = append(allApiProjectIds, apiProject.ProjectId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.DataList)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建项目信息
	if len(allProjectsToUpdate) > 0 {
		utils.LogInfo("开始批量更新项目信息",
			"account_id", accountID,
			"total_projects", len(allProjectsToUpdate))

		for _, project := range allProjectsToUpdate {
			if project.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.ProjectModel.Update(nil, project); err != nil {
					utils.LogError("更新项目信息失败",
						"project_id", project.ProjectId,
						"project_name", project.ProjectName,
						"error", err.Error())
					// 继续处理其他项目，不因单个项目更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				project.CreateTime = time.Now()
				if err := s.svcCtx.ProjectModel.Create(project); err != nil {
					utils.LogError("创建项目信息失败",
						"project_id", project.ProjectId,
						"project_name", project.ProjectName,
						"error", err.Error())
					// 继续处理其他项目，不因单个项目创建失败而中断
				}
			}
		}

		// 获取数据库中该账户下的所有项目
		existingProjects, _, err := s.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{"account_id": accountID})
		if err != nil {
			utils.LogError("获取现有项目失败",
				"account_id", accountID,
				"error", err.Error())
		} else {
			// 找出需要删除的项目（在数据库中存在但API中不存在）
			projectsToDelete := make([]string, 0)
			for _, existingProject := range existingProjects {
				found := false
				for _, apiProjectId := range allApiProjectIds {
					if existingProject.ProjectId == apiProjectId {
						found = true
						break
					}
				}
				if !found {
					projectsToDelete = append(projectsToDelete, existingProject.ProjectId)
				}
			}

			// 标记删除不存在的项目
			if len(projectsToDelete) > 0 {
				if err := s.svcCtx.ProjectModel.BatchMarkDelete(projectsToDelete); err != nil {
					utils.LogError("批量标记删除项目失败",
						"account_id", accountID,
						"delete_count", len(projectsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期项目",
						"account_id", accountID,
						"delete_count", len(projectsToDelete))
				}
			}
		}

		utils.LogInfo("所有项目信息更新完成",
			"account_id", accountID,
			"updated_count", len(allProjectsToUpdate),
			"total_pages", currentPage)
	} else {
		// 如果API没有返回项目数据，将该账户下的所有项目标记为删除
		existingProjects, _, err := s.svcCtx.ProjectModel.List(0, 0, map[string]interface{}{"account_id": accountID})
		if err != nil {
			utils.LogError("获取现有项目失败",
				"account_id", accountID,
				"error", err.Error())
		} else if len(existingProjects) > 0 {
			projectIds := make([]string, 0, len(existingProjects))
			for _, project := range existingProjects {
				projectIds = append(projectIds, project.ProjectId)
			}

			if err := s.svcCtx.ProjectModel.BatchMarkDelete(projectIds); err != nil {
				utils.LogError("批量标记删除所有项目失败",
					"account_id", accountID,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有项目（API无数据）",
					"account_id", accountID,
					"delete_count", len(projectIds))
			}
		}
	}

	return nil
}

// SyncAllPromotionInfo 同步所有广告信息（使用GetPromotionList API）
func (s *DataSyncService) SyncAllPromotionInfo(accountID int64) error {
	// 获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountID)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(accountID)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用GetPromotionList API获取所有广告
	page := int64(1)
	pageSize := int64(100)
	searchType := 0
	keyword := ""
	projectId := "" // 空字符串表示不按项目过滤，获取所有广告
	promotionId := ""

	utils.LogInfo("开始同步所有广告信息",
		"account_id", accountID,
		"page", page,
		"pageSize", pageSize)

	// 收集所有API返回的广告ID
	allApiPromotionIds := make([]string, 0)
	allPromotionsToUpdate := make([]*model.Promotion, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetPromotionList(page, pageSize, searchType, keyword, projectId, promotionId, account.Cookie)
		if err != nil {
			return fmt.Errorf("调用GetPromotionList API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.Total
			utils.LogInfo("获取到分页信息",
				"account_id", accountID,
				"total_pages", totalPages,
				"has_more", resp.Data.Pagination.HasMore)
		}

		// 如果有广告数据，处理当前页数据
		if len(resp.Data.DataList) > 0 {
			utils.LogInfo("获取到广告数据",
				"account_id", accountID,
				"current_page", currentPage,
				"promotion_count", len(resp.Data.DataList))

			// 转换API数据为数据库模型
			for _, apiPromotion := range resp.Data.DataList {
				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiPromotion.ExternalAction != 0 {
					externalAction = apiPromotion.ExternalAction
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiPromotion.AdPricing != 0 {
					adPricing = apiPromotion.AdPricing
				}

				promotion := &model.Promotion{
					AccountId:            accountID,
					PromotionId:          apiPromotion.PromotionId,
					PromotionName:        apiPromotion.PromotionName,
					AdvertiserId:         apiPromotion.AdvertiserId,
					AdvertiserName:       apiPromotion.AdvertiserName,
					ProjectId:            &apiPromotion.ProjectId,
					ProjectName:          apiPromotion.ProjectName,
					PromotionStatus:      apiPromotion.PromotionStatus,
					PromotionStatusName:  apiPromotion.PromotionStatusName,
					AdId:                 apiPromotion.AdId,
					AdBudget:             apiPromotion.AdBudget,
					AdBid:                apiPromotion.AdBid,
					ProjectBid:           apiPromotion.ProjectBid,
					LandingType:          apiPromotion.LandingType,
					LandingTypeName:      apiPromotion.LandingTypeName,
					DeliveryMode:         apiPromotion.DeliveryMode,
					DeliveryModeInternal: apiPromotion.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiPromotion.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiPromotion.AdPricingName,
					StartTime:            apiPromotion.StartTime,
					EndTime:              apiPromotion.EndTime,
					ModifyTime:           apiPromotion.ModifyTime,
					CampaignId:           apiPromotion.CampaignId,
					UpdateTime:           time.Now(),
					// 新增字段
					PromotionStatusFirst:      apiPromotion.PromotionStatusFirst,
					PromotionStatusSecond:     apiPromotion.PromotionStatusSecond,
					PromotionStatusFirstName:  apiPromotion.PromotionStatusFirstName,
					PromotionStatusSecondName: apiPromotion.PromotionStatusSecondName,
				}

				// 获取已存在的广告信息，保留ID和创建时间
				if existingPromotion, err := s.svcCtx.PromotionModel.GetByPromotionId(apiPromotion.PromotionId); err == nil && existingPromotion != nil {
					promotion.Id = existingPromotion.Id
					promotion.CreateTime = existingPromotion.CreateTime
				}

				allPromotionsToUpdate = append(allPromotionsToUpdate, promotion)
				allApiPromotionIds = append(allApiPromotionIds, apiPromotion.PromotionId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.DataList)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建广告信息
	if len(allPromotionsToUpdate) > 0 {
		utils.LogInfo("开始批量更新广告信息",
			"account_id", accountID,
			"total_promotions", len(allPromotionsToUpdate))

		for _, promotion := range allPromotionsToUpdate {
			if promotion.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.PromotionModel.Update(nil, promotion); err != nil {
					utils.LogError("更新广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				promotion.CreateTime = time.Now()
				if err := s.svcCtx.PromotionModel.Create(promotion); err != nil {
					utils.LogError("创建广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告创建失败而中断
				}
			}
		}

		// 获取数据库中该账户下的所有广告
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"account_id": accountID})
		if err != nil {
			utils.LogError("获取现有广告失败",
				"account_id", accountID,
				"error", err.Error())
		} else {
			// 找出需要删除的广告（在数据库中存在但API中不存在）
			promotionsToDelete := make([]string, 0)
			for _, existingPromotion := range existingPromotions {
				found := false
				for _, apiPromotionId := range allApiPromotionIds {
					if existingPromotion.PromotionId == apiPromotionId {
						found = true
						break
					}
				}
				if !found {
					promotionsToDelete = append(promotionsToDelete, existingPromotion.PromotionId)
				}
			}

			// 标记删除不存在的广告
			if len(promotionsToDelete) > 0 {
				if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionsToDelete); err != nil {
					utils.LogError("批量标记删除广告失败",
						"account_id", accountID,
						"delete_count", len(promotionsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期广告",
						"account_id", accountID,
						"delete_count", len(promotionsToDelete))
				}
			}
		}

		utils.LogInfo("所有广告信息更新完成",
			"account_id", accountID,
			"updated_count", len(allPromotionsToUpdate),
			"total_pages", currentPage)
	} else {
		// 如果API没有返回广告数据，将该账户下的所有广告标记为删除
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"account_id": accountID})
		if err != nil {
			utils.LogError("获取现有广告失败",
				"account_id", accountID,
				"error", err.Error())
		} else if len(existingPromotions) > 0 {
			promotionIds := make([]string, 0, len(existingPromotions))
			for _, promotion := range existingPromotions {
				promotionIds = append(promotionIds, promotion.PromotionId)
			}

			if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionIds); err != nil {
				utils.LogError("批量标记删除所有广告失败",
					"account_id", accountID,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有广告（API无数据）",
					"account_id", accountID,
					"delete_count", len(promotionIds))
			}
		}
	}

	return nil
}

// SyncPromotionInfoByProject 按项目同步广告信息（使用GetPromotionList API）
func (s *DataSyncService) SyncPromotionInfoByProject(accountID int64, projectID string) error {
	// 获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountID)
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %w", err)
	}

	if account == nil {
		return fmt.Errorf("账户不存在")
	}

	// 创建Bot实例
	bot, _, err := s.svcCtx.GetBot(accountID)
	if err != nil {
		return fmt.Errorf("创建Bot实例失败: %w", err)
	}

	// 调用GetPromotionList API获取指定项目的广告
	page := int64(1)
	pageSize := int64(100)
	searchType := 0
	keyword := ""
	promotionId := ""

	utils.LogInfo("开始同步项目广告信息",
		"account_id", accountID,
		"project_id", projectID,
		"page", page,
		"pageSize", pageSize)

	// 收集所有API返回的广告ID
	allApiPromotionIds := make([]string, 0)
	allPromotionsToUpdate := make([]*model.Promotion, 0)
	totalPages := 0
	currentPage := 1

	// 分页循环获取所有数据
	for {
		resp, err := bot.GetPromotionList(page, pageSize, searchType, keyword, projectID, promotionId, account.Cookie)
		if err != nil {
			return fmt.Errorf("调用GetPromotionList API失败: %w", err)
		}

		if resp.Code != 0 {
			return fmt.Errorf("API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 记录总页数（只在第一页时记录）
		if currentPage == 1 {
			totalPages = resp.Data.Pagination.Total
			utils.LogInfo("获取到分页信息",
				"account_id", accountID,
				"project_id", projectID,
				"total_pages", totalPages,
				"has_more", resp.Data.Pagination.HasMore)
		}

		// 如果有广告数据，处理当前页数据
		if len(resp.Data.DataList) > 0 {
			utils.LogInfo("获取到项目广告数据",
				"account_id", accountID,
				"project_id", projectID,
				"current_page", currentPage,
				"promotion_count", len(resp.Data.DataList))

			// 转换API数据为数据库模型
			for _, apiPromotion := range resp.Data.DataList {
				// 转换字符串类型的外部行为为int
				externalAction := 0
				if apiPromotion.ExternalAction != 0 {
					externalAction = apiPromotion.ExternalAction
				}

				// 转换字符串类型的广告定价为int
				adPricing := 0
				if apiPromotion.AdPricing != 0 {
					adPricing = apiPromotion.AdPricing
				}

				promotion := &model.Promotion{
					AccountId:            accountID,
					PromotionId:          apiPromotion.PromotionId,
					PromotionName:        apiPromotion.PromotionName,
					AdvertiserId:         apiPromotion.AdvertiserId,
					AdvertiserName:       apiPromotion.AdvertiserName,
					ProjectId:            &apiPromotion.ProjectId,
					ProjectName:          apiPromotion.ProjectName,
					PromotionStatus:      apiPromotion.PromotionStatus,
					PromotionStatusName:  apiPromotion.PromotionStatusName,
					AdId:                 apiPromotion.AdId,
					AdBudget:             apiPromotion.AdBudget,
					AdBid:                apiPromotion.AdBid,
					ProjectBid:           apiPromotion.ProjectBid,
					LandingType:          apiPromotion.LandingType,
					LandingTypeName:      apiPromotion.LandingTypeName,
					DeliveryMode:         apiPromotion.DeliveryMode,
					DeliveryModeInternal: apiPromotion.DeliveryModeInternal,
					ExternalAction:       externalAction,
					ExternalActionName:   apiPromotion.ExternalActionName,
					AdPricing:            adPricing,
					AdPricingName:        apiPromotion.AdPricingName,
					StartTime:            apiPromotion.StartTime,
					EndTime:              apiPromotion.EndTime,
					ModifyTime:           apiPromotion.ModifyTime,
					CampaignId:           apiPromotion.CampaignId,
					UpdateTime:           time.Now(),
					// 新增字段
					PromotionStatusFirst:      apiPromotion.PromotionStatusFirst,
					PromotionStatusSecond:     apiPromotion.PromotionStatusSecond,
					PromotionStatusFirstName:  apiPromotion.PromotionStatusFirstName,
					PromotionStatusSecondName: apiPromotion.PromotionStatusSecondName,
				}

				// 获取已存在的广告信息，保留ID和创建时间
				if existingPromotion, err := s.svcCtx.PromotionModel.GetByPromotionId(apiPromotion.PromotionId); err == nil && existingPromotion != nil {
					promotion.Id = existingPromotion.Id
					promotion.CreateTime = existingPromotion.CreateTime
				}

				allPromotionsToUpdate = append(allPromotionsToUpdate, promotion)
				allApiPromotionIds = append(allApiPromotionIds, apiPromotion.PromotionId)
			}
		}

		// 检查是否还有下一页
		if shouldContinuePagination(resp, currentPage, totalPages, len(resp.Data.DataList)) {
			page++
			currentPage++
		} else {
			break
		}
	}

	// 批量更新或创建广告信息
	if len(allPromotionsToUpdate) > 0 {
		utils.LogInfo("开始批量更新项目广告信息",
			"account_id", accountID,
			"project_id", projectID,
			"total_promotions", len(allPromotionsToUpdate))

		for _, promotion := range allPromotionsToUpdate {
			if promotion.Id > 0 {
				// 记录存在，执行更新
				if err := s.svcCtx.PromotionModel.Update(nil, promotion); err != nil {
					utils.LogError("更新项目广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"project_id", projectID,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告更新失败而中断
				}
			} else {
				// 记录不存在，执行创建
				promotion.CreateTime = time.Now()
				if err := s.svcCtx.PromotionModel.Create(promotion); err != nil {
					utils.LogError("创建项目广告信息失败",
						"promotion_id", promotion.PromotionId,
						"promotion_name", promotion.PromotionName,
						"project_id", projectID,
						"error", err.Error())
					// 继续处理其他广告，不因单个广告创建失败而中断
				}
			}
		}

		// 获取数据库中该项目下的所有广告
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"project_id": projectID})
		if err != nil {
			utils.LogError("获取现有项目广告失败",
				"account_id", accountID,
				"project_id", projectID,
				"error", err.Error())
		} else {
			// 找出需要删除的广告（在数据库中存在但API中不存在）
			promotionsToDelete := make([]string, 0)
			for _, existingPromotion := range existingPromotions {
				found := false
				for _, apiPromotionId := range allApiPromotionIds {
					if existingPromotion.PromotionId == apiPromotionId {
						found = true
						break
					}
				}
				if !found {
					promotionsToDelete = append(promotionsToDelete, existingPromotion.PromotionId)
				}
			}

			// 标记删除不存在的广告
			if len(promotionsToDelete) > 0 {
				if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionsToDelete); err != nil {
					utils.LogError("批量标记删除项目广告失败",
						"account_id", accountID,
						"project_id", projectID,
						"delete_count", len(promotionsToDelete),
						"error", err.Error())
				} else {
					utils.LogInfo("标记删除过期项目广告",
						"account_id", accountID,
						"project_id", projectID,
						"delete_count", len(promotionsToDelete))
				}
			}
		}

		utils.LogInfo("项目广告信息更新完成",
			"account_id", accountID,
			"project_id", projectID,
			"updated_count", len(allPromotionsToUpdate),
			"total_pages", currentPage)
	} else {
		// 如果API没有返回广告数据，将该项目下的所有广告标记为删除
		existingPromotions, _, err := s.svcCtx.PromotionModel.List(0, 0, map[string]interface{}{"project_id": projectID})
		if err != nil {
			utils.LogError("获取现有项目广告失败",
				"account_id", accountID,
				"project_id", projectID,
				"error", err.Error())
		} else if len(existingPromotions) > 0 {
			promotionIds := make([]string, 0, len(existingPromotions))
			for _, promotion := range existingPromotions {
				promotionIds = append(promotionIds, promotion.PromotionId)
			}

			if err := s.svcCtx.PromotionModel.BatchMarkDelete(promotionIds); err != nil {
				utils.LogError("批量标记删除所有项目广告失败",
					"account_id", accountID,
					"project_id", projectID,
					"error", err.Error())
			} else {
				utils.LogInfo("标记删除所有项目广告（API无数据）",
					"account_id", accountID,
					"project_id", projectID,
					"delete_count", len(promotionIds))
			}
		}
	}

	return nil
}

// 以下是提供给页面调用的接口方法

// SyncAdvertiserProjectInfoAPI 同步广告主项目信息API
func (s *DataSyncService) SyncAdvertiserProjectInfoAPI(advertiserID int64) *result.Result[any] {
	if advertiserID <= 0 {
		return result.ErrorSimpleResult[any]("广告主ID无效")
	}

	err := s.SyncAdvertiserProjectInfo(advertiserID)
	if err != nil {
		utils.LogError("同步项目信息失败", "advertiser_id", advertiserID, "error", err.Error())
		return result.ErrorSimpleResult[any](err.Error())
	}

	return result.SimpleResult("项目信息同步成功")
}

// SyncAdvertiserPromotionInfoAPI 同步广告主广告信息API
func (s *DataSyncService) SyncAdvertiserPromotionInfoAPI(advertiserID int64) *result.Result[any] {
	if advertiserID <= 0 {
		return result.ErrorSimpleResult[any]("广告主ID无效")
	}

	err := s.SyncAdvertiserPromotionInfo(advertiserID)
	if err != nil {
		utils.LogError("同步广告信息失败", "advertiser_id", advertiserID, "error", err.Error())
		return result.ErrorSimpleResult[any](err.Error())
	}

	return result.SimpleResult("广告信息同步成功")
}

// SyncAdvertiserAllInfoAPI 同步广告主所有信息API
func (s *DataSyncService) SyncAdvertiserAllInfoAPI(advertiserID int64) *result.Result[any] {
	if advertiserID <= 0 {
		return result.ErrorSimpleResult[any]("广告主ID无效")
	}

	// 同步执行同步（前端等待完成）
	// 同步项目信息
	if err := s.SyncAdvertiserProjectInfo(advertiserID); err != nil {
		utils.LogError("同步项目信息失败", "advertiser_id", advertiserID, "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步项目信息失败: %s", err.Error()))
	}

	// 同步广告信息
	if err := s.SyncAdvertiserPromotionInfo(advertiserID); err != nil {
		utils.LogError("同步广告信息失败", "advertiser_id", advertiserID, "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步广告信息失败: %s", err.Error()))
	}

	utils.LogInfo("广告主数据同步完成", "advertiser_id", advertiserID)
	return result.SimpleResult("广告主数据同步成功")
}

// SyncAccountDataAPI 同步账户数据API
func (s *DataSyncService) SyncAccountDataAPI(accountID int64) *result.Result[any] {
	if accountID <= 0 {
		return result.ErrorSimpleResult[any]("账户ID无效")
	}

	// 同步执行同步（前端等待完成）
	if err := s.SyncAccountData(accountID); err != nil {
		utils.LogError("同步账户数据失败", "account_id", accountID, "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步账户数据失败: %s", err.Error()))
	}

	return result.SimpleResult("账户数据同步成功")
}

// SyncAllDataAPI 同步所有数据API
func (s *DataSyncService) SyncAllDataAPI() *result.Result[any] {
	// 同步执行同步（前端等待完成）
	if err := s.SyncAllData(); err != nil {
		utils.LogError("同步所有数据失败", "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步所有数据失败: %s", err.Error()))
	}

	return result.SimpleResult("全量数据同步成功")
}

// GetSyncStatus 获取同步状态
func (s *DataSyncService) GetSyncStatus() *result.Result[map[string]interface{}] {
	// 这里可以实现同步状态查询逻辑
	// 比如查询正在同步的任务、最后同步时间等
	status := map[string]interface{}{
		"is_syncing":     false, // 可以通过锁状态判断
		"last_sync_time": time.Now().Format("2006-01-02 15:04:05"),
	}

	return result.SuccessResult(status)
}

// BatchSyncAdvertiserData 批量同步广告主数据
func (s *DataSyncService) BatchSyncAdvertiserData(advertiserIDs []int64) *result.Result[any] {
	if len(advertiserIDs) == 0 {
		return result.ErrorSimpleResult[any]("广告主ID列表为空")
	}

	// 同步执行批量同步（前端等待完成）
	var errors []string
	successCount := 0

	for _, advertiserID := range advertiserIDs {
		// 同步项目信息
		if err := s.SyncAdvertiserProjectInfo(advertiserID); err != nil {
			utils.LogError("批量同步项目信息失败", "advertiser_id", advertiserID, "error", err.Error())
			errors = append(errors, fmt.Sprintf("广告主%d项目同步失败: %s", advertiserID, err.Error()))
			continue
		}

		// 同步广告信息
		if err := s.SyncAdvertiserPromotionInfo(advertiserID); err != nil {
			utils.LogError("批量同步广告信息失败", "advertiser_id", advertiserID, "error", err.Error())
			errors = append(errors, fmt.Sprintf("广告主%d广告同步失败: %s", advertiserID, err.Error()))
			continue
		}

		utils.LogInfo("批量同步完成", "advertiser_id", advertiserID)
		successCount++
	}

	if len(errors) > 0 {
		return result.ErrorSimpleResult[any](fmt.Sprintf("批量同步部分失败，成功%d个，失败%d个：%v", successCount, len(errors), errors))
	}

	return result.SimpleResult(fmt.Sprintf("批量数据同步成功，共同步%d个广告主", successCount))
}

// SyncProjectPromotionInfoAPI 同步项目广告信息API
func (s *DataSyncService) SyncProjectPromotionInfoAPI(accountID int64, advertiserID int64, projectID string) *result.Result[any] {
	if accountID <= 0 {
		return result.ErrorSimpleResult[any]("账户ID无效")
	}
	if advertiserID <= 0 {
		return result.ErrorSimpleResult[any]("广告主ID无效")
	}
	if projectID == "" {
		return result.ErrorSimpleResult[any]("项目ID无效")
	}

	err := s.SyncProjectPromotionInfo(accountID, advertiserID, projectID)
	if err != nil {
		utils.LogError("同步项目广告信息失败",
			"account_id", accountID,
			"advertiser_id", advertiserID,
			"project_id", projectID,
			"error", err.Error())
		return result.ErrorSimpleResult[any](err.Error())
	}

	return result.SimpleResult("项目广告信息同步成功")
}

// SyncAllProjectInfoAPI 同步所有项目信息API
func (s *DataSyncService) SyncAllProjectInfoAPI(accountID int64) *result.Result[any] {
	if accountID <= 0 {
		return result.ErrorSimpleResult[any]("账户ID无效")
	}

	err := s.SyncAllProjectInfo(accountID)
	if err != nil {
		utils.LogError("同步所有项目信息失败", "account_id", accountID, "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步所有项目信息失败: %s", err.Error()))
	}

	return result.SimpleResult("所有项目信息同步成功")
}

// SyncAllPromotionInfoAPI 同步所有广告信息API
func (s *DataSyncService) SyncAllPromotionInfoAPI(accountID int64) *result.Result[any] {
	if accountID <= 0 {
		return result.ErrorSimpleResult[any]("账户ID无效")
	}

	err := s.SyncAllPromotionInfo(accountID)
	if err != nil {
		utils.LogError("同步所有广告信息失败", "account_id", accountID, "error", err.Error())
		return result.ErrorSimpleResult[any](fmt.Sprintf("同步所有广告信息失败: %s", err.Error()))
	}

	return result.SimpleResult("所有广告信息同步成功")
}

// SyncPromotionInfoByProjectAPI 按项目同步广告信息API
func (s *DataSyncService) SyncPromotionInfoByProjectAPI(accountID int64, projectID string) *result.Result[any] {
	if accountID <= 0 {
		return result.ErrorSimpleResult[any]("账户ID无效")
	}
	if projectID == "" {
		return result.ErrorSimpleResult[any]("项目ID无效")
	}

	err := s.SyncPromotionInfoByProject(accountID, projectID)
	if err != nil {
		utils.LogError("同步项目广告信息失败",
			"account_id", accountID,
			"project_id", projectID,
			"error", err.Error())
		return result.ErrorSimpleResult[any](err.Error())
	}

	return result.SimpleResult("项目广告信息同步成功")
}

// SyncAdvertiserInfoWithContext 支持取消的同步方法
func (s *DataSyncService) SyncAdvertiserInfoWithContext(ctx context.Context, advertiserID int64) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	return s.SyncAdvertiserProjectInfo(advertiserID)
}

// 检查是否还有下一页 - 支持多种分页格式
func shouldContinuePagination(resp interface{}, currentPage, totalPages int, dataLength int) bool {
	// 如果当前页没有数据，停止分页
	if dataLength == 0 {
		return false
	}

	// 尝试从响应中获取分页信息
	switch r := resp.(type) {
	case *bot.GetProjectResp:
		// 格式1：使用 HasMore 字段
		if !r.Data.Pagination.HasMore {
			return false
		}
		// 格式2：使用 Total 字段计算总页数
		if r.Data.Pagination.Total > 0 && r.Data.Pagination.Limit > 0 {
			calculatedTotalPages := (r.Data.Pagination.Total + r.Data.Pagination.Limit - 1) / r.Data.Pagination.Limit
			if currentPage >= calculatedTotalPages {
				return false
			}
		}
	case *bot.GetPromotionResp:
		// 格式1：使用 HasMore 字段
		if !r.Data.Pagination.HasMore {
			return false
		}
		// 格式2：使用 Total 字段计算总页数
		if r.Data.Pagination.Total > 0 && r.Data.Pagination.Limit > 0 {
			calculatedTotalPages := (r.Data.Pagination.Total + r.Data.Pagination.Limit - 1) / r.Data.Pagination.Limit
			if currentPage >= calculatedTotalPages {
				return false
			}
		}
	case *bot.SubAccountProjectResp:
		// 格式3：使用 TotalPage 字段
		if currentPage >= r.Data.Pagination.TotalPage {
			return false
		}
	case *bot.SubAccountPromotionResp:
		// 格式3：使用 TotalPage 字段
		if currentPage >= r.Data.Pagination.TotalPage {
			return false
		}
	default:
		// 默认使用传入的 totalPages 参数
		if totalPages > 0 && currentPage >= totalPages {
			return false
		}
	}

	return true
}
