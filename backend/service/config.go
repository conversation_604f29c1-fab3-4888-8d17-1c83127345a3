package service

import (
	"context"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
)

// ConfigService 配置服务
type ConfigService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewConfigService 创建配置服务
func NewConfigService(svcCtx *svc.ServiceContext) *ConfigService {
	return &ConfigService{
		svcCtx: svcCtx,
	}
}

func (s *ConfigService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *ConfigService) SetApp(app *application.App) {
	s.app = app
}

// GetConfig 获取配置
func (s *ConfigService) GetConfig() *result.Result[*config.Config] {
	return result.SuccessResult(config.GetConfig())
}

// GetConfigByKey 根据键获取配置
func (s *ConfigService) GetConfigByKey(key string) *result.Result[*model.Config] {
	config, err := s.svcCtx.ConfigModel.GetByKey(key)
	if err != nil {
		return result.ToError[*model.Config](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(config)
}

// GetConfigValueByKey 根据键获取配置值
func (s *ConfigService) GetConfigValueByKey(key string) *result.Result[string] {
	value, err := s.svcCtx.ConfigModel.GetValueByKey(key)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(value)
}

// CreateConfig 创建配置
func (s *ConfigService) CreateConfig(config *model.Config) *result.Result[*model.Config] {
	err := s.svcCtx.ConfigModel.Create(config)
	if err != nil {
		return result.ToError[*model.Config](result.ErrorAdd.AddError(err))
	}
	return result.SuccessResult(config)
}

// UpdateConfig 更新配置
func (s *ConfigService) UpdateConfig(config *model.Config) *result.Result[*model.Config] {
	err := s.svcCtx.ConfigModel.Update(nil, config)
	if err != nil {
		return result.ToError[*model.Config](result.ErrorUpdate.AddError(err))
	}
	return result.SuccessResult(config)
}

// UpdateConfigByKey 根据键更新配置值
func (s *ConfigService) UpdateConfigByKey(key, value string) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.UpdateByKey(key, value)
	if err != nil {
		return result.ToError[bool](result.ErrorUpdate.AddError(err))
	}
	return result.SuccessResult(true)
}

// UpsertConfigByKey 根据键插入或更新配置
func (s *ConfigService) UpsertConfigByKey(key, value, description string) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.UpsertByKey(key, value, description)
	if err != nil {
		return result.ToError[bool](result.ErrorUpdate.AddError(err))
	}
	return result.SuccessResult(true)
}

// DeleteConfig 删除配置
func (s *ConfigService) DeleteConfig(config *model.Config) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.Delete(config)
	if err != nil {
		return result.ToError[bool](result.ErrorDelete.AddError(err))
	}
	return result.SuccessResult(true)
}

// DeleteConfigByKey 根据键删除配置
func (s *ConfigService) DeleteConfigByKey(key string) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.DeleteByKey(key)
	if err != nil {
		return result.ToError[bool](result.ErrorDelete.AddError(err))
	}
	return result.SuccessResult(true)
}

// ListConfigs 获取配置列表
func (s *ConfigService) ListConfigs(page, pageSize int, conditions map[string]interface{}) *result.Result[map[string]interface{}] {
	configs, total, err := s.svcCtx.ConfigModel.List(page, pageSize, conditions)
	if err != nil {
		return result.ToError[map[string]interface{}](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult(map[string]interface{}{
		"list":  configs,
		"total": total,
		"page":  page,
		"size":  pageSize,
	})
}

// GetConfigsByCategory 根据分类获取配置
func (s *ConfigService) GetConfigsByCategory(category string) *result.Result[[]*model.Config] {
	configs, err := s.svcCtx.ConfigModel.GetByCategory(category)
	if err != nil {
		return result.ToError[[]*model.Config](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(configs)
}

// GetAllConfigs 获取所有配置
func (s *ConfigService) GetAllConfigs() *result.Result[[]*model.Config] {
	configs, err := s.svcCtx.ConfigModel.GetAll()
	if err != nil {
		return result.ToError[[]*model.Config](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(configs)
}

// GetStrategyNotificationEmail 获取策略通知邮箱
func (s *ConfigService) GetStrategyNotificationEmail() *result.Result[string] {
	value, err := s.svcCtx.ConfigModel.GetValueByKey(model.ConfigKeyStrategyNotificationEmail)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(value)
}

// SetStrategyNotificationEmail 设置策略通知邮箱
func (s *ConfigService) SetStrategyNotificationEmail(email string) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.UpsertByKey(
		model.ConfigKeyStrategyNotificationEmail,
		email,
		"策略通知邮箱",
	)
	if err != nil {
		return result.ToError[bool](result.ErrorAdd.AddError(err))
	}
	return result.SuccessResult(true)
}

// GetSystemEmail 获取系统邮箱
func (s *ConfigService) GetSystemEmail() *result.Result[string] {
	value, err := s.svcCtx.ConfigModel.GetValueByKey(model.ConfigKeySystemEmail)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(value)
}

// SetSystemEmail 设置系统邮箱
func (s *ConfigService) SetSystemEmail(email string) *result.Result[bool] {
	err := s.svcCtx.ConfigModel.UpsertByKey(
		model.ConfigKeySystemEmail,
		email,
		"系统邮箱",
	)
	if err != nil {
		return result.ToError[bool](result.ErrorAdd.AddError(err))
	}
	return result.SuccessResult(true)
}
