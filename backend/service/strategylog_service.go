package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// StrategyLogService 策略日志服务
type StrategyLogService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewStrategyLogService 创建策略日志服务
func NewStrategyLogService(svcCtx *svc.ServiceContext) *StrategyLogService {
	return &StrategyLogService{
		svcCtx: svcCtx,
	}
}

func (s *StrategyLogService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *StrategyLogService) SetApp(app *application.App) {
	s.app = app
}

// GetStrategyLogList 获取策略日志列表
func (s *StrategyLogService) GetStrategyLogList(req *types.StrategyLogListReq) *result.Result[types.StrategyLogListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.StrategyName != "" {
		conditions["strategy_name"] = req.StrategyName
	}
	if req.TargetType != "" {
		conditions["target_type"] = req.TargetType
	}
	if req.TargetID != "" {
		conditions["target_id"] = req.TargetID
	}
	if req.ConditionMet != nil {
		conditions["condition_met"] = *req.ConditionMet
	}
	if req.ActionCompleted != nil {
		conditions["action_completed"] = *req.ActionCompleted
	}
	if !req.StartTime.IsZero() {
		conditions["start_time"] = req.StartTime
	}
	if !req.EndTime.IsZero() {
		conditions["end_time"] = req.EndTime
	}

	// 调用model层获取数据
	logs, total, err := s.svcCtx.StrategyLogModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.StrategyLogListResp](result.ErrorSelect.AddError(err))
	}

	// 为每个日志添加格式化的描述
	formattedLogs := make([]*model.StrategyLog, len(logs))
	for i, log := range logs {
		formattedLog := *log
		formattedLog.Conditions = s.formatConditions(log.Conditions)
		formattedLog.Actions = s.formatActionsWithStatus(log.Actions, log.ActionCompleted, log.ErrorMessage, log.ActionDetails)
		formattedLogs[i] = &formattedLog
	}

	resp := types.StrategyLogListResp{
		Total: total,
		List:  formattedLogs,
	}

	return result.SuccessResult(resp)
}

// GetStrategyLogById 根据ID获取策略日志详情
func (s *StrategyLogService) GetStrategyLogById(req *types.StrategyLogDetailReq) *result.Result[types.StrategyLogDetailResp] {
	log, err := s.svcCtx.StrategyLogModel.GetById(req.ID)
	if err != nil {
		return result.ToError[types.StrategyLogDetailResp](result.ErrorSelect.AddError(err))
	}

	// 格式化日志内容
	log.Conditions = s.formatConditions(log.Conditions)
	log.Actions = s.formatActionsWithStatus(log.Actions, log.ActionCompleted, log.ErrorMessage, log.ActionDetails)

	resp := types.StrategyLogDetailResp{
		StrategyLog: log,
	}

	return result.SuccessResult(resp)
}

// CreateStrategyLog 创建策略日志
func (s *StrategyLogService) CreateStrategyLog(req *types.StrategyLogCreateReq) *result.Result[model.StrategyLog] {
	// 验证请求数据
	if req.StrategyID <= 0 {
		return result.ToError[model.StrategyLog](result.ErrorReqParam.AddMessage("策略ID不能为空"))
	}
	if req.StrategyName == "" {
		return result.ToError[model.StrategyLog](result.ErrorReqParam.AddMessage("策略名称不能为空"))
	}
	if req.TargetType == "" {
		return result.ToError[model.StrategyLog](result.ErrorReqParam.AddMessage("目标类型不能为空"))
	}
	if req.TargetID == "" {
		return result.ToError[model.StrategyLog](result.ErrorReqParam.AddMessage("目标ID不能为空"))
	}

	// 转换为数据库模型
	log := req.ToModel()

	// 保存到数据库
	if err := s.svcCtx.StrategyLogModel.Create(log); err != nil {
		return result.ToError[model.StrategyLog](result.ErrorAdd.AddError(err))
	}

	return result.SuccessResult(*log)
}

// DeleteStrategyLogByTime 根据时间删除策略日志
func (s *StrategyLogService) DeleteStrategyLogByTime(beforeTime time.Time) *result.Result[any] {
	if err := s.svcCtx.StrategyLogModel.DeleteByTime(beforeTime); err != nil {
		return result.ErrorDelete.AddError(err)
	}

	return result.SimpleResult("删除策略日志成功")
}

// GetStrategyLogStats 获取策略日志统计
func (s *StrategyLogService) GetStrategyLogStats(req *types.StrategyLogStatsReq) *result.Result[types.StrategyLogStatsResp] {
	// 设置默认时间范围（最近7天）
	if req.StartTime.IsZero() {
		req.StartTime = time.Now().AddDate(0, 0, -7)
	}
	if req.EndTime.IsZero() {
		// 将结束时间设置为当天的23:59:59
		now := time.Now()
		req.EndTime = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())
	}

	// 构建时间范围条件
	timeRangeConditions := make(map[string]interface{})
	timeRangeConditions["start_time"] = req.StartTime
	timeRangeConditions["end_time"] = req.EndTime

	// 获取时间范围内的总数
	timeRangeTotal, err := s.svcCtx.StrategyLogModel.CountByConditions(timeRangeConditions)
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	// 获取所有时间的总数作为对比
	allTimeTotal, err := s.svcCtx.StrategyLogModel.CountAll()
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	// 如果时间范围内没有数据，使用所有数据的统计
	if timeRangeTotal == 0 && allTimeTotal > 0 {
		// 使用所有数据的统计
		timeRangeTotal = allTimeTotal
		timeRangeConditions = make(map[string]interface{}) // 清空时间条件
	}

	// 获取动作执行成功和失败数量
	successConditions := make(map[string]interface{})
	successConditions["action_completed"] = true
	// 复制时间范围条件
	for k, v := range timeRangeConditions {
		successConditions[k] = v
	}

	successTotal, err := s.svcCtx.StrategyLogModel.CountByConditions(successConditions)
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	errorConditions := make(map[string]interface{})
	errorConditions["action_completed"] = false
	// 复制时间范围条件
	for k, v := range timeRangeConditions {
		errorConditions[k] = v
	}

	errorTotal, err := s.svcCtx.StrategyLogModel.CountByConditions(errorConditions)
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	// 获取条件满足和不满足的数量
	conditionMetConditions := make(map[string]interface{})
	conditionMetConditions["condition_met"] = true
	// 复制时间范围条件
	for k, v := range timeRangeConditions {
		conditionMetConditions[k] = v
	}

	conditionMetTotal, err := s.svcCtx.StrategyLogModel.CountByConditions(conditionMetConditions)
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	conditionNotMetConditions := make(map[string]interface{})
	conditionNotMetConditions["condition_met"] = false
	// 复制时间范围条件
	for k, v := range timeRangeConditions {
		conditionNotMetConditions[k] = v
	}

	conditionNotMetTotal, err := s.svcCtx.StrategyLogModel.CountByConditions(conditionNotMetConditions)
	if err != nil {
		return result.ToError[types.StrategyLogStatsResp](result.ErrorSelect.AddError(err))
	}

	resp := types.StrategyLogStatsResp{
		TotalCount:           timeRangeTotal,
		SuccessCount:         successTotal,
		ErrorCount:           errorTotal,
		ConditionMetCount:    conditionMetTotal,
		ConditionNotMetCount: conditionNotMetTotal,
		StartTime:            req.StartTime,
		EndTime:              req.EndTime,
	}

	return result.SuccessResult(resp)
}

// LogStrategyExecution 记录策略执行日志（供策略引擎调用）
func (s *StrategyLogService) LogStrategyExecution(
	strategyID int64,
	strategyName string,
	targetType string,
	targetID string,
	targetName string,
	conditions string,
	conditionMet bool,
	actions string,
	actionCompleted bool,
	errorMessage string,
	executionTime time.Time,
	duration int64,
) error {
	// 格式化条件和动作描述
	formattedConditions := s.formatConditions(conditions)
	formattedActions := s.formatActions(actions)

	log := &model.StrategyLog{
		StrategyID:      strategyID,
		StrategyName:    strategyName,
		TargetType:      targetType,
		TargetID:        targetID,
		TargetName:      targetName,
		Conditions:      formattedConditions,
		ConditionMet:    conditionMet,
		Actions:         formattedActions,
		ActionCompleted: actionCompleted,
		ErrorMessage:    errorMessage,
		ExecutionTime:   executionTime,
		Duration:        duration,
	}

	return s.svcCtx.StrategyLogModel.Create(log)
}

// formatConditions 格式化条件描述
func (s *StrategyLogService) formatConditions(conditionsJSON string) string {
	if conditionsJSON == "" {
		return "无条件"
	}

	var conditions []map[string]interface{}
	if err := json.Unmarshal([]byte(conditionsJSON), &conditions); err != nil {
		return fmt.Sprintf("条件解析失败: %s", conditionsJSON)
	}

	if len(conditions) == 0 {
		return "无条件"
	}

	var formattedConditions []string
	for _, condition := range conditions {
		field := s.getStringValue(condition, "field")
		operator := s.getStringValue(condition, "operator")
		value := s.getValueDescription(condition, "value")

		// 将字段名转换为友好的中文描述
		fieldDesc := s.getFieldDescription(field)
		operatorDesc := s.getOperatorDescription(operator)

		conditionDesc := fmt.Sprintf("%s %s %s", fieldDesc, operatorDesc, value)
		formattedConditions = append(formattedConditions, conditionDesc)
	}

	if len(formattedConditions) == 1 {
		return formattedConditions[0]
	}

	// 多个条件时使用更简洁的格式
	return strings.Join(formattedConditions, " 且 ")
}

// getFieldDescription 获取字段的中文描述
func (s *StrategyLogService) getFieldDescription(field string) string {
	fieldMap := map[string]string{
		"promotion_status":      "广告状态",
		"promotion_status_name": "广告状态名称",
		"promotion_name":        "广告名称",
		"promotion_budget":      "广告预算",
		"promotion_bid":         "广告出价",
		"project_status":        "项目状态",
		"project_status_name":   "项目状态名称",
		"project_name":          "项目名称",
		"project_budget":        "项目预算",
		"advertiser_status":     "广告主状态",
		"advertiser_name":       "广告主名称",
		"account_status":        "账户状态",
		"account_name":          "账户名称",
		"cost":                  "消耗金额",
		"show_count":            "展示次数",
		"click_count":           "点击次数",
		"ctr":                   "点击率",
		"cpc":                   "点击单价",
		"cpm":                   "千次展示成本",
		"conversion_count":      "转化次数",
		"conversion_rate":       "转化率",
		"conversion_cost":       "转化成本",
	}

	if desc, exists := fieldMap[field]; exists {
		return desc
	}
	return field
}

// getOperatorDescription 获取操作符的中文描述
func (s *StrategyLogService) getOperatorDescription(operator string) string {
	operatorMap := map[string]string{
		"equals":                "等于",
		"not_equals":            "不等于",
		"contains":              "包含",
		"not_contains":          "不包含",
		"greater_than":          "大于",
		"less_than":             "小于",
		"greater_than_or_equal": "大于等于",
		"less_than_or_equal":    "小于等于",
		"in":                    "在列表中",
		"not_in":                "不在列表中",
		"is_null":               "为空",
		"is_not_null":           "不为空",
		"starts_with":           "以...开头",
		"ends_with":             "以...结尾",
	}

	if desc, exists := operatorMap[operator]; exists {
		return desc
	}
	return operator
}

// formatActions 格式化动作描述
func (s *StrategyLogService) formatActions(actionsJSON string) string {
	if actionsJSON == "" {
		return "无动作"
	}

	var actions []map[string]interface{}
	if err := json.Unmarshal([]byte(actionsJSON), &actions); err != nil {
		return fmt.Sprintf("动作解析失败: %s", actionsJSON)
	}

	if len(actions) == 0 {
		return "无动作"
	}

	var formattedActions []string
	for _, action := range actions {
		actionType := s.getStringValue(action, "type")
		actionDesc := s.getActionDescription(actionType, action)
		formattedActions = append(formattedActions, actionDesc)
	}

	if len(formattedActions) == 1 {
		return formattedActions[0]
	}

	// 多个动作时使用更简洁的格式
	return strings.Join(formattedActions, " + ")
}

// formatActionsWithStatus 格式化动作描述（带执行状态）
func (s *StrategyLogService) formatActionsWithStatus(actionsJSON string, actionCompleted bool, errorMessage string, actionDetails string) string {
	if actionsJSON == "" {
		return "无动作"
	}

	var actions []map[string]interface{}
	if err := json.Unmarshal([]byte(actionsJSON), &actions); err != nil {
		return fmt.Sprintf("动作解析失败: %s", actionsJSON)
	}

	if len(actions) == 0 {
		return "无动作"
	}

	// 尝试解析ActionDetails来获取每个动作的执行状态
	var details []types.ActionExecutionDetail
	hasActionDetails := false
	if actionDetails != "" {
		if err := json.Unmarshal([]byte(actionDetails), &details); err == nil && len(details) > 0 {
			hasActionDetails = true
		}
	}

	var formattedActions []string
	for i, action := range actions {
		actionType := s.getStringValue(action, "type")
		actionDesc := s.getActionDescription(actionType, action)

		// 确定动作执行状态
		var isSuccess bool

		// 特殊处理企业微信通知动作 - 默认成功
		if actionType == "wechat_notification" {
			isSuccess = true
		} else if hasActionDetails && i < len(details) {
			// 使用ActionDetails中的状态
			isSuccess = details[i].Success
		} else {
			// 回退到全局状态
			isSuccess = actionCompleted
		}

		// 添加执行状态图标
		if isSuccess {
			actionDesc = "✅ " + actionDesc
		} else {
			actionDesc = "❌ " + actionDesc
		}

		formattedActions = append(formattedActions, actionDesc)
	}

	return strings.Join(formattedActions, " + ")
}

// getStringValue 安全获取字符串值
func (s *StrategyLogService) getStringValue(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// getValueDescription 获取值的描述
func (s *StrategyLogService) getValueDescription(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		switch v := value.(type) {
		case string:
			return fmt.Sprintf("'%s'", v)
		case float64:
			return fmt.Sprintf("%.2f", v)
		case int:
			return fmt.Sprintf("%d", v)
		case bool:
			if v {
				return "是"
			}
			return "否"
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	return "未设置"
}

// getActionDescription 获取动作描述
func (s *StrategyLogService) getActionDescription(actionType string, action map[string]interface{}) string {
	switch actionType {
	// 广告相关动作
	case "pause_ad":
		return "⏸️ 暂停广告"
	case "resume_ad":
		return "▶️ 启用广告"
	case "delete_ad":
		return "🗑️ 删除广告"
	case "copy_ad":
		return "📋 复制广告"
	case "copy_multiple_ads":
		if config, ok := action["config"].(map[string]interface{}); ok {
			if count, ok := config["copy_count"].(float64); ok && count > 0 {
				return fmt.Sprintf("📋 复制%d个广告", int(count))
			}
		}
		return "📋 复制多个广告"
	case "delete_and_create_ad":
		return "🔄 删除并新建广告"

	// 项目相关动作
	case "copy_project":
		return "📋 复制项目"
	case "enable_project":
		return "▶️ 启用项目"
	case "pause_project":
		return "⏸️ 暂停项目"
	case "delete_project":
		return "🗑️ 删除项目"

	// 广告主相关动作
	case "pause_advertiser":
		return "⏸️ 暂停广告主"
	case "resume_advertiser":
		return "▶️ 恢复广告主"

	// 通知动作
	case "wechat_notification":
		message := s.getStringValue(action, "message")
		if message != "" {
			return fmt.Sprintf("📱 企业微信通知: %s", message)
		}
		return "📱 企业微信通知"

	// 兼容旧版本的动作类型
	case "pause_promotion":
		return "⏸️ 暂停广告"
	case "resume_promotion":
		return "▶️ 启用广告"
	case "delete_promotion":
		return "🗑️ 删除广告"
	case "update_bid":
		bidValue := s.getValueDescription(action, "bid")
		return fmt.Sprintf("💰 调整出价至 %s 元", bidValue)
	case "update_budget":
		budgetValue := s.getValueDescription(action, "budget")
		return fmt.Sprintf("💳 调整预算至 %s 元", budgetValue)

	default:
		return fmt.Sprintf("❓ 未知动作: %s", actionType)
	}
}
