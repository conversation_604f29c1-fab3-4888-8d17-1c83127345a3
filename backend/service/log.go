package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// LogService 日志服务
type LogService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewLogService 创建日志服务
func NewLogService(svcCtx *svc.ServiceContext) *LogService {
	return &LogService{
		svcCtx: svcCtx,
	}
}

func (s *LogService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *LogService) SetApp(app *application.App) {
	s.app = app
}

// GetLogLevel 获取当前日志级别
func (s *LogService) GetLogLevel() *result.Result[string] {
	cfg := config.GetConfig()
	if cfg == nil {
		return result.ToError[string](result.ErrorSystem.AddError(fmt.Errorf("无法获取配置")))
	}
	return result.SuccessResult(cfg.Log.Level)
}

// SetLogLevel 设置日志级别
func (s *LogService) SetLogLevel(level string) *result.Result[any] {
	if err := utils.SetLevel(level); err != nil {
		return result.ToError[any](result.ErrorSystem.AddError(err))
	}
	return result.SimpleResult("日志级别设置成功")
}

// GetLogFiles 获取日志文件列表
func (s *LogService) GetLogFiles() *result.Result[[]map[string]interface{}] {
	cfg := config.GetConfig()
	if cfg == nil {
		return result.ToError[[]map[string]interface{}](result.ErrorSystem.AddError(fmt.Errorf("无法获取配置")))
	}

	logDir := cfg.Log.Directory
	if logDir == "" {
		logDir = filepath.Join(os.Getenv("LOCALAPPDATA"), "OceanEngineManager", "Logs")
	}

	files, err := os.ReadDir(logDir)
	if err != nil {
		return result.ToError[[]map[string]interface{}](result.ErrorSystem.AddError(err))
	}

	var logFiles []map[string]interface{}
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".log") {
			fileInfo, err := file.Info()
			if err != nil {
				continue
			}

			logFiles = append(logFiles, map[string]interface{}{
				"name":           file.Name(),
				"size":           fileInfo.Size(),
				"modified":       fileInfo.ModTime(),
				"size_formatted": formatFileSize(fileInfo.Size()),
			})
		}
	}

	return result.SuccessResult(logFiles)
}

// GetLogContent 获取日志文件内容
func (s *LogService) GetLogContent(filename string, lines int) *result.Result[string] {
	cfg := config.GetConfig()
	if cfg == nil {
		return result.ToError[string](result.ErrorSystem.AddError(fmt.Errorf("无法获取配置")))
	}

	logDir := cfg.Log.Directory
	if logDir == "" {
		logDir = filepath.Join(os.Getenv("LOCALAPPDATA"), "OceanEngineManager", "Logs")
	}

	filePath := filepath.Join(logDir, filename)

	// 安全检查：确保文件在日志目录内
	if !strings.HasPrefix(filePath, logDir) {
		return result.ToError[string](result.ErrorSystem.AddError(fmt.Errorf("无效的文件路径")))
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return result.ToError[string](result.ErrorSystem.AddError(err))
	}

	// 如果指定了行数，只返回最后几行
	if lines > 0 {
		linesList := strings.Split(string(content), "\n")
		if len(linesList) > lines {
			linesList = linesList[len(linesList)-lines:]
		}
		content = []byte(strings.Join(linesList, "\n"))
	}

	return result.SuccessResult(string(content))
}

// ClearLogs 清理日志文件
func (s *LogService) ClearLogs() *result.Result[any] {
	cfg := config.GetConfig()
	if cfg == nil {
		return result.ToError[any](result.ErrorSystem.AddError(fmt.Errorf("无法获取配置")))
	}

	logDir := cfg.Log.Directory
	if logDir == "" {
		logDir = filepath.Join(os.Getenv("LOCALAPPDATA"), "OceanEngineManager", "Logs")
	}

	files, err := os.ReadDir(logDir)
	if err != nil {
		return result.ToError[any](result.ErrorSystem.AddError(err))
	}

	var deletedCount int
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".log") {
			filePath := filepath.Join(logDir, file.Name())
			if err := os.Remove(filePath); err != nil {
				utils.LogError("删除日志文件失败", "file", file.Name(), "error", err.Error())
			} else {
				deletedCount++
			}
		}
	}

	utils.LogInfo("清理日志文件完成", "deleted_count", deletedCount)
	return result.SimpleResult(fmt.Sprintf("成功清理 %d 个日志文件", deletedCount))
}

// GetLogStats 获取日志统计信息
func (s *LogService) GetLogStats() *result.Result[map[string]interface{}] {
	cfg := config.GetConfig()
	if cfg == nil {
		return result.ToError[map[string]interface{}](result.ErrorSystem.AddError(fmt.Errorf("无法获取配置")))
	}

	logDir := cfg.Log.Directory
	if logDir == "" {
		logDir = filepath.Join(os.Getenv("LOCALAPPDATA"), "OceanEngineManager", "Logs")
	}

	files, err := os.ReadDir(logDir)
	if err != nil {
		return result.ToError[map[string]interface{}](result.ErrorSystem.AddError(err))
	}

	var totalSize int64
	var fileCount int
	var oldestFile time.Time
	var newestFile time.Time

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".log") {
			fileInfo, err := file.Info()
			if err != nil {
				continue
			}

			totalSize += fileInfo.Size()
			fileCount++

			if oldestFile.IsZero() || fileInfo.ModTime().Before(oldestFile) {
				oldestFile = fileInfo.ModTime()
			}
			if newestFile.IsZero() || fileInfo.ModTime().After(newestFile) {
				newestFile = fileInfo.ModTime()
			}
		}
	}

	stats := map[string]interface{}{
		"file_count":    fileCount,
		"total_size":    totalSize,
		"total_size_mb": float64(totalSize) / 1024 / 1024,
		"oldest_file":   oldestFile,
		"newest_file":   newestFile,
		"log_directory": logDir,
	}

	return result.SuccessResult(stats)
}

// formatFileSize 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
