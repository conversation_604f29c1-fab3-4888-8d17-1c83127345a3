package service

import (
	"context"
	"fmt"
	"os"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// SystemService 系统服务
type SystemService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewSystemService 创建系统服务
func NewSystemService(svcCtx *svc.ServiceContext) *SystemService {
	return &SystemService{
		svcCtx: svcCtx,
	}
}

func (s *SystemService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *SystemService) SetApp(app *application.App) {
	s.app = app
}

// OpenFile 打开文件
func (s *SystemService) OpenFile(req reqType.OpenFileReq) *result.Result[[]string] {
	dialog := application.OpenFileDialog()
	dialog.SetTitle(req.Title)
	dialog.AddFilter(req.DisplayName, req.Pattern)
	if req.IsMultiple {
		if pathList, err := dialog.PromptForMultipleSelection(); err != nil {
			return result.ToError[[]string](result.ErrorSystem.AddError(err))
		} else {
			return result.SuccessResult(pathList)
		}
	} else {
		if path, err := dialog.PromptForSingleSelection(); err != nil {
			return result.ToError[[]string](result.ErrorSystem.AddError(err))
		} else {
			return result.SuccessResult([]string{path})
		}
	}
}

// ReadFile 读取文件
func (s *SystemService) ReadFile(req reqType.ReadFileReq) *result.Result[[][]byte] {
	// 读取文件内容
	var contents [][]byte
	for _, path := range req.FilePathList {
		content, err := os.ReadFile(path)
		if err != nil {
			return result.ToError[[][]byte](result.ErrorSystem.AddError(err))
		}
		contents = append(contents, content)
	}
	return result.SuccessResult(contents)
}

// DeleteFile 删除文件
func (s *SystemService) DeleteFile(req reqType.DeleteFileReq) *result.Result[bool] {
	for _, path := range req.FilePathList {
		if err := os.Remove(path); err != nil {
			return result.ToError[bool](result.ErrorSystem.AddError(err))
		}
	}
	return result.SuccessResult(true)
}

// DownloadFile 下载文件到本地缓存目录
func (s *SystemService) DownloadFile(req reqType.DownFileReq) *result.Result[*bot.DownloadResourceResp] {
	// 获取Bot实例
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[*bot.DownloadResourceResp](result.ErrorSystem.AddError(err))
	}

	// 调用Bot的DownloadResource方法下载文件
	downloadResp, err := b.DownloadResource(req.Url, req.Format, accountInfo.Cookie)
	if err != nil {
		return result.ToError[*bot.DownloadResourceResp](result.ErrorSystem.AddError(err))
	}

	// 如果下载失败，直接返回响应
	if !downloadResp.Success {
		return result.SuccessResult(downloadResp)
	}

	// 获取缓存目录路径
	cacheDir := config.GetConfig().Storage.CacheDir

	// 使用utils.SaveFile保存文件到本地
	actualFileName, err := utils.SaveFile(cacheDir, downloadResp.FileName, downloadResp.Data, false)
	if err != nil {
		return result.ToError[*bot.DownloadResourceResp](result.ErrorSystem.AddError(err))
	}

	// 填充LocalFileName字段
	downloadResp.LocalFileName = actualFileName
	downloadResp.Data = nil // 清空Data字段，避免传输大数据量
	downloadResp.Message = "下载并保存成功"

	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，下载文件，URL: %s，文件名: %s，大小: %d bytes",
				accountInfo.AccountId, accountInfo.AccountName, req.Url, actualFileName, downloadResp.FileSize)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_System,
				OperationType: constant.OperationLog_OperationType_Download,
				Operation:     "下载文件",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(downloadResp)
}
