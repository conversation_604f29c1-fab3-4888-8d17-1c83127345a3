package service

import (
	"context"
	"fmt"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/event"
	"time"

	"encoding/base64"

	"github.com/jinzhu/copier"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// AdvertiserService 广告主服务
type AdvertiserService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewAdvertiserService 创建广告主服务
func NewAdvertiserService(svcCtx *svc.ServiceContext) *AdvertiserService {
	return &AdvertiserService{
		svcCtx: svcCtx,
	}
}
func (s *AdvertiserService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *AdvertiserService) SetApp(app *application.App) {
	s.app = app
}

// AdvertiserListReq 广告主列表请求参数
type AdvertiserListReq struct {
	Page           int64             `json:"page"`
	PageSize       int64             `json:"page_size"`
	AccountId      int64             `json:"account_id"`
	AdvertiserName string            `json:"advertiser_name"`
	GroupName      string            `json:"group_name"`
	Status         int               `json:"status"`
	OrderBy        string            `json:"order_by"`
	OrderDesc      bool              `json:"order_desc"`
	ExtraFilters   map[string]string `json:"extra_filters"`
}

// AdvertiserListResp 广告主列表响应
type AdvertiserListResp struct {
	Total    int64               `json:"total"`
	List     []*model.Advertiser `json:"list"`
	Page     int64               `json:"page"`
	PageSize int64               `json:"page_size"`
}

// GetAdvertiserList 获取广告主列表
func (s *AdvertiserService) GetAdvertiserList(req AdvertiserListReq) *result.Result[AdvertiserListResp] {
	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.AccountId > 0 {
		conditions["account_id"] = req.AccountId
	}
	if req.AdvertiserName != "" {
		conditions["advertiser_name"] = req.AdvertiserName
	}
	if req.GroupName != "" {
		conditions["group_name"] = req.GroupName
	}
	if req.Status > 0 {
		conditions["advertiser_status"] = req.Status
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	advertisers, total, err := s.svcCtx.AdvertiserModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[AdvertiserListResp](result.ErrorSelect.AddError(err))
	}

	// 为每个广告主获取策略绑定信息
	for _, advertiser := range advertisers {
		strategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("advertiser", fmt.Sprintf("%d", advertiser.AdvertiserId))
		if err == nil {
			// 提取策略ID列表
			strategyIds := make([]int64, len(strategies))
			for i, strategy := range strategies {
				strategyIds[i] = strategy.ID
			}
			// 将策略ID列表添加到广告主数据中
			advertiser.StrategyIds = strategyIds
		}
	}

	resp := AdvertiserListResp{
		Total:    total,
		List:     advertisers,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetAdvertiserById 根据ID获取广告主详情
func (s *AdvertiserService) GetAdvertiserById(id int64) *result.Result[model.Advertiser] {
	advertiser, err := s.svcCtx.AdvertiserModel.GetById(id)
	if err != nil {
		return result.ToError[model.Advertiser](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult[model.Advertiser](*advertiser)
}

// GetAdvertiserByAdvertiserId 根据广告主ID获取广告主详情
func (s *AdvertiserService) GetAdvertiserByAdvertiserId(advertiserId int64) *result.Result[model.Advertiser] {
	advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserId)
	if err != nil {
		return result.ToError[model.Advertiser](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult[model.Advertiser](*advertiser)
}

// GetAdvertisersByAccountId 根据账户ID获取广告主列表
func (s *AdvertiserService) GetAdvertisersByAccountId(accountId int64) *result.Result[[]model.Advertiser] {
	advertisers, err := s.svcCtx.AdvertiserModel.GetByAccountId(accountId)
	if err != nil {
		return result.ToError[[]model.Advertiser](result.ErrorSelect.AddError(err))
	}
	// 转换为响应格式
	advList := make([]model.Advertiser, len(advertisers))
	for i, adv := range advertisers {
		advList[i] = *adv
	}
	return result.SuccessResult[[]model.Advertiser](advList)
}

// CreateAdvertiser 创建广告主
func (s *AdvertiserService) CreateAdvertiser(advertiser *model.Advertiser) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.Create(advertiser); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	return result.SimpleResult("创建广告主成功")
}

// BatchCreateAdvertisers 批量创建广告主
func (s *AdvertiserService) BatchCreateAdvertisers(advertisers []*model.Advertiser) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.BatchCreate(nil, advertisers); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	return result.SimpleResult("批量创建广告主成功")
}

// UpdateAdvertiser 更新广告主
func (s *AdvertiserService) UpdateAdvertiser(advertiser *model.Advertiser) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.Update(nil, advertiser); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新广告主成功")
}

// DeleteAdvertiser 删除广告主
func (s *AdvertiserService) DeleteAdvertiser(id int64) *result.Result[any] {
	advertiser, err := s.svcCtx.AdvertiserModel.GetById(id)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}
	if err := s.svcCtx.AdvertiserModel.Delete(advertiser); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除广告主成功")
}

// BatchDeleteAdvertisers 批量删除广告主
func (s *AdvertiserService) BatchDeleteAdvertisers(ids []int64) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.BatchDelete(ids); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("批量删除广告主成功")
}

// DeleteAdvertiserByAdvertiserId 根据广告主ID删除广告主
func (s *AdvertiserService) DeleteAdvertiserByAdvertiserId(advertiserId int64) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.DeleteByAdvertiserId(advertiserId); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除广告主成功")
}

// DeleteAdvertisersByAccountId 根据账户ID删除所有广告主
func (s *AdvertiserService) DeleteAdvertisersByAccountId(accountId int64) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.DeleteByAccountId(accountId); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除账户下所有广告主成功")
}

// UpdateAdvertiserWithMap 更新广告主
func (s *AdvertiserService) UpdateAdvertiserWithMap(id int64, data map[string]interface{}) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.MapUpdate(nil, id, data); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新广告主成功")
}

// UpdateAutoDeleteComment 更新广告主自动删除评论开关
func (s *AdvertiserService) UpdateAutoDeleteComment(id int64, autoDeleteComment int) *result.Result[any] {
	// 验证参数
	if autoDeleteComment != 0 && autoDeleteComment != 1 {
		return result.ToError[any](result.ErrorUpdate.AddError(fmt.Errorf("自动删除评论开关值无效，只能是0或1")))
	}

	// 获取广告主信息用于日志记录
	advertiser, err := s.svcCtx.AdvertiserModel.GetById(id)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	// 更新自动删除评论开关
	data := map[string]interface{}{
		"auto_delete_comment": autoDeleteComment,
		"update_time":         time.Now(),
	}

	if err := s.svcCtx.AdvertiserModel.MapUpdate(nil, id, data); err != nil {
		return result.ErrorUpdate.AddError(err)
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			operation := "开启自动删除评论"
			if autoDeleteComment == 0 {
				operation = "关闭自动删除评论"
			}

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   advertiser.AdvertiserName, // 使用广告主名称作为账户名称
				AccountId:     advertiser.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     operation,
				Description:   fmt.Sprintf("%s，广告主: %s (ID: %d)", operation, advertiser.AdvertiserName, advertiser.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()

	statusText := "开启"
	if autoDeleteComment == 0 {
		statusText = "关闭"
	}

	return result.SimpleResult(fmt.Sprintf("自动删除评论已%s", statusText))
}

// UpdateIsFollowed 更新广告主关注状态
func (s *AdvertiserService) UpdateIsFollowed(accountId, advertiserId int64, isFollowed bool) *result.Result[any] {
	// 获取广告主信息用于日志记录
	advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(advertiserId)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	if err := s.svcCtx.AdvertiserModel.UpdateIsFollowed(accountId, advertiserId, isFollowed); err != nil {
		return result.ErrorUpdate.AddError(err)
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			operation := "关注广告主"
			if !isFollowed {
				operation = "取消关注广告主"
			}

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   advertiser.AdvertiserName, // 使用广告主名称作为账户名称
				AccountId:     advertiser.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     operation,
				Description:   fmt.Sprintf("%s，广告主: %s (ID: %d)", operation, advertiser.AdvertiserName, advertiser.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()

	statusText := "已关注"
	if !isFollowed {
		statusText = "已取消关注"
	}

	return result.SimpleResult(fmt.Sprintf("广告主%s", statusText))
}

// SyncAdvertisers 同步指定账户下的广告主
func (s *AdvertiserService) SyncAdvertisers(accountId int64, advertisers []*model.Advertiser) *result.Result[any] {
	if err := s.svcCtx.AdvertiserModel.SyncAdvertisers(accountId, advertisers); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("同步广告主数据成功")
}

// RefreshAdvertisers 刷新指定账户下的广告主数据
func (s *AdvertiserService) RefreshAdvertisers(accountId int64) *result.Result[string] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(accountId)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}

	// 循环获取所有广告主数据
	page := int64(1)
	pageSize := int64(100) // 每次获取100条数据
	hasMore := true
	allAdvertisers := make([]model.Advertiser, 0, 100)

	for hasMore {
		resp, err := b.GetSubAccountList(page, pageSize, 1, "", accountInfo.Cookie)
		if err != nil {
			return result.ToError[string](result.ErrorSelect.AddError(err))
		}

		// 转换API响应数据为model.Advertiser
		tempAdvertiserList := make([]model.Advertiser, 0, 100)
		if err := copier.Copy(&tempAdvertiserList, resp.Data.DataList); err != nil {
			return result.ToError[string](result.ErrorCopy.AddError(err))
		}
		allAdvertisers = append(allAdvertisers, tempAdvertiserList...)
		// 检查是否还有更多数据
		hasMore = resp.Data.Pagination.HasMore
		page++
	}

	// 获取数据库中该账户下的广告主ID列表（只查询必要字段）
	AdvertiserIdList, err := s.svcCtx.AdvertiserModel.GetAdvertiserIdByAccountId(accountId)
	if err != nil {
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}

	// 创建数据库中已存在的advertiser_id映射
	advertiserIdMap := make(map[int64]*model.Advertiser)
	for _, adv := range AdvertiserIdList {
		advertiserIdMap[adv.AdvertiserId] = adv
	}

	// 创建API返回的advertiser_id映射
	apiAdvertiserMap := make(map[int64]*model.Advertiser)
	for i := range allAdvertisers {
		apiAdvertiserMap[allAdvertisers[i].AdvertiserId] = &allAdvertisers[i]
	}

	// 开始数据同步处理
	var toCreate []*model.Advertiser // 需要新增的
	var toUpdate []*model.Advertiser // 需要更新的
	var toDelete []int64             // 需要标记删除的ID

	// 处理API返回的数据（新增或更新）
	for advertiserId, apiAdv := range apiAdvertiserMap {
		apiAdv.AccountId = accountId // 设置账户ID
		if dbAdv, exists := advertiserIdMap[advertiserId]; exists {
			apiAdv.Id = dbAdv.Id                 // 保持数据库ID不变
			apiAdv.CreateTime = dbAdv.CreateTime // 保持创建时间不变
			apiAdv.UpdateTime = time.Now()       // 更新时间为当前时间
			toUpdate = append(toUpdate, apiAdv)
		} else { // 数据库中不存在，需要新增
			toCreate = append(toCreate, apiAdv)
		}
	}

	// 处理需要标记删除的数据（数据库中存在但API中不存在）
	for advertiserId, dbAdvId := range advertiserIdMap {
		if _, exists := apiAdvertiserMap[advertiserId]; !exists {
			// API中不存在，需要标记删除
			if dbAdvId.DeleteTime == 0 { // 检查是否已经标记删除（时间戳为0表示未删除）
				toDelete = append(toDelete, advertiserId)
			}
		}
	}

	// 执行数据库操作
	if len(toCreate) > 0 {
		if err := s.svcCtx.AdvertiserModel.BatchCreate(nil, toCreate); err != nil {
			return result.ToError[string](result.ErrorAdd.AddError(err))
		}
	}
	// 更新操作
	if len(toUpdate) > 0 {
		for _, adv := range toUpdate {
			if err := s.svcCtx.AdvertiserModel.Update(nil, adv); err != nil {
				return result.ToError[string](result.ErrorUpdate.AddError(err))
			}
		}
	}
	// 标记删除操作
	if len(toDelete) > 0 {
		if err := s.svcCtx.AdvertiserModel.BatchMarkDelete(toDelete); err != nil {
			return result.ToError[string](result.ErrorDelete.AddError(err))
		}
	}
	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，刷新广告主数据。新增: %d, 更新: %d, 删除: %d",
				accountInfo.AccountId, accountInfo.AccountName, len(toCreate), len(toUpdate), len(toDelete))

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "刷新广告主数据",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult[string](fmt.Sprintf("刷新广告主数据成功，新增: %d, 更新: %d, 删除: %d",
		len(toCreate), len(toUpdate), len(toDelete)))
}

// GetAdvertiserListAsync 获取广告主列表异步方法
func (s *AdvertiserService) GetAdvertiserListAsync(req types.AdvertiserReq) *result.Result[bot.GetSubAccountResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetSubAccountResp](result.ErrorSelect.AddError(err))
	}
	resp, err := b.GetSubAccountList(req.Page, req.PageSize, 1, "", accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.GetSubAccountResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询广告主列表",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，查询广告主列表", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// UploadVideoAsMaterial 上传视频作为素材
func (s *AdvertiserService) UploadVideoAsMaterial(accountId, advertiserId int64, filePath string) *result.Result[*bot.VideoMaterialInfo] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(accountId)
	if err != nil {
		return result.ToError[*bot.VideoMaterialInfo](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的UploadVideoAsMaterial方法
	uploadResp, err := b.UploadVideoAsMaterial(advertiserId, accountInfo.Cookie, filePath)
	if err != nil {
		return result.ToError[*bot.VideoMaterialInfo](result.ErrorSelect.AddError(err))
	}

	// 将上传响应转换为VideoMaterialInfo结构
	videoMaterial := &bot.VideoMaterialInfo{
		ImageMode: uploadResp.ImageMode,
		ImageInfo: make([]bot.ImageInfo, len(uploadResp.ImageInfo)),
		VideoInfo: bot.VideoInfo{
			VideoID:         uploadResp.VideoInfo.VideoID,
			Status:          uploadResp.VideoInfo.Status,
			VideoDuration:   uploadResp.VideoInfo.Duration,
			InitialSize:     uploadResp.VideoInfo.InitialSize,
			VideoUnique:     "", // 需要根据实际API返回设置
			FileMD5:         uploadResp.VideoInfo.FileMD5,
			ThumbHeight:     uploadResp.VideoInfo.ThumbHeight,
			ThumbWidth:      uploadResp.VideoInfo.ThumbWidth,
			ThumbURI:        uploadResp.VideoInfo.CoverURI,
			OriginalFileURI: "", // 需要根据实际API返回设置
			Duration:        uploadResp.VideoInfo.Duration,
			ErrorDesc:       "",
			UserReference:   "",
			Width:           uploadResp.VideoInfo.Width,
			Height:          uploadResp.VideoInfo.Height,
			VID:             uploadResp.VideoInfo.VID,
			UploadID:        "", // 需要根据实际API返回设置
			CoverURI:        uploadResp.VideoInfo.CoverURI,
			Codec:           "", // 需要根据实际API返回设置
			Bitrate:         uploadResp.VideoInfo.Bitrate,
		},
		CopyMode: 0, // 默认复制模式
	}

	// 转换图片信息
	for i, imgInfo := range uploadResp.ImageInfo {
		videoMaterial.ImageInfo[i] = bot.ImageInfo{
			Height:  imgInfo.Height,
			Width:   imgInfo.Width,
			WebURI:  imgInfo.WebURI,
			SignURL: imgInfo.SignURL,
		}
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，上传视频素材: %s",
				accountInfo.AccountId, accountInfo.AccountName, advertiserId, filePath)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "上传视频素材",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(videoMaterial)
}

// GetMaterialVideoInfo 获取视频素材信息
func (s *AdvertiserService) GetMaterialVideoInfo(req types.GetMaterialVideoInfoReq) *result.Result[bot.GetMaterialVideoInfoResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetMaterialVideoInfoResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的GetMaterialVideoInfo方法
	resp, err := b.GetMaterialVideoInfo(req.AdvertiserId, accountInfo.Cookie, req.Vids)
	if err != nil {
		return result.ToError[bot.GetMaterialVideoInfoResp](result.ErrorSelect.AddError(err))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，获取视频素材信息，视频数量: %d",
				accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, len(req.Vids))

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取视频素材信息",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// GetMaterialTitle 获取素材标题
func (s *AdvertiserService) GetMaterialTitle(req types.GetMaterialTitleReq) *result.Result[bot.GetMaterialTitleResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetMaterialTitleResp](result.ErrorSelect.AddError(err))
	}

	// 构建bot包需要的请求参数
	botReq := &bot.GetMaterialTitleReq{
		VideoMaterials:      make([]bot.VideoMaterial, len(req.VideoMaterials)),
		ImageMaterials:      make([]bot.ImageMaterial, len(req.ImageMaterials)),
		AwemePhotoMaterials: make([]bot.AwemePhotoMaterial, len(req.AwemePhotoMaterials)),
	}

	// 转换视频素材数据
	for i, vm := range req.VideoMaterials {
		botReq.VideoMaterials[i] = bot.VideoMaterial{
			MaterialID: vm.MaterialID,
			LegoMid:    vm.LegoMid,
		}
	}

	// 转换图片素材数据
	for i, im := range req.ImageMaterials {
		botReq.ImageMaterials[i] = bot.ImageMaterial{
			MaterialID: im.MaterialID,
			LegoMid:    im.LegoMid,
		}
	}

	// 转换抖音图片素材数据
	for i, apm := range req.AwemePhotoMaterials {
		botReq.AwemePhotoMaterials[i] = bot.AwemePhotoMaterial{
			MaterialID: apm.MaterialID,
			LegoMid:    apm.LegoMid,
		}
	}

	// 调用bot包的GetMaterialTitle方法
	resp, err := b.GetMaterialTitle(req.AdvertiserId, accountInfo.Cookie, botReq)
	if err != nil {
		return result.ToError[bot.GetMaterialTitleResp](result.ErrorSelect.AddError(err))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			totalMaterials := len(req.VideoMaterials) + len(req.ImageMaterials) + len(req.AwemePhotoMaterials)
			description := fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，获取素材标题，素材数量: %d",
				accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, totalMaterials)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取素材标题",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// UploadAvatarReq 上传头像请求参数
type UploadAvatarReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId string `json:"advertiser_id"` // 广告主ID
	FileName     string `json:"file_name"`     // 文件名
	Base64Data   string `json:"base64_data"`   // 图片base64数据
	Width        int    `json:"width"`         // 图片宽度
	Height       int    `json:"height"`        // 图片高度
}

// UploadAvatarResp 上传头像响应
type UploadAvatarResp struct {
	Success bool   `json:"success"`  // 是否成功
	Message string `json:"message"`  // 响应消息
	ImageId string `json:"image_id"` // 图片ID
}

// UploadAvatar 上传广告主头像
func (s *AdvertiserService) UploadAvatar(req *UploadAvatarReq) *result.Result[UploadAvatarResp] {
	// 解码base64数据
	fileData, err := base64.StdEncoding.DecodeString(req.Base64Data)
	if err != nil {
		return result.ToError[UploadAvatarResp](result.ErrorReqParam.AddError(fmt.Errorf("base64解码失败: %v", err)))
	}

	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[UploadAvatarResp](result.ErrorSelect.AddError(err))
	}

	// 上传头像
	uploadResp, err := b.UploadAvatarFromBytes(req.AdvertiserId, fileData, req.FileName, req.Width, req.Height, accountInfo.Cookie)
	fmt.Println("上传头像res", uploadResp)

	if err != nil {
		return result.ToError[UploadAvatarResp](result.ErrorSystem.AddError(fmt.Errorf("上传头像失败 : %v", err)))
	}

	// 检查上传是否成功 - 上传头像API返回成功码是0
	if uploadResp.Code != 200 {
		return result.ToError[UploadAvatarResp](result.ErrorSystem.AddError(fmt.Errorf("上传失败: %s", uploadResp.Message)))
	}

	// 保存头像
	saveRequest := &bot.SaveAvatarRequest{
		AvatarInfo: bot.AvatarInfo{
			Width:  req.Width,
			Height: req.Height,
			WebURI: uploadResp.Data.ImageInfo.WebUri,
			WebUri: uploadResp.Data.ImageInfo.WebUri,
		},
	}

	saveResp, err := b.SaveAvatar(req.AdvertiserId, saveRequest, accountInfo.Cookie)
	if err != nil {
		return result.ToError[UploadAvatarResp](result.ErrorSystem.AddError(fmt.Errorf("保存头像失败: %v", err)))
	}

	// 检查保存是否成功 - 保存头像API返回成功码是200
	if saveResp.Code != 200 {
		// 添加调试日志
		fmt.Printf("SaveAvatar failed for advertiser %s: Code=%d, Message=%s\n", req.AdvertiserId, saveResp.Code, saveResp.Message)
		return result.ToError[UploadAvatarResp](result.ErrorSystem.AddError(fmt.Errorf("保存头像失败: %s", saveResp.Message)))
	}

	// 保存成功
	fmt.Printf("SaveAvatar success for advertiser %s\n", req.AdvertiserId)

	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %s，上传头像: %s",
				accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.FileName)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "上传头像",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(UploadAvatarResp{
		Success: true,
		Message: "头像上传成功",
		ImageId: uploadResp.Data.ImageInfo.WebUri,
	})
}

// BatchUploadAvatarReq 批量上传头像请求参数
type BatchUploadAvatarReq struct {
	AccountId     int64    `json:"account_id"`     // 账户ID
	FileName      string   `json:"file_name"`      // 文件名
	Base64Data    string   `json:"base64_data"`    // 图片base64数据
	Width         int      `json:"width"`          // 图片宽度
	Height        int      `json:"height"`         // 图片高度
	AdvertiserIds []string `json:"advertiser_ids"` // 广告主ID列表
}

// BatchUploadAvatarResp 批量上传头像响应
type BatchUploadAvatarResp struct {
	Success      bool                        `json:"success"`       // 是否成功
	Message      string                      `json:"message"`       // 响应消息
	TotalCount   int                         `json:"total_count"`   // 总数
	SuccessCount int                         `json:"success_count"` // 成功数量
	ErrorCount   int                         `json:"error_count"`   // 失败数量
	Results      map[string]UploadAvatarResp `json:"results"`       // 每个广告主的上传结果
}

// BatchUploadAvatar 批量上传广告主头像
func (s *AdvertiserService) BatchUploadAvatar(req *BatchUploadAvatarReq) *result.Result[BatchUploadAvatarResp] {
	// 解码base64数据
	fileData, err := base64.StdEncoding.DecodeString(req.Base64Data)
	if err != nil {
		return result.ToError[BatchUploadAvatarResp](result.ErrorReqParam.AddError(fmt.Errorf("base64解码失败: %v", err)))
	}

	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[BatchUploadAvatarResp](result.ErrorSelect.AddError(err))
	}

	// 初始化结果
	results := make(map[string]UploadAvatarResp)
	successCount := 0
	errorCount := 0

	// 优化：只上传一次头像文件，获取图片URI
	var uploadedImageURI string
	var uploadError error

	// 使用第一个广告主ID上传头像文件（只需要上传一次）
	if len(req.AdvertiserIds) > 0 {
		firstAdvertiserId := req.AdvertiserIds[0]
		uploadResp, err := b.UploadAvatarFromBytes(firstAdvertiserId, fileData, req.FileName, req.Width, req.Height, accountInfo.Cookie)
		if err != nil {
			uploadError = err
		} else if uploadResp.Code != 200 {
			uploadError = fmt.Errorf("上传头像失败: %s", uploadResp.Message)
		} else {
			uploadedImageURI = uploadResp.Data.ImageInfo.WebUri
		}
	}

	// 如果上传失败，所有广告主都标记为失败
	if uploadError != nil {
		for _, advertiserId := range req.AdvertiserIds {
			results[advertiserId] = UploadAvatarResp{
				Success: false,
				Message: fmt.Sprintf("上传头像失败: %v", uploadError),
				ImageId: "",
			}
			errorCount++
		}
	} else {
		// 为每个广告主保存已上传的头像
		for _, advertiserId := range req.AdvertiserIds {
			// 保存头像
			saveRequest := &bot.SaveAvatarRequest{
				AvatarInfo: bot.AvatarInfo{
					Width:  req.Width,
					Height: req.Height,
					WebURI: uploadedImageURI,
					WebUri: uploadedImageURI,
				},
			}

			saveResp, err := b.SaveAvatar(advertiserId, saveRequest, accountInfo.Cookie)
			if err != nil {
				results[advertiserId] = UploadAvatarResp{
					Success: false,
					Message: fmt.Sprintf("保存头像失败: %v", err),
					ImageId: "",
				}
				errorCount++
				continue
			}

			// 检查保存是否成功
			if saveResp.Code != 200 {
				// 添加调试日志
				fmt.Printf("SaveAvatar failed for advertiser %s: Code=%d, Message=%s\n", advertiserId, saveResp.Code, saveResp.Message)
				results[advertiserId] = UploadAvatarResp{
					Success: false,
					Message: fmt.Sprintf("保存头像失败: %s", saveResp.Message),
					ImageId: "",
				}
				errorCount++
				continue
			}

			// 保存成功
			fmt.Printf("SaveAvatar success for advertiser %s\n", advertiserId)
			results[advertiserId] = UploadAvatarResp{
				Success: true,
				Message: "头像上传成功",
				ImageId: uploadedImageURI,
			}
			successCount++
		}
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			description := fmt.Sprintf("账号ID: %d，账户名: %s，批量上传头像: %s，总数: %d，成功: %d，失败: %d",
				accountInfo.AccountId, accountInfo.AccountName, req.FileName, len(req.AdvertiserIds), successCount, errorCount)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "批量上传头像",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(BatchUploadAvatarResp{
		Success:      errorCount == 0,
		Message:      fmt.Sprintf("批量上传完成，成功 %d 个，失败 %d 个", successCount, errorCount),
		TotalCount:   len(req.AdvertiserIds),
		SuccessCount: successCount,
		ErrorCount:   errorCount,
		Results:      results,
	})
}

// GetOrangeNormalListReq 获取橙子建站普通站点列表请求参数
type GetOrangeNormalListReq struct {
	AccountId    int64 `json:"account_id"`    // 账户ID
	AdvertiserId int64 `json:"advertiser_id"` // 广告主ID
	OrderMode    int   `json:"order_mode"`    // 排序模式
	SearchMode   int   `json:"search_mode"`   // 搜索模式
	Page         int   `json:"page"`          // 页码
	Size         int   `json:"size"`          // 每页大小
}

// GetOrangeNormalList 获取橙子建站普通站点列表
func (s *AdvertiserService) GetOrangeNormalList(req *GetOrangeNormalListReq) *result.Result[*bot.GetOrangeNormalListResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[*bot.GetOrangeNormalListResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的GetOrangeNormalList方法
	orangeListResp, err := b.GetOrangeNormalList(req.AdvertiserId, req.OrderMode, req.SearchMode, req.Page, req.Size, accountInfo.Cookie)
	if err != nil {
		return result.ToError[*bot.GetOrangeNormalListResp](result.ErrorSelect.AddError(err))
	}

	// 检查API响应是否成功
	if orangeListResp.Code != 0 {
		return result.ToError[*bot.GetOrangeNormalListResp](result.ErrorSelect.AddError(fmt.Errorf("API返回错误: %s", orangeListResp.Msg)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询橙子建站普通站点列表",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，查询橙子建站普通站点列表", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(orangeListResp)
}

// XureTemplatedReq Xure模板请求参数
type XureTemplatedReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	TemplateId   string `json:"template_id"`   // 模板ID
}

// XureTemplatedResp Xure模板响应
type XureTemplatedResp struct {
	Success  bool   `json:"success"`   // 是否成功
	Message  string `json:"message"`   // 响应消息
	SiteId   string `json:"site_id"`   // 站点ID
	PageType int    `json:"page_type"` // 页面类型
}

// XureTemplated 调用Xure模板接口
func (s *AdvertiserService) XureTemplated(req *XureTemplatedReq) *result.Result[XureTemplatedResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[XureTemplatedResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的XureTemplated方法 - 现在直接返回模板数据
	xureData, err := b.XureTemplated(req.AdvertiserId, req.TemplateId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[XureTemplatedResp](result.ErrorSelect.AddError(fmt.Errorf("调用Xure模板接口失败: %v", err)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "调用Xure模板接口",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，模板ID: %s，调用Xure模板接口", accountInfo.AccountId, accountInfo.AccountName, req.TemplateId),
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应 - 直接使用返回的数据
	resp := XureTemplatedResp{
		Success:  true,
		Message:  "调用Xure模板接口成功",
		SiteId:   xureData.SiteId,
		PageType: xureData.PageType,
	}

	return result.SuccessResult(resp)
}

// UpdatePageReq 更新页面请求参数
type UpdatePageReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	PageId       string `json:"page_id"`       // 页面ID
	Data         string `json:"data"`          // 页面数据
}

// UpdatePageResp 更新页面响应
type UpdatePageResp struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 响应消息
}

// UpdatePage 更新页面
func (s *AdvertiserService) UpdatePage(req *UpdatePageReq) *result.Result[UpdatePageResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[UpdatePageResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的UpdatePage方法
	updateResp, err := b.UpdatePage(req.AdvertiserId, req.PageId, req.Data, accountInfo.Cookie)
	if err != nil {
		return result.ToError[UpdatePageResp](result.ErrorSelect.AddError(fmt.Errorf("更新页面失败: %v", err)))
	}

	// 检查API响应是否成功
	if updateResp.StatusCode != 0 {
		return result.ToError[UpdatePageResp](result.ErrorSelect.AddError(fmt.Errorf("API返回错误: %s", updateResp.Message)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新页面",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，页面ID: %s，更新页面", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.PageId),
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应
	resp := UpdatePageResp{
		Success: true,
		Message: "更新页面成功",
	}

	return result.SuccessResult(resp)
}

// UpdatePageNameReq 更新页面名称请求参数
type UpdatePageNameReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	SiteId       string `json:"site_id"`       // 站点ID
	Name         string `json:"name"`          // 页面名称
}

// UpdatePageNameResp 更新页面名称响应
type UpdatePageNameResp struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 响应消息
}

// UpdatePageName 更新页面名称
func (s *AdvertiserService) UpdatePageName(req *UpdatePageNameReq) *result.Result[UpdatePageNameResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[UpdatePageNameResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的UpdatePageName方法
	updateResp, err := b.UpdatePageName(req.AdvertiserId, req.SiteId, req.Name, accountInfo.Cookie)
	if err != nil {
		return result.ToError[UpdatePageNameResp](result.ErrorSelect.AddError(fmt.Errorf("更新页面名称失败: %v", err)))
	}

	// 检查API响应是否成功
	if updateResp.StatusCode != 0 {
		return result.ToError[UpdatePageNameResp](result.ErrorSelect.AddError(fmt.Errorf("API返回错误: %s", updateResp.Message)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新页面名称",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，站点ID: %s，新名称: %s，更新页面名称", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.SiteId, req.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应
	resp := UpdatePageNameResp{
		Success: true,
		Message: "更新页面名称成功",
	}

	return result.SuccessResult(resp)
}

// PublishNewReq 发布新页面请求参数
type PublishNewReq struct {
	AccountId     int64  `json:"account_id"`     // 账户ID
	AdvertiserId  int64  `json:"advertiser_id"`  // 广告主ID
	SiteId        string `json:"site_id"`        // 站点ID
	Name          string `json:"name"`           // 页面名称
	AuditStandard int    `json:"audit_standard"` // 审核标准
	Version       int    `json:"version"`        // 版本
}

// PublishNewResp 发布新页面响应
type PublishNewResp struct {
	Success         bool   `json:"success"`           // 是否成功
	Message         string `json:"message"`           // 响应消息
	AuditPreviewURL string `json:"audit_preview_url"` // 审核预览URL
	URL             string `json:"url"`               // 页面URL
	SensitiveInfo   struct {
		Message       string `json:"message"`
		SensitiveType int    `json:"sensitive_type"`
	} `json:"sensitive_info"` // 敏感信息
}

// PublishNew 发布新页面
func (s *AdvertiserService) PublishNew(req *PublishNewReq) *result.Result[PublishNewResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[PublishNewResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的PublishNew方法
	publishResp, err := b.PublishNew(req.AdvertiserId, req.SiteId, req.Name, req.AuditStandard, req.Version, accountInfo.Cookie)
	if err != nil {
		return result.ToError[PublishNewResp](result.ErrorSelect.AddError(fmt.Errorf("发布新页面失败: %v", err)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "发布新页面",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，站点ID: %s，页面名称: %s，发布新页面", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.SiteId, req.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应
	resp := PublishNewResp{
		Success:         true,
		Message:         "发布新页面成功",
		AuditPreviewURL: publishResp.AuditPreviewUrl,
		URL:             publishResp.Url,
		SensitiveInfo: struct {
			Message       string `json:"message"`
			SensitiveType int    `json:"sensitive_type"`
		}{
			Message:       publishResp.SensitiveInfo.Message,
			SensitiveType: publishResp.SensitiveInfo.SensitiveType,
		},
	}

	return result.SuccessResult(resp)
}

// GetPageInfoReq 获取页面信息请求参数
type GetPageInfoReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	SiteId       string `json:"site_id"`       // 站点ID
}

// GetPageInfoResp 获取页面信息响应
type GetPageInfoResp struct {
	Success       bool                     `json:"success"`        // 是否成功
	Message       string                   `json:"message"`        // 响应消息
	IndustryId    int                      `json:"industry_id"`    // 行业ID
	Name          string                   `json:"name"`           // 页面名称
	SiteId        string                   `json:"site_id"`        // 站点ID
	PageId        string                   `json:"page_id"`        // 页面ID
	Status        int                      `json:"status"`         // 状态
	ModifyTime    string                   `json:"modify_time"`    // 修改时间
	CreateTime    string                   `json:"create_time"`    // 创建时间
	SiteType      string                   `json:"site_type"`      // 站点类型
	FunctionId    string                   `json:"function_id"`    // 功能ID
	Title         string                   `json:"title"`          // 标题
	Thumbnail     string                   `json:"thumbnail"`      // 缩略图
	ShareThumb    string                   `json:"share_thumb"`    // 分享缩略图
	Description   string                   `json:"description"`    // 描述
	TemplateId    string                   `json:"template_id"`    // 模板ID
	SourceType    int                      `json:"source_type"`    // 来源类型
	AuditStandard int                      `json:"audit_standard"` // 审核标准
	IsAuditPre    bool                     `json:"is_audit_pre"`   // 是否预审核
	CustomerName  string                   `json:"customer_name"`  // 客户名称
	Pages         []map[string]interface{} `json:"pages"`          // 页面数据
	Meta          map[string]interface{}   `json:"meta"`           // 元信息
	Part          map[string]interface{}   `json:"part"`           // 部分信息
}

// GetPageInfo 获取页面信息
func (s *AdvertiserService) GetPageInfo(req *GetPageInfoReq) *result.Result[GetPageInfoResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[GetPageInfoResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的GetPageInfo方法 - 现在直接返回页面数据
	pageInfoData, err := b.GetPageInfo(req.AdvertiserId, req.SiteId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[GetPageInfoResp](result.ErrorSelect.AddError(fmt.Errorf("获取页面信息失败: %v", err)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取页面信息",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，站点ID: %s，获取页面信息", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.SiteId),
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应 - 直接使用返回的数据
	resp := GetPageInfoResp{
		Success:       true,
		Message:       "获取页面信息成功",
		IndustryId:    pageInfoData.IndustryId,
		Name:          pageInfoData.Name,
		SiteId:        pageInfoData.SiteId,
		PageId:        pageInfoData.PageId,
		Status:        pageInfoData.Status,
		ModifyTime:    pageInfoData.ModifyTime,
		CreateTime:    pageInfoData.CreateTime,
		SiteType:      pageInfoData.SiteType,
		FunctionId:    pageInfoData.FunctionId,
		Title:         pageInfoData.Title,
		Thumbnail:     pageInfoData.Thumbnail,
		ShareThumb:    pageInfoData.ShareThumb,
		Description:   pageInfoData.Description,
		TemplateId:    pageInfoData.TemplateId,
		SourceType:    pageInfoData.SourceType,
		AuditStandard: pageInfoData.AuditStandard,
		IsAuditPre:    pageInfoData.IsAuditPre,
		CustomerName:  pageInfoData.CustomerName,
		Pages:         convertPages(pageInfoData.Pages),
		Meta:          convertMeta(pageInfoData.Meta),
		Part:          convertPart(pageInfoData.Part),
	}

	return result.SuccessResult(resp)
}

// CopyPageItem 复制页面项
type CopyPageItem struct {
	PageId   string `json:"page_id"`   // 落地页ID
	SourceId string `json:"source_id"` // 源模版ID
}

// CopyPageReq 复制页面请求参数
type CopyPageReq struct {
	SourceAccountId    int64          `json:"source_account_id"`    // 源账户ID
	SourceAdvertiserId int64          `json:"source_advertiser_id"` // 源广告主ID
	TargetAccountId    int64          `json:"target_account_id"`    // 目标账户ID
	TargetAdvertiserId int64          `json:"target_advertiser_id"` // 目标广告主ID
	Pages              []CopyPageItem `json:"pages"`                // 选中的落地页列表
}

// CopyPageResp 复制页面响应
type CopyPageResp struct {
	Success      bool                          `json:"success"`       // 是否成功
	Message      string                        `json:"message"`       // 响应消息
	TotalCount   int                           `json:"total_count"`   // 总数
	SuccessCount int                           `json:"success_count"` // 成功数量
	ErrorCount   int                           `json:"error_count"`   // 失败数量
	Results      map[string]bot.CopyPageResult `json:"results"`       // 每个页面的复制结果
	Summary      string                        `json:"summary"`       // 汇总信息
}

// CopyPage 复制落地页
func (s *AdvertiserService) CopyPage(req *CopyPageReq) *result.Result[CopyPageResp] {
	// 创建任务进度
	task := event.NewTaskProgress(
		"复制落地页",
		fmt.Sprintf("从广告主%d复制%d个落地页到广告主%d", req.SourceAdvertiserId, len(req.Pages), req.TargetAdvertiserId),
		2, // 总共4个步骤：1.获取源账户 2.获取目标账户 3.获取页面详情 4.执行复制
	)

	// 步骤1：获取源账户bot和账户信息
	task.UpdateProgress(1, "获取源账户信息", 0, 20, 50)
	sourceBot, sourceAccountInfo, err := s.svcCtx.GetBot(req.SourceAccountId)
	if err != nil {
		task.Error(fmt.Sprintf("获取源账户信息失败: %v", err))
		return result.ToError[CopyPageResp](result.ErrorSelect.AddError(err))
	}

	// 步骤2：获取目标账户bot和账户信息
	targetBot, targetAccountInfo, err := s.svcCtx.GetBot(req.TargetAccountId)
	if err != nil {
		task.Error(fmt.Sprintf("获取目标账户信息失败: %v", err))
		return result.ToError[CopyPageResp](result.ErrorSelect.AddError(err))
	}
	task.UpdateProgress(1, "获取目标账户信息", 20, 100, 500)

	// 步骤3：查询源落地页详情列表
	var pageInfoList []*bot.GetPageInfoData
	for _, pageItem := range req.Pages {
		pageInfoData, err := sourceBot.GetPageInfo(req.SourceAdvertiserId, pageItem.PageId, sourceAccountInfo.Cookie)
		if err != nil {
			task.Error(fmt.Sprintf("获取页面%s信息失败: %v", pageItem.PageId, err))
			return result.ToError[CopyPageResp](result.ErrorSelect.AddError(fmt.Errorf("获取页面%s信息失败: %v", pageItem.PageId, err)))
		}
		pageInfoData.SourceId = pageItem.SourceId
		pageInfoList = append(pageInfoList, pageInfoData)
	}
	task.UpdateProgress(1, "获取落地页详情", 100, 0, 300)

	// 步骤4：调用bot的CopyPage方法
	task.UpdateProgress(2, "执行落地页复制", 0, 80, int64(len(req.Pages)*1500))
	copyResp, err := targetBot.CopyPage(req.TargetAdvertiserId, pageInfoList, targetAccountInfo.Cookie)
	if err != nil {
		task.Error(fmt.Sprintf("复制落地页失败: %v", err))
		return result.ToError[CopyPageResp](result.ErrorSelect.AddError(fmt.Errorf("复制落地页失败: %v", err)))
	}
	task.UpdateProgress(2, "执行落地页复制", 80, 100, 200)

	// 完成任务
	task.Complete()

	// 记录操作日志
	go func() {
		if s.app != nil {
			logMsg := fmt.Sprintf("从账户%d广告主%d复制%d个落地页到账户%d广告主%d，成功%d个，失败%d个",
				req.SourceAccountId, req.SourceAdvertiserId, copyResp.TotalCount,
				req.TargetAccountId, req.TargetAdvertiserId, copyResp.SuccessCount, copyResp.ErrorCount)

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   sourceAccountInfo.AccountName,
				AccountId:     sourceAccountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "复制落地页",
				Description:   logMsg,
				OperationTime: time.Now(),
			})
		}
	}()

	// 构建响应
	resp := CopyPageResp{
		Success:      copyResp.SuccessCount > 0,
		Message:      "落地页复制操作完成",
		TotalCount:   copyResp.TotalCount,
		SuccessCount: copyResp.SuccessCount,
		ErrorCount:   copyResp.ErrorCount,
		Results:      copyResp.Results,
		Summary:      copyResp.Summary,
	}

	return result.SuccessResult(resp)
}

// convertPages 转换页面数据
func convertPages(pages []bot.PageData) []map[string]interface{} {
	result := make([]map[string]interface{}, len(pages))
	for i, page := range pages {
		result[i] = map[string]interface{}{
			"id":           page.ID,
			"status":       page.Status,
			"nickname":     page.Nickname,
			"data":         page.Data,
			"convertInfos": page.ConvertInfos,
			"layerMap":     page.LayerMap,
			"createTime":   page.CreateTime,
			"modifyTime":   page.ModifyTime,
		}
	}
	return result
}

// convertMeta 转换元信息
func convertMeta(meta bot.MetaInfo) map[string]interface{} {
	return map[string]interface{}{
		"firstIndustry":  meta.FirstIndustry,
		"secondIndustry": meta.SecondIndustry,
		"calcColor":      meta.CalcColor,
	}
}

// convertPart 转换部分信息
func convertPart(part bot.MetaInfo) map[string]interface{} {
	return map[string]interface{}{
		"firstIndustry":  part.FirstIndustry,
		"secondIndustry": part.SecondIndustry,
		"calcColor":      part.CalcColor,
	}
}

// GetPageListReq 获取页面列表请求参数
type GetPageListReq struct {
	AccountId    int64  `json:"account_id"`    // 账户ID
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID
	Limit        int    `json:"limit"`         // 每页大小
	Page         int    `json:"page"`          // 页码
	Status       string `json:"status"`        // 状态过滤
	Search       string `json:"search"`        // 搜索关键词
	//ShowAuditModel bool   `json:"show_audit_model"` // 是否显示审核模式
	//NeedDeriveList bool   `json:"need_derive_list"` // 是否需要派生列表
}

// GetPageListResp 获取页面列表响应
type GetPageListResp struct {
	Success bool                   `json:"success"` // 是否成功
	Message string                 `json:"message"` // 响应消息
	Data    *[]bot.GetPageListItem `json:"data"`    // 页面列表数据
	Total   int                    `json:"total"`   // 总数
}

// GetPageList 获取页面列表
func (s *AdvertiserService) GetPageList(req *GetPageListReq) *result.Result[GetPageListResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[GetPageListResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot包的GetPageList方法
	pageList, err := b.GetPageList(req.AdvertiserId, req.Limit, req.Page, req.Status, req.Search, accountInfo.Cookie)
	if err != nil {
		return result.ToError[GetPageListResp](result.ErrorSelect.AddError(fmt.Errorf("获取页面列表失败: %v", err)))
	}

	// 记录操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Advertiser,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取页面列表",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，广告主ID: %d，获取页面列表", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(GetPageListResp{
		Success: true,
		Message: "获取页面列表成功",
		Data:    pageList,
		Total:   len(*pageList),
	})
}
