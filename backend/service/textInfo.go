package service

import (
	"context"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// TextInfoService 文本信息服务
type TextInfoService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewTextInfoService 创建文本信息服务
func NewTextInfoService(svcCtx *svc.ServiceContext) *TextInfoService {
	return &TextInfoService{
		svcCtx: svcCtx,
	}
}

func (s *TextInfoService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *TextInfoService) SetApp(app *application.App) {
	s.app = app
}

// GetTextInfoList 获取文本信息列表
func (s *TextInfoService) GetTextInfoList(req types.TextInfoListReq) *result.Result[types.TextInfoListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.Text != "" {
		conditions["text"] = req.Text
	}
	if req.Remark != "" {
		conditions["remark"] = req.Remark
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	textInfos, total, err := s.svcCtx.TextInfoModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.TextInfoListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.TextInfoListResp{
		Total:    total,
		List:     textInfos,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetTextInfoById 根据ID获取文本信息详情
func (s *TextInfoService) GetTextInfoById(id int64) *result.Result[model.TextInfo] {
	textInfo, err := s.svcCtx.TextInfoModel.GetById(id)
	if err != nil {
		return result.ToError[model.TextInfo](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult[model.TextInfo](*textInfo)
}

// CreateTextInfo 创建文本信息
func (s *TextInfoService) CreateTextInfo(textInfo *model.TextInfo) *result.Result[any] {
	if err := s.svcCtx.TextInfoModel.Create(textInfo); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	return result.SimpleResult("创建文本信息成功")
}

// UpdateTextInfo 更新文本信息
func (s *TextInfoService) UpdateTextInfo(textInfo *model.TextInfo) *result.Result[any] {
	if err := s.svcCtx.TextInfoModel.Update(nil, textInfo); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新文本信息成功")
}

// DeleteTextInfo 删除文本信息
func (s *TextInfoService) DeleteTextInfo(id int64) *result.Result[any] {
	textInfo, err := s.svcCtx.TextInfoModel.GetById(id)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}
	if err := s.svcCtx.TextInfoModel.Delete(textInfo); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除文本信息成功")
}

// BatchDeleteTextInfo 批量删除文本信息
func (s *TextInfoService) BatchDeleteTextInfo(ids []int64) *result.Result[any] {
	if err := s.svcCtx.TextInfoModel.BatchDelete(ids); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("批量删除文本信息成功")
}

// UpdateTextInfoWithMap 使用Map更新文本信息
func (s *TextInfoService) UpdateTextInfoWithMap(id int64, data map[string]interface{}) *result.Result[any] {
	if err := s.svcCtx.TextInfoModel.MapUpdate(nil, id, data); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新文本信息成功")
}

// GetTextInfoByText 根据文本内容获取文本信息
func (s *TextInfoService) GetTextInfoByText(text string) *result.Result[model.TextInfo] {
	textInfo, err := s.svcCtx.TextInfoModel.GetByText(text)
	if err != nil {
		return result.ToError[model.TextInfo](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(*textInfo)
}

// GetTextInfoStats 获取文本信息统计信息
func (s *TextInfoService) GetTextInfoStats() *result.Result[map[string]interface{}] {
	stats, err := s.svcCtx.TextInfoModel.GetStat()
	if err != nil {
		return result.ToError[map[string]interface{}](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(stats)
}

// GetFontOptions 获取字体选项列表
func (s *TextInfoService) GetFontOptions() *result.Result[[]utils.FontOption] {
	options := utils.GetFontOptions()
	return result.SuccessResult(options)
}
