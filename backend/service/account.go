package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gorm.io/gorm"
)

// AccountService 账户服务
type AccountService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewAccountService 创建账户服务
func NewAccountService(svcCtx *svc.ServiceContext) *AccountService {
	return &AccountService{
		svcCtx: svcCtx,
	}
}
func (s *AccountService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *AccountService) SetApp(app *application.App) {
	s.app = app
}

// GetAccountList 获取账户列表
func (s *AccountService) GetAccountList(req types.AccountListReq) *result.Result[types.AccountListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.AccountName != "" {
		conditions["account_name"] = req.AccountName
	}
	if req.LoginType != "" {
		conditions["login_type"] = req.LoginType
	}
	if req.Remark != "" {
		conditions["remark"] = req.Remark
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	accounts, total, err := s.svcCtx.AccountModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.AccountListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.AccountListResp{
		Total:    total,
		List:     accounts,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// UpdateAccount 更新账户
func (s *AccountService) UpdateAccount(account *model.Account) *result.Result[any] {
	if err := s.svcCtx.AccountModel.Update(nil, account); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新账户成功")
}

// DeleteAccount 删除账户
func (s *AccountService) DeleteAccount(id int64) *result.Result[any] {
	account, err := s.svcCtx.AccountModel.GetById(id)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}
	if err := s.svcCtx.AccountModel.Delete(account); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("删除账户成功")
}

// BatchDeleteAccounts 批量删除账户
func (s *AccountService) BatchDeleteAccounts(ids []int64) *result.Result[any] {
	if err := s.svcCtx.AccountModel.BatchDelete(ids); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("批量删除账户成功")
}

// UpdateAccountWithMap 使用Map更新账户
func (s *AccountService) UpdateAccountWithMap(id int64, data map[string]interface{}) *result.Result[any] {
	if err := s.svcCtx.AccountModel.MapUpdate(nil, id, data); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新账户成功")
}

// GetAccountByAccountId 根据平台ID获取账户
func (s *AccountService) GetAccountByAccountId(accountId int64) *result.Result[model.Account] {
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountId)
	if err != nil {
		return result.ToError[model.Account](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(*account)
}

// LoginWithCookie 使用cookie登录
func (s *AccountService) LoginWithCookie(cookie string) *result.Result[any] {
	b, err := bot.NewBot("")
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	// 登录成功后，获取用户信息
	userInfo, err := b.GetUserInfo(cookie)
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	accountId := userInfo.Data.CoreUser.Id
	//更新cookie
	accountInfo, err := s.svcCtx.AccountModel.GetByAccountId(accountId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		accountInfo = &model.Account{
			AccountId:       accountId,
			Cookie:          cookie,
			AccountName:     userInfo.Data.CoreUser.Name,
			LoginType:       constant.Account_LoginType_Email,
			SubAccountCount: userInfo.Data.CoreUser.TotalAccounts,
		}
		if err := s.svcCtx.AccountModel.Create(accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	} else if err != nil {
		return result.ErrorSelect.AddError(err)
	} else {
		accountInfo.Cookie = cookie
		accountInfo.AccountName = userInfo.Data.CoreUser.Name
		accountInfo.LoginType = constant.Account_LoginType_Email
		accountInfo.SubAccountCount = userInfo.Data.CoreUser.TotalAccounts
		if err := s.svcCtx.AccountModel.Update(nil, accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Login,
				Operation:     "Cookie登录",
				Description:   fmt.Sprintf("登录成功，账号ID: %d，账户名: %s", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("登录成功")
}

// LoginWithEmailDirectly 使用邮箱直接登录
func (s *AccountService) LoginWithEmailDirectly(email, password string) *result.Result[any] {
	b, err := bot.NewBot("")
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	// 邮箱直接登录
	accountId, cookies, err := b.LoginWithEmailDirectly(email, password)
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	// 登录成功后，获取用户信息
	userInfo, err := b.GetUserInfo(cookies)
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	//更新cookie
	accountInfo, err := s.svcCtx.AccountModel.GetByAccountId(accountId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		accountInfo = &model.Account{
			AccountId:       accountId,
			Cookie:          cookies,
			Email:           email,
			Password:        password,
			AccountName:     userInfo.Data.CoreUser.Name,
			LoginType:       constant.Account_LoginType_Email,
			SubAccountCount: userInfo.Data.CoreUser.TotalAccounts,
		}
		if err := s.svcCtx.AccountModel.Create(accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	} else if err != nil {
		return result.ErrorSelect.AddError(err)
	} else {
		accountInfo.Cookie = cookies
		accountInfo.Email = email
		accountInfo.Password = password
		accountInfo.AccountName = userInfo.Data.CoreUser.Name
		accountInfo.LoginType = constant.Account_LoginType_Email
		accountInfo.SubAccountCount = userInfo.Data.CoreUser.TotalAccounts
		if err := s.svcCtx.AccountModel.Update(nil, accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Login,
				Operation:     "邮箱登录",
				Description:   fmt.Sprintf("登录成功，账号ID: %d，账户名: %s", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("登录成功")
}

// SendLoginCode 发送登录手机验证码
func (s *AccountService) SendLoginCode(phone string) *result.Result[any] {
	b, err := bot.NewBot("")
	if err != nil {
		return result.ErrorSendPhoneCode.AddError(err)
	}
	// 发送登录手机验证码
	resp, err := b.SendLoginCode(phone)
	if err != nil {
		return result.ErrorSendPhoneCode.AddError(err)
	}
	if resp.ErrorCode != 0 {
		return result.ErrorSendPhoneCode.AddError(fmt.Errorf("发送验证码失败: %s", resp.Description))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_SendCode,
				Operation:     "发送手机验证码",
				Description:   fmt.Sprintf("发送手机验证码成功，手机号ID: %s", phone),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("验证码已发送，请注意查收")
}

// LoginWithPhoneCode 使用手机验证码登录
func (s *AccountService) LoginWithPhoneCode(phone, code string) *result.Result[any] {
	b, err := bot.NewBot("")
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	// 手机验证码登录
	accountId, cookies, err := b.LoginMobile(phone, code)
	if err != nil {
		return result.ErrorUserLogin.AddError(err)
	}
	// 登录成功后，获取用户信息
	userInfo, err := b.GetUserInfo(cookies)

	//更新cookie
	accountInfo, err := s.svcCtx.AccountModel.GetByAccountId(accountId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		accountInfo = &model.Account{
			AccountId:       accountId,
			Cookie:          cookies,
			Mobile:          phone,
			AccountName:     userInfo.Data.CoreUser.Name,
			LoginType:       constant.Account_LoginType_Email,
			SubAccountCount: userInfo.Data.CoreUser.TotalAccounts,
		}
		if err := s.svcCtx.AccountModel.Create(accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	} else if err != nil {
		return result.ErrorSelect.AddError(err)
	} else {
		accountInfo.Cookie = cookies
		accountInfo.Mobile = phone
		accountInfo.AccountName = userInfo.Data.CoreUser.Name
		accountInfo.LoginType = constant.Account_LoginType_Email
		accountInfo.SubAccountCount = userInfo.Data.CoreUser.TotalAccounts
		if err := s.svcCtx.AccountModel.Update(nil, accountInfo); err != nil {
			return result.ErrorUpdate.AddError(err)
		}
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Login,
				Operation:     "手机验证码登录",
				Description:   fmt.Sprintf("登录成功，账号ID: %d，账户名: %s", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("登录成功")
}

// ConcernAddSubAccount 收藏子账户
func (s *AccountService) ConcernAddSubAccount(req types.ConcernAddSubAccountReq) *result.Result[bot.ConcernAddSubAccountResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.ConcernAddSubAccountResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.ConcernAddSubAccount(req.AdvertiserId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.ConcernAddSubAccountResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "收藏子账户",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，收藏子账户[%d]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// ConcernRemoveSubAccount 取消收藏子账户
func (s *AccountService) ConcernRemoveSubAccount(req types.ConcernRemoveSubAccountReq) *result.Result[bot.ConcernRemoveSubAccountResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.ConcernRemoveSubAccountResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.ConcernRemoveSubAccount(req.AdvertiserId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.ConcernRemoveSubAccountResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "取消收藏子账户",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，取消收藏子账户[%d]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// LoginOA OA系统登录
func (s *AccountService) LoginOA(username, password string) *result.Result[bot.OALoginResponse] {
	// 创建Bot实例
	b, err := bot.NewBot("")
	if err != nil {
		return result.ToError[bot.OALoginResponse](result.ErrorUserLogin.AddError(err))
	}

	// 调用bot包中的LoginOA方法
	resp, err := b.LoginOA(username, password)
	if err != nil {
		return result.ToError[bot.OALoginResponse](result.ErrorUserLogin.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   resp.User.Name,
				AccountId:     int64(resp.User.ID),
				Module:        constant.OperationLog_Module_Account,
				OperationType: constant.OperationLog_OperationType_Login,
				Operation:     "OA系统登录",
				Description:   fmt.Sprintf("OA登录成功，用户ID: %d，用户名: %s，部门: %s", resp.User.ID, resp.User.Name, resp.User.Department),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}
