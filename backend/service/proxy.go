package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// ProxyService 代理服务
type ProxyService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewProxyService 创建代理服务
func NewProxyService(svcCtx *svc.ServiceContext) *ProxyService {
	return &ProxyService{
		svcCtx: svcCtx,
	}
}
func (s *ProxyService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *ProxyService) SetApp(app *application.App) {
	s.app = app
}

// GetProxyList 获取代理列表
func (s *ProxyService) GetProxyList(req types.ProxyListReq) *result.Result[types.ProxyListResp] {
	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.IpAddress != "" {
		conditions["ip_address"] = req.IpAddress
	}
	if req.Type != "" {
		conditions["type"] = req.Type
	}
	if req.Status != 0 { // 状态为0表示不过滤
		conditions["status"] = req.Status
	}
	if req.Remark != "" {
		conditions["remark"] = req.Remark
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	proxies, total, err := s.svcCtx.ProxyModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.ProxyListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.ProxyListResp{
		Total:    total,
		List:     proxies,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetProxyById 根据ID获取代理详情
func (s *ProxyService) GetProxyById(id int64) *result.Result[model.Proxy] {
	proxy, err := s.svcCtx.ProxyModel.GetById(id)
	if err != nil {
		return result.ToError[model.Proxy](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult[model.Proxy](*proxy)
}

// verifyProxy 验证代理数据是否符合规范
func (s *ProxyService) verifyProxy(proxy *model.Proxy) error {
	// 验证代理类型是否合法
	if proxy.Type == "" {
		return errors.New("代理类型不能为空")
	}
	proxy.Type = strings.ToUpper(proxy.Type)
	if _, ok := constant.ValidProxyTypes[proxy.Type]; !ok {
		return errors.New("代理类型不合法，只支持http、https、socks4、socks5")
	}

	// 验证IP地址和端口
	if proxy.IpAddress == "" {
		return errors.New("IP地址不能为空")
	}

	return nil
}

// CreateProxy 创建代理
func (s *ProxyService) CreateProxy(proxy *model.Proxy) *result.Result[any] {
	// 验证代理数据
	if err := s.verifyProxy(proxy); err != nil {
		return result.ErrorAdd.AddError(err)
	}

	// 设置添加时间
	proxy.AddTime = time.Now()
	// 默认设置为禁用状态
	proxy.Status = constant.Proxy_Status_Disable

	// 检查代理是否已存在
	exist, err := s.svcCtx.ProxyModel.CheckProxyExist(proxy.IpAddress, proxy.Port)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}
	if exist {
		return result.ErrorAdd.AddMessage("代理已存在")
	}

	if err := s.svcCtx.ProxyModel.Create(proxy); err != nil {
		return result.ErrorAdd.AddError(err)
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        constant.OperationLog_Module_Proxy,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "新增代理",
				Description:   fmt.Sprintf("代理ID: %d，新增代理成功", proxy.Id),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SimpleResult("创建代理成功")
}

// UpdateProxy 更新代理
func (s *ProxyService) UpdateProxy(proxy *model.Proxy) *result.Result[any] {
	// 验证代理数据
	if err := s.verifyProxy(proxy); err != nil {
		return result.ErrorUpdate.AddError(err)
	}

	// 检查是否要将状态修改为启用
	if proxy.Status == constant.Proxy_Status_Enable {
		// 如果err不为空，说明代理不可用
		if err := testProxy(proxy); err != nil {
			proxy.Status = constant.Proxy_Status_Disable
			if err := s.svcCtx.ProxyModel.Update(nil, proxy); err != nil {
				return result.ErrorUpdate.AddError(err)
			}
			return result.ErrorUpdate.AddMessage("要启用代理，请先测试可用性").AddError(err)
		}
	}

	if err := s.svcCtx.ProxyModel.Save(nil, proxy); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        constant.OperationLog_Module_Proxy,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新代理",
				Description:   fmt.Sprintf("代理ID: %d，更新代理成功", proxy.Id),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SimpleResult("更新代理成功")
}

// DeleteProxy 删除代理
func (s *ProxyService) DeleteProxy(id int64) *result.Result[any] {
	proxy, err := s.svcCtx.ProxyModel.GetById(id)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}
	if err := s.svcCtx.ProxyModel.Delete(proxy); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        constant.OperationLog_Module_Proxy,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "删除代理",
				Description:   fmt.Sprintf("代理ID: %d，删除代理成功", proxy.Id),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SimpleResult("删除代理成功")
}

// BatchDeleteProxies 批量删除代理
func (s *ProxyService) BatchDeleteProxies(ids []int) *result.Result[any] {
	if err := s.svcCtx.ProxyModel.BatchDelete(ids); err != nil {
		return result.ErrorDelete.AddError(err)
	}
	return result.SimpleResult("批量删除代理成功")
}

// UpdateProxyStatus 更新代理状态
func (s *ProxyService) UpdateProxyStatus(id int64, status int) *result.Result[any] {
	// 如果要将状态修改为启用，需要先测试
	if status == constant.Proxy_Status_Enable {
		// 获取当前代理状态
		currentProxy, err := s.svcCtx.ProxyModel.GetById(id)
		if err != nil {
			return result.ErrorSelect.AddError(err)
		}
		// 如果err不为空，说明代理不可用
		if err := testProxy(currentProxy); err != nil {
			if err := s.svcCtx.ProxyModel.UpdateStatus(id, constant.Proxy_Status_Disable); err != nil {
				return result.ErrorUpdate.AddError(err)
			}
			return result.ErrorUpdate.AddMessage("要启用代理，请先测试可用性").AddError(err)
		}
	}

	if err := s.svcCtx.ProxyModel.UpdateStatus(id, status); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        constant.OperationLog_Module_Proxy,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新代理状态",
				Description:   fmt.Sprintf("代理ID: %d，更新代理状态成功", id),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SimpleResult("更新代理状态成功")
}

// ImportProxies 导入代理
func (s *ProxyService) ImportProxies(req types.ProxyImportReq) *result.Result[any] {
	if req.Content == "" {
		return result.ErrorAdd.AddMessage("导入内容不能为空")
	}

	// 解析代理内容
	proxyList := utils.ParseProxies(req.Content)
	if len(proxyList) == 0 {
		return result.ErrorAdd.AddMessage("未找到有效的代理")
	}

	// 计数器
	successCount := 0
	existCount := 0

	// 批量导入代理
	for _, p := range proxyList {
		// 如果未指定类型，使用请求中的类型
		if p.Type == "" && req.Type != "" {
			p.Type = req.Type
		}

		// 默认使用HTTP类型
		if p.Type == "" {
			p.Type = constant.Proxy_Type_HTTP
		}

		// 检查代理是否存在
		exist, err := s.svcCtx.ProxyModel.CheckProxyExist(p.IpAddress, p.Port)
		if err != nil {
			continue
		}
		if exist {
			existCount++
			continue
		}

		// 创建代理模型
		proxy := &model.Proxy{
			IpAddress: p.IpAddress,
			Port:      p.Port,
			Username:  p.Username,
			Password:  p.Password,
			Type:      p.Type,
			AddTime:   time.Now(),
			Status:    constant.Proxy_Status_Disable, // 默认设置为禁用状态，直到测试通过
			Remark:    p.Remark,
		}

		if err := s.svcCtx.ProxyModel.Create(proxy); err != nil {
			continue
		}
		successCount++
	}

	return result.SimpleResult("成功导入" + strconv.Itoa(successCount) + "个代理，" + strconv.Itoa(existCount) + "个代理已存在")
}

// testProxy 测试代理
func testProxy(proxy *model.Proxy) error {
	// 获取代理字符串
	parseUrl, err := url.Parse(proxy.FormattedProxy())
	if err != nil {
		return err
	}
	fmt.Println(proxy.FormattedProxy())
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(parseUrl),
		},
	}
	// 发送请求
	req, err := http.NewRequest(http.MethodGet, "https://myip.ipip.net/json", nil)
	if err != nil {
		return err
	}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}
