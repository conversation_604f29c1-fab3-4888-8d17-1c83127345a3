package service

import (
	"context"
	"fmt"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/interfaces"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// StrategyService 策略服务
type StrategyService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewStrategyService 创建策略服务
func NewStrategyService(svcCtx *svc.ServiceContext) *StrategyService {
	return &StrategyService{
		svcCtx: svcCtx,
	}
}

func (s *StrategyService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx

	// 自动启动策略引擎
	go func() {
		// 等待一小段时间确保应用完全启动
		time.Sleep(2 * time.Second)

		schedulerManager, err := s.getSchedulerManager()
		if err != nil {
			utils.LogStrategyError("自动启动策略引擎失败 - 获取调度器管理器失败", "error", err.Error())
			return
		}

		if err := schedulerManager.Start(ctx, s.app); err != nil {
			utils.LogStrategyError("自动启动策略引擎失败", "error", err.Error())
		} else {
			utils.LogStrategyInfo("策略引擎已自动启动")
		}
	}()

	return nil
}

// OnShutdown 应用关闭时的回调
func (s *StrategyService) OnShutdown(ctx context.Context) error {
	utils.LogStrategyInfo("应用关闭，正在停止策略引擎...")

	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		utils.LogStrategyError("停止策略引擎失败 - 获取调度器管理器失败", "error", err.Error())
		return err
	}

	if err := schedulerManager.Stop(); err != nil {
		utils.LogStrategyError("停止策略引擎失败", "error", err.Error())
		return err
	}

	utils.LogStrategyInfo("策略引擎已停止")
	return nil
}

func (s *StrategyService) SetApp(app *application.App) {
	s.app = app
}

// GetStrategyList 获取策略列表
func (s *StrategyService) GetStrategyList(req types.StrategyListReq) *result.Result[types.StrategyListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.Name != "" {
		conditions["name"] = req.Name
	}
	if req.Enabled != nil {
		conditions["enabled"] = *req.Enabled
	}
	if req.LogicType != "" {
		conditions["logic_type"] = req.LogicType
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	strategies, total, err := s.svcCtx.StrategyModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.StrategyListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.StrategyListResp{
		Total:    total,
		List:     strategies,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetStrategyById 根据ID获取策略详情
func (s *StrategyService) GetStrategyById(req types.StrategyDetailReq) *result.Result[types.StrategyDetailResp] {
	strategy, err := s.svcCtx.StrategyModel.GetById(req.ID)
	if err != nil {
		return result.ToError[types.StrategyDetailResp](result.ErrorSelect.AddError(err))
	}

	// 转换为详情响应
	resp, err := types.StrategyToDetailResp(strategy)
	if err != nil {
		return result.ToError[types.StrategyDetailResp](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult(*resp)
}

// CreateStrategy 创建策略
func (s *StrategyService) CreateStrategy(req types.CreateStrategyReq) *result.Result[model.Strategy] {
	// 验证请求数据
	if req.Name == "" {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("策略名称不能为空"))
	}
	if len(req.Conditions) == 0 {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("至少需要一个触发条件"))
	}
	if len(req.Actions) == 0 {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("至少需要一个触发动作"))
	}

	// 转换为数据库模型
	strategy, err := req.ToModel()
	if err != nil {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddError(err))
	}

	// 保存到数据库
	if err := s.svcCtx.StrategyModel.Create(strategy); err != nil {
		return result.ToError[model.Strategy](result.ErrorAdd.AddError(err))
	}

	// 自动刷新策略引擎
	go s.autoRefreshStrategyEngine("创建策略")

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "", // 策略管理暂时不需要账户信息
				AccountId:     0,
				Module:        "strategy", // 策略模块
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "创建策略",
				Description:   fmt.Sprintf("创建策略：%s", req.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*strategy)
}

// UpdateStrategy 更新策略
func (s *StrategyService) UpdateStrategy(req types.UpdateStrategyReq) *result.Result[model.Strategy] {
	// 验证请求数据
	if req.Name == "" {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("策略名称不能为空"))
	}
	if len(req.Conditions) == 0 {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("至少需要一个触发条件"))
	}
	if len(req.Actions) == 0 {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddMessage("至少需要一个触发动作"))
	}

	// 检查策略是否存在
	existingStrategy, err := s.svcCtx.StrategyModel.GetById(req.ID)
	if err != nil {
		return result.ToError[model.Strategy](result.ErrorSelect.AddError(err))
	}

	// 转换为数据库模型
	strategy, err := req.ToModel()
	if err != nil {
		return result.ToError[model.Strategy](result.ErrorReqParam.AddError(err))
	}

	// 保持原有的创建时间
	strategy.CreatedAt = existingStrategy.CreatedAt

	// 更新到数据库
	if err := s.svcCtx.StrategyModel.Update(nil, strategy); err != nil {
		return result.ToError[model.Strategy](result.ErrorUpdate.AddError(err))
	}

	// 自动刷新策略引擎
	go s.autoRefreshStrategyEngine("更新策略")

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy",
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新策略",
				Description:   fmt.Sprintf("更新策略：%s", req.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*strategy)
}

// DeleteStrategy 删除策略
func (s *StrategyService) DeleteStrategy(req types.DeleteStrategyReq) *result.Result[any] {
	// 获取策略信息用于日志
	strategy, err := s.svcCtx.StrategyModel.GetById(req.ID)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	// 删除策略
	if err := s.svcCtx.StrategyModel.Delete(strategy); err != nil {
		return result.ErrorDelete.AddError(err)
	}

	// 自动刷新策略引擎
	go s.autoRefreshStrategyEngine("删除策略")

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy",
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "删除策略",
				Description:   fmt.Sprintf("删除策略：%s", strategy.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("删除策略成功")
}

// BatchDeleteStrategies 批量删除策略
func (s *StrategyService) BatchDeleteStrategies(req types.BatchDeleteStrategiesReq) *result.Result[any] {
	if len(req.IDs) == 0 {
		return result.ErrorReqParam.AddMessage("请选择要删除的策略")
	}

	// 批量删除
	if err := s.svcCtx.StrategyModel.BatchDelete(req.IDs); err != nil {
		return result.ErrorDelete.AddError(err)
	}

	// 自动刷新策略引擎
	go s.autoRefreshStrategyEngine("批量删除策略")

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy",
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "批量删除策略",
				Description:   fmt.Sprintf("批量删除 %d 个策略", len(req.IDs)),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("批量删除策略成功")
}

// ToggleStrategyStatus 切换策略启用状态
func (s *StrategyService) ToggleStrategyStatus(req types.ToggleStrategyStatusReq) *result.Result[any] {
	// 获取策略信息用于日志
	strategy, err := s.svcCtx.StrategyModel.GetById(req.ID)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	// 切换状态
	if err := s.svcCtx.StrategyModel.ToggleEnabled(req.ID); err != nil {
		return result.ErrorUpdate.AddError(err)
	}

	// 自动刷新策略引擎
	go s.autoRefreshStrategyEngine("切换策略状态")

	// 添加操作日志
	statusText := "启用"
	if strategy.Enabled {
		statusText = "禁用"
	}

	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy",
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     statusText + "策略",
				Description:   fmt.Sprintf("%s策略：%s", statusText, strategy.Name),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult(fmt.Sprintf("%s策略成功", statusText))
}

// UpdateStrategyTriggerInfo 更新策略触发信息（由监控系统调用）
func (s *StrategyService) UpdateStrategyTriggerInfo(id int64) *result.Result[any] {
	if err := s.svcCtx.StrategyModel.UpdateTriggerInfo(id); err != nil {
		return result.ErrorUpdate.AddError(err)
	}
	return result.SimpleResult("更新触发信息成功")
}

// GetEnabledStrategies 获取所有启用的策略（供监控系统使用）
func (s *StrategyService) GetEnabledStrategies() *result.Result[[]*model.Strategy] {
	strategies, err := s.svcCtx.StrategyModel.GetEnabledStrategies()
	if err != nil {
		return result.ToError[[]*model.Strategy](result.ErrorSelect.AddError(err))
	}
	return result.SuccessResult(strategies)
}

// ExecuteStrategyNow 立即执行指定策略
func (s *StrategyService) ExecuteStrategyNow(strategyID int64) *result.Result[any] {
	// 检查策略是否存在
	strategy, err := s.svcCtx.StrategyModel.GetById(strategyID)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	if !strategy.Enabled {
		return result.ErrorReqParam.AddMessage("策略已禁用，无法执行")
	}

	// 获取调度器管理器
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	// 执行策略
	if err := schedulerManager.ExecuteStrategyNow(strategyID); err != nil {
		return result.ErrorExecute.AddError(err)
	}

	return result.SimpleResult("策略执行成功")
}

// GetStrategyEngineStatus 获取策略执行引擎状态
func (s *StrategyService) GetStrategyEngineStatus() *result.Result[map[string]interface{}] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.SuccessResult(map[string]interface{}{
			"running": false,
			"error":   err.Error(),
		})
	}

	status := schedulerManager.GetEngineStatus()
	return result.SuccessResult(status)
}

// StartStrategyEngine 启动策略执行引擎
func (s *StrategyService) StartStrategyEngine() *result.Result[any] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	if err := schedulerManager.Start(s.ctx, s.app); err != nil {
		return result.ErrorExecute.AddError(err)
	}

	return result.SimpleResult("策略执行引擎启动成功")
}

// StopStrategyEngine 停止策略执行引擎
func (s *StrategyService) StopStrategyEngine() *result.Result[any] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	if err := schedulerManager.Stop(); err != nil {
		return result.ErrorExecute.AddError(err)
	}

	return result.SimpleResult("策略执行引擎停止成功")
}

// RestartStrategyEngine 重启策略执行引擎
func (s *StrategyService) RestartStrategyEngine() *result.Result[any] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	// 先停止引擎
	if err := schedulerManager.Stop(); err != nil {
		return result.ErrorExecute.AddError(err)
	}

	// 等待一小段时间
	time.Sleep(1 * time.Second)

	// 重新启动引擎
	if err := schedulerManager.Start(s.ctx, s.app); err != nil {
		return result.ErrorExecute.AddError(err)
	}

	return result.SimpleResult("策略执行引擎重启成功")
}

// getSchedulerManager 获取调度器管理器（延迟初始化）
func (s *StrategyService) getSchedulerManager() (interfaces.SchedulerManager, error) {
	// 如果调度器管理器未初始化，则返回错误
	if s.svcCtx.SchedulerManager == nil {
		return nil, fmt.Errorf("调度器管理器未初始化，请先启动应用")
	}

	// 类型断言
	schedulerManager, ok := s.svcCtx.SchedulerManager.(interfaces.SchedulerManager)
	if !ok {
		return nil, fmt.Errorf("调度器管理器类型错误")
	}

	return schedulerManager, nil
}

// InitSchedulerManager 初始化调度器管理器
func (s *StrategyService) InitSchedulerManager() error {
	if s.svcCtx.SchedulerManager != nil {
		return nil // 已经初始化
	}

	// 这里我们需要动态创建调度器管理器
	// 由于循环导入的限制，我们暂时返回错误
	// 在实际应用中，这应该在应用启动时完成
	return fmt.Errorf("调度器管理器需要在应用启动时初始化")
}

// updateMonitorMetrics 立即更新监控指标
func (s *StrategyService) updateMonitorMetrics() {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return
	}

	// 强制更新监控指标
	schedulerManager.ForceUpdateMetrics()
}

// ForceCleanupStrategies 强制执行策略清理
func (s *StrategyService) ForceCleanupStrategies() *result.Result[any] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	// 尝试执行强制清理
	if cleanupable, ok := schedulerManager.(interface{ ForceCleanupStrategies() error }); ok {
		if err := cleanupable.ForceCleanupStrategies(); err != nil {
			return result.ErrorExecute.AddError(err)
		}
		return result.SimpleResult("策略清理执行成功")
	}

	return result.ErrorExecute.AddMessage("当前调度器不支持策略清理")
}

// GetCleanupStats 获取策略清理统计信息
func (s *StrategyService) GetCleanupStats() *result.Result[map[string]interface{}] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.SuccessResult(map[string]interface{}{
			"running": false,
			"error":   err.Error(),
		})
	}

	// 尝试获取清理统计
	if statable, ok := schedulerManager.(interface{ GetCleanupStats() interface{} }); ok {
		stats := statable.GetCleanupStats()
		return result.SuccessResult(map[string]interface{}{
			"stats": stats,
		})
	}

	return result.SuccessResult(map[string]interface{}{
		"error": "当前调度器不支持策略清理统计",
	})
}

// SetCleanupInterval 设置策略清理间隔
func (s *StrategyService) SetCleanupInterval(intervalMinutes int) *result.Result[any] {
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		return result.ErrorExecute.AddError(err)
	}

	// 验证间隔时间
	if intervalMinutes < 1 || intervalMinutes > 1440 { // 1分钟到24小时
		return result.ErrorReqParam.AddMessage("清理间隔必须在1-1440分钟之间")
	}

	interval := time.Duration(intervalMinutes) * time.Minute

	// 尝试设置清理间隔
	if intervalSettable, ok := schedulerManager.(interface{ SetCleanupInterval(time.Duration) error }); ok {
		if err := intervalSettable.SetCleanupInterval(interval); err != nil {
			return result.ErrorExecute.AddError(err)
		}
		return result.SimpleResult(fmt.Sprintf("策略清理间隔已设置为 %d 分钟", intervalMinutes))
	}

	return result.ErrorExecute.AddMessage("当前调度器不支持设置清理间隔")
}

// autoRefreshStrategyEngine 自动刷新策略引擎
func (s *StrategyService) autoRefreshStrategyEngine(operation string) {
	utils.LogStrategyInfo("自动刷新策略引擎", "operation", operation)

	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		utils.LogStrategyError("自动刷新策略引擎失败 - 获取调度器管理器失败", "error", err.Error())
		return
	}

	if err := schedulerManager.RefreshStrategies(); err != nil {
		utils.LogStrategyError("自动刷新策略引擎失败", "error", err.Error())
	} else {
		utils.LogStrategyInfo("策略引擎刷新成功", "operation", operation)
	}
}

// GetStrategySystemHealth 获取策略系统健康状态
func (s *StrategyService) GetStrategySystemHealth() *result.Result[map[string]interface{}] {
	healthData := make(map[string]interface{})

	// 检查调度器状态
	schedulerManager, err := s.getSchedulerManager()
	if err != nil {
		healthData["scheduler_status"] = "error"
		healthData["scheduler_error"] = err.Error()
	} else {
		if engineStatus, ok := schedulerManager.(interface{ GetEngineStatus() map[string]interface{} }); ok {
			healthData["scheduler_status"] = "running"
			healthData["engine_status"] = engineStatus.GetEngineStatus()
		} else {
			healthData["scheduler_status"] = "unknown"
		}
	}

	// 检查数据库连接
	if s.svcCtx.DB == nil {
		healthData["database_status"] = "error"
		healthData["database_error"] = "数据库连接为空"
	} else if sqlDB, err := s.svcCtx.DB.DB(); err != nil {
		healthData["database_status"] = "error"
		healthData["database_error"] = err.Error()
	} else if err := sqlDB.Ping(); err != nil {
		healthData["database_status"] = "error"
		healthData["database_error"] = err.Error()
	} else {
		healthData["database_status"] = "connected"
	}

	// 统计策略数据
	if s.svcCtx.StrategyModel != nil {
		totalStrategies, err := s.svcCtx.StrategyModel.CountAll()
		if err != nil {
			healthData["strategy_count_error"] = err.Error()
		} else {
			healthData["total_strategies"] = totalStrategies
		}

		enabledStrategies, err := s.svcCtx.StrategyModel.CountEnabled()
		if err != nil {
			healthData["enabled_strategy_count_error"] = err.Error()
		} else {
			healthData["enabled_strategies"] = enabledStrategies
		}
	}

	// 统计策略绑定数据
	if s.svcCtx.StrategyBindingModel != nil {
		totalBindings, err := s.svcCtx.StrategyBindingModel.CountAll()
		if err != nil {
			healthData["binding_count_error"] = err.Error()
		} else {
			healthData["total_bindings"] = totalBindings
		}

		enabledBindings, err := s.svcCtx.StrategyBindingModel.CountEnabled()
		if err != nil {
			healthData["enabled_binding_count_error"] = err.Error()
		} else {
			healthData["enabled_bindings"] = enabledBindings
		}
	}

	// 添加时间戳
	healthData["check_time"] = time.Now().Format("2006-01-02 15:04:05")

	// 生成健康状态总结
	overallHealth := "healthy"
	if healthData["database_status"] == "error" {
		overallHealth = "error"
	} else if healthData["scheduler_status"] == "error" {
		overallHealth = "warning"
	} else if healthData["total_strategies"] == 0 {
		overallHealth = "warning"
		healthData["warning_message"] = "没有配置任何策略"
	} else if healthData["enabled_strategies"] == 0 {
		overallHealth = "warning"
		healthData["warning_message"] = "没有启用的策略"
	} else if healthData["total_bindings"] == 0 {
		overallHealth = "warning"
		healthData["warning_message"] = "没有配置任何策略绑定"
	}

	healthData["overall_health"] = overallHealth

	return result.SuccessResult(healthData)
}

// DiagnoseStrategyIssues 诊断策略问题
func (s *StrategyService) DiagnoseStrategyIssues() *result.Result[map[string]interface{}] {
	diagnosis := make(map[string]interface{})
	issues := []string{}
	suggestions := []string{}

	// 检查策略数量
	totalStrategies, err := s.svcCtx.StrategyModel.CountAll()
	if err != nil {
		issues = append(issues, "无法获取策略总数: "+err.Error())
	} else if totalStrategies == 0 {
		issues = append(issues, "系统中没有任何策略")
		suggestions = append(suggestions, "请在策略管理页面创建策略")
	}

	// 检查启用的策略
	enabledStrategies, err := s.svcCtx.StrategyModel.CountEnabled()
	if err != nil {
		issues = append(issues, "无法获取启用策略数量: "+err.Error())
	} else if enabledStrategies == 0 && totalStrategies > 0 {
		issues = append(issues, "存在策略但都已禁用")
		suggestions = append(suggestions, "请在策略管理页面启用策略")
	}

	// 检查策略绑定
	totalBindings, err := s.svcCtx.StrategyBindingModel.CountAll()
	if err != nil {
		issues = append(issues, "无法获取策略绑定数量: "+err.Error())
	} else if totalBindings == 0 && enabledStrategies > 0 {
		issues = append(issues, "存在启用的策略但没有配置绑定")
		suggestions = append(suggestions, "请为策略绑定目标实体(广告主、项目或广告)")
	}

	// 检查调度器状态
	_, err = s.getSchedulerManager()
	if err != nil {
		issues = append(issues, "无法获取调度器管理器: "+err.Error())
		suggestions = append(suggestions, "检查系统初始化状态")
	}

	diagnosis["issues"] = issues
	diagnosis["suggestions"] = suggestions
	diagnosis["issue_count"] = len(issues)
	diagnosis["check_time"] = time.Now().Format("2006-01-02 15:04:05")

	// 如果没有问题，给出正面反馈
	if len(issues) == 0 {
		diagnosis["status"] = "healthy"
		diagnosis["message"] = "策略系统运行正常"
	} else {
		diagnosis["status"] = "issues_found"
		diagnosis["message"] = fmt.Sprintf("发现 %d 个问题需要处理", len(issues))
	}

	return result.SuccessResult(diagnosis)
}
