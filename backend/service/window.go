package service

import (
	"context"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
)

// WindowService 保存所有次级窗口句柄，并向前端暴露操作方法
type WindowService struct {
	windowMap map[string]*application.WebviewWindow
	reminder  *utils.ReminderUtil

	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewWindowService 创建操作日志服务
func NewWindowService(svcCtx *svc.ServiceContext) *WindowService {
	return &WindowService{
		svcCtx: svcCtx,
	}
}

func (s *WindowService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *WindowService) SetApp(app *application.App) {
	s.app = app
	s.reminder = utils.NewReminderUtil(app)
}

func (s *WindowService) SetWindowMap(windowMap map[string]*application.WebviewWindow) {
	s.windowMap = windowMap
}

func (s *WindowService) ShowWindow(window string) *result.Result[string] {
	if win, ok := s.windowMap[window]; ok {
		win.Show()
		return result.SuccessResult(window + "窗体已显示")
	} else {
		return result.ToError[string](result.ErrorNotFoundWindow.AddMessage(window))
	}
}

func (s *WindowService) HideWindow(window string) *result.Result[string] {
	if win, ok := s.windowMap[window]; ok {
		win.Hide()
		return result.SuccessResult(window + "窗体已隐藏")
	} else {
		return result.ToError[string](result.ErrorNotFoundWindow.AddMessage(window))
	}
}

// ShowReminder 显示提醒弹窗
func (s *WindowService) ShowReminder(title, message string) *result.Result[string] {
	return s.reminder.ShowReminder(title, message)
}

// ShowSuccessReminder 显示成功提醒
func (s *WindowService) ShowSuccessReminder(title, message string) *result.Result[string] {
	return s.reminder.ShowSuccessReminder(title, message)
}

// ShowWarningReminder 显示警告提醒
func (s *WindowService) ShowWarningReminder(title, message string) *result.Result[string] {
	return s.reminder.ShowWarningReminder(title, message)
}

// ShowErrorReminder 显示错误提醒
func (s *WindowService) ShowErrorReminder(title, message string) *result.Result[string] {
	return s.reminder.ShowErrorReminder(title, message)
}

// ShowInfoReminder 显示信息提醒
func (s *WindowService) ShowInfoReminder(title, message string) *result.Result[string] {
	return s.reminder.ShowInfoReminder(title, message)
}

// TestReminder 测试提醒弹窗功能
func (s *WindowService) TestReminder() *result.Result[string] {
	title := "🧪 测试提醒"
	message := "这是一个测试提醒弹窗\n\n如果您能看到这个弹窗，说明提醒功能正常工作。\n\n此弹窗将在10秒后自动关闭。"
	return s.reminder.ShowInfoReminder(title, message)
}

// CloseReminder 关闭提醒弹窗
func (s *WindowService) CloseReminder() {
	s.reminder.CloseReminder()
}

// CloseReminderWindow 关闭提醒窗口（供前端调用）
func (s *WindowService) CloseReminderWindow() *result.Result[string] {
	s.reminder.CloseReminder()
	return result.SuccessResult("提醒窗口已关闭")
}
