package service

import (
	"context"
	"fmt"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
)

// RetargetingTagService 项目可用人群包服务
type RetargetingTagService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewRetargetingTagService 创建项目可用人群包服务
func NewRetargetingTagService(svcCtx *svc.ServiceContext) *RetargetingTagService {
	return &RetargetingTagService{
		svcCtx: svcCtx,
	}
}

func (s *RetargetingTagService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *RetargetingTagService) SetApp(app *application.App) {
	s.app = app
}

// GetRetargetingTags 获取项目可用人群包列表
func (s *RetargetingTagService) GetRetargetingTags(advertiserId string, req *bot.GetRetargetingTagsReq) *result.Result[bot.GetRetargetingTagsResp] {
	// 这里需要根据实际情况获取账户信息
	// 暂时使用第一个可用的账户
	accounts, err := s.svcCtx.AccountModel.ListAll()
	if err != nil || len(accounts) == 0 {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorSelect.AddError(fmt.Errorf("没有可用的账户")))
	}

	account := accounts[0] // 使用第一个账户

	// 创建bot实例
	b, err := bot.NewBot("")
	if err != nil {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorUserLogin.AddError(err))
	}

	// 确保advertiserId是字符串类型
	advertiserIdStr := fmt.Sprintf("%v", advertiserId)

	// 调用bot获取项目可用人群包列表
	resp, err := b.GetRetargetingTags(advertiserIdStr, req, account.Cookie)
	if err != nil {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult(*resp)
}

// GetRetargetingTagsByAccount 根据账户ID获取项目可用人群包列表
func (s *RetargetingTagService) GetRetargetingTagsByAccount(accountId int64, advertiserId string) *result.Result[bot.GetRetargetingTagsResp] {
	// 根据账户ID获取账户信息
	account, err := s.svcCtx.AccountModel.GetByAccountId(accountId)
	if err != nil {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorSelect.AddError(err))
	}

	// 创建bot实例
	b, err := bot.NewBot("")
	if err != nil {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorUserLogin.AddError(err))
	}

	// 确保advertiserId是字符串类型
	advertiserIdStr := fmt.Sprintf("%v", advertiserId)

	// 调用bot获取项目可用人群包列表
	req := &bot.GetRetargetingTagsReq{}
	resp, err := b.GetRetargetingTags(advertiserIdStr, req, account.Cookie)
	if err != nil {
		return result.ToError[bot.GetRetargetingTagsResp](result.ErrorSelect.AddError(err))
	}

	return result.SuccessResult(*resp)
}
