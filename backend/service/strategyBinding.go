package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gorm.io/gorm"
)

// StrategyBindingService 策略绑定服务
type StrategyBindingService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewStrategyBindingService 创建策略绑定服务
func NewStrategyBindingService(svcCtx *svc.ServiceContext) *StrategyBindingService {
	return &StrategyBindingService{
		svcCtx: svcCtx,
	}
}

func (s *StrategyBindingService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *StrategyBindingService) SetApp(app *application.App) {
	s.app = app
}

// GetStrategyBindingList 获取策略绑定列表
func (s *StrategyBindingService) GetStrategyBindingList(req types.StrategyBindingListReq) *result.Result[types.StrategyBindingListResp] {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.StrategyID > 0 {
		conditions["strategy_id"] = req.StrategyID
	}
	if req.BindingType != "" {
		conditions["binding_type"] = req.BindingType
	}
	if req.BindingID != "" {
		conditions["binding_id"] = req.BindingID
	}
	if req.BindingName != "" {
		conditions["binding_name"] = req.BindingName
	}
	if req.Enabled != nil {
		conditions["enabled"] = *req.Enabled
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	bindings, total, err := s.svcCtx.StrategyBindingModel.List(req.Page, req.PageSize, conditions)
	if err != nil {
		return result.ToError[types.StrategyBindingListResp](result.ErrorSelect.AddError(err))
	}

	resp := types.StrategyBindingListResp{
		Total:    total,
		List:     bindings,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetStrategyBindingById 根据ID获取策略绑定详情
func (s *StrategyBindingService) GetStrategyBindingById(req types.StrategyBindingDetailReq) *result.Result[types.StrategyBindingDetailResp] {
	binding, err := s.svcCtx.StrategyBindingModel.GetById(req.ID)
	if err != nil {
		return result.ToError[types.StrategyBindingDetailResp](result.ErrorSelect.AddError(err))
	}

	// 获取策略名称
	strategy, err := s.svcCtx.StrategyModel.GetById(binding.StrategyID)
	strategyName := ""
	if err == nil {
		strategyName = strategy.Name
	}

	resp := types.StrategyBindingDetailResp{
		StrategyBinding: binding,
		StrategyName:    strategyName,
	}

	return result.SuccessResult(resp)
}

// CreateStrategyBinding 创建策略绑定
func (s *StrategyBindingService) CreateStrategyBinding(req types.CreateStrategyBindingReq) *result.Result[model.StrategyBinding] {
	// 验证请求数据
	if req.StrategyID <= 0 {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("策略ID不能为空"))
	}
	if req.BindingType == "" {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定类型不能为空"))
	}
	if req.BindingID == "" {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定实体ID不能为空"))
	}

	// 验证绑定类型
	if req.BindingType != "advertiser" && req.BindingType != "project" && req.BindingType != "promotion" {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定类型必须是 advertiser、project 或 promotion"))
	}

	// 检查策略是否存在
	_, err := s.svcCtx.StrategyModel.GetById(req.StrategyID)
	if err != nil {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("策略不存在"))
	}

	// 检查是否已存在相同的绑定
	existingBindings, err := s.svcCtx.StrategyBindingModel.GetByBindingTypeAndID(req.BindingType, req.BindingID)
	if err == nil {
		for _, existing := range existingBindings {
			if existing.StrategyID == req.StrategyID {
				return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("该策略已绑定到此实体"))
			}
		}
	}

	// 转换为数据库模型
	binding := req.ToModel()

	// 保存到数据库
	if err := s.svcCtx.StrategyBindingModel.Create(binding); err != nil {
		return result.ToError[model.StrategyBinding](result.ErrorAdd.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "创建策略绑定",
				Description:   fmt.Sprintf("将策略绑定到%s: %s", req.BindingType, req.BindingName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*binding)
}

// UpdateStrategyBinding 更新策略绑定
func (s *StrategyBindingService) UpdateStrategyBinding(req types.UpdateStrategyBindingReq) *result.Result[model.StrategyBinding] {
	// 验证请求数据
	if req.ID <= 0 {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定ID不能为空"))
	}
	if req.StrategyID <= 0 {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("策略ID不能为空"))
	}
	if req.BindingType == "" {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定类型不能为空"))
	}
	if req.BindingID == "" {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("绑定实体ID不能为空"))
	}

	// 检查绑定是否存在
	existingBinding, err := s.svcCtx.StrategyBindingModel.GetById(req.ID)
	if err != nil {
		return result.ToError[model.StrategyBinding](result.ErrorSelect.AddError(err))
	}

	// 检查策略是否存在
	_, err = s.svcCtx.StrategyModel.GetById(req.StrategyID)
	if err != nil {
		return result.ToError[model.StrategyBinding](result.ErrorReqParam.AddMessage("策略不存在"))
	}

	// 转换为数据库模型
	binding := req.ToModel()

	// 保持原有的创建时间
	binding.CreatedAt = existingBinding.CreatedAt

	// 更新到数据库
	if err := s.svcCtx.StrategyBindingModel.Update(nil, binding); err != nil {
		return result.ToError[model.StrategyBinding](result.ErrorUpdate.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新策略绑定",
				Description:   fmt.Sprintf("更新策略绑定: %s", req.BindingName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*binding)
}

// DeleteStrategyBinding 删除策略绑定
func (s *StrategyBindingService) DeleteStrategyBinding(req types.DeleteStrategyBindingReq) *result.Result[any] {
	// 获取绑定信息用于日志
	binding, err := s.svcCtx.StrategyBindingModel.GetById(req.ID)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	// 删除绑定
	if err := s.svcCtx.StrategyBindingModel.Delete(binding); err != nil {
		return result.ErrorDelete.AddError(err)
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "删除策略绑定",
				Description:   fmt.Sprintf("删除策略绑定: %s", binding.BindingName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("删除策略绑定成功")
}

// BatchDeleteStrategyBindings 批量删除策略绑定
func (s *StrategyBindingService) BatchDeleteStrategyBindings(req types.BatchDeleteStrategyBindingsReq) *result.Result[any] {
	if len(req.IDs) == 0 {
		return result.ErrorReqParam.AddMessage("请选择要删除的策略绑定")
	}

	// 批量删除
	if err := s.svcCtx.StrategyBindingModel.BatchDelete(req.IDs); err != nil {
		return result.ErrorDelete.AddError(err)
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "批量删除策略绑定",
				Description:   fmt.Sprintf("批量删除 %d 个策略绑定", len(req.IDs)),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult("批量删除策略绑定成功")
}

// ToggleStrategyBindingStatus 切换策略绑定启用状态
func (s *StrategyBindingService) ToggleStrategyBindingStatus(req types.ToggleStrategyBindingStatusReq) *result.Result[any] {
	// 获取绑定信息用于日志
	binding, err := s.svcCtx.StrategyBindingModel.GetById(req.ID)
	if err != nil {
		return result.ErrorSelect.AddError(err)
	}

	// 切换状态
	if err := s.svcCtx.StrategyBindingModel.ToggleEnabled(req.ID); err != nil {
		return result.ErrorUpdate.AddError(err)
	}

	// 添加操作日志
	statusText := "启用"
	if binding.Enabled {
		statusText = "禁用"
	}

	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     statusText + "策略绑定",
				Description:   fmt.Sprintf("%s策略绑定：%s", statusText, binding.BindingName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult(fmt.Sprintf("%s策略绑定成功", statusText))
}

// GetStrategiesByBinding 根据绑定实体获取策略列表
func (s *StrategyBindingService) GetStrategiesByBinding(req types.GetStrategiesByBindingReq) *result.Result[types.GetStrategiesByBindingResp] {
	if req.BindingType == "" {
		return result.ToError[types.GetStrategiesByBindingResp](result.ErrorReqParam.AddMessage("绑定类型不能为空"))
	}
	if req.BindingID == "" {
		return result.ToError[types.GetStrategiesByBindingResp](result.ErrorReqParam.AddMessage("绑定实体ID不能为空"))
	}

	strategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding(req.BindingType, req.BindingID)
	if err != nil {
		return result.ToError[types.GetStrategiesByBindingResp](result.ErrorSelect.AddError(err))
	}

	resp := types.GetStrategiesByBindingResp{
		Strategies: strategies,
	}

	return result.SuccessResult(resp)
}

// BindStrategyToEntity 绑定策略到实体
func (s *StrategyBindingService) BindStrategyToEntity(req types.BindStrategyToEntityReq) *result.Result[any] {
	if len(req.StrategyIDs) == 0 {
		return result.ErrorReqParam.AddMessage("请选择要绑定的策略")
	}
	if req.BindingType == "" {
		return result.ErrorReqParam.AddMessage("绑定类型不能为空")
	}
	if req.BindingID == "" {
		return result.ErrorReqParam.AddMessage("绑定实体ID不能为空")
	}

	// 验证绑定类型
	if req.BindingType != "advertiser" && req.BindingType != "project" && req.BindingType != "promotion" {
		return result.ErrorReqParam.AddMessage("绑定类型必须是 advertiser、project 或 promotion")
	}

	// 开启事务
	err := s.svcCtx.DB.Transaction(func(tx *gorm.DB) error {
		for _, strategyID := range req.StrategyIDs {
			// 检查策略是否存在
			_, err := s.svcCtx.StrategyModel.GetById(strategyID)
			if err != nil {
				return fmt.Errorf("策略ID %d 不存在", strategyID)
			}

			// 检查是否已存在相同的绑定
			existingBindings, err := s.svcCtx.StrategyBindingModel.GetByBindingTypeAndID(req.BindingType, req.BindingID)
			if err == nil {
				for _, existing := range existingBindings {
					if existing.StrategyID == strategyID {
						return fmt.Errorf("策略ID %d 已绑定到此实体", strategyID)
					}
				}
			}

			// 创建绑定
			binding := &model.StrategyBinding{
				StrategyID:  strategyID,
				BindingType: req.BindingType,
				BindingID:   req.BindingID,
				BindingName: req.BindingName,
				Enabled:     true,
				Priority:    req.Priority,
				Description: req.Description,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}

			if err := s.svcCtx.StrategyBindingModel.Create(binding); err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return result.ErrorAdd.AddError(err)
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "批量绑定策略",
				Description:   fmt.Sprintf("将 %d 个策略绑定到%s: %s", len(req.StrategyIDs), req.BindingType, req.BindingName),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SimpleResult(fmt.Sprintf("成功绑定 %d 个策略", len(req.StrategyIDs)))
}

// BatchBindStrategyToEntities 批量绑定策略到多个实体
func (s *StrategyBindingService) BatchBindStrategyToEntities(req types.BatchBindStrategyToEntitiesReq) *result.Result[any] {
	if len(req.StrategyIDs) == 0 {
		return result.ErrorReqParam.AddMessage("请选择要绑定的策略")
	}
	if req.BindingType == "" {
		return result.ErrorReqParam.AddMessage("绑定类型不能为空")
	}
	if len(req.Entities) == 0 {
		return result.ErrorReqParam.AddMessage("请选择要绑定的实体")
	}

	// 验证绑定类型
	if req.BindingType != "advertiser" && req.BindingType != "project" && req.BindingType != "promotion" {
		return result.ErrorReqParam.AddMessage("绑定类型必须是 advertiser、project 或 promotion")
	}

	// 统计绑定结果
	var successCount int
	var skippedCount int
	var errorMessages []string

	// 开启事务
	err := s.svcCtx.DB.Transaction(func(tx *gorm.DB) error {
		for _, entity := range req.Entities {
			for _, strategyID := range req.StrategyIDs {
				// 检查策略是否存在
				_, err := s.svcCtx.StrategyModel.GetById(strategyID)
				if err != nil {
					errorMessages = append(errorMessages, fmt.Sprintf("策略ID %d 不存在", strategyID))
					continue
				}

				// 检查是否已存在相同的绑定
				existingBindings, err := s.svcCtx.StrategyBindingModel.GetByBindingTypeAndID(req.BindingType, entity.BindingID)
				if err == nil {
					alreadyBound := false
					for _, existing := range existingBindings {
						if existing.StrategyID == strategyID {
							alreadyBound = true
							skippedCount++
							break
						}
					}
					if alreadyBound {
						continue // 跳过已绑定的策略
					}
				}

				// 创建绑定
				binding := &model.StrategyBinding{
					StrategyID:  strategyID,
					BindingType: req.BindingType,
					BindingID:   entity.BindingID,
					BindingName: entity.BindingName,
					Enabled:     true,
					Priority:    req.Priority,
					Description: req.Description,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				if err := s.svcCtx.StrategyBindingModel.Create(binding); err != nil {
					errorMessages = append(errorMessages, fmt.Sprintf("创建绑定失败: 策略ID %d -> %s: %v", strategyID, entity.BindingName, err))
					continue
				}
				successCount++
			}
		}
		return nil
	})

	if err != nil {
		return result.ErrorAdd.AddError(err)
	}

	// 构建结果消息
	var resultMsg string
	if successCount > 0 {
		resultMsg = fmt.Sprintf("成功绑定 %d 个策略", successCount)
		if skippedCount > 0 {
			resultMsg += fmt.Sprintf("，跳过 %d 个已绑定的策略", skippedCount)
		}
	} else {
		resultMsg = "没有新的策略绑定"
		if skippedCount > 0 {
			resultMsg += fmt.Sprintf("，跳过 %d 个已绑定的策略", skippedCount)
		}
	}

	// 如果有错误信息，添加到结果中
	if len(errorMessages) > 0 {
		resultMsg += fmt.Sprintf("，%d 个绑定失败", len(errorMessages))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "批量绑定策略到多个实体",
				Description:   fmt.Sprintf("将 %d 个策略绑定到 %d 个%s，成功 %d 个，跳过 %d 个", len(req.StrategyIDs), len(req.Entities), req.BindingType, successCount, skippedCount),
				OperationTime: time.Now(),
			})
		}
	}()

	// 如果有成功绑定的，返回成功结果
	if successCount > 0 {
		return result.SimpleResult(resultMsg)
	}

	// 如果全部跳过或失败，返回警告结果
	return result.NewResult[any](1, resultMsg, nil)
}

// UnbindStrategyFromEntity 从实体解绑策略
func (s *StrategyBindingService) UnbindStrategyFromEntity(req types.UnbindStrategyFromEntityReq) *result.Result[any] {
	if req.BindingType == "" {
		return result.ErrorReqParam.AddMessage("绑定类型不能为空")
	}
	if req.BindingID == "" {
		return result.ErrorReqParam.AddMessage("绑定实体ID不能为空")
	}

	// 开启事务
	err := s.svcCtx.DB.Transaction(func(tx *gorm.DB) error {
		// 如果策略ID数组为空，表示解绑该实体的所有策略
		if len(req.StrategyIDs) == 0 {
			// 删除该实体的所有策略绑定
			if err := s.svcCtx.StrategyBindingModel.DeleteByBinding(req.BindingType, req.BindingID); err != nil {
				return err
			}
		} else {
			// 解绑指定的策略
			for _, strategyID := range req.StrategyIDs {
				// 查找并删除绑定
				bindings, err := s.svcCtx.StrategyBindingModel.GetByBindingTypeAndID(req.BindingType, req.BindingID)
				if err != nil {
					continue
				}

				for _, binding := range bindings {
					if binding.StrategyID == strategyID {
						if err := s.svcCtx.StrategyBindingModel.Delete(binding); err != nil {
							return err
						}
						break
					}
				}
			}
		}
		return nil
	})

	if err != nil {
		return result.ErrorDelete.AddError(err)
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			operation := "批量解绑策略"
			description := ""
			if len(req.StrategyIDs) == 0 {
				operation = "清空策略绑定"
				description = fmt.Sprintf("清空%s的所有策略绑定", req.BindingType)
			} else {
				description = fmt.Sprintf("从%s解绑 %d 个策略", req.BindingType, len(req.StrategyIDs))
			}

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     0,
				Module:        "strategy_binding",
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     operation,
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	if len(req.StrategyIDs) == 0 {
		return result.SimpleResult("已清空所有策略绑定")
	}
	return result.SimpleResult(fmt.Sprintf("成功解绑 %d 个策略", len(req.StrategyIDs)))
}

// GetInheritedStrategiesByBinding 获取实体的所有策略（包括继承的策略）
func (s *StrategyBindingService) GetInheritedStrategiesByBinding(req types.GetInheritedStrategiesReq) *result.Result[types.InheritedStrategiesResp] {
	// 验证请求参数
	if req.BindingType == "" {
		return result.ToError[types.InheritedStrategiesResp](result.ErrorReqParam.AddMessage("绑定类型不能为空"))
	}
	if req.BindingID == "" {
		return result.ToError[types.InheritedStrategiesResp](result.ErrorReqParam.AddMessage("绑定实体ID不能为空"))
	}

	resp := types.InheritedStrategiesResp{
		DirectStrategies:    []*types.StrategyWithSource{},
		InheritedStrategies: []*types.StrategyWithSource{},
		AllStrategies:       []*types.StrategyWithSource{},
	}

	// 1. 获取直接绑定的策略
	directStrategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding(req.BindingType, req.BindingID)
	if err != nil {
		return result.ToError[types.InheritedStrategiesResp](result.ErrorSelect.AddError(err))
	}

	// 转换为带来源的策略对象
	for _, strategy := range directStrategies {
		strategyWithSource := &types.StrategyWithSource{
			Strategy:   strategy,
			Source:     "direct",
			SourceType: req.BindingType,
			SourceID:   req.BindingID,
			SourceName: req.BindingName,
		}
		resp.DirectStrategies = append(resp.DirectStrategies, strategyWithSource)
		resp.AllStrategies = append(resp.AllStrategies, strategyWithSource)
	}

	// 2. 获取继承的策略
	inheritedStrategies := s.getInheritedStrategiesInternal(req.BindingType, req.BindingID, req.AdvertiserID, req.ProjectID)
	resp.InheritedStrategies = inheritedStrategies
	resp.AllStrategies = append(resp.AllStrategies, inheritedStrategies...)

	return result.SuccessResult(resp)
}

// getInheritedStrategiesInternal 内部方法：获取继承的策略
func (s *StrategyBindingService) getInheritedStrategiesInternal(bindingType, bindingID, advertiserID, projectID string) []*types.StrategyWithSource {
	var inheritedStrategies []*types.StrategyWithSource

	switch bindingType {
	case "promotion":
		// 广告继承项目和广告主的策略

		// 继承项目策略
		if projectID != "" {
			projectStrategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("project", projectID)
			if err == nil {
				for _, strategy := range projectStrategies {
					// 获取项目信息用于显示
					project, err := s.svcCtx.ProjectModel.GetByProjectId(projectID)
					sourceName := fmt.Sprintf("项目#%s", projectID)
					if err == nil && project != nil {
						sourceName = project.ProjectName
					}

					strategyWithSource := &types.StrategyWithSource{
						Strategy:   strategy,
						Source:     "inherited",
						SourceType: "project",
						SourceID:   projectID,
						SourceName: sourceName,
					}
					inheritedStrategies = append(inheritedStrategies, strategyWithSource)
				}
			}
		}

		// 继承广告主策略（全局策略）
		if advertiserID != "" {
			advertiserStrategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("advertiser", advertiserID)
			if err == nil {
				for _, strategy := range advertiserStrategies {
					// 只有全局策略才会影响到广告
					if strategy.Type == "global" {
						// 获取广告主信息用于显示
						advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(parseAdvertiserID(advertiserID))
						sourceName := fmt.Sprintf("广告主#%s", advertiserID)
						if err == nil && advertiser != nil {
							sourceName = advertiser.AdvertiserName
						}

						strategyWithSource := &types.StrategyWithSource{
							Strategy:   strategy,
							Source:     "inherited",
							SourceType: "advertiser",
							SourceID:   advertiserID,
							SourceName: sourceName,
						}
						inheritedStrategies = append(inheritedStrategies, strategyWithSource)
					}
				}
			}
		}

	case "project":
		// 项目继承广告主的策略（只有全局策略）
		if advertiserID != "" {
			advertiserStrategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("advertiser", advertiserID)
			if err == nil {
				for _, strategy := range advertiserStrategies {
					// 只有全局策略才会影响到项目
					if strategy.Type == "global" {
						// 获取广告主信息用于显示
						advertiser, err := s.svcCtx.AdvertiserModel.GetByAdvertiserId(parseAdvertiserID(advertiserID))
						sourceName := fmt.Sprintf("广告主#%s", advertiserID)
						if err == nil && advertiser != nil {
							sourceName = advertiser.AdvertiserName
						}

						strategyWithSource := &types.StrategyWithSource{
							Strategy:   strategy,
							Source:     "inherited",
							SourceType: "advertiser",
							SourceID:   advertiserID,
							SourceName: sourceName,
						}
						inheritedStrategies = append(inheritedStrategies, strategyWithSource)
					}
				}
			}
		}

	case "advertiser":
		// 广告主不继承任何策略
		break
	}

	return inheritedStrategies
}

// parseAdvertiserID 解析广告主ID
func parseAdvertiserID(advertiserIDStr string) int64 {
	if advertiserIDStr == "" {
		return 0
	}

	advertiserID, err := strconv.ParseInt(advertiserIDStr, 10, 64)
	if err != nil {
		return 0
	}
	return advertiserID
}
