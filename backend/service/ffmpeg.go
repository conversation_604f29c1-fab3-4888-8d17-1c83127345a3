package service

import (
	"context"
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/config"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/ffmpeg"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// FfmpegService 视频加工服务
type FfmpegService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewFfmpegService 创建视频加工服务
func NewFfmpegService(svcCtx *svc.ServiceContext) *FfmpegService {
	return &FfmpegService{
		svcCtx: svcCtx,
	}
}

func (s *FfmpegService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}

func (s *FfmpegService) SetApp(app *application.App) {
	s.app = app
}

// SetVideoCover 设置视频封面
func (s *FfmpegService) SetVideoCover(req types.SetVideoCoverReq) *result.Result[string] {
	videoPath, err := ffmpeg.StartGenHasCoverVideo(s.ctx, req.VideoFile, req.CoverFile)
	if err != nil {
		return result.ToError[string](result.ErrorFfmpeg.AddError(err))
	}
	// 打开文件所在目录
	go func() {
		ctx, _ := context.WithTimeout(context.Background(), 2*time.Second)
		if err := utils.OpenFolder(ctx, filepath.Dir(videoPath)); err != nil {
			log.Printf("打开文件所在目录失败: %v", err)
		}
	}()
	return result.SuccessResult[string](videoPath)
}

// SetTextInfo 设置视频文本信息
func (s *FfmpegService) SetTextInfo(req types.SetTextInfoReq) *result.Result[[]string] {
	videoPathList := make([]string, 0, len(req.TextInfoList))
	for i, textInfo := range req.TextInfoList {
		req.TextInfoList[i].FontFile = utils.ToFontFile(req.TextInfoList[i].FontFile)
		videoPath, err := ffmpeg.StartGenSingleVideo(s.ctx, req.VideoFile, textInfo, req.IsOriginSound, req.Width, req.Height, req.ScaleMode)
		if err != nil {
			return result.ToError[[]string](result.ErrorFfmpeg.AddError(err))
		}
		videoPathList = append(videoPathList, videoPath)
	}
	return result.SuccessResult[[]string](videoPathList)
}

// GenerateVideoVariations 生成视频变体
func (s *FfmpegService) GenerateVideoVariations(req types.VideoVariationReq) *result.Result[types.VideoVariationResp] {
	// 验证输入参数
	if req.Count <= 0 {
		return result.ToError[types.VideoVariationResp](result.ErrorDataVerify.AddError(fmt.Errorf("生成数量必须大于0")))
	}
	if req.Count > 100 {
		return result.ToError[types.VideoVariationResp](result.ErrorDataVerify.AddError(fmt.Errorf("生成数量不能超过100")))
	}
	// 获取用户下载目录
	req.OutputDir = config.GetConfig().Storage.DownloadDir
	// 确保输出目录存在
	if err := os.MkdirAll(req.OutputDir, 0755); err != nil {
		return result.ToError[types.VideoVariationResp](result.ErrorDirNotExist.AddError(err))
	}

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 生成视频变体
	var videoPaths []string
	var failedPaths []string

	for i := 0; i < req.Count; i++ {
		// 生成输出文件路径
		outputPath := filepath.Join(req.OutputDir, fmt.Sprintf("%s_%d.mp4", strings.TrimSuffix(filepath.Base(req.VideoFile), filepath.Ext(req.VideoFile)), ffmpeg.GetNoRepeatId()))

		// 随机选择效果
		selectedEffects := s.selectRandomEffects(req.Effects)

		// 生成视频变体
		err := ffmpeg.GenerateVideoVariation(s.ctx, ffmpeg.VideoVariationOptions{
			InputPath:     req.VideoFile,
			OutputPath:    outputPath,
			AspectRatio:   string(req.AspectRatio),
			Width:         req.Resolution.Width,
			Height:        req.Resolution.Height,
			Effects:       selectedEffects,
			IsOriginSound: req.IsOriginSound,
		})

		if err != nil {
			failedPaths = append(failedPaths, outputPath)
			continue
		}

		videoPaths = append(videoPaths, outputPath)
		s.app.EmitEvent(constant.Runtime_Event_VideoVariationProgress, types.VideoVariationProgress{
			VideoFile: req.VideoFile,
			Progress:  0,
			Count:     req.Count,
			GenCount:  i + 1,
			Message:   fmt.Sprintf("已生成 %d/%d 个变体", i+1, req.Count),
			VideoPath: outputPath,
		})
	}

	// 构建响应
	resp := types.VideoVariationResp{
		Success:     len(videoPaths) > 0,
		Message:     fmt.Sprintf("成功生成 %d 个视频变体，失败 %d 个", len(videoPaths), len(failedPaths)),
		VideoPaths:  videoPaths,
		FailedPaths: failedPaths,
	}
	return result.SuccessResult(resp)
}

// selectRandomEffects 随机选择效果
func (s *FfmpegService) selectRandomEffects(availableEffects []types.VideoEffect) []types.VideoEffect {
	if len(availableEffects) == 0 {
		return nil
	}

	// 随机选择1-3个效果
	numEffects := rand.Intn(3) + 1
	if numEffects > len(availableEffects) {
		numEffects = len(availableEffects)
	}

	// 打乱效果顺序
	effects := make([]types.VideoEffect, len(availableEffects))
	copy(effects, availableEffects)
	rand.Shuffle(len(effects), func(i, j int) {
		effects[i], effects[j] = effects[j], effects[i]
	})

	return effects[:numEffects]
}

// BatchGetVideoInfo 批量获取视频信息
func (s *FfmpegService) BatchGetVideoInfo(req types.BatchGetVideoInfoReq) *result.Result[[]*utils.FfmpegVideoInfo] {
	videoInfos := make([]*utils.FfmpegVideoInfo, 0, len(req.VideoFileList))

	for i, videoFile := range req.VideoFileList {
		info, err := utils.GetVideoInfo(videoFile)
		if err != nil {
			log.Printf("获取视频信息失败 [%d/%d] %s: %v", i+1, len(req.VideoFileList), videoFile, err)
			// 创建默认信息，但包含文件名
			fileName := filepath.Base(videoFile)
			if fileName == "" || fileName == "." {
				fileName = "未知文件"
			}
			info = &utils.FfmpegVideoInfo{
				Path:     videoFile,
				Name:     fileName,
				Format:   "未知",
				Size:     0,
				Width:    0,
				Height:   0,
				Duration: 0,
			}
		} else {
			log.Printf("成功获取视频信息 [%d/%d] %s: %dx%d, %.2fs, %.2fMB",
				i+1, len(req.VideoFileList), info.Name, info.Width, info.Height, info.Duration, info.Size)
		}
		videoInfos = append(videoInfos, info)
	}

	return result.SuccessResult[[]*utils.FfmpegVideoInfo](videoInfos)
}

//ffmpeg -i "C:\Users\<USER>\Videos\1月14日(1)_clones\1月14日(1)_微调_1.mp4" -c:v libx264 -preset medium -crf 23 -vf scale=1920:1080 -filter_complex "[0:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,unsharp=3:3:1.5,eq=saturation=0.903848,scale='floor(iw/2)*2:floor(ih/2)*2'[v]" -c:a aac -b:a 128k -map [v] -map 0:a -y "C:\Users\<USER>\Downloads\variation_1.mp4"
