package constant

const (
	Proxy_Status_Disable = 1 //禁用
	Proxy_Status_Enable  = 2 //启用

	// 代理类型常量
	Proxy_Type_HTTP   = "HTTP"   // HTTP代理
	Proxy_Type_HTTPS  = "HTTPS"  // HTTPS代理
	Proxy_Type_SOCKS4 = "SOCKS4" // SOCKS4代理
	Proxy_Type_SOCKS5 = "SOCKS5" // SOCKS5代理

	CameraGroup_IsOriginSound_Yes = 0 // 原声
	CameraGroup_IsOriginSound_No  = 1 // 非原声

	ClipTask_ScaleMode_WidthFix    = "WidthFix"
	ClipTask_ScaleMode_HeightFix   = "HeightFix"
	ClipTask_ScaleMode_ScaleToFill = "ScaleToFill"

	Account_LoginType_Email  = "email"  // 邮箱登录
	Account_LoginType_Sms    = "sms"    // 短信登录
	Account_LoginType_Cookie = "cookie" // Cookie登录

	OperationLog_Module_Account    = "account"    // 账号模块
	OperationLog_Module_Advertiser = "advertiser" // 广告主模块
	OperationLog_Module_Project    = "project"    // 项目模块
	OperationLog_Module_Promontion = "promotion"  // 广告模块
	OperationLog_Module_Proxy      = "proxy"      // 代理模块
	OperationLog_Module_System     = "system"     // 系统模块

	OperationLog_OperationType_Add      = "add"       // 添加
	OperationLog_OperationType_Update   = "update"    // 更新
	OperationLog_OperationType_Delete   = "delete"    // 删除
	OperationLog_OperationType_Select   = "select"    // 查询
	OperationLog_OperationType_Login    = "login"     // 登录
	OperationLog_OperationType_Logout   = "logout"    // 登出
	OperationLog_OperationType_SendCode = "send_code" // 发送验证码
	OperationLog_OperationType_Download = "download"  // 下载

	Runtime_Event_OperationLog           = "operation_log"            // 运行时事件-操作日志
	Runtime_Event_VideoVariationProgress = "video_variation_progress" // 视频变体生成进度事件
	Runtime_Event_UploadVideo            = "upload_video"             // 上传视频进度事件
	Runtime_Event_OpenFolder             = "open_folder"              // 打开文件夹事件
	Runtime_Event_MainProcess            = "main_process"             // 主窗体进度事件

	ClipTask_GenStatus_Ready      = 1 // 准备
	ClipTask_GenStatus_Generating = 2 // 生成中

	ClipTask_MusicMixMode_Mixd    = 1 // 混合
	ClipTask_MusicMixMode_OnlyBgm = 2 // 仅BGM
	ClipTask_MusicMixMode_NoBgm   = 3 // 无BGM

	// 广告状态相关常量
	// 广告状态码 - 巨量引擎API返回的状态码
	PromotionStatus_Active            = 0  // 投放中
	PromotionStatus_Paused            = 61 // 已暂停
	PromotionStatus_AuditFailed       = 41 // 审核不通过
	PromotionStatus_InsufficientFunds = 43 // 账户余额不足
	PromotionStatus_AuditPending      = 22 // 新建审核中
	PromotionStatus_Terminated        = 31 // 已终止
)

const (
	// 广告状态名称 - 巨量引擎API返回的状态名称
	PromotionStatusName_Active            = "投放中"
	PromotionStatusName_Paused            = "已暂停"
	PromotionStatusName_AuditFailed       = "审核不通过"
	PromotionStatusName_InsufficientFunds = "账户余额不足"
	PromotionStatusName_AuditPending      = "新建审核中"
	PromotionStatusName_Terminated        = "已终止"
)

// 前端传入的状态值映射
const (
	PromotionFrontendStatus_Active            = "active"
	PromotionFrontendStatus_Paused            = "paused"
	PromotionFrontendStatus_AuditFailed       = "audit_failed"
	PromotionFrontendStatus_InsufficientFunds = "insufficient_funds"
	PromotionFrontendStatus_AuditPending      = "audit_pending"
	PromotionFrontendStatus_Terminated        = "terminated"
)

var (
	// 有效的代理类型映射
	ValidProxyTypes = map[string]struct{}{
		Proxy_Type_HTTP:   {},
		Proxy_Type_HTTPS:  {},
		Proxy_Type_SOCKS4: {},
		Proxy_Type_SOCKS5: {},
	}
)

// 任务进度事件
const (
	Process_Event_TaskProgress = "progress" // 任务进度更新
	Process_Event_TaskStart    = "start"    // 任务开始
	Process_Event_TaskEnd      = "end"      // 任务结束
	Process_Event_TaskError    = "error"    // 任务错误
)
