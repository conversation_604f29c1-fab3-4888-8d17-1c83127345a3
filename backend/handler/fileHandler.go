package handler

import (
	"embed"
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"net/http"
	"strings"
)

// FileHandler 自定义资源处理器，支持文件服务
type FileHandler struct {
	http.Handler
	assetHandler http.Handler
}

// NewFileHandler 创建自定义资源处理器
func NewFileHandler(assets embed.FS) *FileHandler {
	return &FileHandler{
		assetHandler: application.AssetFileServerFS(assets),
	}
}

// ServeHTTP 实现http.Handler接口
func (h *FileHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 处理文件API请求
	if strings.HasPrefix(r.URL.Path, "/api/preview") {
		fmt.Printf("预览文件请求: %s\n", r.URL.Path)
		full := r.URL.Query().Get("path") // 已做鉴权、白名单校验
		http.ServeFile(w, r, full)        // 浏览器 Range/seek 都能用
		return
	}
	h.assetHandler.ServeHTTP(w, r)
}
