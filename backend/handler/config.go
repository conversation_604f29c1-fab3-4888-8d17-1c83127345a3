package handler

import (
	"strconv"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/service"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types/reqType"
)

// ConfigHandler 配置处理器
type ConfigHandler struct {
	configService *service.ConfigService
}

// NewConfigHandler 创建配置处理器
func NewConfigHandler(configService *service.ConfigService) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
	}
}

// GetConfigByKey 根据键获取配置
func (h *ConfigHandler) GetConfigByKey(key string) map[string]interface{} {
	result := h.configService.GetConfigByKey(key)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// GetConfigValueByKey 根据键获取配置值
func (h *ConfigHandler) GetConfigValueByKey(key string) map[string]interface{} {
	result := h.configService.GetConfigValueByKey(key)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// CreateConfig 创建配置
func (h *ConfigHandler) CreateConfig(req reqType.CreateConfigReq) map[string]interface{} {
	config := &model.Config{
		Key:         req.Key,
		Value:       req.Value,
		Description: req.Description,
		Category:    req.Category,
		IsSystem:    req.IsSystem,
	}

	result := h.configService.CreateConfig(config)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// UpdateConfig 更新配置
func (h *ConfigHandler) UpdateConfig(req reqType.UpdateConfigReq) map[string]interface{} {
	config := &model.Config{
		ID:          req.ID,
		Key:         req.Key,
		Value:       req.Value,
		Description: req.Description,
		Category:    req.Category,
		IsSystem:    req.IsSystem,
	}

	result := h.configService.UpdateConfig(config)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// UpdateConfigByKey 根据键更新配置值
func (h *ConfigHandler) UpdateConfigByKey(key, value string) map[string]interface{} {
	result := h.configService.UpdateConfigByKey(key, value)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// UpsertConfigByKey 根据键插入或更新配置
func (h *ConfigHandler) UpsertConfigByKey(key, value, description string) map[string]interface{} {
	result := h.configService.UpsertConfigByKey(key, value, description)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// DeleteConfig 删除配置
func (h *ConfigHandler) DeleteConfig(id int64) map[string]interface{} {
	config := &model.Config{ID: id}
	result := h.configService.DeleteConfig(config)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// DeleteConfigByKey 根据键删除配置
func (h *ConfigHandler) DeleteConfigByKey(key string) map[string]interface{} {
	result := h.configService.DeleteConfigByKey(key)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// ListConfigs 获取配置列表
func (h *ConfigHandler) ListConfigs(pageStr, pageSizeStr string, conditions map[string]interface{}) map[string]interface{} {
	page, _ := strconv.Atoi(pageStr)
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(pageSizeStr)
	if pageSize <= 0 {
		pageSize = 10
	}

	result := h.configService.ListConfigs(page, pageSize, conditions)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// GetConfigsByCategory 根据分类获取配置
func (h *ConfigHandler) GetConfigsByCategory(category string) map[string]interface{} {
	result := h.configService.GetConfigsByCategory(category)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// GetAllConfigs 获取所有配置
func (h *ConfigHandler) GetAllConfigs() map[string]interface{} {
	result := h.configService.GetAllConfigs()
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// GetStrategyNotificationEmail 获取策略通知邮箱
func (h *ConfigHandler) GetStrategyNotificationEmail() map[string]interface{} {
	result := h.configService.GetStrategyNotificationEmail()
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// SetStrategyNotificationEmail 设置策略通知邮箱
func (h *ConfigHandler) SetStrategyNotificationEmail(email string) map[string]interface{} {
	result := h.configService.SetStrategyNotificationEmail(email)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// GetSystemEmail 获取系统邮箱
func (h *ConfigHandler) GetSystemEmail() map[string]interface{} {
	result := h.configService.GetSystemEmail()
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}

// SetSystemEmail 设置系统邮箱
func (h *ConfigHandler) SetSystemEmail(email string) map[string]interface{} {
	result := h.configService.SetSystemEmail(email)
	return map[string]interface{}{
		"code": result.Code,
		"msg":  result.Msg,
		"data": result.Data,
	}
}
