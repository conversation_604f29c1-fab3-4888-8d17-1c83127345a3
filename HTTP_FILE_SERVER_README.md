# Wails3 内置文件服务使用说明

## 概述

本项目使用Wails3的内置HTTP处理器来提供文件的流式读取功能，无需单独启动HTTP服务器。文件服务直接集成在Wails3应用的Asset Handler中。

## 接口说明

### 1. 文件读取接口

**接口地址：** `GET /api/file/read`

**参数：**
- `path` (必需): 文件的绝对路径

**功能特性：**
- 支持流式传输
- 支持断点续传（Range请求）
- 自动识别文件MIME类型
- 设置适当的响应头

**使用示例：**
```javascript
// 在Wails3前端中，直接使用相对路径
const response = await fetch(`/api/file/read?path=${encodeURIComponent(filePath)}`);
const blob = await response.blob();

// 断点续传（读取文件的前1024字节）
const response = await fetch(`/api/file/read?path=${encodeURIComponent(filePath)}`, {
  headers: {
    'Range': 'bytes=0-1023'
  }
});
```

### 2. 文件信息接口

**接口地址：** `GET /api/file/info`

**参数：**
- `path` (必需): 文件的绝对路径

**返回数据格式：**
```json
{
  "name": "example.txt",
  "size": 1024,
  "modTime": "2023-12-01T10:30:00Z",
  "isDir": false,
  "path": "C:/Users/<USER>/Documents/example.txt",
  "ext": ".txt",
  "mimeType": "text/plain"
}
```

**使用示例：**
```javascript
const response = await fetch(`/api/file/info?path=${encodeURIComponent(filePath)}`);
const fileInfo = await response.json();
console.log('文件大小:', fileInfo.size);
console.log('MIME类型:', fileInfo.mimeType);
```

## 支持的文件类型

服务器会根据文件扩展名自动设置正确的MIME类型：

- **文本文件**: .txt, .html, .css, .js, .json
- **图片文件**: .jpg, .jpeg, .png, .gif, .svg
- **视频文件**: .mp4, .avi, .mov
- **音频文件**: .mp3, .wav
- **文档文件**: .pdf, .doc, .docx, .xls, .xlsx
- **压缩文件**: .zip, .rar
- **其他文件**: application/octet-stream

## 技术实现

### CustomAssetHandler

项目使用自定义的`CustomAssetHandler`来扩展Wails3的默认资源处理器：

```go
type CustomAssetHandler struct {
    defaultHandler http.Handler
}

func (h *CustomAssetHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
    // 处理文件API请求
    if strings.HasPrefix(r.URL.Path, "/api/file/") {
        h.handleFileAPI(w, r)
        return
    }
    
    // 其他请求交给默认处理器
    h.defaultHandler.ServeHTTP(w, r)
}
```

### 集成方式

在`main.go`中配置：

```go
app := application.New(application.Options{
    // ... 其他配置
    Assets: application.AssetOptions{
        Handler: NewCustomAssetHandler(),
    },
})
```

## 安全特性

- 支持CORS跨域请求
- 验证文件是否存在
- 错误处理和适当的HTTP状态码
- 支持OPTIONS预检请求

## 使用注意事项

1. **文件路径编码**: URL中的文件路径需要进行URL编码，特别是包含特殊字符的路径
2. **大文件处理**: 对于大文件，建议使用流式读取避免内存溢出
3. **安全性**: 此服务器主要用于本地开发，生产环境需要考虑额外的安全措施
4. **无端口依赖**: 使用Wails3内置处理器，无需担心端口冲突问题

## 完整的前端使用示例

```javascript
class WailsFileReader {
  constructor() {
    // 使用相对路径，无需指定端口
    this.baseUrl = '/api/file';
  }

  // 获取文件信息
  async getFileInfo(filePath) {
    try {
      const response = await fetch(`${this.baseUrl}/info?path=${encodeURIComponent(filePath)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('获取文件信息失败:', error);
      throw error;
    }
  }

  // 读取文件（小文件）
  async readFile(filePath) {
    try {
      const response = await fetch(`${this.baseUrl}/read?path=${encodeURIComponent(filePath)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.blob();
    } catch (error) {
      console.error('读取文件失败:', error);
      throw error;
    }
  }

  // 流式读取文件（大文件）
  async streamFile(filePath, onChunk) {
    try {
      const response = await fetch(`${this.baseUrl}/read?path=${encodeURIComponent(filePath)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const reader = response.body.getReader();
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        onChunk(value);
      }
    } catch (error) {
      console.error('流式读取文件失败:', error);
      throw error;
    }
  }

  // 断点续传读取
  async readFileRange(filePath, start, end) {
    try {
      const headers = {};
      if (start !== undefined || end !== undefined) {
        headers['Range'] = `bytes=${start || 0}-${end || ''}`;
      }
      
      const response = await fetch(`${this.baseUrl}/read?path=${encodeURIComponent(filePath)}`, {
        headers
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.blob();
    } catch (error) {
      console.error('断点续传读取失败:', error);
      throw error;
    }
  }

  // 创建文件URL（用于video、audio、img等标签）
  createFileURL(filePath) {
    return `${this.baseUrl}/read?path=${encodeURIComponent(filePath)}`;
  }
}

// 使用示例
const fileReader = new WailsFileReader();

// 读取文件信息
const info = await fileReader.getFileInfo('C:/Users/<USER>/Documents/example.mp4');
console.log('文件大小:', info.size);

// 读取小文件
const blob = await fileReader.readFile('C:/Users/<USER>/Documents/small.txt');
const text = await blob.text();
console.log('文件内容:', text);

// 在HTML中使用文件URL
const videoElement = document.getElementById('video');
videoElement.src = fileReader.createFileURL('C:/Users/<USER>/Documents/video.mp4');

// 流式读取大文件
await fileReader.streamFile('C:/Users/<USER>/Documents/large.mp4', (chunk) => {
  console.log('接收到数据块，大小:', chunk.length);
});
```

## 优势

1. **无端口冲突**: 使用Wails3内置处理器，无需担心端口占用
2. **更好的集成**: 与Wails3应用完全集成，无需额外配置
3. **简化部署**: 无需管理独立的HTTP服务器
4. **统一管理**: 所有HTTP请求都通过同一个处理器管理
5. **更好的性能**: 减少了网络层的开销 